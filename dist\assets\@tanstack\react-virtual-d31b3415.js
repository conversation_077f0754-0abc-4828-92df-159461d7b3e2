import{r as S,b as w}from"../vendor-df163860.js";function g(o,l,e){let s=e.initialDeps??[],t;return()=>{var n,i,r,h;let c;e.key&&((n=e.debug)!=null&&n.call(e))&&(c=Date.now());const a=o();if(!(a.length!==s.length||a.some((m,f)=>s[f]!==m)))return t;s=a;let d;if(e.key&&((i=e.debug)!=null&&i.call(e))&&(d=Date.now()),t=l(...a),e.key&&((r=e.debug)!=null&&r.call(e))){const m=Math.round((Date.now()-c)*100)/100,f=Math.round((Date.now()-d)*100)/100,b=f/16,v=(u,z)=>{for(u=String(u);u.length<z;)u=" "+u;return u};console.info(`%c⏱ ${v(f,5)} /${v(m,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*b,120))}deg 100% 31%);`,e==null?void 0:e.key)}return(h=e==null?void 0:e.onChange)==null||h.call(e,t),t}}function x(o,l){if(o===void 0)throw new Error(`Unexpected undefined${l?`: ${l}`:""}`);return o}const I=(o,l)=>Math.abs(o-l)<1,y=(o,l,e)=>{let s;return function(...t){o.clearTimeout(s),s=o.setTimeout(()=>l.apply(this,t),e)}},C=o=>o,T=o=>{const l=Math.max(o.startIndex-o.overscan,0),e=Math.min(o.endIndex+o.overscan,o.count-1),s=[];for(let t=l;t<=e;t++)s.push(t);return s},_=(o,l)=>{const e=o.scrollElement;if(!e)return;const s=o.targetWindow;if(!s)return;const t=i=>{const{width:r,height:h}=i;l({width:Math.round(r),height:Math.round(h)})};if(t(e.getBoundingClientRect()),!s.ResizeObserver)return()=>{};const n=new s.ResizeObserver(i=>{const r=i[0];if(r!=null&&r.borderBoxSize){const h=r.borderBoxSize[0];if(h){t({width:h.inlineSize,height:h.blockSize});return}}t(e.getBoundingClientRect())});return n.observe(e,{box:"border-box"}),()=>{n.unobserve(e)}},E={passive:!0},R=typeof window>"u"?!0:"onscrollend"in window,k=(o,l)=>{const e=o.scrollElement;if(!e)return;const s=o.targetWindow;if(!s)return;let t=0;const n=o.options.useScrollendEvent&&R?()=>{}:y(s,()=>{l(t,!1)},o.options.isScrollingResetDelay),i=c=>()=>{const{horizontal:a,isRtl:p}=o.options;t=a?e.scrollLeft*(p&&-1||1):e.scrollTop,n(),l(t,c)},r=i(!0),h=i(!1);return h(),e.addEventListener("scroll",r,E),e.addEventListener("scrollend",h,E),()=>{e.removeEventListener("scroll",r),e.removeEventListener("scrollend",h)}},D=(o,l,e)=>{if(l!=null&&l.borderBoxSize){const s=l.borderBoxSize[0];if(s)return Math.round(s[e.options.horizontal?"inlineSize":"blockSize"])}return Math.round(o.getBoundingClientRect()[e.options.horizontal?"width":"height"])},F=(o,{adjustments:l=0,behavior:e},s)=>{var t,n;const i=o+l;(n=(t=s.scrollElement)==null?void 0:t.scrollTo)==null||n.call(t,{[s.options.horizontal?"left":"top"]:i,behavior:e})};class A{constructor(l){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const s=()=>e||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:e=new this.targetWindow.ResizeObserver(t=>{t.forEach(n=>{this._measureElement(n.target,n)})}));return{disconnect:()=>{var t;(t=s())==null||t.disconnect(),e=null},observe:t=>{var n;return(n=s())==null?void 0:n.observe(t,{box:"border-box"})},unobserve:t=>{var n;return(n=s())==null?void 0:n.unobserve(t)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([s,t])=>{typeof t>"u"&&delete e[s]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:C,rangeExtractor:T,onChange:()=>{},measureElement:D,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,...e}},this.notify=e=>{var s,t;(t=(s=this.options).onChange)==null||t.call(s,this,e)},this.maybeNotify=g(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;const s=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==s){if(this.cleanup(),!s){this.maybeNotify();return}this.scrollElement=s,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=((e=this.scrollElement)==null?void 0:e.window)??null,this.elementsCache.forEach(t=>{this.observer.observe(t)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,t=>{this.scrollRect=t,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(t,n)=>{this.scrollAdjustments=0,this.scrollDirection=n?this.getScrollOffset()<t?"forward":"backward":null,this.scrollOffset=t,this.isScrolling=n,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??(typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,s)=>{const t=new Map,n=new Map;for(let i=s-1;i>=0;i--){const r=e[i];if(t.has(r.lane))continue;const h=n.get(r.lane);if(h==null||r.end>h.end?n.set(r.lane,r):r.end<h.end&&t.set(r.lane,!0),t.size===this.options.lanes)break}return n.size===this.options.lanes?Array.from(n.values()).sort((i,r)=>i.end===r.end?i.index-r.index:i.end-r.end)[0]:void 0},this.getMeasurementOptions=g(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,s,t,n,i)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:s,scrollMargin:t,getItemKey:n,enabled:i}),{key:!1}),this.getMeasurements=g(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:s,scrollMargin:t,getItemKey:n,enabled:i},r)=>{if(!i)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(a=>{this.itemSizeCache.set(a.key,a.size)}));const h=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const c=this.measurementsCache.slice(0,h);for(let a=h;a<e;a++){const p=n(a),d=this.options.lanes===1?c[a-1]:this.getFurthestMeasurement(c,a),m=d?d.end+this.options.gap:s+t,f=r.get(p),b=typeof f=="number"?f:this.options.estimateSize(a),v=m+b,u=d?d.lane:a%this.options.lanes;c[a]={index:a,start:m,size:b,end:v,key:p,lane:u}}return this.measurementsCache=c,c},{key:!1,debug:()=>this.options.debug}),this.calculateRange=g(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset()],(e,s,t)=>this.range=e.length>0&&s>0?W({measurements:e,outerSize:s,scrollOffset:t}):null,{key:!1,debug:()=>this.options.debug}),this.getIndexes=g(()=>{let e=null,s=null;const t=this.calculateRange();return t&&(e=t.startIndex,s=t.endIndex),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,s]},(e,s,t,n,i)=>n===null||i===null?[]:e({startIndex:n,endIndex:i,overscan:s,count:t}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{const s=this.options.indexAttribute,t=e.getAttribute(s);return t?parseInt(t,10):(console.warn(`Missing attribute name '${s}={index}' on measured element.`),-1)},this._measureElement=(e,s)=>{const t=this.indexFromElement(e),n=this.measurementsCache[t];if(!n)return;const i=n.key,r=this.elementsCache.get(i);r!==e&&(r&&this.observer.unobserve(r),this.observer.observe(e),this.elementsCache.set(i,e)),e.isConnected&&this.resizeItem(t,this.options.measureElement(e,s,this))},this.resizeItem=(e,s)=>{const t=this.measurementsCache[e];if(!t)return;const n=this.itemSizeCache.get(t.key)??t.size,i=s-n;i!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(t,i,this):t.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(t.index),this.itemSizeCache=new Map(this.itemSizeCache.set(t.key,s)),this.notify(!1))},this.measureElement=e=>{if(!e){this.elementsCache.forEach((s,t)=>{s.isConnected||(this.observer.unobserve(s),this.elementsCache.delete(t))});return}this._measureElement(e,void 0)},this.getVirtualItems=g(()=>[this.getIndexes(),this.getMeasurements()],(e,s)=>{const t=[];for(let n=0,i=e.length;n<i;n++){const r=e[n],h=s[r];t.push(h)}return t},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const s=this.getMeasurements();if(s.length!==0)return x(s[O(0,s.length-1,t=>x(s[t]).start,e)])},this.getOffsetForAlignment=(e,s)=>{const t=this.getSize(),n=this.getScrollOffset();s==="auto"&&e>=n+t&&(s="end"),s==="end"&&(e-=t);const i=this.options.horizontal?"scrollWidth":"scrollHeight",h=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[i]:this.scrollElement[i]:0)-t;return Math.max(Math.min(h,e),0)},this.getOffsetForIndex=(e,s="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const t=this.measurementsCache[e];if(!t)return;const n=this.getSize(),i=this.getScrollOffset();if(s==="auto")if(t.end>=i+n-this.options.scrollPaddingEnd)s="end";else if(t.start<=i+this.options.scrollPaddingStart)s="start";else return[i,s];const r=t.start-this.options.scrollPaddingStart+(t.size-n)/2;switch(s){case"center":return[this.getOffsetForAlignment(r,s),s];case"end":return[this.getOffsetForAlignment(t.end+this.options.scrollPaddingEnd,s),s];default:return[this.getOffsetForAlignment(t.start-this.options.scrollPaddingStart,s),s]}},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:s="start",behavior:t}={})=>{this.cancelScrollToIndex(),t==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,s),{adjustments:void 0,behavior:t})},this.scrollToIndex=(e,{align:s="auto",behavior:t}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),t==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const n=this.getOffsetForIndex(e,s);if(!n)return;const[i,r]=n;this._scrollToOffset(i,{adjustments:void 0,behavior:t}),t!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){const[c]=x(this.getOffsetForIndex(e,r));I(c,this.getScrollOffset())||this.scrollToIndex(e,{align:r,behavior:t})}else this.scrollToIndex(e,{align:r,behavior:t})}))},this.scrollBy=(e,{behavior:s}={})=>{this.cancelScrollToIndex(),s==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:s})},this.getTotalSize=()=>{var e;const s=this.getMeasurements();let t;return s.length===0?t=this.options.paddingStart:t=this.options.lanes===1?((e=s[s.length-1])==null?void 0:e.end)??0:Math.max(...s.slice(-this.options.lanes).map(n=>n.end)),Math.max(t-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:s,behavior:t})=>{this.options.scrollToFn(e,{behavior:t,adjustments:s},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(l)}}const O=(o,l,e,s)=>{for(;o<=l;){const t=(o+l)/2|0,n=e(t);if(n<s)o=t+1;else if(n>s)l=t-1;else return t}return o>0?o-1:0};function W({measurements:o,outerSize:l,scrollOffset:e}){const s=o.length-1,n=O(0,s,r=>o[r].start,e);let i=n;for(;i<s&&o[i].end<e+l;)i++;return{startIndex:n,endIndex:i}}const M=typeof document<"u"?S.useLayoutEffect:S.useEffect;function j(o){const l=S.useReducer(()=>({}),{})[1],e={...o,onChange:(t,n)=>{var i;n?w.flushSync(l):l(),(i=o.onChange)==null||i.call(o,t,n)}},[s]=S.useState(()=>new A(e));return s.setOptions(e),M(()=>s._didMount(),[]),M(()=>s._willUpdate()),s}function P(o){return j({observeElementRect:_,observeElementOffset:k,scrollToFn:F,...o})}export{P as u};
