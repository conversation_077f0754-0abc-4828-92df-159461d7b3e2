import{e as ae,t as le,T as de,n as ue,B as ce}from"./aws-s3-67e39052.js";import{g as ee,p as B}from"./compressor-abfad62a.js";import{d as he}from"../vendor-df163860.js";import{D as J}from"../@fullcalendar/core-7f118d00.js";function L(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var pe=0;function te(r){return"__private_"+pe+++"_"+r}const fe={version:"3.2.2"};var x=te("callbacks"),G=te("publish");class se{constructor(){Object.defineProperty(this,G,{value:ge}),this.state={},Object.defineProperty(this,x,{writable:!0,value:new Set})}getState(){return this.state}setState(e){const s={...this.state},t={...this.state,...e};this.state=t,L(this,G)[G](s,t,e)}subscribe(e){return L(this,x)[x].add(e),()=>{L(this,x)[x].delete(e)}}}function ge(){for(var r=arguments.length,e=new Array(r),s=0;s<r;s++)e[s]=arguments[s];L(this,x)[x].forEach(t=>{t(...e)})}se.VERSION=fe.version;const Z={__proto__:null,md:"text/markdown",markdown:"text/markdown",mp4:"video/mp4",mp3:"audio/mp3",svg:"image/svg+xml",jpg:"image/jpeg",png:"image/png",webp:"image/webp",gif:"image/gif",heic:"image/heic",heif:"image/heif",yaml:"text/yaml",yml:"text/yaml",csv:"text/csv",tsv:"text/tab-separated-values",tab:"text/tab-separated-values",avi:"video/x-msvideo",mks:"video/x-matroska",mkv:"video/x-matroska",mov:"video/quicktime",dicom:"application/dicom",doc:"application/msword",docm:"application/vnd.ms-word.document.macroenabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroenabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",xla:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlc:"application/vnd.ms-excel",xlf:"application/x-xliff+xml",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroenabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xlw:"application/vnd.ms-excel",txt:"text/plain",text:"text/plain",conf:"text/plain",log:"text/plain",pdf:"application/pdf",zip:"application/zip","7z":"application/x-7z-compressed",rar:"application/x-rar-compressed",tar:"application/x-tar",gz:"application/gzip",dmg:"application/x-apple-diskimage"};function ie(r){var e;if(r.type)return r.type;const s=r.name?(e=ee(r.name).extension)==null?void 0:e.toLowerCase():null;return s&&s in Z?Z[s]:"application/octet-stream"}function me(r){return r.charCodeAt(0).toString(32)}function Q(r){let e="";return r.replace(/[^A-Z0-9]/gi,s=>(e+=`-${me(s)}`,"/"))+e}function ve(r){let e="uppy";return typeof r.name=="string"&&(e+=`-${Q(r.name.toLowerCase())}`),r.type!==void 0&&(e+=`-${r.type}`),r.meta&&typeof r.meta.relativePath=="string"&&(e+=`-${Q(r.meta.relativePath.toLowerCase())}`),r.data.size!==void 0&&(e+=`-${r.data.size}`),r.data.lastModified!==void 0&&(e+=`-${r.data.lastModified}`),e}function we(r){return!r.isRemote||!r.remote?!1:new Set(["box","dropbox","drive","facebook","unsplash"]).has(r.remote.provider)}function ye(r){if(we(r))return r.id;const e=ie(r);return ve({...r,type:e})}function Fe(r){if(r==null&&typeof navigator<"u"&&(r=navigator.userAgent),!r)return!0;const e=/Edge\/(\d+\.\d+)/.exec(r);if(!e)return!0;const t=e[1].split(".",2),i=parseInt(t[0],10),o=parseInt(t[1],10);return i<15||i===15&&o<15063||i>18||i===18&&o>=18218}function be(r,e){return e.name?e.name:r.split("/")[0]==="image"?`${r.split("/")[0]}.${r.split("/")[1]}`:"noname"}function Y(r){return r<10?`0${r}`:r.toString()}function q(){const r=new Date,e=Y(r.getHours()),s=Y(r.getMinutes()),t=Y(r.getSeconds());return`${e}:${s}:${t}`}const Se={debug:()=>{},warn:()=>{},error:function(){for(var r=arguments.length,e=new Array(r),s=0;s<r;s++)e[s]=arguments[s];return console.error(`[Uppy] [${q()}]`,...e)}},Pe={debug:function(){for(var r=arguments.length,e=new Array(r),s=0;s<r;s++)e[s]=arguments[s];return console.debug(`[Uppy] [${q()}]`,...e)},warn:function(){for(var r=arguments.length,e=new Array(r),s=0;s<r;s++)e[s]=arguments[s];return console.warn(`[Uppy] [${q()}]`,...e)},error:function(){for(var r=arguments.length,e=new Array(r),s=0;s<r;s++)e[s]=arguments[s];return console.error(`[Uppy] [${q()}]`,...e)}};function re(r,e){this.text=r=r||"",this.hasWild=~r.indexOf("*"),this.separator=e,this.parts=r.split(e)}re.prototype.match=function(r){var e=!0,s=this.parts,t,i=s.length,o;if(typeof r=="string"||r instanceof String)if(!this.hasWild&&this.text!=r)e=!1;else{for(o=(r||"").split(this.separator),t=0;e&&t<i;t++)s[t]!=="*"&&(t<o.length?e=s[t]===o[t]:e=!1);e=e&&o}else if(typeof r.splice=="function")for(e=[],t=r.length;t--;)this.match(r[t])&&(e[e.length]=r[t]);else if(typeof r=="object"){e={};for(var n in r)this.match(n)&&(e[n]=r[n])}return e};var Ue=function(r,e,s){var t=new re(r,s||/[\/\.]/);return typeof e<"u"?t.match(e):t},xe=Ue,Oe=/[\/\+\.]/,Ee=function(r,e){function s(t){var i=xe(t,r,Oe);return i&&i.length>=2}return e?s(e.split(";")[0]):s};const Te=he(Ee),$e={maxFileSize:null,minFileSize:null,maxTotalFileSize:null,maxNumberOfFiles:null,minNumberOfFiles:null,allowedFileTypes:null,requiredMetaFields:[]};class w extends Error{constructor(e,s){var t;super(e),this.isRestriction=!0,this.isUserFacing=(t=s==null?void 0:s.isUserFacing)!=null?t:!0,s!=null&&s.file&&(this.file=s.file)}}class Ae{constructor(e,s){this.i18n=s,this.getOpts=()=>{var t;const i=e();if(((t=i.restrictions)==null?void 0:t.allowedFileTypes)!=null&&!Array.isArray(i.restrictions.allowedFileTypes))throw new TypeError("`restrictions.allowedFileTypes` must be an array");return i}}validateAggregateRestrictions(e,s){const{maxTotalFileSize:t,maxNumberOfFiles:i}=this.getOpts().restrictions;if(i&&e.filter(n=>!n.isGhost).length+s.length>i)throw new w(`${this.i18n("youCanOnlyUploadX",{smart_count:i})}`);if(t){let o=e.reduce((n,a)=>{var u;return n+((u=a.size)!=null?u:0)},0);for(const n of s)if(n.size!=null&&(o+=n.size,o>t))throw new w(this.i18n("exceedsSize",{size:B(t),file:n.name}))}}validateSingleFile(e){const{maxFileSize:s,minFileSize:t,allowedFileTypes:i}=this.getOpts().restrictions;if(i&&!i.some(n=>n.includes("/")?e.type?Te(e.type.replace(/;.*?$/,""),n):!1:n[0]==="."&&e.extension?e.extension.toLowerCase()===n.slice(1).toLowerCase():!1)){const n=i.join(", ");throw new w(this.i18n("youCanOnlyUploadFileTypes",{types:n}),{file:e})}if(s&&e.size!=null&&e.size>s)throw new w(this.i18n("exceedsSize",{size:B(s),file:e.name}),{file:e});if(t&&e.size!=null&&e.size<t)throw new w(this.i18n("inferiorSize",{size:B(t)}),{file:e})}validate(e,s){s.forEach(t=>{this.validateSingleFile(t)}),this.validateAggregateRestrictions(e,s)}validateMinNumberOfFiles(e){const{minNumberOfFiles:s}=this.getOpts().restrictions;if(s&&Object.keys(e).length<s)throw new w(this.i18n("youHaveToAtLeastSelectX",{smart_count:s}))}getMissingRequiredMetaFields(e){const s=new w(this.i18n("missingRequiredMetaFieldOnFile",{fileName:e.name})),{requiredMetaFields:t}=this.getOpts().restrictions,i=[];for(const o of t)(!Object.hasOwn(e.meta,o)||e.meta[o]==="")&&i.push(o);return{missingFields:i,error:s}}}const je={strings:{addBulkFilesFailed:{0:"Failed to add %{smart_count} file due to an internal error",1:"Failed to add %{smart_count} files due to internal errors"},youCanOnlyUploadX:{0:"You can only upload %{smart_count} file",1:"You can only upload %{smart_count} files"},youHaveToAtLeastSelectX:{0:"You have to select at least %{smart_count} file",1:"You have to select at least %{smart_count} files"},exceedsSize:"%{file} exceeds maximum allowed size of %{size}",missingRequiredMetaField:"Missing required meta fields",missingRequiredMetaFieldOnFile:"Missing required meta fields in %{fileName}",inferiorSize:"This file is smaller than the allowed size of %{size}",youCanOnlyUploadFileTypes:"You can only upload: %{types}",noMoreFilesAllowed:"Cannot add more files",noDuplicates:"Cannot add the duplicate file '%{fileName}', it already exists",companionError:"Connection with Companion failed",authAborted:"Authentication aborted",companionUnauthorizeHint:"To unauthorize to your %{provider} account, please go to %{url}",failedToUpload:"Failed to upload %{file}",noInternetConnection:"No Internet connection",connectedToInternet:"Connected to the Internet",noFilesFound:"You have no files or folders here",noSearchResults:"Unfortunately, there are no results for this search",selectX:{0:"Select %{smart_count}",1:"Select %{smart_count}"},allFilesFromFolderNamed:"All files from folder %{name}",openFolderNamed:"Open folder %{name}",cancel:"Cancel",logOut:"Log out",filter:"Filter",resetFilter:"Reset filter",loading:"Loading...",loadedXFiles:"Loaded %{numFiles} files",authenticateWithTitle:"Please authenticate with %{pluginName} to select files",authenticateWith:"Connect to %{pluginName}",signInWithGoogle:"Sign in with Google",searchImages:"Search for images",enterTextToSearch:"Enter text to search for images",search:"Search",resetSearch:"Reset search",emptyFolderAdded:"No files were added from empty folder",addedNumFiles:"Added %{numFiles} file(s)",folderAlreadyAdded:'The folder "%{folder}" was already added',folderAdded:{0:"Added %{smart_count} file from %{folder}",1:"Added %{smart_count} files from %{folder}"},additionalRestrictionsFailed:"%{count} additional restrictions were not fulfilled"}};let oe,ne;function l(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Ne=0;function p(r){return"__private_"+Ne+++"_"+r}const Me={version:"3.10.0"},_={totalProgress:0,allowNewUpload:!0,error:null,recoveredState:null};var f=p("plugins"),g=p("restricter"),A=p("storeUnsubscribe"),F=p("emitter"),O=p("preProcessors"),E=p("uploaders"),P=p("postProcessors"),m=p("informAndEmit"),z=p("checkRequiredMetaFieldsOnFile"),H=p("checkRequiredMetaFields"),j=p("assertNewUploadAllowed"),X=p("transformFile"),N=p("startIfAutoProceed"),M=p("checkAndUpdateFileState"),V=p("addListeners"),y=p("updateOnlineStatus"),C=p("requestClientById"),b=p("createUpload"),W=p("getUpload"),T=p("removeUpload"),S=p("runUpload");oe=Symbol.for("uppy test: getPlugins");ne=Symbol.for("uppy test: createUpload");class k{constructor(e){Object.defineProperty(this,S,{value:He}),Object.defineProperty(this,T,{value:Ye}),Object.defineProperty(this,W,{value:Ge}),Object.defineProperty(this,b,{value:Be}),Object.defineProperty(this,V,{value:ke}),Object.defineProperty(this,M,{value:Ie}),Object.defineProperty(this,N,{value:qe}),Object.defineProperty(this,X,{value:Le}),Object.defineProperty(this,j,{value:_e}),Object.defineProperty(this,H,{value:ze}),Object.defineProperty(this,z,{value:Re}),Object.defineProperty(this,m,{value:Ce}),Object.defineProperty(this,f,{writable:!0,value:Object.create(null)}),Object.defineProperty(this,g,{writable:!0,value:void 0}),Object.defineProperty(this,A,{writable:!0,value:void 0}),Object.defineProperty(this,F,{writable:!0,value:ae()}),Object.defineProperty(this,O,{writable:!0,value:new Set}),Object.defineProperty(this,E,{writable:!0,value:new Set}),Object.defineProperty(this,P,{writable:!0,value:new Set}),this.scheduledAutoProceed=null,this.wasOffline=!1,this.calculateProgress=le((i,o)=>{const n=this.getFile(i==null?void 0:i.id);if(i==null||!n){this.log(`Not setting progress for a file that has been removed: ${i==null?void 0:i.id}`);return}if(n.progress.percentage===100){this.log(`Not setting progress for a file that has been already uploaded: ${i.id}`);return}const a=Number.isFinite(o.bytesTotal)&&o.bytesTotal>0;this.setFileState(i.id,{progress:{...n.progress,bytesUploaded:o.bytesUploaded,bytesTotal:o.bytesTotal,percentage:a?Math.round(o.bytesUploaded/o.bytesTotal*100):0}}),this.calculateTotalProgress()},500,{leading:!0,trailing:!0}),Object.defineProperty(this,y,{writable:!0,value:this.updateOnlineStatus.bind(this)}),Object.defineProperty(this,C,{writable:!0,value:new Map}),this.defaultLocale=je;const s={id:"uppy",autoProceed:!1,allowMultipleUploadBatches:!0,debug:!1,restrictions:$e,meta:{},onBeforeFileAdded:(i,o)=>!Object.hasOwn(o,i.id),onBeforeUpload:i=>i,store:new se,logger:Se,infoTimeout:5e3},t={...s,...e};this.opts={...t,restrictions:{...s.restrictions,...e&&e.restrictions}},e&&e.logger&&e.debug?this.log("You are using a custom `logger`, but also set `debug: true`, which uses built-in logger to output logs to console. Ignoring `debug: true` and using your custom `logger`.","warning"):e&&e.debug&&(this.opts.logger=Pe),this.log(`Using Core v${k.VERSION}`),this.i18nInit(),this.store=this.opts.store,this.setState({..._,plugins:{},files:{},currentUploads:{},capabilities:{uploadProgress:Fe(),individualCancellation:!0,resumableUploads:!1},meta:{...this.opts.meta},info:[]}),l(this,g)[g]=new Ae(()=>this.opts,this.i18n),l(this,A)[A]=this.store.subscribe((i,o,n)=>{this.emit("state-update",i,o,n),this.updateAll(o)}),this.opts.debug&&typeof window<"u"&&(window[this.opts.id]=this),l(this,V)[V]()}emit(e){for(var s=arguments.length,t=new Array(s>1?s-1:0),i=1;i<s;i++)t[i-1]=arguments[i];l(this,F)[F].emit(e,...t)}on(e,s){return l(this,F)[F].on(e,s),this}once(e,s){return l(this,F)[F].once(e,s),this}off(e,s){return l(this,F)[F].off(e,s),this}updateAll(e){this.iteratePlugins(s=>{s.update(e)})}setState(e){this.store.setState(e)}getState(){return this.store.getState()}patchFilesState(e){const s=this.getState().files;this.setState({files:{...s,...Object.fromEntries(Object.entries(e).map(t=>{let[i,o]=t;return[i,{...s[i],...o}]}))}})}setFileState(e,s){if(!this.getState().files[e])throw new Error(`Can’t set state for ${e} (the file could have been removed)`);this.patchFilesState({[e]:s})}i18nInit(){const e=t=>this.log(`Missing i18n string: ${t}`,"error"),s=new de([this.defaultLocale,this.opts.locale],{onMissingKey:e});this.i18n=s.translate.bind(s),this.i18nArray=s.translateArray.bind(s),this.locale=s.locale}setOptions(e){this.opts={...this.opts,...e,restrictions:{...this.opts.restrictions,...e==null?void 0:e.restrictions}},e.meta&&this.setMeta(e.meta),this.i18nInit(),e.locale&&this.iteratePlugins(s=>{s.setOptions(e)}),this.setState(void 0)}resetProgress(){const e={percentage:0,bytesUploaded:0,uploadComplete:!1,uploadStarted:null},s={...this.getState().files},t={};Object.keys(s).forEach(i=>{t[i]={...s[i],progress:{...s[i].progress,...e}}}),this.setState({files:t,..._}),this.emit("reset-progress")}clearUploadedFiles(){this.setState({..._,files:{}})}addPreProcessor(e){l(this,O)[O].add(e)}removePreProcessor(e){return l(this,O)[O].delete(e)}addPostProcessor(e){l(this,P)[P].add(e)}removePostProcessor(e){return l(this,P)[P].delete(e)}addUploader(e){l(this,E)[E].add(e)}removeUploader(e){return l(this,E)[E].delete(e)}setMeta(e){const s={...this.getState().meta,...e},t={...this.getState().files};Object.keys(t).forEach(i=>{t[i]={...t[i],meta:{...t[i].meta,...e}}}),this.log("Adding metadata:"),this.log(e),this.setState({meta:s,files:t})}setFileMeta(e,s){const t={...this.getState().files};if(!t[e]){this.log("Was trying to set metadata for a file that has been removed: ",e);return}const i={...t[e].meta,...s};t[e]={...t[e],meta:i},this.setState({files:t})}getFile(e){return this.getState().files[e]}getFiles(){const{files:e}=this.getState();return Object.values(e)}getFilesByIds(e){return e.map(s=>this.getFile(s))}getObjectOfFilesPerState(){const{files:e,totalProgress:s,error:t}=this.getState(),i=Object.values(e),o=i.filter(h=>{let{progress:K}=h;return!K.uploadComplete&&K.uploadStarted}),n=i.filter(h=>!h.progress.uploadStarted),a=i.filter(h=>h.progress.uploadStarted||h.progress.preprocess||h.progress.postprocess),u=i.filter(h=>h.progress.uploadStarted),d=i.filter(h=>h.isPaused),v=i.filter(h=>h.progress.uploadComplete),c=i.filter(h=>h.error),U=o.filter(h=>!h.isPaused),$=i.filter(h=>h.progress.preprocess||h.progress.postprocess);return{newFiles:n,startedFiles:a,uploadStartedFiles:u,pausedFiles:d,completeFiles:v,erroredFiles:c,inProgressFiles:o,inProgressNotPausedFiles:U,processingFiles:$,isUploadStarted:u.length>0,isAllComplete:s===100&&v.length===i.length&&$.length===0,isAllErrored:!!t&&c.length===i.length,isAllPaused:o.length!==0&&d.length===o.length,isUploadInProgress:o.length>0,isSomeGhost:i.some(h=>h.isGhost)}}validateRestrictions(e,s){s===void 0&&(s=this.getFiles());try{l(this,g)[g].validate(s,[e])}catch(t){return t}return null}checkIfFileAlreadyExists(e){const{files:s}=this.getState();return!!(s[e]&&!s[e].isGhost)}addFile(e){l(this,j)[j](e);const{nextFilesState:s,validFilesToAdd:t,errors:i}=l(this,M)[M]([e]),o=i.filter(a=>a.isRestriction);if(l(this,m)[m](o),i.length>0)throw i[0];this.setState({files:s});const[n]=t;return this.emit("file-added",n),this.emit("files-added",t),this.log(`Added file: ${n.name}, ${n.id}, mime type: ${n.type}`),l(this,N)[N](),n.id}addFiles(e){l(this,j)[j]();const{nextFilesState:s,validFilesToAdd:t,errors:i}=l(this,M)[M](e),o=i.filter(a=>a.isRestriction);l(this,m)[m](o);const n=i.filter(a=>!a.isRestriction);if(n.length>0){let a=`Multiple errors occurred while adding files:
`;if(n.forEach(u=>{a+=`
 * ${u.message}`}),this.info({message:this.i18n("addBulkFilesFailed",{smart_count:n.length}),details:a},"error",this.opts.infoTimeout),typeof AggregateError=="function")throw new AggregateError(n,a);{const u=new Error(a);throw u.errors=n,u}}this.setState({files:s}),t.forEach(a=>{this.emit("file-added",a)}),this.emit("files-added",t),t.length>5?this.log(`Added batch of ${t.length} files`):Object.values(t).forEach(a=>{this.log(`Added file: ${a.name}
 id: ${a.id}
 type: ${a.type}`)}),t.length>0&&l(this,N)[N]()}removeFiles(e,s){const{files:t,currentUploads:i}=this.getState(),o={...t},n={...i},a=Object.create(null);e.forEach(c=>{t[c]&&(a[c]=t[c],delete o[c])});function u(c){return a[c]===void 0}Object.keys(n).forEach(c=>{const U=i[c].fileIDs.filter(u);if(U.length===0){delete n[c];return}const{capabilities:$}=this.getState();if(U.length!==i[c].fileIDs.length&&!$.individualCancellation)throw new Error("individualCancellation is disabled");n[c]={...i[c],fileIDs:U}});const d={currentUploads:n,files:o};Object.keys(o).length===0&&(d.allowNewUpload=!0,d.error=null,d.recoveredState=null),this.setState(d),this.calculateTotalProgress();const v=Object.keys(a);v.forEach(c=>{this.emit("file-removed",a[c],s)}),v.length>5?this.log(`Removed ${v.length} files`):this.log(`Removed files: ${v.join(", ")}`)}removeFile(e,s){this.removeFiles([e],s)}pauseResume(e){if(!this.getState().capabilities.resumableUploads||this.getFile(e).progress.uploadComplete)return;const t=!(this.getFile(e).isPaused||!1);return this.setFileState(e,{isPaused:t}),this.emit("upload-pause",e,t),t}pauseAll(){const e={...this.getState().files};Object.keys(e).filter(t=>!e[t].progress.uploadComplete&&e[t].progress.uploadStarted).forEach(t=>{const i={...e[t],isPaused:!0};e[t]=i}),this.setState({files:e}),this.emit("pause-all")}resumeAll(){const e={...this.getState().files};Object.keys(e).filter(t=>!e[t].progress.uploadComplete&&e[t].progress.uploadStarted).forEach(t=>{const i={...e[t],isPaused:!1,error:null};e[t]=i}),this.setState({files:e}),this.emit("resume-all")}retryAll(){const e={...this.getState().files},s=Object.keys(e).filter(i=>e[i].error);if(s.forEach(i=>{const o={...e[i],isPaused:!1,error:null};e[i]=o}),this.setState({files:e,error:null}),this.emit("retry-all",s),s.length===0)return Promise.resolve({successful:[],failed:[]});const t=l(this,b)[b](s,{forceAllowNewUpload:!0});return l(this,S)[S](t)}cancelAll(e){let{reason:s="user"}=e===void 0?{}:e;if(this.emit("cancel-all",{reason:s}),s==="user"){const{files:t}=this.getState(),i=Object.keys(t);i.length&&this.removeFiles(i,"cancel-all"),this.setState(_)}}retryUpload(e){this.setFileState(e,{error:null,isPaused:!1}),this.emit("upload-retry",e);const s=l(this,b)[b]([e],{forceAllowNewUpload:!0});return l(this,S)[S](s)}logout(){this.iteratePlugins(e=>{var s;(s=e.provider)==null||s.logout==null||s.logout()})}calculateTotalProgress(){const s=this.getFiles().filter(d=>d.progress.uploadStarted||d.progress.preprocess||d.progress.postprocess);if(s.length===0){this.emit("progress",0),this.setState({totalProgress:0});return}const t=s.filter(d=>d.progress.bytesTotal!=null),i=s.filter(d=>d.progress.bytesTotal==null);if(t.length===0){const d=s.length*100,v=i.reduce((U,$)=>U+$.progress.percentage,0),c=Math.round(v/d*100);this.setState({totalProgress:c});return}let o=t.reduce((d,v)=>{var c;return d+((c=v.progress.bytesTotal)!=null?c:0)},0);const n=o/t.length;o+=n*i.length;let a=0;t.forEach(d=>{a+=d.progress.bytesUploaded}),i.forEach(d=>{a+=n*(d.progress.percentage||0)/100});let u=o===0?0:Math.round(a/o*100);u>100&&(u=100),this.setState({totalProgress:u}),this.emit("progress",u)}updateOnlineStatus(){var e;((e=window.navigator.onLine)!=null?e:!0)?(this.emit("is-online"),this.wasOffline&&(this.emit("back-online"),this.info(this.i18n("connectedToInternet"),"success",3e3),this.wasOffline=!1)):(this.emit("is-offline"),this.info(this.i18n("noInternetConnection"),"error",0),this.wasOffline=!0)}getID(){return this.opts.id}use(e,s){if(typeof e!="function"){const n=`Expected a plugin class, but got ${e===null?"null":typeof e}. Please verify that the plugin was imported and spelled correctly.`;throw new TypeError(n)}const t=new e(this,s),i=t.id;if(!i)throw new Error("Your plugin must have an id");if(!t.type)throw new Error("Your plugin must have a type");const o=this.getPlugin(i);if(o){const n=`Already found a plugin named '${o.id}'. Tried to use: '${i}'.
Uppy plugins must have unique \`id\` options. See https://uppy.io/docs/plugins/#id.`;throw new Error(n)}return e.VERSION&&this.log(`Using ${i} v${e.VERSION}`),t.type in l(this,f)[f]?l(this,f)[f][t.type].push(t):l(this,f)[f][t.type]=[t],t.install(),this.emit("plugin-added",t),this}getPlugin(e){for(const s of Object.values(l(this,f)[f])){const t=s.find(i=>i.id===e);if(t!=null)return t}}[oe](e){return l(this,f)[f][e]}iteratePlugins(e){Object.values(l(this,f)[f]).flat(1).forEach(e)}removePlugin(e){this.log(`Removing plugin ${e.id}`),this.emit("plugin-remove",e),e.uninstall&&e.uninstall();const s=l(this,f)[f][e.type],t=s.findIndex(n=>n.id===e.id);t!==-1&&s.splice(t,1);const o={plugins:{...this.getState().plugins,[e.id]:void 0}};this.setState(o)}close(e){let{reason:s}=e===void 0?{}:e;this.log(`Closing Uppy instance ${this.opts.id}: removing all files and uninstalling plugins`),this.cancelAll({reason:s}),l(this,A)[A](),this.iteratePlugins(t=>{this.removePlugin(t)}),typeof window<"u"&&window.removeEventListener&&(window.removeEventListener("online",l(this,y)[y]),window.removeEventListener("offline",l(this,y)[y]))}hideInfo(){const{info:e}=this.getState();this.setState({info:e.slice(1)}),this.emit("info-hidden")}info(e,s,t){s===void 0&&(s="info"),t===void 0&&(t=3e3);const i=typeof e=="object";this.setState({info:[...this.getState().info,{type:s,message:i?e.message:e,details:i?e.details:null}]}),setTimeout(()=>this.hideInfo(),t),this.emit("info-visible")}log(e,s){const{logger:t}=this.opts;switch(s){case"error":t.error(e);break;case"warning":t.warn(e);break;default:t.debug(e);break}}registerRequestClient(e,s){l(this,C)[C].set(e,s)}getRequestClientForFile(e){if(!e.remote)throw new Error(`Tried to get RequestClient for a non-remote file ${e.id}`);const s=l(this,C)[C].get(e.remote.requestClientId);if(s==null)throw new Error(`requestClientId "${e.remote.requestClientId}" not registered for file "${e.id}"`);return s}restore(e){return this.log(`Core: attempting to restore upload "${e}"`),this.getState().currentUploads[e]?l(this,S)[S](e):(l(this,T)[T](e),Promise.reject(new Error("Nonexistent upload")))}[ne](){return l(this,b)[b](...arguments)}addResultData(e,s){if(!l(this,W)[W](e)){this.log(`Not setting result for an upload that has been removed: ${e}`);return}const{currentUploads:t}=this.getState(),i={...t[e],result:{...t[e].result,...s}};this.setState({currentUploads:{...t,[e]:i}})}upload(){var e;(e=l(this,f)[f].uploader)!=null&&e.length||this.log("No uploader type plugins are used","warning");let{files:s}=this.getState();const t=this.opts.onBeforeUpload(s);return t===!1?Promise.reject(new Error("Not starting the upload because onBeforeUpload returned false")):(t&&typeof t=="object"&&(s=t,this.setState({files:s})),Promise.resolve().then(()=>l(this,g)[g].validateMinNumberOfFiles(s)).catch(i=>{throw l(this,m)[m]([i]),i}).then(()=>{if(!l(this,H)[H](s))throw new w(this.i18n("missingRequiredMetaField"))}).catch(i=>{throw i}).then(()=>{const{currentUploads:i}=this.getState(),o=Object.values(i).flatMap(u=>u.fileIDs),n=[];Object.keys(s).forEach(u=>{const d=this.getFile(u);!d.progress.uploadStarted&&o.indexOf(u)===-1&&n.push(d.id)});const a=l(this,b)[b](n);return l(this,S)[S](a)}).catch(i=>{throw this.emit("error",i),this.log(i,"error"),i}))}}function Ce(r){for(const o of r)o.isRestriction?this.emit("restriction-failed",o.file,o):this.emit("error",o,o.file),this.log(o,"warning");const e=r.filter(o=>o.isUserFacing),s=4,t=e.slice(0,s),i=e.slice(s);t.forEach(o=>{let{message:n,details:a=""}=o;this.info({message:n,details:a},"error",this.opts.infoTimeout)}),i.length>0&&this.info({message:this.i18n("additionalRestrictionsFailed",{count:i.length})})}function Re(r){const{missingFields:e,error:s}=l(this,g)[g].getMissingRequiredMetaFields(r);return e.length>0?(this.setFileState(r.id,{missingRequiredMetaFields:e}),this.log(s.message),this.emit("restriction-failed",r,s),!1):!0}function ze(r){let e=!0;for(const s of Object.values(r))l(this,z)[z](s)||(e=!1);return e}function _e(r){const{allowNewUpload:e}=this.getState();if(e===!1){const s=new w(this.i18n("noMoreFilesAllowed"),{file:r});throw l(this,m)[m]([s]),s}}function Le(r){const e=r instanceof File?{name:r.name,type:r.type,size:r.size,data:r}:r,s=ie(e),t=be(s,e),i=ee(t).extension,o=ye(e),n=e.meta||{};n.name=t,n.type=s;const a=Number.isFinite(e.data.size)?e.data.size:null;return{source:e.source||"",id:o,name:t,extension:i||"",meta:{...this.getState().meta,...n},type:s,data:e.data,progress:{percentage:0,bytesUploaded:0,bytesTotal:a,uploadComplete:!1,uploadStarted:null},size:a,isGhost:!1,isRemote:e.isRemote||!1,remote:e.remote||"",preview:e.preview}}function qe(){this.opts.autoProceed&&!this.scheduledAutoProceed&&(this.scheduledAutoProceed=setTimeout(()=>{this.scheduledAutoProceed=null,this.upload().catch(r=>{r.isRestriction||this.log(r.stack||r.message||r)})},4))}function Ie(r){const{files:e}=this.getState(),s={...e},t=[],i=[];for(const n of r)try{var o;let a=l(this,X)[X](n);const u=(o=e[a.id])==null?void 0:o.isGhost;u&&(a={...e[a.id],isGhost:!1,data:n.data},this.log(`Replaced the blob in the restored ghost file: ${a.name}, ${a.id}`));const d=this.opts.onBeforeFileAdded(a,s);if(!d&&this.checkIfFileAlreadyExists(a.id))throw new w(this.i18n("noDuplicates",{fileName:a.name}),{file:n});if(d===!1&&!u)throw new w("Cannot add the file because onBeforeFileAdded returned false.",{isUserFacing:!1,file:n});typeof d=="object"&&d!==null&&(a=d),l(this,g)[g].validateSingleFile(a),s[a.id]=a,t.push(a)}catch(a){i.push(a)}try{l(this,g)[g].validateAggregateRestrictions(Object.values(e),t)}catch(n){return i.push(n),{nextFilesState:e,validFilesToAdd:[],errors:i}}return{nextFilesState:s,validFilesToAdd:t,errors:i}}function ke(){const r=(t,i,o)=>{let n=t.message||"Unknown error";t.details&&(n+=` ${t.details}`),this.setState({error:n}),i!=null&&i.id in this.getState().files&&this.setFileState(i.id,{error:n,response:o})};this.on("error",r),this.on("upload-error",(t,i,o)=>{if(r(i,t,o),typeof i=="object"&&i.message){var n;this.log(i.message,"error");const a=new Error(this.i18n("failedToUpload",{file:(n=t==null?void 0:t.name)!=null?n:""}));a.isUserFacing=!0,a.details=i.message,i.details&&(a.details+=` ${i.details}`),l(this,m)[m]([a])}else l(this,m)[m]([i])});let e=null;this.on("upload-stalled",(t,i)=>{const{message:o}=t,n=i.map(a=>a.meta.name).join(", ");e||(this.info({message:o,details:n},"warning",this.opts.infoTimeout),e=setTimeout(()=>{e=null},this.opts.infoTimeout)),this.log(`${o} ${n}`.trim(),"warning")}),this.on("upload",()=>{this.setState({error:null})});const s=t=>{const i=t.filter(n=>{const a=n!=null&&this.getFile(n.id);return a||this.log(`Not setting progress for a file that has been removed: ${n==null?void 0:n.id}`),a}),o=Object.fromEntries(i.map(n=>[n.id,{progress:{uploadStarted:Date.now(),uploadComplete:!1,percentage:0,bytesUploaded:0,bytesTotal:n.size}}]));this.patchFilesState(o)};this.on("upload-start",t=>{t.forEach(i=>{this.emit("upload-started",i)}),s(t)}),this.on("upload-progress",this.calculateProgress),this.on("upload-success",(t,i)=>{if(t==null||!this.getFile(t.id)){this.log(`Not setting progress for a file that has been removed: ${t==null?void 0:t.id}`);return}const o=this.getFile(t.id).progress;this.setFileState(t.id,{progress:{...o,postprocess:l(this,P)[P].size>0?{mode:"indeterminate"}:void 0,uploadComplete:!0,percentage:100,bytesUploaded:o.bytesTotal},response:i,uploadURL:i.uploadURL,isPaused:!1}),t.size==null&&this.setFileState(t.id,{size:i.bytesUploaded||o.bytesTotal}),this.calculateTotalProgress()}),this.on("preprocess-progress",(t,i)=>{if(t==null||!this.getFile(t.id)){this.log(`Not setting progress for a file that has been removed: ${t==null?void 0:t.id}`);return}this.setFileState(t.id,{progress:{...this.getFile(t.id).progress,preprocess:i}})}),this.on("preprocess-complete",t=>{if(t==null||!this.getFile(t.id)){this.log(`Not setting progress for a file that has been removed: ${t==null?void 0:t.id}`);return}const i={...this.getState().files};i[t.id]={...i[t.id],progress:{...i[t.id].progress}},delete i[t.id].progress.preprocess,this.setState({files:i})}),this.on("postprocess-progress",(t,i)=>{if(t==null||!this.getFile(t.id)){this.log(`Not setting progress for a file that has been removed: ${t==null?void 0:t.id}`);return}this.setFileState(t.id,{progress:{...this.getState().files[t.id].progress,postprocess:i}})}),this.on("postprocess-complete",t=>{if(t==null||!this.getFile(t.id)){this.log(`Not setting progress for a file that has been removed: ${t==null?void 0:t.id}`);return}const i={...this.getState().files};i[t.id]={...i[t.id],progress:{...i[t.id].progress}},delete i[t.id].progress.postprocess,this.setState({files:i})}),this.on("restored",()=>{this.calculateTotalProgress()}),this.on("dashboard:file-edit-complete",t=>{t&&l(this,z)[z](t)}),typeof window<"u"&&window.addEventListener&&(window.addEventListener("online",l(this,y)[y]),window.addEventListener("offline",l(this,y)[y]),setTimeout(l(this,y)[y],3e3))}function Be(r,e){e===void 0&&(e={});const{forceAllowNewUpload:s=!1}=e,{allowNewUpload:t,currentUploads:i}=this.getState();if(!t&&!s)throw new Error("Cannot create a new upload: already uploading.");const o=ue();return this.emit("upload",{id:o,fileIDs:r}),this.setState({allowNewUpload:this.opts.allowMultipleUploadBatches!==!1&&this.opts.allowMultipleUploads!==!1,currentUploads:{...i,[o]:{fileIDs:r,step:0,result:{}}}}),o}function Ge(r){const{currentUploads:e}=this.getState();return e[r]}function Ye(r){const e={...this.getState().currentUploads};delete e[r],this.setState({currentUploads:e})}async function He(r){const e=()=>{const{currentUploads:o}=this.getState();return o[r]};let s=e();const t=[...l(this,O)[O],...l(this,E)[E],...l(this,P)[P]];try{for(let o=s.step||0;o<t.length&&s;o++){const n=t[o];this.setState({currentUploads:{...this.getState().currentUploads,[r]:{...s,step:o}}});const{fileIDs:a}=s;await n(a,r),s=e()}}catch(o){throw l(this,T)[T](r),o}if(s){s.fileIDs.forEach(u=>{const d=this.getFile(u);d&&d.progress.postprocess&&this.emit("postprocess-complete",d)});const o=s.fileIDs.map(u=>this.getFile(u)),n=o.filter(u=>!u.error),a=o.filter(u=>u.error);this.addResultData(r,{successful:n,failed:a,uploadID:r}),s=e()}let i;return s&&(i=s.result,this.emit("complete",i),l(this,T)[T](r)),i==null&&this.log(`Not setting result for an upload that has been removed: ${r}`),i}k.VERSION=Me.version;const st=k;function Ve(r){return typeof r!="object"||r===null||!("nodeType"in r)?!1:r.nodeType===Node.ELEMENT_NODE}function We(r,e){return e===void 0&&(e=document),typeof r=="string"?e.querySelector(r):Ve(r)?r:null}function Xe(r){for(var e;r&&!r.dir;)r=r.parentNode;return(e=r)==null?void 0:e.dir}function D(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Ke=0;function Je(r){return"__private_"+Ke+++"_"+r}function Ze(r){let e=null,s;return function(){for(var t=arguments.length,i=new Array(t),o=0;o<t;o++)i[o]=arguments[o];return s=i,e||(e=Promise.resolve().then(()=>(e=null,r(...s)))),e}}var R=Je("updateUI");class I extends ce{constructor(){super(...arguments),Object.defineProperty(this,R,{writable:!0,value:void 0})}getTargetPlugin(e){let s;if(typeof(e==null?void 0:e.addTarget)=="function")s=e,s instanceof I||console.warn(new Error("The provided plugin is not an instance of UIPlugin. This is an indication of a bug with the way Uppy is bundled.",{cause:{targetPlugin:s,UIPlugin:I}}));else if(typeof e=="function"){const t=e;this.uppy.iteratePlugins(i=>{i instanceof t&&(s=i)})}return s}mount(e,s){const t=s.id,i=We(e);if(i){this.isTargetDOMEl=!0;const a=document.createElement("div");return a.classList.add("uppy-Root"),D(this,R)[R]=Ze(u=>{this.uppy.getPlugin(this.id)&&(J(this.render(u),a),this.afterUpdate())}),this.uppy.log(`Installing ${t} to a DOM element '${e}'`),this.opts.replaceTargetContent&&(i.innerHTML=""),J(this.render(this.uppy.getState()),a),this.el=a,i.appendChild(a),a.dir=this.opts.direction||Xe(a)||"ltr",this.onMount(),this.el}const o=this.getTargetPlugin(e);if(o)return this.uppy.log(`Installing ${t} to ${o.id}`),this.parent=o,this.el=o.addTarget(s),this.onMount(),this.el;this.uppy.log(`Not installing ${t}`);let n=`Invalid target option given to ${t}.`;throw typeof e=="function"?n+=" The given target is not a Plugin class. Please check that you're not specifying a React Component instead of a plugin. If you are using @uppy/* packages directly, make sure you have only 1 version of @uppy/core installed: run `npm ls @uppy/core` on the command line and verify that all the versions match and are deduped correctly.":n+="If you meant to target an HTML element, please make sure that the element exists. Check that the <script> tag initializing Uppy is right before the closing </body> tag at the end of the page. (see https://github.com/transloadit/uppy/issues/1042)\n\nIf you meant to target a plugin, please confirm that your `import` statements or `require` calls are correct.",new Error(n)}render(e){throw new Error("Extend the render method to add your plugin to a DOM element")}update(e){if(this.el!=null){var s,t;(s=(t=D(this,R))[R])==null||s.call(t,e)}}unmount(){if(this.isTargetDOMEl){var e;(e=this.el)==null||e.remove()}this.onUnmount()}onMount(){}onUnmount(){}}const it=I;export{it as U,st as a,Xe as g,Ve as i};
