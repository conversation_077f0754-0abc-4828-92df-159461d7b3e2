import{a as r}from"./vendor-df163860.js";import{e as b,G as k,A as y,r as E,t as A}from"./index-ca2a1632.js";import"./pdf-lib-d770586c.js";const K=(l={refresh:!1,filter:["status,eq,1"]})=>{var d;new b;const{state:{useWarehouseState:D},dispatch:S}=r.useContext(k),{state:{},dispatch:f}=r.useContext(y),[c,W]=r.useState({all:[],single:null}),[w,h]=r.useState({all:!1,single:!1,update:!1,delete:!1}),g=r.useCallback((a={filter:[],join:[]})=>(async()=>{var u,m,s,x,j,C;try{h(t=>({...t,all:!0}));const e=await E(S,f,"warehouse",{...a!=null&&a.filter&&((u=a==null?void 0:a.filter)!=null&&u.length)?{filter:a==null?void 0:a.filter}:null,...a!=null&&a.join&&((m=a==null?void 0:a.join)!=null&&m.length)?{join:a==null?void 0:a.join}:null},"useWarehouseState");if(!(e!=null&&e.error))return W(t=>({...t,all:e==null?void 0:e.data})),e==null?void 0:e.data}catch(e){console.log(e.message);const t=(x=(s=e==null?void 0:e.response)==null?void 0:s.data)!=null&&x.message?(C=(j=e==null?void 0:e.response)==null?void 0:j.data)==null?void 0:C.message:e==null?void 0:e.message;return A(f,t),null}finally{h(e=>({...e,all:!1}))}})(),[c]);return r.useEffect(()=>{g({filter:l==null?void 0:l.filter})},[l==null?void 0:l.refresh,(d=l==null?void 0:l.filter)==null?void 0:d.length]),{warehouse:c,getWarehouses:g,loading:w}};export{K as u};
