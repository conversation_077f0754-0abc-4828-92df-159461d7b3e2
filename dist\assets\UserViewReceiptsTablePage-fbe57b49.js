import{j as s}from"./@chakra-ui/react-e5fbf24f.js";import{a as i,u as n}from"./vendor-df163860.js";import"./yup-b43a9d72.js";import{e as h,G as m,t as j,c as N}from"./index-ca2a1632.js";import"./pdf-lib-d770586c.js";import"./react-toggle-6e1dcb63.js";import"./index-f1c3f18b.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./@tanstack/react-query-eaef12f9.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@uppy/dashboard-12912511.js";import"./@uppy/core-59463b77.js";import"./@uppy/aws-s3-67e39052.js";import"./@uppy/compressor-abfad62a.js";let r=new h;const X=()=>{i.useContext(m);const{dispatch:d}=i.useContext(m),[e,t]=i.useState({}),[x,l]=i.useState(!0),c=n();return i.useEffect(function(){(async function(){try{l(!0),r.setTable("receipts");const a=await r.callRestAPI({id:Number(c==null?void 0:c.id),join:""},"GET");a.error||(t(a.model),l(!1))}catch(a){l(!1),console.log("error",a),j(d,a.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:x?s.jsx(N,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Receipts"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Customer"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.customer})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Division"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.division})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Reference"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.reference})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Purchase Order"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.purchase_order})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Quantity"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.quantity})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Campaign"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.campaign})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Category"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.category})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Receipt Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.receipt_status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Arrival Start Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.arrival_start_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Arrival End Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.arrival_end_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Sku"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.sku})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Creation Start Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.creation_start_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Creation End Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.creation_end_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Expected Start Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.expected_start_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Expected End Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.expected_end_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Notes"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.notes})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Warehouse"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.warehouse})]})})]})})};export{X as default};
