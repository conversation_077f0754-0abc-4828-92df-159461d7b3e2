import{j as s}from"./@chakra-ui/react-e5fbf24f.js";import{a as p,r as g}from"./vendor-df163860.js";import{u}from"./react-hook-form-c0d39979.js";import{e as S,G as y,A as _,c as D,o as E,r as b}from"./index-ca2a1632.js";import{c as j,a as d}from"./yup-b43a9d72.js";import"./pdf-lib-d770586c.js";import"./react-toggle-6e1dcb63.js";import"./index-f1c3f18b.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./@hookform/resolvers-6ec18a70.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./@tanstack/react-query-eaef12f9.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@uppy/dashboard-12912511.js";import"./@uppy/core-59463b77.js";import"./@uppy/aws-s3-67e39052.js";import"./@uppy/compressor-abfad62a.js";const me=({id:t,onSuccess:w,addressBookType:k={options:[],mapping:{}}})=>{new S;const r=[{header:"Row",accessor:"row"},{header:"Row",accessor:"row"},{header:"Id",accessor:"id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"address book",accessor:"address_book_id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"theater_name",accessor:"theater_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"tds_id",accessor:"tds_id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"rtk_number",accessor:"rtk_number",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"maccs_id",accessor:"maccs_id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"address",accessor:"address",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"second_address",accessor:"second_address",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"state",accessor:"state",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"city",accessor:"city",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"country",accessor:"country",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"postal_code",accessor:"postal_code",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}}],{state:{isOpen:v,path:N,listModel:a,updateModel:C},dispatch:c}=p.useContext(y),{state:R,dispatch:m}=p.useContext(_),[i,n]=p.useState([]),l=j({name:d(),type:d()}).required();u({resolver:E(l)});const h=async()=>{try{const e=await b(c,m,"theatrical_database",{filter:[`address_book_id,cs,${t}`]});e!=null&&e.error||n(e==null?void 0:e.data)}catch{}};return g.useEffect(()=>{t&&h()},[t]),s.jsx("div",{className:" mx-auto rounded p-5",children:a!=null&&a.loading?s.jsx(D,{}):s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsx("tr",{children:r.map((e,o)=>s.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${e.isSorted?"cursor-pointer":""} `,children:[e.header,s.jsx("span",{children:e.isSorted?e.isSortedDesc?" ▼":" ▲":""})]},o))})}),s.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:i==null?void 0:i.map((e,o)=>s.jsx("tr",{children:r==null?void 0:r.map((f,x)=>s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e[f.accessor]},x))},o))})]})})};export{me as ViewAddressBookTheaters,me as default};
