import{j as r}from"./@chakra-ui/react-e5fbf24f.js";import{a as m,r as p}from"./vendor-df163860.js";import{G as W,A as $,c as y,r as D,b as E}from"./index-ca2a1632.js";import{M as L}from"./MkdInput-5efaf8cf.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./pdf-lib-d770586c.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./yup-b43a9d72.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@tanstack/react-query-eaef12f9.js";import"./react-toggle-6e1dcb63.js";import"./@uppy/dashboard-12912511.js";import"./@uppy/core-59463b77.js";import"./@uppy/aws-s3-67e39052.js";import"./@uppy/compressor-abfad62a.js";import"./index-f1c3f18b.js";const ut=({name:c,label:u,errors:d,register:l,className:s="w-[23rem] ",display:g="location_name",warehouseLocation:a=0,warehouse:n=null})=>{const{dispatch:f,state:{warehouse_location:i}}=m.useContext(W),{dispatch:h}=m.useContext($),[x,b]=p.useState({}),[_,j]=p.useState([]),[M,C]=p.useState([]),S=async()=>{const t=await D(f,h,"warehouse_location",{...n?{filter:[`warehouse_id,cs,${n}`]}:null});if(!t.error){const e={};for(let o of t==null?void 0:t.data)e[o==null?void 0:o.id]=E(`${o[g]}`,{casetype:"capitalize",separator:" "});const w=t==null?void 0:t.data.map(o=>o==null?void 0:o.id);j(w),b(e),C(t==null?void 0:t.data)}};return p.useEffect(()=>{a.length||S()},[a]),r.jsx(r.Fragment,{children:i!=null&&i.loading?r.jsx(y,{count:1,counts:[2],className:`!h-[3rem] !gap-0 rounded-[.625rem] !bg-[#ebebeb] !p-0
          ${s}`}):r.jsx(L,{type:"mapping",name:c,label:u,className:`${s}`,options:[..._],mapping:x,value:a,errors:d,register:l})})};export{ut as default};
