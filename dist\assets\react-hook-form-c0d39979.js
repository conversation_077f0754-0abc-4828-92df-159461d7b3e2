import{a as D}from"./vendor-df163860.js";var fe=e=>e.type==="checkbox",ie=e=>e instanceof Date,O=e=>e==null;const et=e=>typeof e=="object";var R=e=>!O(e)&&!Array.isArray(e)&&et(e)&&!ie(e),tt=e=>R(e)&&e.target?fe(e.target)?e.target.checked:e.target.value:e,At=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,rt=(e,s)=>e.has(At(s)),xt=e=>{const s=e.constructor&&e.constructor.prototype;return R(s)&&s.hasOwnProperty("isPrototypeOf")},Re=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function p(e){let s;const t=Array.isArray(e);if(e instanceof Date)s=new Date(e);else if(e instanceof Set)s=new Set(e);else if(!(Re&&(e instanceof Blob||e instanceof FileList))&&(t||R(e)))if(s=t?[]:{},!t&&!xt(e))s=e;else for(const u in e)e.hasOwnProperty(u)&&(s[u]=p(e[u]));else return e;return s}var ce=e=>Array.isArray(e)?e.filter(Boolean):[],w=e=>e===void 0,f=(e,s,t)=>{if(!s||!R(e))return t;const u=ce(s.split(/[,[\].]+?/)).reduce((n,l)=>O(n)?n:n[l],e);return w(u)||u===e?w(e[s])?t:e[s]:u},W=e=>typeof e=="boolean";const _e={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},H={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},z={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},mt=D.createContext(null),Le=()=>D.useContext(mt);var st=(e,s,t,u=!0)=>{const n={defaultValues:s._defaultValues};for(const l in e)Object.defineProperty(n,l,{get:()=>{const y=l;return s._proxyFormState[y]!==H.all&&(s._proxyFormState[y]=!u||H.all),t&&(t[y]=!0),e[y]}});return n},B=e=>R(e)&&!Object.keys(e).length,it=(e,s,t,u)=>{t(e);const{name:n,...l}=e;return B(l)||Object.keys(l).length>=Object.keys(s).length||Object.keys(l).find(y=>s[y]===(!u||H.all))},he=e=>Array.isArray(e)?e:[e],ut=(e,s,t)=>!e||!s||e===s||he(e).some(u=>u&&(t?u===s:u.startsWith(s)||s.startsWith(u)));function Te(e){const s=D.useRef(e);s.current=e,D.useEffect(()=>{const t=!e.disabled&&s.current.subject&&s.current.subject.subscribe({next:s.current.next});return()=>{t&&t.unsubscribe()}},[e.disabled])}function Dt(e){const s=Le(),{control:t=s.control,disabled:u,name:n,exact:l}=e||{},[y,h]=D.useState(t._formState),m=D.useRef(!0),S=D.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),_=D.useRef(n);return _.current=n,Te({disabled:u,next:v=>m.current&&ut(_.current,v.name,l)&&it(v,S.current,t._updateFormState)&&h({...t._formState,...v}),subject:t._subjects.state}),D.useEffect(()=>(m.current=!0,S.current.isValid&&t._updateValid(!0),()=>{m.current=!1}),[t]),st(y,t,S.current,!1)}var $=e=>typeof e=="string",at=(e,s,t,u,n)=>$(e)?(u&&s.watch.add(e),f(t,e,n)):Array.isArray(e)?e.map(l=>(u&&s.watch.add(l),f(t,l))):(u&&(s.watchAll=!0),t);function wt(e){const s=Le(),{control:t=s.control,name:u,defaultValue:n,disabled:l,exact:y}=e||{},h=D.useRef(u);h.current=u,Te({disabled:l,subject:t._subjects.values,next:_=>{ut(h.current,_.name,y)&&S(p(at(h.current,t._names,_.values||t._formValues,!1,n)))}});const[m,S]=D.useState(t._getWatch(u,n));return D.useEffect(()=>t._removeUnmounted()),m}var Ue=e=>/^\w*$/.test(e),lt=e=>ce(e.replace(/["|']|\]/g,"").split(/\.|\[/)),x=(e,s,t)=>{let u=-1;const n=Ue(s)?[s]:lt(s),l=n.length,y=l-1;for(;++u<l;){const h=n[u];let m=t;if(u!==y){const S=e[h];m=R(S)||Array.isArray(S)?S:isNaN(+n[u+1])?{}:[]}e[h]=m,e=e[h]}return e};function qt(e){const s=Le(),{name:t,disabled:u,control:n=s.control,shouldUnregister:l}=e,y=rt(n._names.array,t),h=wt({control:n,name:t,defaultValue:f(n._formValues,t,f(n._defaultValues,t,e.defaultValue)),exact:!0}),m=Dt({control:n,name:t}),S=D.useRef(n.register(t,{...e.rules,value:h,...W(e.disabled)?{disabled:e.disabled}:{}}));return D.useEffect(()=>{const _=n._options.shouldUnregister||l,v=(N,j)=>{const T=f(n._fields,N);T&&(T._f.mount=j)};if(v(t,!0),_){const N=p(f(n._options.defaultValues,t));x(n._defaultValues,t,N),w(f(n._formValues,t))&&x(n._formValues,t,N)}return()=>{(y?_&&!n._state.action:_)?n.unregister(t):v(t,!1)}},[t,n,y,l]),D.useEffect(()=>{f(n._fields,t)&&n._updateDisabledField({disabled:u,fields:n._fields,name:t,value:f(n._fields,t)._f.value})},[u,t,n]),{field:{name:t,value:h,...W(u)||m.disabled?{disabled:m.disabled||u}:{},onChange:D.useCallback(_=>S.current.onChange({target:{value:tt(_),name:t},type:_e.CHANGE}),[t]),onBlur:D.useCallback(()=>S.current.onBlur({target:{value:f(n._formValues,t),name:t},type:_e.BLUR}),[t,n]),ref:_=>{const v=f(n._fields,t);v&&_&&(v._f.ref={focus:()=>_.focus(),select:()=>_.select(),setCustomValidity:N=>_.setCustomValidity(N),reportValidity:()=>_.reportValidity()})}},formState:m,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!f(m.errors,t)},isDirty:{enumerable:!0,get:()=>!!f(m.dirtyFields,t)},isTouched:{enumerable:!0,get:()=>!!f(m.touchedFields,t)},isValidating:{enumerable:!0,get:()=>!!f(m.validatingFields,t)},error:{enumerable:!0,get:()=>f(m.errors,t)}})}}var St=(e,s,t,u,n)=>s?{...t[e],types:{...t[e]&&t[e].types?t[e].types:{},[u]:n||!0}}:{},Ke=e=>({isOnSubmit:!e||e===H.onSubmit,isOnBlur:e===H.onBlur,isOnChange:e===H.onChange,isOnAll:e===H.all,isOnTouch:e===H.onTouched}),Ge=(e,s,t)=>!t&&(s.watchAll||s.watch.has(e)||[...s.watch].some(u=>e.startsWith(u)&&/^\.\w+/.test(e.slice(u.length))));const oe=(e,s,t,u)=>{for(const n of t||Object.keys(e)){const l=f(e,n);if(l){const{_f:y,...h}=l;if(y){if(y.refs&&y.refs[0]&&s(y.refs[0],n)&&!u)break;if(y.ref&&s(y.ref,y.name)&&!u)break;oe(h,s)}else R(h)&&oe(h,s)}}};var kt=(e,s,t)=>{const u=ce(f(e,t));return x(u,"root",s[t]),x(e,t,u),e},Oe=e=>e.type==="file",X=e=>typeof e=="function",be=e=>{if(!Re)return!1;const s=e?e.ownerDocument:0;return e instanceof(s&&s.defaultView?s.defaultView.HTMLElement:HTMLElement)},ve=e=>$(e),pe=e=>e.type==="radio",Ve=e=>e instanceof RegExp;const ze={value:!1,isValid:!1},Je={value:!0,isValid:!0};var nt=e=>{if(Array.isArray(e)){if(e.length>1){const s=e.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:s,isValid:!!s.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!w(e[0].attributes.value)?w(e[0].value)||e[0].value===""?Je:{value:e[0].value,isValid:!0}:Je:ze}return ze};const Qe={isValid:!1,value:null};var ot=e=>Array.isArray(e)?e.reduce((s,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:s,Qe):Qe;function Xe(e,s,t="validate"){if(ve(e)||Array.isArray(e)&&e.every(ve)||W(e)&&!e)return{type:t,message:ve(e)?e:"",ref:s}}var se=e=>R(e)&&!Ve(e)?e:{value:e,message:""},Ye=async(e,s,t,u,n)=>{const{ref:l,refs:y,required:h,maxLength:m,minLength:S,min:_,max:v,pattern:N,validate:j,name:T,valueAsNumber:xe,mount:J,disabled:Q}=e._f,V=f(s,T);if(!J||Q)return{};const K=y?y[0]:l,G=b=>{u&&K.reportValidity&&(K.setCustomValidity(W(b)?"":b||""),K.reportValidity())},k={},ee=pe(l),de=fe(l),Y=ee||de,te=(xe||Oe(l))&&w(l.value)&&w(V)||be(l)&&l.value===""||V===""||Array.isArray(V)&&!V.length,P=St.bind(null,T,t,k),ye=(b,F,E,U=z.maxLength,q=z.minLength)=>{const I=b?F:E;k[T]={type:b?U:q,message:I,ref:l,...P(b?U:q,I)}};if(n?!Array.isArray(V)||!V.length:h&&(!Y&&(te||O(V))||W(V)&&!V||de&&!nt(y).isValid||ee&&!ot(y).isValid)){const{value:b,message:F}=ve(h)?{value:!!h,message:h}:se(h);if(b&&(k[T]={type:z.required,message:F,ref:K,...P(z.required,F)},!t))return G(F),k}if(!te&&(!O(_)||!O(v))){let b,F;const E=se(v),U=se(_);if(!O(V)&&!isNaN(V)){const q=l.valueAsNumber||V&&+V;O(E.value)||(b=q>E.value),O(U.value)||(F=q<U.value)}else{const q=l.valueAsDate||new Date(V),I=le=>new Date(new Date().toDateString()+" "+le),ue=l.type=="time",ae=l.type=="week";$(E.value)&&V&&(b=ue?I(V)>I(E.value):ae?V>E.value:q>new Date(E.value)),$(U.value)&&V&&(F=ue?I(V)<I(U.value):ae?V<U.value:q<new Date(U.value))}if((b||F)&&(ye(!!b,E.message,U.message,z.max,z.min),!t))return G(k[T].message),k}if((m||S)&&!te&&($(V)||n&&Array.isArray(V))){const b=se(m),F=se(S),E=!O(b.value)&&V.length>+b.value,U=!O(F.value)&&V.length<+F.value;if((E||U)&&(ye(E,b.message,F.message),!t))return G(k[T].message),k}if(N&&!te&&$(V)){const{value:b,message:F}=se(N);if(Ve(b)&&!V.match(b)&&(k[T]={type:z.pattern,message:F,ref:l,...P(z.pattern,F)},!t))return G(F),k}if(j){if(X(j)){const b=await j(V,s),F=Xe(b,K);if(F&&(k[T]={...F,...P(z.validate,F.message)},!t))return G(F.message),k}else if(R(j)){let b={};for(const F in j){if(!B(b)&&!t)break;const E=Xe(await j[F](V,s),K,F);E&&(b={...E,...P(F,E.message)},G(E.message),t&&(k[T]=b))}if(!B(b)&&(k[T]={ref:K,...b},!t))return k}}return G(!0),k};function Et(e,s){const t=s.slice(0,-1).length;let u=0;for(;u<t;)e=w(e)?u++:e[s[u++]];return e}function Ct(e){for(const s in e)if(e.hasOwnProperty(s)&&!w(e[s]))return!1;return!0}function C(e,s){const t=Array.isArray(s)?s:Ue(s)?[s]:lt(s),u=t.length===1?e:Et(e,t),n=t.length-1,l=t[n];return u&&delete u[l],n!==0&&(R(u)&&B(u)||Array.isArray(u)&&Ct(u))&&C(e,t.slice(0,-1)),e}var ke=()=>{let e=[];return{get observers(){return e},next:n=>{for(const l of e)l.next&&l.next(n)},subscribe:n=>(e.push(n),{unsubscribe:()=>{e=e.filter(l=>l!==n)}}),unsubscribe:()=>{e=[]}}},Fe=e=>O(e)||!et(e);function Z(e,s){if(Fe(e)||Fe(s))return e===s;if(ie(e)&&ie(s))return e.getTime()===s.getTime();const t=Object.keys(e),u=Object.keys(s);if(t.length!==u.length)return!1;for(const n of t){const l=e[n];if(!u.includes(n))return!1;if(n!=="ref"){const y=s[n];if(ie(l)&&ie(y)||R(l)&&R(y)||Array.isArray(l)&&Array.isArray(y)?!Z(l,y):l!==y)return!1}}return!0}var ft=e=>e.type==="select-multiple",Rt=e=>pe(e)||fe(e),Ee=e=>be(e)&&e.isConnected,ct=e=>{for(const s in e)if(X(e[s]))return!0;return!1};function Ae(e,s={}){const t=Array.isArray(e);if(R(e)||t)for(const u in e)Array.isArray(e[u])||R(e[u])&&!ct(e[u])?(s[u]=Array.isArray(e[u])?[]:{},Ae(e[u],s[u])):O(e[u])||(s[u]=!0);return s}function dt(e,s,t){const u=Array.isArray(e);if(R(e)||u)for(const n in e)Array.isArray(e[n])||R(e[n])&&!ct(e[n])?w(s)||Fe(t[n])?t[n]=Array.isArray(e[n])?Ae(e[n],[]):{...Ae(e[n])}:dt(e[n],O(s)?{}:s[n],t[n]):t[n]=!Z(e[n],s[n]);return t}var ge=(e,s)=>dt(e,s,Ae(s)),yt=(e,{valueAsNumber:s,valueAsDate:t,setValueAs:u})=>w(e)?e:s?e===""?NaN:e&&+e:t&&$(e)?new Date(e):u?u(e):e;function Ce(e){const s=e.ref;if(!(e.refs?e.refs.every(t=>t.disabled):s.disabled))return Oe(s)?s.files:pe(s)?ot(e.refs).value:ft(s)?[...s.selectedOptions].map(({value:t})=>t):fe(s)?nt(e.refs).value:yt(w(s.value)?e.ref.value:s.value,e)}var Lt=(e,s,t,u)=>{const n={};for(const l of e){const y=f(s,l);y&&x(n,l,y._f)}return{criteriaMode:t,names:[...e],fields:n,shouldUseNativeValidation:u}},ne=e=>w(e)?e:Ve(e)?e.source:R(e)?Ve(e.value)?e.value.source:e.value:e,Tt=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function Ze(e,s,t){const u=f(e,t);if(u||Ue(t))return{error:u,name:t};const n=t.split(".");for(;n.length;){const l=n.join("."),y=f(s,l),h=f(e,l);if(y&&!Array.isArray(y)&&t!==l)return{name:t};if(h&&h.type)return{name:l,error:h};n.pop()}return{name:t}}var Ut=(e,s,t,u,n)=>n.isOnAll?!1:!t&&n.isOnTouch?!(s||e):(t?u.isOnBlur:n.isOnBlur)?!e:(t?u.isOnChange:n.isOnChange)?e:!0,Ot=(e,s)=>!ce(f(e,s)).length&&C(e,s);const pt={mode:H.onSubmit,reValidateMode:H.onChange,shouldFocusError:!0};function Mt(e={}){let s={...pt,...e},t={submitCount:0,isDirty:!1,isLoading:X(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1},u={},n=R(s.defaultValues)||R(s.values)?p(s.defaultValues||s.values)||{}:{},l=s.shouldUnregister?{}:p(n),y={action:!1,mount:!1,watch:!1},h={mount:new Set,unMount:new Set,array:new Set,watch:new Set},m,S=0;const _={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},v={values:ke(),array:ke(),state:ke()},N=Ke(s.mode),j=Ke(s.reValidateMode),T=s.criteriaMode===H.all,xe=r=>i=>{clearTimeout(S),S=setTimeout(r,i)},J=async r=>{if(_.isValid||r){const i=s.resolver?B((await Y()).errors):await P(u,!0);i!==t.isValid&&v.state.next({isValid:i})}},Q=(r,i)=>{(_.isValidating||_.validatingFields)&&((r||Array.from(h.mount)).forEach(a=>{a&&(i?x(t.validatingFields,a,i):C(t.validatingFields,a))}),v.state.next({validatingFields:t.validatingFields,isValidating:!B(t.validatingFields)}))},V=(r,i=[],a,d,c=!0,o=!0)=>{if(d&&a){if(y.action=!0,o&&Array.isArray(f(u,r))){const g=a(f(u,r),d.argA,d.argB);c&&x(u,r,g)}if(o&&Array.isArray(f(t.errors,r))){const g=a(f(t.errors,r),d.argA,d.argB);c&&x(t.errors,r,g),Ot(t.errors,r)}if(_.touchedFields&&o&&Array.isArray(f(t.touchedFields,r))){const g=a(f(t.touchedFields,r),d.argA,d.argB);c&&x(t.touchedFields,r,g)}_.dirtyFields&&(t.dirtyFields=ge(n,l)),v.state.next({name:r,isDirty:b(r,i),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else x(l,r,i)},K=(r,i)=>{x(t.errors,r,i),v.state.next({errors:t.errors})},G=r=>{t.errors=r,v.state.next({errors:t.errors,isValid:!1})},k=(r,i,a,d)=>{const c=f(u,r);if(c){const o=f(l,r,w(a)?f(n,r):a);w(o)||d&&d.defaultChecked||i?x(l,r,i?o:Ce(c._f)):U(r,o),y.mount&&J()}},ee=(r,i,a,d,c)=>{let o=!1,g=!1;const A={name:r},L=!!(f(u,r)&&f(u,r)._f.disabled);if(!a||d){_.isDirty&&(g=t.isDirty,t.isDirty=A.isDirty=b(),o=g!==A.isDirty);const M=L||Z(f(n,r),i);g=!!(!L&&f(t.dirtyFields,r)),M||L?C(t.dirtyFields,r):x(t.dirtyFields,r,!0),A.dirtyFields=t.dirtyFields,o=o||_.dirtyFields&&g!==!M}if(a){const M=f(t.touchedFields,r);M||(x(t.touchedFields,r,a),A.touchedFields=t.touchedFields,o=o||_.touchedFields&&M!==a)}return o&&c&&v.state.next(A),o?A:{}},de=(r,i,a,d)=>{const c=f(t.errors,r),o=_.isValid&&W(i)&&t.isValid!==i;if(e.delayError&&a?(m=xe(()=>K(r,a)),m(e.delayError)):(clearTimeout(S),m=null,a?x(t.errors,r,a):C(t.errors,r)),(a?!Z(c,a):c)||!B(d)||o){const g={...d,...o&&W(i)?{isValid:i}:{},errors:t.errors,name:r};t={...t,...g},v.state.next(g)}},Y=async r=>{Q(r,!0);const i=await s.resolver(l,s.context,Lt(r||h.mount,u,s.criteriaMode,s.shouldUseNativeValidation));return Q(r),i},te=async r=>{const{errors:i}=await Y(r);if(r)for(const a of r){const d=f(i,a);d?x(t.errors,a,d):C(t.errors,a)}else t.errors=i;return i},P=async(r,i,a={valid:!0})=>{for(const d in r){const c=r[d];if(c){const{_f:o,...g}=c;if(o){const A=h.array.has(o.name);Q([d],!0);const L=await Ye(c,l,T,s.shouldUseNativeValidation&&!i,A);if(Q([d]),L[o.name]&&(a.valid=!1,i))break;!i&&(f(L,o.name)?A?kt(t.errors,L,o.name):x(t.errors,o.name,L[o.name]):C(t.errors,o.name))}g&&await P(g,i,a)}}return a.valid},ye=()=>{for(const r of h.unMount){const i=f(u,r);i&&(i._f.refs?i._f.refs.every(a=>!Ee(a)):!Ee(i._f.ref))&&me(r)}h.unMount=new Set},b=(r,i)=>(r&&i&&x(l,r,i),!Z(Me(),n)),F=(r,i,a)=>at(r,h,{...y.mount?l:w(i)?n:$(r)?{[r]:i}:i},a,i),E=r=>ce(f(y.mount?l:n,r,e.shouldUnregister?f(n,r,[]):[])),U=(r,i,a={})=>{const d=f(u,r);let c=i;if(d){const o=d._f;o&&(!o.disabled&&x(l,r,yt(i,o)),c=be(o.ref)&&O(i)?"":i,ft(o.ref)?[...o.ref.options].forEach(g=>g.selected=c.includes(g.value)):o.refs?fe(o.ref)?o.refs.length>1?o.refs.forEach(g=>(!g.defaultChecked||!g.disabled)&&(g.checked=Array.isArray(c)?!!c.find(A=>A===g.value):c===g.value)):o.refs[0]&&(o.refs[0].checked=!!c):o.refs.forEach(g=>g.checked=g.value===c):Oe(o.ref)?o.ref.value="":(o.ref.value=c,o.ref.type||v.values.next({name:r,values:{...l}})))}(a.shouldDirty||a.shouldTouch)&&ee(r,c,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&le(r)},q=(r,i,a)=>{for(const d in i){const c=i[d],o=`${r}.${d}`,g=f(u,o);(h.array.has(r)||!Fe(c)||g&&!g._f)&&!ie(c)?q(o,c,a):U(o,c,a)}},I=(r,i,a={})=>{const d=f(u,r),c=h.array.has(r),o=p(i);x(l,r,o),c?(v.array.next({name:r,values:{...l}}),(_.isDirty||_.dirtyFields)&&a.shouldDirty&&v.state.next({name:r,dirtyFields:ge(n,l),isDirty:b(r,o)})):d&&!d._f&&!O(o)?q(r,o,a):U(r,o,a),Ge(r,h)&&v.state.next({...t}),v.values.next({name:y.mount?r:void 0,values:{...l}})},ue=async r=>{const i=r.target;let a=i.name,d=!0;const c=f(u,a),o=()=>i.type?Ce(c._f):tt(r),g=A=>{d=Number.isNaN(A)||A===f(l,a,A)};if(c){let A,L;const M=o(),re=r.type===_e.BLUR||r.type===_e.FOCUS_OUT,bt=!Tt(c._f)&&!s.resolver&&!f(t.errors,a)&&!c._f.deps||Ut(re,f(t.touchedFields,a),t.isSubmitted,j,N),we=Ge(a,h,re);x(l,a,M),re?(c._f.onBlur&&c._f.onBlur(r),m&&m(0)):c._f.onChange&&c._f.onChange(r);const Se=ee(a,M,re,!1),Vt=!B(Se)||we;if(!re&&v.values.next({name:a,type:r.type,values:{...l}}),bt)return _.isValid&&J(),Vt&&v.state.next({name:a,...we?{}:Se});if(!re&&we&&v.state.next({...t}),s.resolver){const{errors:je}=await Y([a]);if(g(M),d){const Ft=Ze(t.errors,u,a),$e=Ze(je,u,Ft.name||a);A=$e.error,a=$e.name,L=B(je)}}else Q([a],!0),A=(await Ye(c,l,T,s.shouldUseNativeValidation))[a],Q([a]),g(M),d&&(A?L=!1:_.isValid&&(L=await P(u,!0)));d&&(c._f.deps&&le(c._f.deps),de(a,L,A,Se))}},ae=(r,i)=>{if(f(t.errors,i)&&r.focus)return r.focus(),1},le=async(r,i={})=>{let a,d;const c=he(r);if(s.resolver){const o=await te(w(r)?r:c);a=B(o),d=r?!c.some(g=>f(o,g)):a}else r?(d=(await Promise.all(c.map(async o=>{const g=f(u,o);return await P(g&&g._f?{[o]:g}:g)}))).every(Boolean),!(!d&&!t.isValid)&&J()):d=a=await P(u);return v.state.next({...!$(r)||_.isValid&&a!==t.isValid?{}:{name:r},...s.resolver||!r?{isValid:a}:{},errors:t.errors}),i.shouldFocus&&!d&&oe(u,ae,r?c:h.mount),d},Me=r=>{const i={...n,...y.mount?l:{}};return w(r)?i:$(r)?f(i,r):r.map(a=>f(i,a))},Be=(r,i)=>({invalid:!!f((i||t).errors,r),isDirty:!!f((i||t).dirtyFields,r),isTouched:!!f((i||t).touchedFields,r),isValidating:!!f((i||t).validatingFields,r),error:f((i||t).errors,r)}),gt=r=>{r&&he(r).forEach(i=>C(t.errors,i)),v.state.next({errors:r?t.errors:{}})},Pe=(r,i,a)=>{const d=(f(u,r,{_f:{}})._f||{}).ref;x(t.errors,r,{...i,ref:d}),v.state.next({name:r,errors:t.errors,isValid:!1}),a&&a.shouldFocus&&d&&d.focus&&d.focus()},ht=(r,i)=>X(r)?v.values.subscribe({next:a=>r(F(void 0,i),a)}):F(r,i,!0),me=(r,i={})=>{for(const a of r?he(r):h.mount)h.mount.delete(a),h.array.delete(a),i.keepValue||(C(u,a),C(l,a)),!i.keepError&&C(t.errors,a),!i.keepDirty&&C(t.dirtyFields,a),!i.keepTouched&&C(t.touchedFields,a),!i.keepIsValidating&&C(t.validatingFields,a),!s.shouldUnregister&&!i.keepDefaultValue&&C(n,a);v.values.next({values:{...l}}),v.state.next({...t,...i.keepDirty?{isDirty:b()}:{}}),!i.keepIsValid&&J()},Ie=({disabled:r,name:i,field:a,fields:d,value:c})=>{if(W(r)){const o=r?void 0:w(c)?Ce(a?a._f:f(d,i)._f):c;x(l,i,o),ee(i,o,!1,!1,!0)}},De=(r,i={})=>{let a=f(u,r);const d=W(i.disabled);return x(u,r,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:r}},name:r,mount:!0,...i}}),h.mount.add(r),a?Ie({field:a,disabled:i.disabled,name:r,value:i.value}):k(r,!0,i.value),{...d?{disabled:i.disabled}:{},...s.progressive?{required:!!i.required,min:ne(i.min),max:ne(i.max),minLength:ne(i.minLength),maxLength:ne(i.maxLength),pattern:ne(i.pattern)}:{},name:r,onChange:ue,onBlur:ue,ref:c=>{if(c){De(r,i),a=f(u,r);const o=w(c.value)&&c.querySelectorAll&&c.querySelectorAll("input,select,textarea")[0]||c,g=Rt(o),A=a._f.refs||[];if(g?A.find(L=>L===o):o===a._f.ref)return;x(u,r,{_f:{...a._f,...g?{refs:[...A.filter(Ee),o,...Array.isArray(f(n,r))?[{}]:[]],ref:{type:o.type,name:r}}:{ref:o}}}),k(r,!1,void 0,o)}else a=f(u,r,{}),a._f&&(a._f.mount=!1),(s.shouldUnregister||i.shouldUnregister)&&!(rt(h.array,r)&&y.action)&&h.unMount.add(r)}}},Ne=()=>s.shouldFocusError&&oe(u,ae,h.mount),vt=r=>{W(r)&&(v.state.next({disabled:r}),oe(u,(i,a)=>{let d=r;const c=f(u,a);c&&W(c._f.disabled)&&(d||(d=c._f.disabled)),i.disabled=d},0,!1))},qe=(r,i)=>async a=>{let d;a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist());let c=p(l);if(v.state.next({isSubmitting:!0}),s.resolver){const{errors:o,values:g}=await Y();t.errors=o,c=g}else await P(u);if(C(t.errors,"root"),B(t.errors)){v.state.next({errors:{}});try{await r(c,a)}catch(o){d=o}}else i&&await i({...t.errors},a),Ne(),setTimeout(Ne);if(v.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:B(t.errors)&&!d,submitCount:t.submitCount+1,errors:t.errors}),d)throw d},_t=(r,i={})=>{f(u,r)&&(w(i.defaultValue)?I(r,p(f(n,r))):(I(r,i.defaultValue),x(n,r,p(i.defaultValue))),i.keepTouched||C(t.touchedFields,r),i.keepDirty||(C(t.dirtyFields,r),t.isDirty=i.defaultValue?b(r,p(f(n,r))):b()),i.keepError||(C(t.errors,r),_.isValid&&J()),v.state.next({...t}))},We=(r,i={})=>{const a=r?p(r):n,d=p(a),c=B(r),o=c?n:d;if(i.keepDefaultValues||(n=a),!i.keepValues){if(i.keepDirtyValues)for(const g of h.mount)f(t.dirtyFields,g)?x(o,g,f(l,g)):I(g,f(o,g));else{if(Re&&w(r))for(const g of h.mount){const A=f(u,g);if(A&&A._f){const L=Array.isArray(A._f.refs)?A._f.refs[0]:A._f.ref;if(be(L)){const M=L.closest("form");if(M){M.reset();break}}}}u={}}l=e.shouldUnregister?i.keepDefaultValues?p(n):{}:p(o),v.array.next({values:{...o}}),v.values.next({values:{...o}})}h={mount:i.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},y.mount=!_.isValid||!!i.keepIsValid||!!i.keepDirtyValues,y.watch=!!e.shouldUnregister,v.state.next({submitCount:i.keepSubmitCount?t.submitCount:0,isDirty:c?!1:i.keepDirty?t.isDirty:!!(i.keepDefaultValues&&!Z(r,n)),isSubmitted:i.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:c?[]:i.keepDirtyValues?i.keepDefaultValues&&l?ge(n,l):t.dirtyFields:i.keepDefaultValues&&r?ge(n,r):{},touchedFields:i.keepTouched?t.touchedFields:{},errors:i.keepErrors?t.errors:{},isSubmitSuccessful:i.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},He=(r,i)=>We(X(r)?r(l):r,i);return{control:{register:De,unregister:me,getFieldState:Be,handleSubmit:qe,setError:Pe,_executeSchema:Y,_getWatch:F,_getDirty:b,_updateValid:J,_removeUnmounted:ye,_updateFieldArray:V,_updateDisabledField:Ie,_getFieldArray:E,_reset:We,_resetDefaultValues:()=>X(s.defaultValues)&&s.defaultValues().then(r=>{He(r,s.resetOptions),v.state.next({isLoading:!1})}),_updateFormState:r=>{t={...t,...r}},_disableForm:vt,_subjects:v,_proxyFormState:_,_setErrors:G,get _fields(){return u},get _formValues(){return l},get _state(){return y},set _state(r){y=r},get _defaultValues(){return n},get _names(){return h},set _names(r){h=r},get _formState(){return t},set _formState(r){t=r},get _options(){return s},set _options(r){s={...s,...r}}},trigger:le,register:De,handleSubmit:qe,watch:ht,setValue:I,getValues:Me,reset:He,resetField:_t,clearErrors:gt,unregister:me,setError:Pe,setFocus:(r,i={})=>{const a=f(u,r),d=a&&a._f;if(d){const c=d.refs?d.refs[0]:d.ref;c.focus&&(c.focus(),i.shouldSelect&&c.select())}},getFieldState:Be}}function Wt(e={}){const s=D.useRef(),t=D.useRef(),[u,n]=D.useState({isDirty:!1,isValidating:!1,isLoading:X(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:X(e.defaultValues)?void 0:e.defaultValues});s.current||(s.current={...Mt(e),formState:u});const l=s.current.control;return l._options=e,Te({subject:l._subjects.state,next:y=>{it(y,l._proxyFormState,l._updateFormState,!0)&&n({...l._formState})}}),D.useEffect(()=>l._disableForm(e.disabled),[l,e.disabled]),D.useEffect(()=>{if(l._proxyFormState.isDirty){const y=l._getDirty();y!==u.isDirty&&l._subjects.state.next({isDirty:y})}},[l,u.isDirty]),D.useEffect(()=>{e.values&&!Z(e.values,t.current)?(l._reset(e.values,l._options.resetOptions),t.current=e.values,n(y=>({...y}))):l._resetDefaultValues()},[e.values,l]),D.useEffect(()=>{e.errors&&l._setErrors(e.errors)},[e.errors,l]),D.useEffect(()=>{l._state.mount||(l._updateValid(),l._state.mount=!0),l._state.watch&&(l._state.watch=!1,l._subjects.state.next({...l._formState})),l._removeUnmounted()}),D.useEffect(()=>{e.shouldUnregister&&l._subjects.values.next({values:l._getWatch()})},[e.shouldUnregister,l]),s.current.formState=st(u,l),s.current}export{wt as a,qt as b,St as c,f as g,x as s,Wt as u};
