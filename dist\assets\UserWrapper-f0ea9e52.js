import{j as r}from"./@chakra-ui/react-e5fbf24f.js";import{r as e,a as t}from"./vendor-df163860.js";import{u as s,G as p,S as m}from"./index-ca2a1632.js";import a from"./UserTopHeader-126475ed.js";import"./index-d2192f39.js";import{c as l}from"./config-17eada4f.js";import{B as n,a as c}from"./index-97d15536.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./pdf-lib-d770586c.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./yup-b43a9d72.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@tanstack/react-query-eaef12f9.js";import"./index-fb1d1582.js";import"./index.esm-a69c97e8.js";import"./index-99246d2f.js";const u=({children:i})=>{t.useRef(null),s();const{dispatch:d,state:{headerType:o}}=t.useContext(p);return r.jsxs(r.Fragment,{children:[["header"].includes(o)?r.jsx(a,{}):null,["backpanel"].includes(o)?r.jsx(n,{}):null,r.jsx(c,{}),r.jsx("div",{className:"overflow-hidden",children:r.jsx(e.Suspense,{fallback:r.jsx("div",{className:"flex h-svh w-full items-center justify-center",children:r.jsx(m,{size:40,color:l.primary})}),children:i})})]})},Q=e.memo(u);export{Q as default};
