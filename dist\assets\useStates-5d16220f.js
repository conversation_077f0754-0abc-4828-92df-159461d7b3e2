import{a as c}from"./vendor-df163860.js";import{e as x,G as S,A as k,z as n,B as w,t as y,F as E}from"./index-ca2a1632.js";const A=(s={refresh:!1,filter:["status,eq,1"],country:""})=>{var f;new x;const{state:{useStatesState:a},dispatch:u}=c.useContext(S),{state:{},dispatch:C}=c.useContext(k),[r,l]=c.useState([]),i=c.useCallback(()=>{(async()=>{var h,g,m,d;n(u,!0,"useStatesState");try{const e=await fetch("https://countriesnow.space/api/v0.1/countries/states"),t=await e.json();e!=null&&e.ok&&(l(()=>t==null?void 0:t.data),w(u,{data:t==null?void 0:t.data},"useStatesState")),n(u,!1,"useStatesState")}catch(e){n(u,!1,"useStatesState"),console.log(e.message);const t=(g=(h=e==null?void 0:e.response)==null?void 0:h.data)!=null&&g.message?(d=(m=e==null?void 0:e.response)==null?void 0:m.data)==null?void 0:d.message:e==null?void 0:e.message;y(C,t)}})()},[r]);return c.useEffect(()=>{if(E(a==null?void 0:a.data)||s!=null&&s.refresh)return i();l(()=>a==null?void 0:a.data)},[s==null?void 0:s.refresh,(f=s==null?void 0:s.filter)==null?void 0:f.length]),[r,l]};export{A as u};
