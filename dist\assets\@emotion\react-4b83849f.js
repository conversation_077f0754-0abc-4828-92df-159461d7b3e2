import{r as i}from"../vendor-df163860.js";import{c as F}from"./cache-febbd770.js";import{i as I}from"./utils-8a8f62c5.js";import{u as T}from"../@chakra-ui/react-e5fbf24f.js";import{s as k}from"./serialize-358fdd7f.js";var M={exports:{}},t={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=typeof Symbol=="function"&&Symbol.for,g=r?Symbol.for("react.element"):60103,E=r?Symbol.for("react.portal"):60106,y=r?Symbol.for("react.fragment"):60107,m=r?Symbol.for("react.strict_mode"):60108,p=r?Symbol.for("react.profiler"):60114,d=r?Symbol.for("react.provider"):60109,S=r?Symbol.for("react.context"):60110,w=r?Symbol.for("react.async_mode"):60111,b=r?Symbol.for("react.concurrent_mode"):60111,v=r?Symbol.for("react.forward_ref"):60112,$=r?Symbol.for("react.suspense"):60113,z=r?Symbol.for("react.suspense_list"):60120,C=r?Symbol.for("react.memo"):60115,h=r?Symbol.for("react.lazy"):60116,L=r?Symbol.for("react.block"):60121,j=r?Symbol.for("react.fundamental"):60117,O=r?Symbol.for("react.responder"):60118,q=r?Symbol.for("react.scope"):60119;function n(e){if(typeof e=="object"&&e!==null){var o=e.$$typeof;switch(o){case g:switch(e=e.type,e){case w:case b:case y:case p:case m:case $:return e;default:switch(e=e&&e.$$typeof,e){case S:case v:case h:case C:case d:return e;default:return o}}case E:return o}}}function _(e){return n(e)===b}t.AsyncMode=w;t.ConcurrentMode=b;t.ContextConsumer=S;t.ContextProvider=d;t.Element=g;t.ForwardRef=v;t.Fragment=y;t.Lazy=h;t.Memo=C;t.Portal=E;t.Profiler=p;t.StrictMode=m;t.Suspense=$;t.isAsyncMode=function(e){return _(e)||n(e)===w};t.isConcurrentMode=_;t.isContextConsumer=function(e){return n(e)===S};t.isContextProvider=function(e){return n(e)===d};t.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===g};t.isForwardRef=function(e){return n(e)===v};t.isFragment=function(e){return n(e)===y};t.isLazy=function(e){return n(e)===h};t.isMemo=function(e){return n(e)===C};t.isPortal=function(e){return n(e)===E};t.isProfiler=function(e){return n(e)===p};t.isStrictMode=function(e){return n(e)===m};t.isSuspense=function(e){return n(e)===$};t.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===y||e===b||e===p||e===m||e===$||e===z||typeof e=="object"&&e!==null&&(e.$$typeof===h||e.$$typeof===C||e.$$typeof===d||e.$$typeof===S||e.$$typeof===v||e.$$typeof===j||e.$$typeof===O||e.$$typeof===q||e.$$typeof===L)};t.typeOf=n;M.exports=t;var G=M.exports,P=G,N={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},W={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},R={};R[P.ForwardRef]=N;R[P.Memo]=W;var A=i.createContext(typeof HTMLElement<"u"?F({key:"css"}):null);A.Provider;var D=function(o){return i.forwardRef(function(x,f){var c=i.useContext(A);return o(x,c,f)})},H=i.createContext({}),Q=D(function(e,o){var x=e.styles,f=k([x],void 0,i.useContext(H)),c=i.useRef();return T(function(){var u=o.key+"-global",s=new o.sheet.constructor({key:u,nonce:o.sheet.nonce,container:o.sheet.container,speedy:o.sheet.isSpeedy}),l=!1,a=document.querySelector('style[data-emotion="'+u+" "+f.name+'"]');return o.sheet.tags.length&&(s.before=o.sheet.tags[0]),a!==null&&(l=!0,a.setAttribute("data-emotion",u),s.hydrate([a])),c.current=[s,l],function(){s.flush()}},[o]),T(function(){var u=c.current,s=u[0],l=u[1];if(l){u[1]=!1;return}if(f.next!==void 0&&I(o,f.next,!0),s.tags.length){var a=s.tags[s.tags.length-1].nextElementSibling;s.before=a,s.flush()}o.insert("",f,s,!1)},[o,f.name]),null});export{Q as G,H as T,D as w};
