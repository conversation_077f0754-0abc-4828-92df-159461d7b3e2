import{a as t}from"./vendor-df163860.js";import{e as b,G as E,A as y,w as f,B as k,t as v,F as w}from"./index-ca2a1632.js";const D=({refresh:i=!1,filter:o=["status,eq,1"],requestState:c="useServiceCarrierState"})=>{new b;const{state:r,dispatch:d}=t.useContext(E),{state:{},dispatch:m}=t.useContext(y),s=r[c],[p,n]=t.useState([]),x=t.useCallback(()=>{(async()=>{var g,l,u,C,h;try{const a=await f(d,m,{endpoint:"/v3/api/custom/qualitysign/label/list",method:"GET"},c,!1);a!=null&&a.error||(n(()=>{var e;return(e=a==null?void 0:a.data)==null?void 0:e.carriers}),k(d,{data:(g=a==null?void 0:a.data)==null?void 0:g.carriers},c))}catch(a){console.log(a.message);const e=(u=(l=a==null?void 0:a.response)==null?void 0:l.data)!=null&&u.message?(h=(C=a==null?void 0:a.response)==null?void 0:C.data)==null?void 0:h.message:a==null?void 0:a.message;v(m,e)}})()},[p]);return t.useEffect(()=>{if(w(s==null?void 0:s.data)||i)return x();n(()=>s==null?void 0:s.data)},[i,o==null?void 0:o.length]),[p,n]};export{D as u};
