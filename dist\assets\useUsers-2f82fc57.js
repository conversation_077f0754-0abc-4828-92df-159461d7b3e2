import{a as e}from"./vendor-df163860.js";import{e as f,G as x,A as h,r as C,t as r}from"./index-ca2a1632.js";const U=({refresh:a=!1,filter:p=[]})=>{new f;const{state:{},dispatch:u}=e.useContext(x),{state:{},dispatch:o}=e.useContext(h),[t,n]=e.useState([]),l=e.useCallback(()=>{(async()=>{var c,g,i,m;try{const s=await C(u,o,"user",{filter:p});s!=null&&s.error||n(()=>s==null?void 0:s.data)}catch(s){console.log(s.message);const d=(g=(c=s==null?void 0:s.response)==null?void 0:c.data)!=null&&g.message?(m=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:m.message:s==null?void 0:s.message;r(o,d)}})()},[t]);return e.useEffect(()=>{if(!(t!=null&&t.length)||a)return l()},[a]),{users:t,setUsers:n}},E=U;export{E as u};
