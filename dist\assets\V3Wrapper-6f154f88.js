import{j as y}from"./@chakra-ui/react-e5fbf24f.js";import{r as C}from"./vendor-df163860.js";import{q as h}from"./index-7c404e79.js";import{a as Z,bN as O,b5 as q,b6 as w}from"./index-ca2a1632.js";import{a as D}from"./listModel-6f35aeb5.js";import"./pdf-lib-d770586c.js";import{b as a}from"./@tanstack/react-query-eaef12f9.js";import{u as oo}from"./useSDK-3fcd5e15.js";import{getProcessedTableData as so}from"./TableRowListColumn-244cc54d.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./yup-b43a9d72.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./queryKeys-83dec963.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./config-17eada4f.js";import"./index-fc060b5c.js";const io=()=>{const{sdk:u}=oo(),{showToast:t,tokenExpireError:P}=Z();return a({mutationFn:async m=>await u.customRequest(m.endpoint,m==null?void 0:m.method,m==null?void 0:m.body),onSuccess:m=>{console.log(m)},onError:m=>{t(m.message,5e3,O.ERROR),P(m.message),console.error(m)}})},mo=async(u,t,P)=>{if(!(u!=null&&u.length))return t;let x=t;for(const m of u)["function"].includes(typeof m)&&(x=await m(x,P));return x},ro=({table:u,profile:t,processes:P=[]})=>{var J;const{showToast:x,tableState:m,globalDispatch:L,getListByFilter:$,tokenExpireError:F,create:Q,authDispatch:b}=Z(),[V,z]=C.useState(null),[M,S]=C.useState({columns:!1}),[d,f]=C.useState({ready:!1,columnState:null,processing:!1}),{isPending:W,mutateAsync:B}=io(),{queryOptions:i,columnState:g,filterState:_}=(m==null?void 0:m[u])??{queryOptions:{},columnState:{},filterState:{},paginationState:{},modalState:{}},G=C.useMemo(()=>({size:i==null?void 0:i.size,page:i==null?void 0:i.page,join:i==null?void 0:i.join,order:i==null?void 0:i.order,direction:i==null?void 0:i.direction,filter:i==null?void 0:i.filter}),[i==null?void 0:i.size,i==null?void 0:i.page,i==null?void 0:i.join,i==null?void 0:i.order,i==null?void 0:i.direction,i==null?void 0:i.filter]),{data:c,isLoading:I,error:K,isFetched:k,isFetching:v,isRefetching:Y}=D(u,G,{enabled:!!(_!=null&&_.enabled)&&!!((J=d==null?void 0:d.columnState)!=null&&J.columnsReady)}),A=C.useCallback(async s=>{if(!(g!=null&&g.columns))return null;let r=s==null?void 0:s.data;return g!=null&&g.columns&&(r=await mo(P,r,g==null?void 0:g.columns),r=await so(r,g==null?void 0:g.columns,L,b)),{data:r,total:(s==null?void 0:s.total)??0,limit:(s==null?void 0:s.limit)??0,num_pages:(s==null?void 0:s.num_pages)??0,page:(s==null?void 0:s.page)??1}},[g==null?void 0:g.columns,u,m,c==null?void 0:c.total,c==null?void 0:c.limit,c==null?void 0:c.num_pages,c==null?void 0:c.page,P,L,b]),R=C.useCallback(async(s,r)=>{try{const l=`/v3/api/custom/qualitysign/generic/search/${u}?limit=${s==null?void 0:s.limit}&page=${s==null?void 0:s.page}`,n=await B({endpoint:l,method:"POST",body:r});if(n!=null&&n.error)throw new Error(n==null?void 0:n.message);const E={...n,num_pages:Math.ceil(Number(n==null?void 0:n.total)/Number(n==null?void 0:n.limit))};f(e=>({...e,processing:!0,ready:!1}));const o=await A(E);o&&(z(()=>o),f(e=>({...e,ready:!0})))}catch(l){console.error(l),x(l.message,5e3,"error"),F(l.message)}finally{f(l=>({...l,processing:!1}))}},[]),N=C.useCallback((s,r=[])=>{var E;if(!s)return;const l=s!=null&&s.columns?JSON.parse(s==null?void 0:s.columns):[],n={...(E=m==null?void 0:m[u])==null?void 0:E.columnState,data:s,views:r,columnId:r!=null&&r.length?s==null?void 0:s.column_id:s==null?void 0:s.id,columnsReady:!0,columns:l!=null&&l.length?l:[]};f(o=>({...o,columnState:n}))},[u,m,f]),U=C.useCallback(async s=>{var r,l,n,E,o;try{S(p=>({...p,columns:!0}));const e=await $("column_views",[`model,eq,'${s}'`,`user_id,eq,${t==null?void 0:t.id}`]);if(!(e!=null&&e.error)&&((r=e==null?void 0:e.data)!=null&&r.length)){const p=e==null?void 0:e.data.find(T=>T==null?void 0:T.current_view);N(p,(l=e==null?void 0:e.data)==null?void 0:l.reverse())}else{const p=await $("column",[`model,eq,'${s}'`,"user_id,eq,0"]);if(!(p!=null&&p.error)&&((n=p==null?void 0:p.data)!=null&&n.length)){const T={name:"default",default_view:!0,current_view:!0,user_id:t==null?void 0:t.id,model:s,column_id:(E=p==null?void 0:p.data[0])==null?void 0:E.id,columns:(o=p==null?void 0:p.data[0])==null?void 0:o.columns},j=await Q("column_views",T,!1);N({...T,id:j==null?void 0:j.data},[{...T,id:j==null?void 0:j.data}])}else N(null,[])}}catch(e){console.error(e),F(e.message)}finally{S(e=>({...e,columns:!1}))}},[u,t==null?void 0:t.id,N]),H=async()=>{if(c!=null&&c.data)try{f(r=>({...r,processing:!0}));const s=await A(c);s&&(z(s),f(r=>({...r,ready:!0})))}finally{f(s=>({...s,processing:!1}))}},X=C.useCallback(s=>{f(r=>({...r,...s}))},[]);return C.useEffect(()=>{d!=null&&d.processing||v||!k||!(_!=null&&_.enabled)||H()},[c==null?void 0:c.data,_==null?void 0:_.enabled,v,k]),{error:K,fetchColumns:U,currentState:d,updateCurrentState:X,search:R,data:V,isLoading:I||W||(M==null?void 0:M.columns)||(d==null?void 0:d.processing)}},Ko=({table:u,tableRole:t="admin",defaultColumns:P=[],excludeColumns:x=[],columnModel:m=null,processes:L=[],actions:$={},actionPostion:F=[q.DROPDOWN],actionId:Q="id",tableTitle:b="",tableSchema:V=[],hasFilter:z=!0,schemaFields:M=[],showPagination:S=!0,defaultFilter:d=[],refreshRef:f=null,allowEditing:W=!1,allowSortColumns:B=!0,showSearch:i=!0,topClasses:g="",join:_=[],filterDisplays:G=[w.COLUMNS],resetFilters:c=null,defaultPageSize:I=500,searchFilter:K=[],onReady:k,maxHeight:v=null,rawFilter:Y=[],noDataComponent:A,useImage:R=!0,canChangeLimit:N=!0,selectedItemsRef:U=null,useDefaultColumns:H=!1,profile:X=null,ref:J=null})=>{const{search:s,isLoading:r,fetchColumns:l,currentState:n,updateCurrentState:E,data:o}=ro({table:u,profile:X,processes:L}),e=C.useMemo(()=>({use:!0,loading:r,pageSize:o==null?void 0:o.limit,pageCount:o==null?void 0:o.num_pages,currentPage:o==null?void 0:o.page,dataTotal:o==null?void 0:o.total,canPreviousPage:(o==null?void 0:o.page)&&(o==null?void 0:o.page)>1,canNextPage:(o==null?void 0:o.page)&&(o==null?void 0:o.page)+1<=(o==null?void 0:o.num_pages),page:o==null?void 0:o.page,pages:o==null?void 0:o.num_pages,data:(o==null?void 0:o.data)??[],limit:o==null?void 0:o.limit,total:o==null?void 0:o.total,search:s,fetchColumns:l,currentState:n,updateCurrentState:E,stringifiedData:JSON.stringify((o==null?void 0:o.data)??[])}),[o==null?void 0:o.data,r,o==null?void 0:o.page,o==null?void 0:o.num_pages,o==null?void 0:o.total,o==null?void 0:o.limit]);return y.jsx(h,{table:u,tableRole:t,defaultColumns:P,excludeColumns:x,columnModel:m,processes:L,actions:$,actionPostion:F,actionId:Q,tableTitle:b,tableSchema:V,hasFilter:z,schemaFields:M,showPagination:S,defaultFilter:d,allowEditing:W,allowSortColumns:B,showSearch:i,topClasses:g,join:_,filterDisplays:G,resetFilters:c,defaultPageSize:I,searchFilter:K,onReady:k,maxHeight:v,rawFilter:Y,noDataComponent:A,useImage:R,canChangeLimit:N,useDefaultColumns:H,externalData:e,refreshRef:f,selectedItemsRef:U,ref:J})};export{Ko as default};
