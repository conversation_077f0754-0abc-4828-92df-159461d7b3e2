function Je(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const a in n)if(a!=="default"&&!(a in e)){const i=Object.getOwnPropertyDescriptor(n,a);i&&Object.defineProperty(e,a,i.get?i:{enumerable:!0,get:()=>n[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var yr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function qe(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Er(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function n(){return this instanceof n?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}),r}var Te={exports:{}},h={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ie=Symbol.for("react.transitional.element"),Xe=Symbol.for("react.portal"),Qe=Symbol.for("react.fragment"),Ze=Symbol.for("react.strict_mode"),et=Symbol.for("react.profiler"),tt=Symbol.for("react.consumer"),rt=Symbol.for("react.context"),nt=Symbol.for("react.forward_ref"),at=Symbol.for("react.suspense"),ot=Symbol.for("react.memo"),Oe=Symbol.for("react.lazy"),me=Symbol.iterator;function it(e){return e===null||typeof e!="object"?null:(e=me&&e[me]||e["@@iterator"],typeof e=="function"?e:null)}var be={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Le=Object.assign,Ne={};function I(e,t,r){this.props=e,this.context=t,this.refs=Ne,this.updater=r||be}I.prototype.isReactComponent={};I.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};I.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ae(){}Ae.prototype=I.prototype;function se(e,t,r){this.props=e,this.context=t,this.refs=Ne,this.updater=r||be}var le=se.prototype=new Ae;le.constructor=se;Le(le,I.prototype);le.isPureReactComponent=!0;var ve=Array.isArray,_={H:null,A:null,T:null,S:null},Ue=Object.prototype.hasOwnProperty;function ue(e,t,r,n,a,i){return r=i.ref,{$$typeof:ie,type:e,key:t,ref:r!==void 0?r:null,props:i}}function st(e,t){return ue(e.type,t,void 0,void 0,void 0,e.props)}function ce(e){return typeof e=="object"&&e!==null&&e.$$typeof===ie}function lt(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var ge=/\/+/g;function Z(e,t){return typeof e=="object"&&e!==null&&e.key!=null?lt(""+e.key):t.toString(36)}function ye(){}function ut(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch(typeof e.status=="string"?e.then(ye,ye):(e.status="pending",e.then(function(t){e.status==="pending"&&(e.status="fulfilled",e.value=t)},function(t){e.status==="pending"&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}function j(e,t,r,n,a){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"bigint":case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ie:case Xe:o=!0;break;case Oe:return o=e._init,j(o(e._payload),t,r,n,a)}}if(o)return a=a(e),o=n===""?"."+Z(e,0):n,ve(a)?(r="",o!=null&&(r=o.replace(ge,"$&/")+"/"),j(a,t,r,"",function(c){return c})):a!=null&&(ce(a)&&(a=st(a,r+(a.key==null||e&&e.key===a.key?"":(""+a.key).replace(ge,"$&/")+"/")+o)),t.push(a)),1;o=0;var l=n===""?".":n+":";if(ve(e))for(var s=0;s<e.length;s++)n=e[s],i=l+Z(n,s),o+=j(n,t,r,i,a);else if(s=it(e),typeof s=="function")for(e=s.call(e),s=0;!(n=e.next()).done;)n=n.value,i=l+Z(n,s++),o+=j(n,t,r,i,a);else if(i==="object"){if(typeof e.then=="function")return j(ut(e),t,r,n,a);throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return o}function W(e,t,r){if(e==null)return e;var n=[],a=0;return j(e,n,"","",function(i){return t.call(r,i,a++)}),n}function ct(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ee=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function ft(){}h.Children={map:W,forEach:function(e,t,r){W(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return W(e,function(){t++}),t},toArray:function(e){return W(e,function(t){return t})||[]},only:function(e){if(!ce(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};h.Component=I;h.Fragment=Qe;h.Profiler=et;h.PureComponent=se;h.StrictMode=Ze;h.Suspense=at;h.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=_;h.act=function(){throw Error("act(...) is not supported in production builds of React.")};h.cache=function(e){return function(){return e.apply(null,arguments)}};h.cloneElement=function(e,t,r){if(e==null)throw Error("The argument must be a React element, but you passed "+e+".");var n=Le({},e.props),a=e.key,i=void 0;if(t!=null)for(o in t.ref!==void 0&&(i=void 0),t.key!==void 0&&(a=""+t.key),t)!Ue.call(t,o)||o==="key"||o==="__self"||o==="__source"||o==="ref"&&t.ref===void 0||(n[o]=t[o]);var o=arguments.length-2;if(o===1)n.children=r;else if(1<o){for(var l=Array(o),s=0;s<o;s++)l[s]=arguments[s+2];n.children=l}return ue(e.type,a,void 0,void 0,i,n)};h.createContext=function(e){return e={$$typeof:rt,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null},e.Provider=e,e.Consumer={$$typeof:tt,_context:e},e};h.createElement=function(e,t,r){var n,a={},i=null;if(t!=null)for(n in t.key!==void 0&&(i=""+t.key),t)Ue.call(t,n)&&n!=="key"&&n!=="__self"&&n!=="__source"&&(a[n]=t[n]);var o=arguments.length-2;if(o===1)a.children=r;else if(1<o){for(var l=Array(o),s=0;s<o;s++)l[s]=arguments[s+2];a.children=l}if(e&&e.defaultProps)for(n in o=e.defaultProps,o)a[n]===void 0&&(a[n]=o[n]);return ue(e,i,void 0,void 0,null,a)};h.createRef=function(){return{current:null}};h.forwardRef=function(e){return{$$typeof:nt,render:e}};h.isValidElement=ce;h.lazy=function(e){return{$$typeof:Oe,_payload:{_status:-1,_result:e},_init:ct}};h.memo=function(e,t){return{$$typeof:ot,type:e,compare:t===void 0?null:t}};h.startTransition=function(e){var t=_.T,r={};_.T=r;try{var n=e(),a=_.S;a!==null&&a(r,n),typeof n=="object"&&n!==null&&typeof n.then=="function"&&n.then(ft,Ee)}catch(i){Ee(i)}finally{_.T=t}};h.unstable_useCacheRefresh=function(){return _.H.useCacheRefresh()};h.use=function(e){return _.H.use(e)};h.useActionState=function(e,t,r){return _.H.useActionState(e,t,r)};h.useCallback=function(e,t){return _.H.useCallback(e,t)};h.useContext=function(e){return _.H.useContext(e)};h.useDebugValue=function(){};h.useDeferredValue=function(e,t){return _.H.useDeferredValue(e,t)};h.useEffect=function(e,t){return _.H.useEffect(e,t)};h.useId=function(){return _.H.useId()};h.useImperativeHandle=function(e,t,r){return _.H.useImperativeHandle(e,t,r)};h.useInsertionEffect=function(e,t){return _.H.useInsertionEffect(e,t)};h.useLayoutEffect=function(e,t){return _.H.useLayoutEffect(e,t)};h.useMemo=function(e,t){return _.H.useMemo(e,t)};h.useOptimistic=function(e,t){return _.H.useOptimistic(e,t)};h.useReducer=function(e,t,r){return _.H.useReducer(e,t,r)};h.useRef=function(e){return _.H.useRef(e)};h.useState=function(e){return _.H.useState(e)};h.useSyncExternalStore=function(e,t,r){return _.H.useSyncExternalStore(e,t,r)};h.useTransition=function(){return _.H.useTransition()};h.version="19.0.0";Te.exports=h;var u=Te.exports;const dt=qe(u),pt=Je({__proto__:null,default:dt},[u]);var je={exports:{}},x={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ht=u;function $e(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function L(){}var w={d:{f:L,r:function(){throw Error($e(522))},D:L,C:L,L,m:L,X:L,S:L,M:L},p:0,findDOMNode:null},mt=Symbol.for("react.portal");function vt(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:mt,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}var B=ht.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function V(e,t){if(e==="font")return"";if(typeof t=="string")return t==="use-credentials"?t:""}x.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w;x.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)throw Error($e(299));return vt(e,t,null,r)};x.flushSync=function(e){var t=B.T,r=w.p;try{if(B.T=null,w.p=2,e)return e()}finally{B.T=t,w.p=r,w.d.f()}};x.preconnect=function(e,t){typeof e=="string"&&(t?(t=t.crossOrigin,t=typeof t=="string"?t==="use-credentials"?t:"":void 0):t=null,w.d.C(e,t))};x.prefetchDNS=function(e){typeof e=="string"&&w.d.D(e)};x.preinit=function(e,t){if(typeof e=="string"&&t&&typeof t.as=="string"){var r=t.as,n=V(r,t.crossOrigin),a=typeof t.integrity=="string"?t.integrity:void 0,i=typeof t.fetchPriority=="string"?t.fetchPriority:void 0;r==="style"?w.d.S(e,typeof t.precedence=="string"?t.precedence:void 0,{crossOrigin:n,integrity:a,fetchPriority:i}):r==="script"&&w.d.X(e,{crossOrigin:n,integrity:a,fetchPriority:i,nonce:typeof t.nonce=="string"?t.nonce:void 0})}};x.preinitModule=function(e,t){if(typeof e=="string")if(typeof t=="object"&&t!==null){if(t.as==null||t.as==="script"){var r=V(t.as,t.crossOrigin);w.d.M(e,{crossOrigin:r,integrity:typeof t.integrity=="string"?t.integrity:void 0,nonce:typeof t.nonce=="string"?t.nonce:void 0})}}else t==null&&w.d.M(e)};x.preload=function(e,t){if(typeof e=="string"&&typeof t=="object"&&t!==null&&typeof t.as=="string"){var r=t.as,n=V(r,t.crossOrigin);w.d.L(e,r,{crossOrigin:n,integrity:typeof t.integrity=="string"?t.integrity:void 0,nonce:typeof t.nonce=="string"?t.nonce:void 0,type:typeof t.type=="string"?t.type:void 0,fetchPriority:typeof t.fetchPriority=="string"?t.fetchPriority:void 0,referrerPolicy:typeof t.referrerPolicy=="string"?t.referrerPolicy:void 0,imageSrcSet:typeof t.imageSrcSet=="string"?t.imageSrcSet:void 0,imageSizes:typeof t.imageSizes=="string"?t.imageSizes:void 0,media:typeof t.media=="string"?t.media:void 0})}};x.preloadModule=function(e,t){if(typeof e=="string")if(t){var r=V(t.as,t.crossOrigin);w.d.m(e,{as:typeof t.as=="string"&&t.as!=="script"?t.as:void 0,crossOrigin:r,integrity:typeof t.integrity=="string"?t.integrity:void 0})}else w.d.m(e)};x.requestFormReset=function(e){w.d.r(e)};x.unstable_batchedUpdates=function(e,t){return e(t)};x.useFormState=function(e,t,r){return B.H.useFormState(e,t,r)};x.useFormStatus=function(){return B.H.useHostTransitionStatus()};x.version="19.0.0";function Ie(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ie)}catch(e){console.error(e)}}Ie(),je.exports=x;var _r=je.exports;/**
 * @remix-run/router v1.15.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function D(){return D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},D.apply(this,arguments)}var N;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(N||(N={}));const _e="popstate";function gt(e){e===void 0&&(e={});function t(n,a){let{pathname:i,search:o,hash:l}=n.location;return te("",{pathname:i,search:o,hash:l},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:F(a)}return Et(t,r,null,e)}function C(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Me(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function yt(){return Math.random().toString(36).substr(2,8)}function Ce(e,t){return{usr:e.state,key:e.key,idx:t}}function te(e,t,r,n){return r===void 0&&(r=null),D({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?M(t):t,{state:r,key:t&&t.key||n||yt()})}function F(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function M(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function Et(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:i=!1}=n,o=a.history,l=N.Pop,s=null,c=d();c==null&&(c=0,o.replaceState(D({},o.state,{idx:c}),""));function d(){return(o.state||{idx:null}).idx}function f(){l=N.Pop;let p=d(),R=p==null?null:p-c;c=p,s&&s({action:l,location:g.location,delta:R})}function m(p,R){l=N.Push;let v=te(g.location,p,R);r&&r(v,p),c=d()+1;let E=Ce(v,c),S=g.createHref(v);try{o.pushState(E,"",S)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;a.location.assign(S)}i&&s&&s({action:l,location:g.location,delta:1})}function P(p,R){l=N.Replace;let v=te(g.location,p,R);r&&r(v,p),c=d();let E=Ce(v,c),S=g.createHref(v);o.replaceState(E,"",S),i&&s&&s({action:l,location:g.location,delta:0})}function y(p){let R=a.location.origin!=="null"?a.location.origin:a.location.href,v=typeof p=="string"?p:F(p);return v=v.replace(/ $/,"%20"),C(R,"No window.location.(origin|href) available to create URL for href: "+v),new URL(v,R)}let g={get action(){return l},get location(){return e(a,o)},listen(p){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(_e,f),s=p,()=>{a.removeEventListener(_e,f),s=null}},createHref(p){return t(a,p)},createURL:y,encodeLocation(p){let R=y(p);return{pathname:R.pathname,search:R.search,hash:R.hash}},push:m,replace:P,go(p){return o.go(p)}};return g}var Re;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Re||(Re={}));function _t(e,t,r){r===void 0&&(r="/");let n=typeof t=="string"?M(t):t,a=$(n.pathname||"/",r);if(a==null)return null;let i=ke(e);Ct(i);let o=null;for(let l=0;o==null&&l<i.length;++l){let s=At(a);o=Lt(i[l],s)}return o}function ke(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(i,o,l)=>{let s={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};s.relativePath.startsWith("/")&&(C(s.relativePath.startsWith(n),'Absolute route path "'+s.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),s.relativePath=s.relativePath.slice(n.length));let c=A([n,s.relativePath]),d=r.concat(s);i.children&&i.children.length>0&&(C(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),ke(i.children,t,d,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:Ot(c,i.index),routesMeta:d})};return e.forEach((i,o)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))a(i,o);else for(let s of Be(i.path))a(i,o,s)}),t}function Be(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),i=r.replace(/\?$/,"");if(n.length===0)return a?[i,""]:[i];let o=Be(n.join("/")),l=[];return l.push(...o.map(s=>s===""?i:[i,s].join("/"))),a&&l.push(...o),l.map(s=>e.startsWith("/")&&s===""?"/":s)}function Ct(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:bt(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Rt=/^:[\w-]+$/,St=3,Pt=2,wt=1,xt=10,Tt=-2,Se=e=>e==="*";function Ot(e,t){let r=e.split("/"),n=r.length;return r.some(Se)&&(n+=Tt),t&&(n+=Pt),r.filter(a=>!Se(a)).reduce((a,i)=>a+(Rt.test(i)?St:i===""?wt:xt),n)}function bt(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Lt(e,t){let{routesMeta:r}=e,n={},a="/",i=[];for(let o=0;o<r.length;++o){let l=r[o],s=o===r.length-1,c=a==="/"?t:t.slice(a.length)||"/",d=re({path:l.relativePath,caseSensitive:l.caseSensitive,end:s},c);if(!d)return null;Object.assign(n,d.params);let f=l.route;i.push({params:n,pathname:A([a,d.pathname]),pathnameBase:It(A([a,d.pathnameBase])),route:f}),d.pathnameBase!=="/"&&(a=A([a,d.pathnameBase]))}return i}function re(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Nt(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let i=a[0],o=i.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:n.reduce((c,d,f)=>{let{paramName:m,isOptional:P}=d;if(m==="*"){let g=l[f]||"";o=i.slice(0,i.length-g.length).replace(/(.)\/+$/,"$1")}const y=l[f];return P&&!y?c[m]=void 0:c[m]=(y||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:o,pattern:e}}function Nt(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Me(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,l,s)=>(n.push({paramName:l,isOptional:s!=null}),s?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function At(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Me(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function $(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Ut(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?M(e):e;return{pathname:r?r.startsWith("/")?r:jt(r,t):t,search:Mt(n),hash:kt(a)}}function jt(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function ee(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function $t(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function fe(e,t){let r=$t(e);return t?r.map((n,a)=>a===e.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function de(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=M(e):(a=D({},e),C(!a.pathname||!a.pathname.includes("?"),ee("?","pathname","search",a)),C(!a.pathname||!a.pathname.includes("#"),ee("#","pathname","hash",a)),C(!a.search||!a.search.includes("#"),ee("#","search","hash",a)));let i=e===""||a.pathname==="",o=i?"/":a.pathname,l;if(o==null)l=r;else{let f=t.length-1;if(!n&&o.startsWith("..")){let m=o.split("/");for(;m[0]==="..";)m.shift(),f-=1;a.pathname=m.join("/")}l=f>=0?t[f]:"/"}let s=Ut(a,l),c=o&&o!=="/"&&o.endsWith("/"),d=(i||o===".")&&r.endsWith("/");return!s.pathname.endsWith("/")&&(c||d)&&(s.pathname+="/"),s}const A=e=>e.join("/").replace(/\/\/+/g,"/"),It=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Mt=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,kt=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Bt(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const De=["post","put","patch","delete"];new Set(De);const Dt=["get",...De];new Set(Dt);/**
 * React Router v6.22.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function H(){return H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},H.apply(this,arguments)}const K=u.createContext(null),He=u.createContext(null),T=u.createContext(null),G=u.createContext(null),O=u.createContext({outlet:null,matches:[],isDataRoute:!1}),We=u.createContext(null);function Ht(e,t){let{relative:r}=t===void 0?{}:t;k()||C(!1);let{basename:n,navigator:a}=u.useContext(T),{hash:i,pathname:o,search:l}=J(e,{relative:r}),s=o;return n!=="/"&&(s=o==="/"?n:A([n,o])),a.createHref({pathname:s,search:l,hash:i})}function k(){return u.useContext(G)!=null}function U(){return k()||C(!1),u.useContext(G).location}function Fe(e){u.useContext(T).static||u.useLayoutEffect(e)}function pe(){let{isDataRoute:e}=u.useContext(O);return e?er():Wt()}function Wt(){k()||C(!1);let e=u.useContext(K),{basename:t,future:r,navigator:n}=u.useContext(T),{matches:a}=u.useContext(O),{pathname:i}=U(),o=JSON.stringify(fe(a,r.v7_relativeSplatPath)),l=u.useRef(!1);return Fe(()=>{l.current=!0}),u.useCallback(function(c,d){if(d===void 0&&(d={}),!l.current)return;if(typeof c=="number"){n.go(c);return}let f=de(c,JSON.parse(o),i,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:A([t,f.pathname])),(d.replace?n.replace:n.push)(f,d.state,d)},[t,n,o,i,e])}function Cr(){let{matches:e}=u.useContext(O),t=e[e.length-1];return t?t.params:{}}function J(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=u.useContext(T),{matches:a}=u.useContext(O),{pathname:i}=U(),o=JSON.stringify(fe(a,n.v7_relativeSplatPath));return u.useMemo(()=>de(e,JSON.parse(o),i,r==="path"),[e,o,i,r])}function Ft(e,t){return Yt(e,t)}function Yt(e,t,r,n){k()||C(!1);let{navigator:a}=u.useContext(T),{matches:i}=u.useContext(O),o=i[i.length-1],l=o?o.params:{};o&&o.pathname;let s=o?o.pathnameBase:"/";o&&o.route;let c=U(),d;if(t){var f;let p=typeof t=="string"?M(t):t;s==="/"||(f=p.pathname)!=null&&f.startsWith(s)||C(!1),d=p}else d=c;let m=d.pathname||"/",P=m;if(s!=="/"){let p=s.replace(/^\//,"").split("/");P="/"+m.replace(/^\//,"").split("/").slice(p.length).join("/")}let y=_t(e,{pathname:P}),g=Jt(y&&y.map(p=>Object.assign({},p,{params:Object.assign({},l,p.params),pathname:A([s,a.encodeLocation?a.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?s:A([s,a.encodeLocation?a.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),i,r,n);return t&&g?u.createElement(G.Provider,{value:{location:H({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:N.Pop}},g):g}function zt(){let e=Zt(),t=Bt(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),r?u.createElement("pre",{style:a},r):null,i)}const Vt=u.createElement(zt,null);class Kt extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?u.createElement(O.Provider,{value:this.props.routeContext},u.createElement(We.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Gt(e){let{routeContext:t,match:r,children:n}=e,a=u.useContext(K);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),u.createElement(O.Provider,{value:t},n)}function Jt(e,t,r,n){var a;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var i;if((i=r)!=null&&i.errors)e=r.matches;else return null}let o=e,l=(a=r)==null?void 0:a.errors;if(l!=null){let d=o.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id]));d>=0||C(!1),o=o.slice(0,Math.min(o.length,d+1))}let s=!1,c=-1;if(r&&n&&n.v7_partialHydration)for(let d=0;d<o.length;d++){let f=o[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(c=d),f.route.id){let{loaderData:m,errors:P}=r,y=f.route.loader&&m[f.route.id]===void 0&&(!P||P[f.route.id]===void 0);if(f.route.lazy||y){s=!0,c>=0?o=o.slice(0,c+1):o=[o[0]];break}}}return o.reduceRight((d,f,m)=>{let P,y=!1,g=null,p=null;r&&(P=l&&f.route.id?l[f.route.id]:void 0,g=f.route.errorElement||Vt,s&&(c<0&&m===0?(tr("route-fallback",!1),y=!0,p=null):c===m&&(y=!0,p=f.route.hydrateFallbackElement||null)));let R=t.concat(o.slice(0,m+1)),v=()=>{let E;return P?E=g:y?E=p:f.route.Component?E=u.createElement(f.route.Component,null):f.route.element?E=f.route.element:E=d,u.createElement(Gt,{match:f,routeContext:{outlet:d,matches:R,isDataRoute:r!=null},children:E})};return r&&(f.route.ErrorBoundary||f.route.errorElement||m===0)?u.createElement(Kt,{location:r.location,revalidation:r.revalidation,component:g,error:P,children:v(),routeContext:{outlet:null,matches:R,isDataRoute:!0}}):v()},null)}var Ye=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Ye||{}),Y=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Y||{});function qt(e){let t=u.useContext(K);return t||C(!1),t}function Xt(e){let t=u.useContext(He);return t||C(!1),t}function Qt(e){let t=u.useContext(O);return t||C(!1),t}function ze(e){let t=Qt(),r=t.matches[t.matches.length-1];return r.route.id||C(!1),r.route.id}function Zt(){var e;let t=u.useContext(We),r=Xt(Y.UseRouteError),n=ze(Y.UseRouteError);return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function er(){let{router:e}=qt(Ye.UseNavigateStable),t=ze(Y.UseNavigateStable),r=u.useRef(!1);return Fe(()=>{r.current=!0}),u.useCallback(function(a,i){i===void 0&&(i={}),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,H({fromRouteId:t},i)))},[e,t])}const Pe={};function tr(e,t,r){!t&&!Pe[e]&&(Pe[e]=!0)}function Rr(e){let{to:t,replace:r,state:n,relative:a}=e;k()||C(!1);let{future:i,static:o}=u.useContext(T),{matches:l}=u.useContext(O),{pathname:s}=U(),c=pe(),d=de(t,fe(l,i.v7_relativeSplatPath),s,a==="path"),f=JSON.stringify(d);return u.useEffect(()=>c(JSON.parse(f),{replace:r,state:n,relative:a}),[c,f,a,r,n]),null}function rr(e){C(!1)}function nr(e){let{basename:t="/",children:r=null,location:n,navigationType:a=N.Pop,navigator:i,static:o=!1,future:l}=e;k()&&C(!1);let s=t.replace(/^\/*/,"/"),c=u.useMemo(()=>({basename:s,navigator:i,static:o,future:H({v7_relativeSplatPath:!1},l)}),[s,l,i,o]);typeof n=="string"&&(n=M(n));let{pathname:d="/",search:f="",hash:m="",state:P=null,key:y="default"}=n,g=u.useMemo(()=>{let p=$(d,s);return p==null?null:{location:{pathname:p,search:f,hash:m,state:P,key:y},navigationType:a}},[s,d,f,m,P,y,a]);return g==null?null:u.createElement(T.Provider,{value:c},u.createElement(G.Provider,{children:r,value:g}))}function Sr(e){let{children:t,location:r}=e;return Ft(ne(t),r)}new Promise(()=>{});function ne(e,t){t===void 0&&(t=[]);let r=[];return u.Children.forEach(e,(n,a)=>{if(!u.isValidElement(n))return;let i=[...t,a];if(n.type===u.Fragment){r.push.apply(r,ne(n.props.children,i));return}n.type!==rr&&C(!1),!n.props.index||!n.props.children||C(!1);let o={id:n.props.id||i.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(o.children=ne(n.props.children,i)),r.push(o)}),r}/**
 * React Router DOM v6.22.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function z(){return z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},z.apply(this,arguments)}function Ve(e,t){if(e==null)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(t.indexOf(a)>=0)&&(r[a]=e[a]);return r}function ar(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function or(e,t){return e.button===0&&(!t||t==="_self")&&!ar(e)}function ae(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(a=>[r,a]):[[r,n]])},[]))}function ir(e,t){let r=ae(e);return t&&t.forEach((n,a)=>{r.has(a)||t.getAll(a).forEach(i=>{r.append(a,i)})}),r}const sr=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","unstable_viewTransition"],lr=["aria-current","caseSensitive","className","end","style","to","unstable_viewTransition","children"],ur="6";try{window.__reactRouterVersion=ur}catch{}const cr=u.createContext({isTransitioning:!1}),fr="startTransition",we=pt[fr];function Pr(e){let{basename:t,children:r,future:n,window:a}=e,i=u.useRef();i.current==null&&(i.current=gt({window:a,v5Compat:!0}));let o=i.current,[l,s]=u.useState({action:o.action,location:o.location}),{v7_startTransition:c}=n||{},d=u.useCallback(f=>{c&&we?we(()=>s(f)):s(f)},[s,c]);return u.useLayoutEffect(()=>o.listen(d),[o,d]),u.createElement(nr,{basename:t,children:r,location:l.location,navigationType:l.action,navigator:o,future:n})}const dr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",pr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,hr=u.forwardRef(function(t,r){let{onClick:n,relative:a,reloadDocument:i,replace:o,state:l,target:s,to:c,preventScrollReset:d,unstable_viewTransition:f}=t,m=Ve(t,sr),{basename:P}=u.useContext(T),y,g=!1;if(typeof c=="string"&&pr.test(c)&&(y=c,dr))try{let E=new URL(window.location.href),S=c.startsWith("//")?new URL(E.protocol+c):new URL(c),b=$(S.pathname,P);S.origin===E.origin&&b!=null?c=b+S.search+S.hash:g=!0}catch{}let p=Ht(c,{relative:a}),R=vr(c,{replace:o,state:l,target:s,preventScrollReset:d,relative:a,unstable_viewTransition:f});function v(E){n&&n(E),E.defaultPrevented||R(E)}return u.createElement("a",z({},m,{href:y||p,onClick:g||i?n:v,ref:r,target:s}))}),wr=u.forwardRef(function(t,r){let{"aria-current":n="page",caseSensitive:a=!1,className:i="",end:o=!1,style:l,to:s,unstable_viewTransition:c,children:d}=t,f=Ve(t,lr),m=J(s,{relative:f.relative}),P=U(),y=u.useContext(He),{navigator:g,basename:p}=u.useContext(T),R=y!=null&&gr(m)&&c===!0,v=g.encodeLocation?g.encodeLocation(m).pathname:m.pathname,E=P.pathname,S=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;a||(E=E.toLowerCase(),S=S?S.toLowerCase():null,v=v.toLowerCase()),S&&p&&(S=$(S,p)||S);const b=v!=="/"&&v.endsWith("/")?v.length-1:v.length;let q=E===v||!o&&E.startsWith(v)&&E.charAt(b)==="/",he=S!=null&&(S===v||!o&&S.startsWith(v)&&S.charAt(v.length)==="/"),X={isActive:q,isPending:he,isTransitioning:R},Ke=q?n:void 0,Q;typeof i=="function"?Q=i(X):Q=[i,q?"active":null,he?"pending":null,R?"transitioning":null].filter(Boolean).join(" ");let Ge=typeof l=="function"?l(X):l;return u.createElement(hr,z({},f,{"aria-current":Ke,className:Q,ref:r,style:Ge,to:s,unstable_viewTransition:c}),typeof d=="function"?d(X):d)});var oe;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(oe||(oe={}));var xe;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(xe||(xe={}));function mr(e){let t=u.useContext(K);return t||C(!1),t}function vr(e,t){let{target:r,replace:n,state:a,preventScrollReset:i,relative:o,unstable_viewTransition:l}=t===void 0?{}:t,s=pe(),c=U(),d=J(e,{relative:o});return u.useCallback(f=>{if(or(f,r)){f.preventDefault();let m=n!==void 0?n:F(c)===F(d);s(e,{replace:m,state:a,preventScrollReset:i,relative:o,unstable_viewTransition:l})}},[c,s,d,n,a,r,e,i,o,l])}function xr(e){let t=u.useRef(ae(e)),r=u.useRef(!1),n=U(),a=u.useMemo(()=>ir(n.search,r.current?null:t.current),[n.search]),i=pe(),o=u.useCallback((l,s)=>{const c=ae(typeof l=="function"?l(a):l);r.current=!0,i("?"+c,s)},[i,a]);return[a,o]}function gr(e,t){t===void 0&&(t={});let r=u.useContext(cr);r==null&&C(!1);let{basename:n}=mr(oe.useViewTransitionState),a=J(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=$(r.currentLocation.pathname,n)||r.currentLocation.pathname,o=$(r.nextLocation.pathname,n)||r.nextLocation.pathname;return re(a.pathname,o)!=null||re(a.pathname,i)!=null}export{Pr as B,hr as L,wr as N,pt as R,dt as a,_r as b,yr as c,qe as d,pe as e,xr as f,Er as g,U as h,Rr as i,Sr as j,rr as k,u as r,Cr as u};
