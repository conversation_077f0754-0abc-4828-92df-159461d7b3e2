import{j as t}from"./@chakra-ui/react-e5fbf24f.js";import"./vendor-df163860.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";const h=({className:o="",stroke:r="black",onClick:i=()=>{}})=>t.jsx("svg",{className:`${o}`,onClick:i,width:"18",height:"16",viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.00053 7.12508V10.0417M9.00053 12.1251V12.1167M8.28147 1.68609L1.19365 13.7872C0.868261 14.3428 1.2689 15.0417 1.91272 15.0417H16.0884C16.7322 15.0417 17.1328 14.3428 16.8074 13.7872L9.71961 1.68609C9.39772 1.13654 8.60335 1.13654 8.28147 1.68609ZM9.20887 12.1251C9.20887 12.2401 9.11559 12.3334 9.00053 12.3334C8.88548 12.3334 8.7922 12.2401 8.7922 12.1251C8.7922 12.01 8.88548 11.9167 9.00053 11.9167C9.11559 11.9167 9.20887 12.01 9.20887 12.1251Z",stroke:r,strokeWidth:"1.5",strokeLinecap:"round"})});export{h as default};
