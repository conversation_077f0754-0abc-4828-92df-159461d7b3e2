import{d as ie,a as s}from"../vendor-df163860.js";var $={exports:{}},ue="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",ce=ue,pe=ce;function Y(){}function K(){}K.resetWarningCache=Y;var le=function(){function r(n,u,a,l,d,o){if(o!==pe){var v=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw v.name="Invariant Violation",v}}r.isRequired=r;function e(){return r}var t={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:e,element:r,elementType:r,instanceOf:e,node:r,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:K,resetWarningCache:Y};return t.PropTypes=t,t};$.exports=le();var fe=$.exports;const i=ie(fe);function W(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(u){return Object.getOwnPropertyDescriptor(r,u).enumerable})),t.push.apply(t,n)}return t}function B(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?W(Object(t),!0).forEach(function(n){J(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):W(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function T(r){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?T=function(e){return typeof e}:T=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(r)}function J(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function z(r,e){return de(r)||me(r,e)||ye(r,e)||ve()}function de(r){if(Array.isArray(r))return r}function me(r,e){var t=r&&(typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"]);if(t!=null){var n=[],u=!0,a=!1,l,d;try{for(t=t.call(r);!(u=(l=t.next()).done)&&(n.push(l.value),!(e&&n.length===e));u=!0);}catch(o){a=!0,d=o}finally{try{!u&&t.return!=null&&t.return()}finally{if(a)throw d}}return n}}function ye(r,e){if(r){if(typeof r=="string")return M(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);if(t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set")return Array.from(r);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return M(r,e)}}function M(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function ve(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var y=function(e,t,n){var u=!!n,a=s.useRef(n);s.useEffect(function(){a.current=n},[n]),s.useEffect(function(){if(!u||!e)return function(){};var l=function(){a.current&&a.current.apply(a,arguments)};return e.on(t,l),function(){e.off(t,l)}},[u,t,e,a])},_=function(e){var t=s.useRef(e);return s.useEffect(function(){t.current=e},[e]),t.current},O=function(e){return e!==null&&T(e)==="object"},he=function(e){return O(e)&&typeof e.then=="function"},Ce=function(e){return O(e)&&typeof e.elements=="function"&&typeof e.createToken=="function"&&typeof e.createPaymentMethod=="function"&&typeof e.confirmCardPayment=="function"},D="[object Object]",ge=function r(e,t){if(!O(e)||!O(t))return e===t;var n=Array.isArray(e),u=Array.isArray(t);if(n!==u)return!1;var a=Object.prototype.toString.call(e)===D,l=Object.prototype.toString.call(t)===D;if(a!==l)return!1;if(!a&&!n)return e===t;var d=Object.keys(e),o=Object.keys(t);if(d.length!==o.length)return!1;for(var v={},h=0;h<d.length;h+=1)v[d[h]]=!0;for(var g=0;g<o.length;g+=1)v[o[g]]=!0;var f=Object.keys(v);if(f.length!==d.length)return!1;var k=e,E=t,S=function(x){return r(k[x],E[x])};return f.every(S)},H=function(e,t,n){return O(e)?Object.keys(e).reduce(function(u,a){var l=!O(t)||!ge(e[a],t[a]);return n.includes(a)?(l&&console.warn("Unsupported prop change: options.".concat(a," is not a mutable property.")),u):l?B(B({},u||{}),{},J({},a,e[a])):u},null):null},V="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",q=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:V;if(e===null||Ce(e))return e;throw new Error(t)},Se=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:V;if(he(e))return{tag:"async",stripePromise:Promise.resolve(e).then(function(u){return q(u,t)})};var n=q(e,t);return n===null?{tag:"empty"}:{tag:"sync",stripe:n}},be=function(e){!e||!e._registerWrapper||!e.registerAppInfo||(e._registerWrapper({name:"react-stripe-js",version:"2.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"2.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},I=s.createContext(null);I.displayName="ElementsContext";var Ee=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},ke=function(e){var t=e.stripe,n=e.options,u=e.children,a=s.useMemo(function(){return Se(t)},[t]),l=s.useState(function(){return{stripe:a.tag==="sync"?a.stripe:null,elements:a.tag==="sync"?a.stripe.elements(n):null}}),d=z(l,2),o=d[0],v=d[1];s.useEffect(function(){var f=!0,k=function(S){v(function(P){return P.stripe?P:{stripe:S,elements:S.elements(n)}})};return a.tag==="async"&&!o.stripe?a.stripePromise.then(function(E){E&&f&&k(E)}):a.tag==="sync"&&!o.stripe&&k(a.stripe),function(){f=!1}},[a,o,n]);var h=_(t);s.useEffect(function(){h!==null&&h!==t&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[h,t]);var g=_(n);return s.useEffect(function(){if(o.elements){var f=H(n,g,["clientSecret","fonts"]);f&&o.elements.update(f)}},[n,g,o.elements]),s.useEffect(function(){be(o.stripe)},[o.stripe]),s.createElement(I.Provider,{value:o},u)};ke.propTypes={stripe:i.any,options:i.object};i.func.isRequired;var G=s.createContext(null);G.displayName="CustomCheckoutSdkContext";var Oe=function(e,t){if(!e)throw new Error("Could not find CustomCheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CustomCheckoutProvider> provider."));return e},Pe=s.createContext(null);Pe.displayName="CustomCheckoutContext";i.any,i.shape({clientSecret:i.string.isRequired,elementsOptions:i.object}).isRequired;var F=function(e){var t=s.useContext(G),n=s.useContext(I);if(t&&n)throw new Error("You cannot wrap the part of your app that ".concat(e," in both <CustomCheckoutProvider> and <Elements> providers."));return t?Oe(t,e):Ee(n,e)},xe=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},c=function(e,t){var n="".concat(xe(e),"Element"),u=function(o){var v=o.id,h=o.className,g=o.options,f=g===void 0?{}:g,k=o.onBlur,E=o.onFocus,S=o.onReady,P=o.onChange,x=o.onEscape,Q=o.onClick,X=o.onLoadError,Z=o.onLoaderStart,ee=o.onNetworksChange,te=o.onConfirm,re=o.onCancel,ne=o.onShippingAddressChange,oe=o.onShippingRateChange,R=F("mounts <".concat(n,">")),j="elements"in R?R.elements:null,A="customCheckoutSdk"in R?R.customCheckoutSdk:null,ae=s.useState(null),L=z(ae,2),m=L[0],se=L[1],b=s.useRef(null),w=s.useRef(null);y(m,"blur",k),y(m,"focus",E),y(m,"escape",x),y(m,"click",Q),y(m,"loaderror",X),y(m,"loaderstart",Z),y(m,"networkschange",ee),y(m,"confirm",te),y(m,"cancel",re),y(m,"shippingaddresschange",ne),y(m,"shippingratechange",oe),y(m,"change",P);var N;S&&(e==="expressCheckout"?N=S:N=function(){S(m)}),y(m,"ready",N),s.useLayoutEffect(function(){if(b.current===null&&w.current!==null&&(j||A)){var C=null;A?C=A.createElement(e,f):j&&(C=j.create(e,f)),b.current=C,se(C),C&&C.mount(w.current)}},[j,A,f]);var U=_(f);return s.useEffect(function(){if(b.current){var C=H(f,U,["paymentRequest"]);C&&b.current.update(C)}},[f,U]),s.useLayoutEffect(function(){return function(){if(b.current&&typeof b.current.destroy=="function")try{b.current.destroy(),b.current=null}catch{}}},[]),s.createElement("div",{id:v,className:h,ref:w})},a=function(o){F("mounts <".concat(n,">"));var v=o.id,h=o.className;return s.createElement("div",{id:v,className:h})},l=t?a:u;return l.propTypes={id:i.string,className:i.string,onChange:i.func,onBlur:i.func,onFocus:i.func,onReady:i.func,onEscape:i.func,onClick:i.func,onLoadError:i.func,onLoaderStart:i.func,onNetworksChange:i.func,onConfirm:i.func,onCancel:i.func,onShippingAddressChange:i.func,onShippingRateChange:i.func,options:i.object},l.displayName=n,l.__elementType=e,l},p=typeof window>"u",Re=s.createContext(null);Re.displayName="EmbeddedCheckoutProviderContext";c("auBankAccount",p);c("card",p);c("cardNumber",p);c("cardExpiry",p);c("cardCvc",p);c("fpxBank",p);c("iban",p);c("idealBank",p);c("p24Bank",p);c("epsBank",p);c("payment",p);c("expressCheckout",p);c("paymentRequestButton",p);c("linkAuthentication",p);c("address",p);c("shippingAddress",p);c("paymentMethodMessaging",p);c("affirmMessage",p);c("afterpayClearpayMessage",p);export{ke as E,i as P,fe as p};
