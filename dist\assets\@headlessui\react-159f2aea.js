import{u as Ut}from"../@tanstack/react-virtual-d31b3415.js";import{r as s,a as w,R as Se,b as Vt}from"../vendor-df163860.js";var qt=Object.defineProperty,Wt=(e,t,n)=>t in e?qt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ve=(e,t,n)=>(Wt(e,typeof t!="symbol"?t+"":t,n),n);let Yt=class{constructor(){Ve(this,"current",this.detect()),Ve(this,"handoffState","pending"),Ve(this,"currentId",0)}set(t){this.current!==t&&(this.handoffState="pending",this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},le=new Yt,I=(e,t)=>{le.isServer?s.useEffect(e,t):s.useLayoutEffect(e,t)};function Z(e){let t=s.useRef(e);return I(()=>{t.current=e},[e]),t}function rt(e,t){let[n,r]=s.useState(e),o=Z(e);return I(()=>r(o.current),[o,r,...t]),n}let h=function(e){let t=Z(e);return w.useCallback((...n)=>t.current(...n),[t])};function Kt(e,t,n){let[r,o]=s.useState(n),l=e!==void 0,i=s.useRef(l),a=s.useRef(!1),u=s.useRef(!1);return l&&!i.current&&!a.current?(a.current=!0,i.current=l,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!l&&i.current&&!u.current&&(u.current=!0,i.current=l,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[l?e:r,h(c=>(l||o(c),t==null?void 0:t(c)))]}function Me(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function ie(){let e=[],t={addEventListener(n,r,o,l){return n.addEventListener(r,o,l),t.add(()=>n.removeEventListener(r,o,l))},requestAnimationFrame(...n){let r=requestAnimationFrame(...n);return t.add(()=>cancelAnimationFrame(r))},nextFrame(...n){return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(...n){let r=setTimeout(...n);return t.add(()=>clearTimeout(r))},microTask(...n){let r={current:!0};return Me(()=>{r.current&&n[0]()}),t.add(()=>{r.current=!1})},style(n,r,o){let l=n.style.getPropertyValue(r);return Object.assign(n.style,{[r]:o}),this.add(()=>{Object.assign(n.style,{[r]:l})})},group(n){let r=ie();return n(r),this.add(()=>r.dispose())},add(n){return e.push(n),()=>{let r=e.indexOf(n);if(r>=0)for(let o of e.splice(r,1))o()}},dispose(){for(let n of e.splice(0))n()}};return t}function be(){let[e]=s.useState(ie);return s.useEffect(()=>()=>e.dispose(),[e]),e}function zt(){let e=typeof document>"u";return"useSyncExternalStore"in Se?(t=>t.useSyncExternalStore)(Se)(()=>()=>{},()=>!1,()=>!e):!1}function ge(){let e=zt(),[t,n]=s.useState(le.isHandoffComplete);return t&&le.isHandoffComplete===!1&&n(!1),s.useEffect(()=>{t!==!0&&n(!0)},[t]),s.useEffect(()=>le.handoff(),[]),e?!1:t}var st;let ee=(st=w.useId)!=null?st:function(){let e=ge(),[t,n]=w.useState(e?()=>le.nextId():null);return I(()=>{t===null&&n(le.nextId())},[t]),t!=null?""+t:void 0};function D(e,t,...n){if(e in t){let o=t[e];return typeof o=="function"?o(...n):o}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,D),r}function ot(e){return le.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let Xe=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var ce=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(ce||{}),mt=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(mt||{}),Gt=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(Gt||{});function Xt(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Xe)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var bt=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(bt||{});function Jt(e,t=0){var n;return e===((n=ot(e))==null?void 0:n.body)?!1:D(t,{0(){return e.matches(Xe)},1(){let r=e;for(;r!==null;){if(r.matches(Xe))return!0;r=r.parentElement}return!1}})}var Qt=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Qt||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function fe(e){e==null||e.focus({preventScroll:!0})}let Zt=["textarea","input"].join(",");function en(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,Zt))!=null?n:!1}function gt(e,t=n=>n){return e.slice().sort((n,r)=>{let o=t(n),l=t(r);if(o===null||l===null)return 0;let i=o.compareDocumentPosition(l);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function Le(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){let l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,i=Array.isArray(e)?n?gt(e):e:Xt(e);o.length>0&&i.length>1&&(i=i.filter(d=>!o.includes(d))),r=r??l.activeElement;let a=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,i.indexOf(r))-1;if(t&4)return Math.max(0,i.indexOf(r))+1;if(t&8)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=t&32?{preventScroll:!0}:{},f=0,p=i.length,E;do{if(f>=p||f+p<=0)return 0;let d=u+f;if(t&16)d=(d+p)%p;else{if(d<0)return 3;if(d>=p)return 1}E=i[d],E==null||E.focus(c),f+=a}while(E!==l.activeElement);return t&6&&en(E)&&E.select(),2}function ht(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function tn(){return/Android/gi.test(window.navigator.userAgent)}function Et(){return ht()||tn()}function Ie(e,t,n){let r=Z(t);s.useEffect(()=>{function o(l){r.current(l)}return document.addEventListener(e,o,n),()=>document.removeEventListener(e,o,n)},[e,n])}function xt(e,t,n){let r=Z(t);s.useEffect(()=>{function o(l){r.current(l)}return window.addEventListener(e,o,n),()=>window.removeEventListener(e,o,n)},[e,n])}function wt(e,t,n=!0){let r=s.useRef(!1);s.useEffect(()=>{requestAnimationFrame(()=>{r.current=n})},[n]);function o(i,a){if(!r.current||i.defaultPrevented)return;let u=a(i);if(u===null||!u.getRootNode().contains(u)||!u.isConnected)return;let c=function f(p){return typeof p=="function"?f(p()):Array.isArray(p)||p instanceof Set?p:[p]}(e);for(let f of c){if(f===null)continue;let p=f instanceof HTMLElement?f:f.current;if(p!=null&&p.contains(u)||i.composed&&i.composedPath().includes(p))return}return!Jt(u,bt.Loose)&&u.tabIndex!==-1&&i.preventDefault(),t(i,u)}let l=s.useRef(null);Ie("pointerdown",i=>{var a,u;r.current&&(l.current=((u=(a=i.composedPath)==null?void 0:a.call(i))==null?void 0:u[0])||i.target)},!0),Ie("mousedown",i=>{var a,u;r.current&&(l.current=((u=(a=i.composedPath)==null?void 0:a.call(i))==null?void 0:u[0])||i.target)},!0),Ie("click",i=>{Et()||l.current&&(o(i,()=>l.current),l.current=null)},!0),Ie("touchend",i=>o(i,()=>i.target instanceof HTMLElement?i.target:null),!0),xt("blur",i=>o(i,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function he(...e){return s.useMemo(()=>ot(...e),[...e])}function ct(e){var t;if(e.type)return e.type;let n=(t=e.as)!=null?t:"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function nn(e,t){let[n,r]=s.useState(()=>ct(e));return I(()=>{r(ct(e))},[e.type,e.as]),I(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&r("button")},[n,t]),n}let yt=Symbol();function rn(e,t=!0){return Object.assign(e,{[yt]:t})}function q(...e){let t=s.useRef(e);s.useEffect(()=>{t.current=e},[e]);let n=h(r=>{for(let o of t.current)o!=null&&(typeof o=="function"?o(r):o.current=r)});return e.every(r=>r==null||(r==null?void 0:r[yt]))?void 0:n}function dt(e){return[e.screenX,e.screenY]}function on(){let e=s.useRef([-1,-1]);return{wasMoved(t){let n=dt(t);return e.current[0]===n[0]&&e.current[1]===n[1]?!1:(e.current=n,!0)},update(t){e.current=dt(t)}}}function ln({container:e,accept:t,walk:n,enabled:r=!0}){let o=s.useRef(t),l=s.useRef(n);s.useEffect(()=>{o.current=t,l.current=n},[t,n]),I(()=>{if(!e||!r)return;let i=ot(e);if(!i)return;let a=o.current,u=l.current,c=Object.assign(p=>a(p),{acceptNode:a}),f=i.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,c,!1);for(;f.nextNode();)u(f.currentNode)},[e,r,o,l])}function Te(e,t){let n=s.useRef([]),r=h(e);s.useEffect(()=>{let o=[...n.current];for(let[l,i]of t.entries())if(n.current[l]!==i){let a=r(t,o);return n.current=t,a}},[r,...t])}function De(...e){return Array.from(new Set(e.flatMap(t=>typeof t=="string"?t.split(" "):[]))).filter(Boolean).join(" ")}var me=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(me||{}),ue=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(ue||{});function B({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:o,visible:l=!0,name:i,mergeRefs:a}){a=a??an;let u=St(t,e);if(l)return Fe(u,n,r,i,a);let c=o??0;if(c&2){let{static:f=!1,...p}=u;if(f)return Fe(p,n,r,i,a)}if(c&1){let{unmount:f=!0,...p}=u;return D(f?0:1,{0(){return null},1(){return Fe({...p,hidden:!0,style:{display:"none"}},n,r,i,a)}})}return Fe(u,n,r,i,a)}function Fe(e,t={},n,r,o){let{as:l=n,children:i,refName:a="ref",...u}=qe(e,["unmount","static"]),c=e.ref!==void 0?{[a]:e.ref}:{},f=typeof i=="function"?i(t):i;"className"in u&&u.className&&typeof u.className=="function"&&(u.className=u.className(t));let p={};if(t){let E=!1,d=[];for(let[m,g]of Object.entries(t))typeof g=="boolean"&&(E=!0),g===!0&&d.push(m);E&&(p["data-headlessui-state"]=d.join(" "))}if(l===s.Fragment&&Object.keys(Je(u)).length>0){if(!s.isValidElement(f)||Array.isArray(f)&&f.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(u).map(g=>`  - ${g}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(g=>`  - ${g}`).join(`
`)].join(`
`));let E=f.props,d=typeof(E==null?void 0:E.className)=="function"?(...g)=>De(E==null?void 0:E.className(...g),u.className):De(E==null?void 0:E.className,u.className),m=d?{className:d}:{};return s.cloneElement(f,Object.assign({},St(f.props,Je(qe(u,["ref"]))),p,c,{ref:o(f.ref,c.ref)},m))}return s.createElement(l,Object.assign({},qe(u,["ref"]),l!==s.Fragment&&c,l!==s.Fragment&&p),f)}function an(...e){return e.every(t=>t==null)?void 0:t=>{for(let n of e)n!=null&&(typeof n=="function"?n(t):n.current=t)}}function St(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let o in r)o.startsWith("on")&&typeof r[o]=="function"?(n[o]!=null||(n[o]=[]),n[o].push(r[o])):t[o]=r[o];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(t,{[r](o,...l){let i=n[r];for(let a of i){if((o instanceof Event||(o==null?void 0:o.nativeEvent)instanceof Event)&&o.defaultPrevented)return;a(o,...l)}}});return t}function H(e){var t;return Object.assign(s.forwardRef(e),{displayName:(t=e.displayName)!=null?t:e.name})}function Je(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function qe(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}let un="div";var Oe=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Oe||{});function sn(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(r&2)===2?!0:(n=o["aria-hidden"])!=null?n:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(r&4)===4&&(r&2)!==2&&{display:"none"}}};return B({ourProps:l,theirProps:o,slot:{},defaultTag:un,name:"Hidden"})}let Ae=H(sn),lt=s.createContext(null);lt.displayName="OpenClosedContext";var U=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(U||{});function ke(){return s.useContext(lt)}function Tt({value:e,children:t}){return w.createElement(lt.Provider,{value:e},t)}function cn(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",t),t())}let ne=[];cn(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&ne[0]!==t.target&&(ne.unshift(t.target),ne=ne.filter(n=>n!=null&&n.isConnected),ne.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function Ot(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=(t==null?void 0:t.getAttribute("disabled"))==="";return r&&dn(n)?!1:r}function dn(e){if(!e)return!1;let t=e.previousElementSibling;for(;t!==null;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}function fn(e){throw new Error("Unexpected object: "+e)}var k=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(k||{});function ft(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=r??-1;switch(e.focus){case 0:{for(let l=0;l<n.length;++l)if(!t.resolveDisabled(n[l],l,n))return l;return r}case 1:{for(let l=o-1;l>=0;--l)if(!t.resolveDisabled(n[l],l,n))return l;return r}case 2:{for(let l=o+1;l<n.length;++l)if(!t.resolveDisabled(n[l],l,n))return l;return r}case 3:{for(let l=n.length-1;l>=0;--l)if(!t.resolveDisabled(n[l],l,n))return l;return r}case 4:{for(let l=0;l<n.length;++l)if(t.resolveId(n[l],l,n)===e.id)return l;return r}case 5:return null;default:fn(e)}}function Rt(e={},t=null,n=[]){for(let[r,o]of Object.entries(e))$t(n,Ct(t,r),o);return n}function Ct(e,t){return e?e+"["+t+"]":t}function $t(e,t,n){if(Array.isArray(n))for(let[r,o]of n.entries())$t(e,Ct(t,r.toString()),o);else n instanceof Date?e.push([t,n.toISOString()]):typeof n=="boolean"?e.push([t,n?"1":"0"]):typeof n=="string"?e.push([t,n]):typeof n=="number"?e.push([t,`${n}`]):n==null?e.push([t,""]):Rt(n,t,e)}var G=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(G||{}),pn=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(pn||{}),vn=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(vn||{}),mn=(e=>(e[e.Pointer=0]="Pointer",e[e.Focus=1]="Focus",e[e.Other=2]="Other",e))(mn||{}),bn=(e=>(e[e.OpenCombobox=0]="OpenCombobox",e[e.CloseCombobox=1]="CloseCombobox",e[e.GoToOption=2]="GoToOption",e[e.RegisterOption=3]="RegisterOption",e[e.UnregisterOption=4]="UnregisterOption",e[e.RegisterLabel=5]="RegisterLabel",e[e.SetActivationTrigger=6]="SetActivationTrigger",e[e.UpdateVirtualOptions=7]="UpdateVirtualOptions",e))(bn||{});function We(e,t=n=>n){let n=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,r=t(e.options.slice()),o=r.length>0&&r[0].dataRef.current.order!==null?r.sort((i,a)=>i.dataRef.current.order-a.dataRef.current.order):gt(r,i=>i.dataRef.current.domRef.current),l=n?o.indexOf(n):null;return l===-1&&(l=null),{options:o,activeOptionIndex:l}}let gn={1(e){var t;return(t=e.dataRef.current)!=null&&t.disabled||e.comboboxState===1?e:{...e,activeOptionIndex:null,comboboxState:1}},0(e){var t,n;if((t=e.dataRef.current)!=null&&t.disabled||e.comboboxState===0)return e;if((n=e.dataRef.current)!=null&&n.value){let r=e.dataRef.current.calculateIndex(e.dataRef.current.value);if(r!==-1)return{...e,activeOptionIndex:r,comboboxState:0}}return{...e,comboboxState:0}},2(e,t){var n,r,o,l,i;if((n=e.dataRef.current)!=null&&n.disabled||(r=e.dataRef.current)!=null&&r.optionsRef.current&&!((o=e.dataRef.current)!=null&&o.optionsPropsRef.current.static)&&e.comboboxState===1)return e;if(e.virtual){let f=t.focus===k.Specific?t.idx:ft(t,{resolveItems:()=>e.virtual.options,resolveActiveIndex:()=>{var E,d;return(d=(E=e.activeOptionIndex)!=null?E:e.virtual.options.findIndex(m=>!e.virtual.disabled(m)))!=null?d:null},resolveDisabled:e.virtual.disabled,resolveId(){throw new Error("Function not implemented.")}}),p=(l=t.trigger)!=null?l:2;return e.activeOptionIndex===f&&e.activationTrigger===p?e:{...e,activeOptionIndex:f,activationTrigger:p}}let a=We(e);if(a.activeOptionIndex===null){let f=a.options.findIndex(p=>!p.dataRef.current.disabled);f!==-1&&(a.activeOptionIndex=f)}let u=t.focus===k.Specific?t.idx:ft(t,{resolveItems:()=>a.options,resolveActiveIndex:()=>a.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled}),c=(i=t.trigger)!=null?i:2;return e.activeOptionIndex===u&&e.activationTrigger===c?e:{...e,...a,activeOptionIndex:u,activationTrigger:c}},3:(e,t)=>{var n,r,o;if((n=e.dataRef.current)!=null&&n.virtual)return{...e,options:[...e.options,t.payload]};let l=t.payload,i=We(e,u=>(u.push(l),u));e.activeOptionIndex===null&&(r=e.dataRef.current)!=null&&r.isSelected(t.payload.dataRef.current.value)&&(i.activeOptionIndex=i.options.indexOf(l));let a={...e,...i,activationTrigger:2};return(o=e.dataRef.current)!=null&&o.__demoMode&&e.dataRef.current.value===void 0&&(a.activeOptionIndex=0),a},4:(e,t)=>{var n;if((n=e.dataRef.current)!=null&&n.virtual)return{...e,options:e.options.filter(o=>o.id!==t.id)};let r=We(e,o=>{let l=o.findIndex(i=>i.id===t.id);return l!==-1&&o.splice(l,1),o});return{...e,...r,activationTrigger:2}},5:(e,t)=>e.labelId===t.id?e:{...e,labelId:t.id},6:(e,t)=>e.activationTrigger===t.trigger?e:{...e,activationTrigger:t.trigger},7:(e,t)=>{var n;if(((n=e.virtual)==null?void 0:n.options)===t.options)return e;let r=e.activeOptionIndex;if(e.activeOptionIndex!==null){let o=t.options.indexOf(e.virtual.options[e.activeOptionIndex]);o!==-1?r=o:r=null}return{...e,activeOptionIndex:r,virtual:Object.assign({},e.virtual,{options:t.options})}}},it=s.createContext(null);it.displayName="ComboboxActionsContext";function Re(e){let t=s.useContext(it);if(t===null){let n=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Re),n}return t}let Pt=s.createContext(null);function hn(e){var t;let n=pe("VirtualProvider"),[r,o]=s.useMemo(()=>{let u=n.optionsRef.current;if(!u)return[0,0];let c=window.getComputedStyle(u);return[parseFloat(c.paddingBlockStart||c.paddingTop),parseFloat(c.paddingBlockEnd||c.paddingBottom)]},[n.optionsRef.current]),l=Ut({scrollPaddingStart:r,scrollPaddingEnd:o,count:n.virtual.options.length,estimateSize(){return 40},getScrollElement(){var u;return(u=n.optionsRef.current)!=null?u:null},overscan:12}),[i,a]=s.useState(0);return I(()=>{a(u=>u+1)},[(t=n.virtual)==null?void 0:t.options]),w.createElement(Pt.Provider,{value:l},w.createElement("div",{style:{position:"relative",width:"100%",height:`${l.getTotalSize()}px`},ref:u=>{if(u){if(typeof process<"u"&&{}.JEST_WORKER_ID!==void 0||n.activationTrigger===0)return;n.activeOptionIndex!==null&&n.virtual.options.length>n.activeOptionIndex&&l.scrollToIndex(n.activeOptionIndex)}}},l.getVirtualItems().map(u=>{var c;return w.createElement(s.Fragment,{key:u.key},w.cloneElement((c=e.children)==null?void 0:c.call(e,{option:n.virtual.options[u.index],open:n.comboboxState===0}),{key:`${i}-${u.key}`,"data-index":u.index,"aria-setsize":n.virtual.options.length,"aria-posinset":u.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${u.start}px)`,overflowAnchor:"none"}}))})))}let at=s.createContext(null);at.displayName="ComboboxDataContext";function pe(e){let t=s.useContext(at);if(t===null){let n=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,pe),n}return t}function En(e,t){return D(t.type,gn,e,t)}let xn=s.Fragment;function wn(e,t){let{value:n,defaultValue:r,onChange:o,form:l,name:i,by:a=null,disabled:u=!1,__demoMode:c=!1,nullable:f=!1,multiple:p=!1,immediate:E=!1,virtual:d=null,...m}=e,g=!1,b=null,[y=p?[]:void 0,F]=Kt(n,o,r),[x,$]=s.useReducer(En,{dataRef:s.createRef(),comboboxState:c?0:1,options:[],virtual:null,activeOptionIndex:null,activationTrigger:2,labelId:null}),j=s.useRef(!1),A=s.useRef({static:!1,hold:!1}),X=s.useRef(null),K=s.useRef(null),W=s.useRef(null),O=s.useRef(null),R=h(typeof a=="string"?(S,C)=>{let T=a;return(S==null?void 0:S[T])===(C==null?void 0:C[T])}:a??((S,C)=>S===C)),N=h(S=>x.options.findIndex(C=>R(C.dataRef.current.value,S))),re=s.useCallback(S=>D(v.mode,{1:()=>y.some(C=>R(C,S)),0:()=>R(y,S)}),[y]),oe=h(S=>x.activeOptionIndex===N(S)),v=s.useMemo(()=>({...x,immediate:g,optionsPropsRef:A,labelRef:X,inputRef:K,buttonRef:W,optionsRef:O,value:y,defaultValue:r,disabled:u,mode:p?1:0,virtual:x.virtual,get activeOptionIndex(){if(j.current&&x.activeOptionIndex===null&&x.options.length>0){let S=x.options.findIndex(C=>!C.dataRef.current.disabled);if(S!==-1)return S}return x.activeOptionIndex},calculateIndex:N,compare:R,isSelected:re,isActive:oe,nullable:f,__demoMode:c}),[y,r,u,p,f,c,x,b]);I(()=>{},[b,void 0]),I(()=>{x.dataRef.current=v},[v]),wt([v.buttonRef,v.inputRef,v.optionsRef],()=>Ee.closeCombobox(),v.comboboxState===0);let _=s.useMemo(()=>{var S,C,T;return{open:v.comboboxState===0,disabled:u,activeIndex:v.activeOptionIndex,activeOption:v.activeOptionIndex===null?null:v.virtual?v.virtual.options[(S=v.activeOptionIndex)!=null?S:0]:(T=(C=v.options[v.activeOptionIndex])==null?void 0:C.dataRef.current.value)!=null?T:null,value:y}},[v,u,y]),P=h(()=>{if(v.activeOptionIndex!==null){if(v.virtual)se(v.virtual.options[v.activeOptionIndex]);else{let{dataRef:S}=v.options[v.activeOptionIndex];se(S.current.value)}Ee.goToOption(k.Specific,v.activeOptionIndex)}}),V=h(()=>{$({type:0}),j.current=!0}),L=h(()=>{$({type:1}),j.current=!1}),M=h((S,C,T)=>(j.current=!1,S===k.Specific?$({type:2,focus:k.Specific,idx:C,trigger:T}):$({type:2,focus:S,trigger:T}))),J=h((S,C)=>($({type:3,payload:{id:S,dataRef:C}}),()=>{v.isActive(C.current.value)&&(j.current=!0),$({type:4,id:S})})),te=h(S=>($({type:5,id:S}),()=>$({type:5,id:null}))),se=h(S=>D(v.mode,{0(){return F==null?void 0:F(S)},1(){let C=v.value.slice(),T=C.findIndex(z=>R(z,S));return T===-1?C.push(S):C.splice(T,1),F==null?void 0:F(C)}})),Q=h(S=>{$({type:6,trigger:S})}),Ee=s.useMemo(()=>({onChange:se,registerOption:J,registerLabel:te,goToOption:M,closeCombobox:L,openCombobox:V,setActivationTrigger:Q,selectActiveOption:P}),[]),Be=t===null?{}:{ref:t},ve=s.useRef(null),Ue=be();return s.useEffect(()=>{ve.current&&r!==void 0&&Ue.addEventListener(ve.current,"reset",()=>{F==null||F(r)})},[ve,F]),w.createElement(it.Provider,{value:Ee},w.createElement(at.Provider,{value:v},w.createElement(Tt,{value:D(v.comboboxState,{0:U.Open,1:U.Closed})},i!=null&&y!=null&&Rt({[i]:y}).map(([S,C],T)=>w.createElement(Ae,{features:Oe.Hidden,ref:T===0?z=>{var Y;ve.current=(Y=z==null?void 0:z.closest("form"))!=null?Y:null}:void 0,...Je({key:S,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:l,name:S,value:C})})),B({ourProps:Be,theirProps:m,slot:_,defaultTag:xn,name:"Combobox"}))))}let yn="input";function Sn(e,t){var n,r,o,l,i;let a=ee(),{id:u=`headlessui-combobox-input-${a}`,onChange:c,displayValue:f,type:p="text",...E}=e,d=pe("Combobox.Input"),m=Re("Combobox.Input"),g=q(d.inputRef,t),b=he(d.inputRef),y=s.useRef(!1),F=be(),x=h(()=>{m.onChange(null),d.optionsRef.current&&(d.optionsRef.current.scrollTop=0),m.goToOption(k.Nothing)}),$=function(){var v;return typeof f=="function"&&d.value!==void 0?(v=f(d.value))!=null?v:"":typeof d.value=="string"?d.value:""}();Te(([v,_],[P,V])=>{if(y.current)return;let L=d.inputRef.current;L&&((V===0&&_===1||v!==P)&&(L.value=v),requestAnimationFrame(()=>{if(y.current||!L||(b==null?void 0:b.activeElement)!==L)return;let{selectionStart:M,selectionEnd:J}=L;Math.abs((J??0)-(M??0))===0&&M===0&&L.setSelectionRange(L.value.length,L.value.length)}))},[$,d.comboboxState,b]),Te(([v],[_])=>{if(v===0&&_===1){if(y.current)return;let P=d.inputRef.current;if(!P)return;let V=P.value,{selectionStart:L,selectionEnd:M,selectionDirection:J}=P;P.value="",P.value=V,J!==null?P.setSelectionRange(L,M,J):P.setSelectionRange(L,M)}},[d.comboboxState]);let j=s.useRef(!1),A=h(()=>{j.current=!0}),X=h(()=>{F.nextFrame(()=>{j.current=!1})}),K=h(v=>{switch(y.current=!0,v.key){case G.Enter:if(y.current=!1,d.comboboxState!==0||j.current)return;if(v.preventDefault(),v.stopPropagation(),d.activeOptionIndex===null){m.closeCombobox();return}m.selectActiveOption(),d.mode===0&&m.closeCombobox();break;case G.ArrowDown:return y.current=!1,v.preventDefault(),v.stopPropagation(),D(d.comboboxState,{0:()=>m.goToOption(k.Next),1:()=>m.openCombobox()});case G.ArrowUp:return y.current=!1,v.preventDefault(),v.stopPropagation(),D(d.comboboxState,{0:()=>m.goToOption(k.Previous),1:()=>{m.openCombobox(),F.nextFrame(()=>{d.value||m.goToOption(k.Last)})}});case G.Home:if(v.shiftKey)break;return y.current=!1,v.preventDefault(),v.stopPropagation(),m.goToOption(k.First);case G.PageUp:return y.current=!1,v.preventDefault(),v.stopPropagation(),m.goToOption(k.First);case G.End:if(v.shiftKey)break;return y.current=!1,v.preventDefault(),v.stopPropagation(),m.goToOption(k.Last);case G.PageDown:return y.current=!1,v.preventDefault(),v.stopPropagation(),m.goToOption(k.Last);case G.Escape:return y.current=!1,d.comboboxState!==0?void 0:(v.preventDefault(),d.optionsRef.current&&!d.optionsPropsRef.current.static&&v.stopPropagation(),d.nullable&&d.mode===0&&d.value===null&&x(),m.closeCombobox());case G.Tab:if(y.current=!1,d.comboboxState!==0)return;d.mode===0&&d.activationTrigger!==1&&m.selectActiveOption(),m.closeCombobox();break}}),W=h(v=>{c==null||c(v),d.nullable&&d.mode===0&&v.target.value===""&&x(),m.openCombobox()}),O=h(v=>{var _,P,V;let L=(_=v.relatedTarget)!=null?_:ne.find(M=>M!==v.currentTarget);if(y.current=!1,!((P=d.optionsRef.current)!=null&&P.contains(L))&&!((V=d.buttonRef.current)!=null&&V.contains(L))&&d.comboboxState===0)return v.preventDefault(),d.mode===0&&(d.nullable&&d.value===null?x():d.activationTrigger!==1&&m.selectActiveOption()),m.closeCombobox()}),R=h(v=>{var _,P,V;let L=(_=v.relatedTarget)!=null?_:ne.find(M=>M!==v.currentTarget);(P=d.buttonRef.current)!=null&&P.contains(L)||(V=d.optionsRef.current)!=null&&V.contains(L)||d.disabled||d.immediate&&d.comboboxState!==0&&(m.openCombobox(),F.nextFrame(()=>{m.setActivationTrigger(1)}))}),N=rt(()=>{if(d.labelId)return[d.labelId].join(" ")},[d.labelId]),re=s.useMemo(()=>({open:d.comboboxState===0,disabled:d.disabled}),[d]),oe={ref:g,id:u,role:"combobox",type:p,"aria-controls":(n=d.optionsRef.current)==null?void 0:n.id,"aria-expanded":d.comboboxState===0,"aria-activedescendant":d.activeOptionIndex===null?void 0:d.virtual?(r=d.options.find(v=>{var _;return!((_=d.virtual)!=null&&_.disabled(v.dataRef.current.value))&&d.compare(v.dataRef.current.value,d.virtual.options[d.activeOptionIndex])}))==null?void 0:r.id:(o=d.options[d.activeOptionIndex])==null?void 0:o.id,"aria-labelledby":N,"aria-autocomplete":"list",defaultValue:(i=(l=e.defaultValue)!=null?l:d.defaultValue!==void 0?f==null?void 0:f(d.defaultValue):null)!=null?i:d.defaultValue,disabled:d.disabled,onCompositionStart:A,onCompositionEnd:X,onKeyDown:K,onChange:W,onFocus:R,onBlur:O};return B({ourProps:oe,theirProps:E,slot:re,defaultTag:yn,name:"Combobox.Input"})}let Tn="button";function On(e,t){var n;let r=pe("Combobox.Button"),o=Re("Combobox.Button"),l=q(r.buttonRef,t),i=ee(),{id:a=`headlessui-combobox-button-${i}`,...u}=e,c=be(),f=h(g=>{switch(g.key){case G.ArrowDown:return g.preventDefault(),g.stopPropagation(),r.comboboxState===1&&o.openCombobox(),c.nextFrame(()=>{var b;return(b=r.inputRef.current)==null?void 0:b.focus({preventScroll:!0})});case G.ArrowUp:return g.preventDefault(),g.stopPropagation(),r.comboboxState===1&&(o.openCombobox(),c.nextFrame(()=>{r.value||o.goToOption(k.Last)})),c.nextFrame(()=>{var b;return(b=r.inputRef.current)==null?void 0:b.focus({preventScroll:!0})});case G.Escape:return r.comboboxState!==0?void 0:(g.preventDefault(),r.optionsRef.current&&!r.optionsPropsRef.current.static&&g.stopPropagation(),o.closeCombobox(),c.nextFrame(()=>{var b;return(b=r.inputRef.current)==null?void 0:b.focus({preventScroll:!0})}));default:return}}),p=h(g=>{if(Ot(g.currentTarget))return g.preventDefault();r.comboboxState===0?o.closeCombobox():(g.preventDefault(),o.openCombobox()),c.nextFrame(()=>{var b;return(b=r.inputRef.current)==null?void 0:b.focus({preventScroll:!0})})}),E=rt(()=>{if(r.labelId)return[r.labelId,a].join(" ")},[r.labelId,a]),d=s.useMemo(()=>({open:r.comboboxState===0,disabled:r.disabled,value:r.value}),[r]),m={ref:l,id:a,type:nn(e,r.buttonRef),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":(n=r.optionsRef.current)==null?void 0:n.id,"aria-expanded":r.comboboxState===0,"aria-labelledby":E,disabled:r.disabled,onClick:p,onKeyDown:f};return B({ourProps:m,theirProps:u,slot:d,defaultTag:Tn,name:"Combobox.Button"})}let Rn="label";function Cn(e,t){let n=ee(),{id:r=`headlessui-combobox-label-${n}`,...o}=e,l=pe("Combobox.Label"),i=Re("Combobox.Label"),a=q(l.labelRef,t);I(()=>i.registerLabel(r),[r]);let u=h(()=>{var f;return(f=l.inputRef.current)==null?void 0:f.focus({preventScroll:!0})}),c=s.useMemo(()=>({open:l.comboboxState===0,disabled:l.disabled}),[l]);return B({ourProps:{ref:a,id:r,onClick:u},theirProps:o,slot:c,defaultTag:Rn,name:"Combobox.Label"})}let $n="ul",Pn=me.RenderStrategy|me.Static;function In(e,t){let n=ee(),{id:r=`headlessui-combobox-options-${n}`,hold:o=!1,...l}=e,i=pe("Combobox.Options"),a=q(i.optionsRef,t),u=ke(),c=(()=>u!==null?(u&U.Open)===U.Open:i.comboboxState===0)();I(()=>{var d;i.optionsPropsRef.current.static=(d=e.static)!=null?d:!1},[i.optionsPropsRef,e.static]),I(()=>{i.optionsPropsRef.current.hold=o},[i.optionsPropsRef,o]),ln({container:i.optionsRef.current,enabled:i.comboboxState===0,accept(d){return d.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:d.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(d){d.setAttribute("role","none")}});let f=rt(()=>{var d,m;return(m=i.labelId)!=null?m:(d=i.buttonRef.current)==null?void 0:d.id},[i.labelId,i.buttonRef.current]),p=s.useMemo(()=>({open:i.comboboxState===0,option:void 0}),[i]),E={"aria-labelledby":f,role:"listbox","aria-multiselectable":i.mode===1?!0:void 0,id:r,ref:a};return i.virtual&&i.comboboxState===0&&Object.assign(l,{children:w.createElement(hn,null,l.children)}),B({ourProps:E,theirProps:l,slot:p,defaultTag:$n,features:Pn,visible:c,name:"Combobox.Options"})}let Fn="li";function Ln(e,t){var n;let r=ee(),{id:o=`headlessui-combobox-option-${r}`,disabled:l=!1,value:i,order:a=null,...u}=e,c=pe("Combobox.Option"),f=Re("Combobox.Option"),p=c.virtual?c.activeOptionIndex===c.calculateIndex(i):c.activeOptionIndex===null?!1:((n=c.options[c.activeOptionIndex])==null?void 0:n.id)===o,E=c.isSelected(i),d=s.useRef(null),m=Z({disabled:l,value:i,domRef:d,order:a}),g=s.useContext(Pt),b=q(t,d,g?g.measureElement:null),y=h(()=>f.onChange(i));I(()=>f.registerOption(o,m),[m,o]);let F=s.useRef(!(c.virtual||c.__demoMode));I(()=>{if(!c.virtual||!c.__demoMode)return;let O=ie();return O.requestAnimationFrame(()=>{F.current=!0}),O.dispose},[c.virtual,c.__demoMode]),I(()=>{if(!F.current||c.comboboxState!==0||!p||c.activationTrigger===0)return;let O=ie();return O.requestAnimationFrame(()=>{var R,N;(N=(R=d.current)==null?void 0:R.scrollIntoView)==null||N.call(R,{block:"nearest"})}),O.dispose},[d,p,c.comboboxState,c.activationTrigger,c.activeOptionIndex]);let x=h(O=>{var R;if(l||(R=c.virtual)!=null&&R.disabled(i))return O.preventDefault();y(),Et()||requestAnimationFrame(()=>{var N;return(N=c.inputRef.current)==null?void 0:N.focus({preventScroll:!0})}),c.mode===0&&requestAnimationFrame(()=>f.closeCombobox())}),$=h(()=>{var O;if(l||(O=c.virtual)!=null&&O.disabled(i))return f.goToOption(k.Nothing);let R=c.calculateIndex(i);f.goToOption(k.Specific,R)}),j=on(),A=h(O=>j.update(O)),X=h(O=>{var R;if(!j.wasMoved(O)||l||(R=c.virtual)!=null&&R.disabled(i)||p)return;let N=c.calculateIndex(i);f.goToOption(k.Specific,N,0)}),K=h(O=>{var R;j.wasMoved(O)&&(l||(R=c.virtual)!=null&&R.disabled(i)||p&&(c.optionsPropsRef.current.hold||f.goToOption(k.Nothing)))}),W=s.useMemo(()=>({active:p,selected:E,disabled:l}),[p,E,l]);return B({ourProps:{id:o,ref:b,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":E,disabled:void 0,onClick:x,onFocus:$,onPointerEnter:A,onMouseEnter:A,onPointerMove:X,onMouseMove:X,onPointerLeave:K,onMouseLeave:K},theirProps:u,slot:W,defaultTag:Fn,name:"Combobox.Option"})}let Dn=H(wn),An=H(On),Nn=H(Sn),Mn=H(Cn),kn=H(In),Hn=H(Ln),bo=Object.assign(Dn,{Input:Nn,Button:An,Label:Mn,Options:kn,Option:Hn});function It(e,t,n,r){let o=Z(n);s.useEffect(()=>{e=e??window;function l(i){o.current(i)}return e.addEventListener(t,l,r),()=>e.removeEventListener(t,l,r)},[e,t,r])}function Ce(){let e=s.useRef(!1);return I(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Ft(e){let t=h(e),n=s.useRef(!1);s.useEffect(()=>(n.current=!1,()=>{n.current=!0,Me(()=>{n.current&&t()})}),[t])}var ye=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(ye||{});function jn(){let e=s.useRef(0);return xt("keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function Lt(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}let _n="div";var Dt=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(Dt||{});function Bn(e,t){let n=s.useRef(null),r=q(n,t),{initialFocus:o,containers:l,features:i=30,...a}=e;ge()||(i=1);let u=he(n);qn({ownerDocument:u},!!(i&16));let c=Wn({ownerDocument:u,container:n,initialFocus:o},!!(i&2));Yn({ownerDocument:u,container:n,containers:l,previousActiveElement:c},!!(i&8));let f=jn(),p=h(g=>{let b=n.current;b&&(y=>y())(()=>{D(f.current,{[ye.Forwards]:()=>{Le(b,ce.First,{skipElements:[g.relatedTarget]})},[ye.Backwards]:()=>{Le(b,ce.Last,{skipElements:[g.relatedTarget]})}})})}),E=be(),d=s.useRef(!1),m={ref:r,onKeyDown(g){g.key=="Tab"&&(d.current=!0,E.requestAnimationFrame(()=>{d.current=!1}))},onBlur(g){let b=Lt(l);n.current instanceof HTMLElement&&b.add(n.current);let y=g.relatedTarget;y instanceof HTMLElement&&y.dataset.headlessuiFocusGuard!=="true"&&(At(b,y)||(d.current?Le(n.current,D(f.current,{[ye.Forwards]:()=>ce.Next,[ye.Backwards]:()=>ce.Previous})|ce.WrapAround,{relativeTo:g.target}):g.target instanceof HTMLElement&&fe(g.target)))}};return w.createElement(w.Fragment,null,!!(i&4)&&w.createElement(Ae,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:Oe.Focusable}),B({ourProps:m,theirProps:a,defaultTag:_n,name:"FocusTrap"}),!!(i&4)&&w.createElement(Ae,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:Oe.Focusable}))}let Un=H(Bn),xe=Object.assign(Un,{features:Dt});function Vn(e=!0){let t=s.useRef(ne.slice());return Te(([n],[r])=>{r===!0&&n===!1&&Me(()=>{t.current.splice(0)}),r===!1&&n===!0&&(t.current=ne.slice())},[e,ne,t]),h(()=>{var n;return(n=t.current.find(r=>r!=null&&r.isConnected))!=null?n:null})}function qn({ownerDocument:e},t){let n=Vn(t);Te(()=>{t||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&fe(n())},[t]),Ft(()=>{t&&fe(n())})}function Wn({ownerDocument:e,container:t,initialFocus:n},r){let o=s.useRef(null),l=Ce();return Te(()=>{if(!r)return;let i=t.current;i&&Me(()=>{if(!l.current)return;let a=e==null?void 0:e.activeElement;if(n!=null&&n.current){if((n==null?void 0:n.current)===a){o.current=a;return}}else if(i.contains(a)){o.current=a;return}n!=null&&n.current?fe(n.current):Le(i,ce.First)===mt.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=e==null?void 0:e.activeElement})},[r]),o}function Yn({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){let l=Ce();It(e==null?void 0:e.defaultView,"focus",i=>{if(!o||!l.current)return;let a=Lt(n);t.current instanceof HTMLElement&&a.add(t.current);let u=r.current;if(!u)return;let c=i.target;c&&c instanceof HTMLElement?At(a,c)?(r.current=c,fe(c)):(i.preventDefault(),i.stopPropagation(),fe(u)):fe(r.current)},!0)}function At(e,t){for(let n of e)if(n.contains(t))return!0;return!1}let Nt=s.createContext(!1);function Kn(){return s.useContext(Nt)}function Qe(e){return w.createElement(Nt.Provider,{value:e.force},e.children)}function zn(e){let t=Kn(),n=s.useContext(Mt),r=he(e),[o,l]=s.useState(()=>{if(!t&&n!==null||le.isServer)return null;let i=r==null?void 0:r.getElementById("headlessui-portal-root");if(i)return i;if(r===null)return null;let a=r.createElement("div");return a.setAttribute("id","headlessui-portal-root"),r.body.appendChild(a)});return s.useEffect(()=>{o!==null&&(r!=null&&r.body.contains(o)||r==null||r.body.appendChild(o))},[o,r]),s.useEffect(()=>{t||n!==null&&l(n.current)},[n,l,t]),o}let Gn=s.Fragment;function Xn(e,t){let n=e,r=s.useRef(null),o=q(rn(f=>{r.current=f}),t),l=he(r),i=zn(r),[a]=s.useState(()=>{var f;return le.isServer?null:(f=l==null?void 0:l.createElement("div"))!=null?f:null}),u=s.useContext(Ze),c=ge();return I(()=>{!i||!a||i.contains(a)||(a.setAttribute("data-headlessui-portal",""),i.appendChild(a))},[i,a]),I(()=>{if(a&&u)return u.register(a)},[u,a]),Ft(()=>{var f;!i||!a||(a instanceof Node&&i.contains(a)&&i.removeChild(a),i.childNodes.length<=0&&((f=i.parentElement)==null||f.removeChild(i)))}),c?!i||!a?null:Vt.createPortal(B({ourProps:{ref:o},theirProps:n,defaultTag:Gn,name:"Portal"}),a):null}let Jn=s.Fragment,Mt=s.createContext(null);function Qn(e,t){let{target:n,...r}=e,o={ref:q(t)};return w.createElement(Mt.Provider,{value:n},B({ourProps:o,theirProps:r,defaultTag:Jn,name:"Popover.Group"}))}let Ze=s.createContext(null);function Zn(){let e=s.useContext(Ze),t=s.useRef([]),n=h(l=>(t.current.push(l),e&&e.register(l),()=>r(l))),r=h(l=>{let i=t.current.indexOf(l);i!==-1&&t.current.splice(i,1),e&&e.unregister(l)}),o=s.useMemo(()=>({register:n,unregister:r,portals:t}),[n,r,t]);return[t,s.useMemo(()=>function({children:l}){return w.createElement(Ze.Provider,{value:o},l)},[o])]}let er=H(Xn),tr=H(Qn),et=Object.assign(er,{Group:tr});function nr(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const rr=typeof Object.is=="function"?Object.is:nr,{useState:or,useEffect:lr,useLayoutEffect:ir,useDebugValue:ar}=Se;function ur(e,t,n){const r=t(),[{inst:o},l]=or({inst:{value:r,getSnapshot:t}});return ir(()=>{o.value=r,o.getSnapshot=t,Ye(o)&&l({inst:o})},[e,r,t]),lr(()=>(Ye(o)&&l({inst:o}),e(()=>{Ye(o)&&l({inst:o})})),[e]),ar(r),r}function Ye(e){const t=e.getSnapshot,n=e.value;try{const r=t();return!rr(n,r)}catch{return!0}}function sr(e,t,n){return t()}const cr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",dr=!cr,fr=dr?sr:ur,pr="useSyncExternalStore"in Se?(e=>e.useSyncExternalStore)(Se):fr;function vr(e){return pr(e.subscribe,e.getSnapshot,e.getSnapshot)}function mr(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(o){return r.add(o),()=>r.delete(o)},dispatch(o,...l){let i=t[o].call(n,...l);i&&(n=i,r.forEach(a=>a()))}}}function br(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=((n=t.defaultView)!=null?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,o=r.clientWidth-r.offsetWidth,l=e-o;n.style(r,"paddingRight",`${l}px`)}}}function gr(){return ht()?{before({doc:e,d:t,meta:n}){function r(o){return n.containers.flatMap(l=>l()).some(l=>l.contains(o))}t.microTask(()=>{var o;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let a=ie();a.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>a.dispose()))}let l=(o=window.scrollY)!=null?o:window.pageYOffset,i=null;t.addEventListener(e,"click",a=>{if(a.target instanceof HTMLElement)try{let u=a.target.closest("a");if(!u)return;let{hash:c}=new URL(u.href),f=e.querySelector(c);f&&!r(f)&&(i=f)}catch{}},!0),t.addEventListener(e,"touchstart",a=>{if(a.target instanceof HTMLElement)if(r(a.target)){let u=a.target;for(;u.parentElement&&r(u.parentElement);)u=u.parentElement;t.style(u,"overscrollBehavior","contain")}else t.style(a.target,"touchAction","none")}),t.addEventListener(e,"touchmove",a=>{if(a.target instanceof HTMLElement)if(r(a.target)){let u=a.target;for(;u.parentElement&&u.dataset.headlessuiPortal!==""&&!(u.scrollHeight>u.clientHeight||u.scrollWidth>u.clientWidth);)u=u.parentElement;u.dataset.headlessuiPortal===""&&a.preventDefault()}else a.preventDefault()},{passive:!1}),t.add(()=>{var a;let u=(a=window.scrollY)!=null?a:window.pageYOffset;l!==u&&window.scrollTo(0,l),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function hr(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function Er(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let de=mr(()=>new Map,{PUSH(e,t){var n;let r=(n=this.get(e))!=null?n:{doc:e,count:0,d:ie(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:Er(n)},o=[gr(),br(),hr()];o.forEach(({before:l})=>l==null?void 0:l(r)),o.forEach(({after:l})=>l==null?void 0:l(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});de.subscribe(()=>{let e=de.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let r=t.get(n.doc)==="hidden",o=n.count!==0;(o&&!r||!o&&r)&&de.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&de.dispatch("TEARDOWN",n)}});function xr(e,t,n){let r=vr(de),o=e?r.get(e):void 0,l=o?o.count>0:!1;return I(()=>{if(!(!e||!t))return de.dispatch("PUSH",e,n),()=>de.dispatch("POP",e,n)},[t,e]),l}let Ke=new Map,we=new Map;function pt(e,t=!0){I(()=>{var n;if(!t)return;let r=typeof e=="function"?e():e.current;if(!r)return;function o(){var i;if(!r)return;let a=(i=we.get(r))!=null?i:1;if(a===1?we.delete(r):we.set(r,a-1),a!==1)return;let u=Ke.get(r);u&&(u["aria-hidden"]===null?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",u["aria-hidden"]),r.inert=u.inert,Ke.delete(r))}let l=(n=we.get(r))!=null?n:0;return we.set(r,l+1),l!==0||(Ke.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),o},[e,t])}function wr({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){var r;let o=s.useRef((r=n==null?void 0:n.current)!=null?r:null),l=he(o),i=h(()=>{var a,u,c;let f=[];for(let p of e)p!==null&&(p instanceof HTMLElement?f.push(p):"current"in p&&p.current instanceof HTMLElement&&f.push(p.current));if(t!=null&&t.current)for(let p of t.current)f.push(p);for(let p of(a=l==null?void 0:l.querySelectorAll("html > *, body > *"))!=null?a:[])p!==document.body&&p!==document.head&&p instanceof HTMLElement&&p.id!=="headlessui-portal-root"&&(p.contains(o.current)||p.contains((c=(u=o.current)==null?void 0:u.getRootNode())==null?void 0:c.host)||f.some(E=>p.contains(E))||f.push(p));return f});return{resolveContainers:i,contains:h(a=>i().some(u=>u.contains(a))),mainTreeNodeRef:o,MainTreeNode:s.useMemo(()=>function(){return n!=null?null:w.createElement(Ae,{features:Oe.Hidden,ref:o})},[o,n])}}let ut=s.createContext(()=>{});ut.displayName="StackContext";var tt=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(tt||{});function yr(){return s.useContext(ut)}function Sr({children:e,onUpdate:t,type:n,element:r,enabled:o}){let l=yr(),i=h((...a)=>{t==null||t(...a),l(...a)});return I(()=>{let a=o===void 0||o===!0;return a&&i(0,n,r),()=>{a&&i(1,n,r)}},[i,n,r,o]),w.createElement(ut.Provider,{value:i},e)}let kt=s.createContext(null);function Ht(){let e=s.useContext(kt);if(e===null){let t=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,Ht),t}return e}function Tr(){let[e,t]=s.useState([]);return[e.length>0?e.join(" "):void 0,s.useMemo(()=>function(n){let r=h(l=>(t(i=>[...i,l]),()=>t(i=>{let a=i.slice(),u=a.indexOf(l);return u!==-1&&a.splice(u,1),a}))),o=s.useMemo(()=>({register:r,slot:n.slot,name:n.name,props:n.props}),[r,n.slot,n.name,n.props]);return w.createElement(kt.Provider,{value:o},n.children)},[t])]}let Or="p";function Rr(e,t){let n=ee(),{id:r=`headlessui-description-${n}`,...o}=e,l=Ht(),i=q(t);I(()=>l.register(r),[r,l.register]);let a={ref:i,...l.props,id:r};return B({ourProps:a,theirProps:o,slot:l.slot||{},defaultTag:Or,name:l.name||"Description"})}let Cr=H(Rr),$r=Object.assign(Cr,{});var Pr=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Pr||{}),Ir=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(Ir||{});let Fr={0(e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},Ne=s.createContext(null);Ne.displayName="DialogContext";function $e(e){let t=s.useContext(Ne);if(t===null){let n=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,$e),n}return t}function Lr(e,t,n=()=>[document.body]){xr(e,t,r=>{var o;return{containers:[...(o=r.containers)!=null?o:[],n]}})}function Dr(e,t){return D(t.type,Fr,e,t)}let Ar="div",Nr=me.RenderStrategy|me.Static;function Mr(e,t){let n=ee(),{id:r=`headlessui-dialog-${n}`,open:o,onClose:l,initialFocus:i,role:a="dialog",__demoMode:u=!1,...c}=e,[f,p]=s.useState(0),E=s.useRef(!1);a=function(){return a==="dialog"||a==="alertdialog"?a:(E.current||(E.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let d=ke();o===void 0&&d!==null&&(o=(d&U.Open)===U.Open);let m=s.useRef(null),g=q(m,t),b=he(m),y=e.hasOwnProperty("open")||d!==null,F=e.hasOwnProperty("onClose");if(!y&&!F)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!y)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!F)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof o!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${o}`);if(typeof l!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${l}`);let x=o?0:1,[$,j]=s.useReducer(Dr,{titleId:null,descriptionId:null,panelRef:s.createRef()}),A=h(()=>l(!1)),X=h(T=>j({type:0,id:T})),K=ge()?u?!1:x===0:!1,W=f>1,O=s.useContext(Ne)!==null,[R,N]=Zn(),re={get current(){var T;return(T=$.panelRef.current)!=null?T:m.current}},{resolveContainers:oe,mainTreeNodeRef:v,MainTreeNode:_}=wr({portals:R,defaultContainers:[re]}),P=W?"parent":"leaf",V=d!==null?(d&U.Closing)===U.Closing:!1,L=(()=>O||V?!1:K)(),M=s.useCallback(()=>{var T,z;return(z=Array.from((T=b==null?void 0:b.querySelectorAll("body > *"))!=null?T:[]).find(Y=>Y.id==="headlessui-portal-root"?!1:Y.contains(v.current)&&Y instanceof HTMLElement))!=null?z:null},[v]);pt(M,L);let J=(()=>W?!0:K)(),te=s.useCallback(()=>{var T,z;return(z=Array.from((T=b==null?void 0:b.querySelectorAll("[data-headlessui-portal]"))!=null?T:[]).find(Y=>Y.contains(v.current)&&Y instanceof HTMLElement))!=null?z:null},[v]);pt(te,J);let se=(()=>!(!K||W))();wt(oe,A,se);let Q=(()=>!(W||x!==0))();It(b==null?void 0:b.defaultView,"keydown",T=>{Q&&(T.defaultPrevented||T.key===G.Escape&&(T.preventDefault(),T.stopPropagation(),A()))});let Ee=(()=>!(V||x!==0||O))();Lr(b,Ee,oe),s.useEffect(()=>{if(x!==0||!m.current)return;let T=new ResizeObserver(z=>{for(let Y of z){let Pe=Y.target.getBoundingClientRect();Pe.x===0&&Pe.y===0&&Pe.width===0&&Pe.height===0&&A()}});return T.observe(m.current),()=>T.disconnect()},[x,m,A]);let[Be,ve]=Tr(),Ue=s.useMemo(()=>[{dialogState:x,close:A,setTitleId:X},$],[x,$,A,X]),S=s.useMemo(()=>({open:x===0}),[x]),C={ref:g,id:r,role:a,"aria-modal":x===0?!0:void 0,"aria-labelledby":$.titleId,"aria-describedby":Be};return w.createElement(Sr,{type:"Dialog",enabled:x===0,element:m,onUpdate:h((T,z)=>{z==="Dialog"&&D(T,{[tt.Add]:()=>p(Y=>Y+1),[tt.Remove]:()=>p(Y=>Y-1)})})},w.createElement(Qe,{force:!0},w.createElement(et,null,w.createElement(Ne.Provider,{value:Ue},w.createElement(et.Group,{target:m},w.createElement(Qe,{force:!1},w.createElement(ve,{slot:S,name:"Dialog.Description"},w.createElement(xe,{initialFocus:i,containers:oe,features:K?D(P,{parent:xe.features.RestoreFocus,leaf:xe.features.All&~xe.features.FocusLock}):xe.features.None},w.createElement(N,null,B({ourProps:C,theirProps:c,slot:S,defaultTag:Ar,features:Nr,visible:x===0,name:"Dialog"}))))))))),w.createElement(_,null))}let kr="div";function Hr(e,t){let n=ee(),{id:r=`headlessui-dialog-overlay-${n}`,...o}=e,[{dialogState:l,close:i}]=$e("Dialog.Overlay"),a=q(t),u=h(f=>{if(f.target===f.currentTarget){if(Ot(f.currentTarget))return f.preventDefault();f.preventDefault(),f.stopPropagation(),i()}}),c=s.useMemo(()=>({open:l===0}),[l]);return B({ourProps:{ref:a,id:r,"aria-hidden":!0,onClick:u},theirProps:o,slot:c,defaultTag:kr,name:"Dialog.Overlay"})}let jr="div";function _r(e,t){let n=ee(),{id:r=`headlessui-dialog-backdrop-${n}`,...o}=e,[{dialogState:l},i]=$e("Dialog.Backdrop"),a=q(t);s.useEffect(()=>{if(i.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[i.panelRef]);let u=s.useMemo(()=>({open:l===0}),[l]);return w.createElement(Qe,{force:!0},w.createElement(et,null,B({ourProps:{ref:a,id:r,"aria-hidden":!0},theirProps:o,slot:u,defaultTag:jr,name:"Dialog.Backdrop"})))}let Br="div";function Ur(e,t){let n=ee(),{id:r=`headlessui-dialog-panel-${n}`,...o}=e,[{dialogState:l},i]=$e("Dialog.Panel"),a=q(t,i.panelRef),u=s.useMemo(()=>({open:l===0}),[l]),c=h(f=>{f.stopPropagation()});return B({ourProps:{ref:a,id:r,onClick:c},theirProps:o,slot:u,defaultTag:Br,name:"Dialog.Panel"})}let Vr="h2";function qr(e,t){let n=ee(),{id:r=`headlessui-dialog-title-${n}`,...o}=e,[{dialogState:l,setTitleId:i}]=$e("Dialog.Title"),a=q(t);s.useEffect(()=>(i(r),()=>i(null)),[r,i]);let u=s.useMemo(()=>({open:l===0}),[l]);return B({ourProps:{ref:a,id:r},theirProps:o,slot:u,defaultTag:Vr,name:"Dialog.Title"})}let Wr=H(Mr),Yr=H(_r),Kr=H(Ur),zr=H(Hr),Gr=H(qr),go=Object.assign(Wr,{Backdrop:Yr,Panel:Kr,Overlay:zr,Title:Gr,Description:$r});function Xr(e=0){let[t,n]=s.useState(e),r=Ce(),o=s.useCallback(u=>{r.current&&n(c=>c|u)},[t,r]),l=s.useCallback(u=>!!(t&u),[t]),i=s.useCallback(u=>{r.current&&n(c=>c&~u)},[n,r]),a=s.useCallback(u=>{r.current&&n(c=>c^u)},[n]);return{flags:t,addFlag:o,hasFlag:l,removeFlag:i,toggleFlag:a}}function Jr(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function ze(e,...t){e&&t.length>0&&e.classList.add(...t)}function Ge(e,...t){e&&t.length>0&&e.classList.remove(...t)}function Qr(e,t){let n=ie();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[l,i]=[r,o].map(u=>{let[c=0]=u.split(",").filter(Boolean).map(f=>f.includes("ms")?parseFloat(f):parseFloat(f)*1e3).sort((f,p)=>p-f);return c}),a=l+i;if(a!==0){n.group(c=>{c.setTimeout(()=>{t(),c.dispose()},a),c.addEventListener(e,"transitionrun",f=>{f.target===f.currentTarget&&c.dispose()})});let u=n.addEventListener(e,"transitionend",c=>{c.target===c.currentTarget&&(t(),u())})}else t();return n.add(()=>t()),n.dispose}function Zr(e,t,n,r){let o=n?"enter":"leave",l=ie(),i=r!==void 0?Jr(r):()=>{};o==="enter"&&(e.removeAttribute("hidden"),e.style.display="");let a=D(o,{enter:()=>t.enter,leave:()=>t.leave}),u=D(o,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),c=D(o,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return Ge(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),ze(e,...t.base,...a,...c),l.nextFrame(()=>{Ge(e,...t.base,...a,...c),ze(e,...t.base,...a,...u),Qr(e,()=>(Ge(e,...t.base,...a),ze(e,...t.base,...t.entered),i()))}),l.dispose}function eo({immediate:e,container:t,direction:n,classes:r,onStart:o,onStop:l}){let i=Ce(),a=be(),u=Z(n);I(()=>{e&&(u.current="enter")},[e]),I(()=>{let c=ie();a.add(c.dispose);let f=t.current;if(f&&u.current!=="idle"&&i.current)return c.dispose(),o.current(u.current),c.add(Zr(f,r.current,u.current==="enter",()=>{c.dispose(),l.current(u.current)})),c.dispose},[n])}function ae(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let He=s.createContext(null);He.displayName="TransitionContext";var to=(e=>(e.Visible="visible",e.Hidden="hidden",e))(to||{});function no(){let e=s.useContext(He);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function ro(){let e=s.useContext(je);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}let je=s.createContext(null);je.displayName="NestingContext";function _e(e){return"children"in e?_e(e.children):e.current.filter(({el:t})=>t.current!==null).filter(({state:t})=>t==="visible").length>0}function jt(e,t){let n=Z(e),r=s.useRef([]),o=Ce(),l=be(),i=h((d,m=ue.Hidden)=>{let g=r.current.findIndex(({el:b})=>b===d);g!==-1&&(D(m,{[ue.Unmount](){r.current.splice(g,1)},[ue.Hidden](){r.current[g].state="hidden"}}),l.microTask(()=>{var b;!_e(r)&&o.current&&((b=n.current)==null||b.call(n))}))}),a=h(d=>{let m=r.current.find(({el:g})=>g===d);return m?m.state!=="visible"&&(m.state="visible"):r.current.push({el:d,state:"visible"}),()=>i(d,ue.Unmount)}),u=s.useRef([]),c=s.useRef(Promise.resolve()),f=s.useRef({enter:[],leave:[],idle:[]}),p=h((d,m,g)=>{u.current.splice(0),t&&(t.chains.current[m]=t.chains.current[m].filter(([b])=>b!==d)),t==null||t.chains.current[m].push([d,new Promise(b=>{u.current.push(b)})]),t==null||t.chains.current[m].push([d,new Promise(b=>{Promise.all(f.current[m].map(([y,F])=>F)).then(()=>b())})]),m==="enter"?c.current=c.current.then(()=>t==null?void 0:t.wait.current).then(()=>g(m)):g(m)}),E=h((d,m,g)=>{Promise.all(f.current[m].splice(0).map(([b,y])=>y)).then(()=>{var b;(b=u.current.shift())==null||b()}).then(()=>g(m))});return s.useMemo(()=>({children:r,register:a,unregister:i,onStart:p,onStop:E,wait:c,chains:f}),[a,i,r,p,E,f,c])}function oo(){}let lo=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function vt(e){var t;let n={};for(let r of lo)n[r]=(t=e[r])!=null?t:oo;return n}function io(e){let t=s.useRef(vt(e));return s.useEffect(()=>{t.current=vt(e)},[e]),t}let ao="div",_t=me.RenderStrategy;function uo(e,t){var n,r;let{beforeEnter:o,afterEnter:l,beforeLeave:i,afterLeave:a,enter:u,enterFrom:c,enterTo:f,entered:p,leave:E,leaveFrom:d,leaveTo:m,...g}=e,b=s.useRef(null),y=q(b,t),F=(n=g.unmount)==null||n?ue.Unmount:ue.Hidden,{show:x,appear:$,initial:j}=no(),[A,X]=s.useState(x?"visible":"hidden"),K=ro(),{register:W,unregister:O}=K;s.useEffect(()=>W(b),[W,b]),s.useEffect(()=>{if(F===ue.Hidden&&b.current){if(x&&A!=="visible"){X("visible");return}return D(A,{hidden:()=>O(b),visible:()=>W(b)})}},[A,b,W,O,x,F]);let R=Z({base:ae(g.className),enter:ae(u),enterFrom:ae(c),enterTo:ae(f),entered:ae(p),leave:ae(E),leaveFrom:ae(d),leaveTo:ae(m)}),N=io({beforeEnter:o,afterEnter:l,beforeLeave:i,afterLeave:a}),re=ge();s.useEffect(()=>{if(re&&A==="visible"&&b.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[b,A,re]);let oe=j&&!$,v=$&&x&&j,_=(()=>!re||oe?"idle":x?"enter":"leave")(),P=Xr(0),V=h(Q=>D(Q,{enter:()=>{P.addFlag(U.Opening),N.current.beforeEnter()},leave:()=>{P.addFlag(U.Closing),N.current.beforeLeave()},idle:()=>{}})),L=h(Q=>D(Q,{enter:()=>{P.removeFlag(U.Opening),N.current.afterEnter()},leave:()=>{P.removeFlag(U.Closing),N.current.afterLeave()},idle:()=>{}})),M=jt(()=>{X("hidden"),O(b)},K),J=s.useRef(!1);eo({immediate:v,container:b,classes:R,direction:_,onStart:Z(Q=>{J.current=!0,M.onStart(b,Q,V)}),onStop:Z(Q=>{J.current=!1,M.onStop(b,Q,L),Q==="leave"&&!_e(M)&&(X("hidden"),O(b))})});let te=g,se={ref:y};return v?te={...te,className:De(g.className,...R.current.enter,...R.current.enterFrom)}:J.current&&(te.className=De(g.className,(r=b.current)==null?void 0:r.className),te.className===""&&delete te.className),w.createElement(je.Provider,{value:M},w.createElement(Tt,{value:D(A,{visible:U.Open,hidden:U.Closed})|P.flags},B({ourProps:se,theirProps:te,defaultTag:ao,features:_t,visible:A==="visible",name:"Transition.Child"})))}function so(e,t){let{show:n,appear:r=!1,unmount:o=!0,...l}=e,i=s.useRef(null),a=q(i,t);ge();let u=ke();if(n===void 0&&u!==null&&(n=(u&U.Open)===U.Open),![!0,!1].includes(n))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[c,f]=s.useState(n?"visible":"hidden"),p=jt(()=>{f("hidden")}),[E,d]=s.useState(!0),m=s.useRef([n]);I(()=>{E!==!1&&m.current[m.current.length-1]!==n&&(m.current.push(n),d(!1))},[m,n]);let g=s.useMemo(()=>({show:n,appear:r,initial:E}),[n,r,E]);s.useEffect(()=>{if(n)f("visible");else if(!_e(p))f("hidden");else{let x=i.current;if(!x)return;let $=x.getBoundingClientRect();$.x===0&&$.y===0&&$.width===0&&$.height===0&&f("hidden")}},[n,p]);let b={unmount:o},y=h(()=>{var x;E&&d(!1),(x=e.beforeEnter)==null||x.call(e)}),F=h(()=>{var x;E&&d(!1),(x=e.beforeLeave)==null||x.call(e)});return w.createElement(je.Provider,{value:p},w.createElement(He.Provider,{value:g},B({ourProps:{...b,as:s.Fragment,children:w.createElement(Bt,{ref:a,...b,...l,beforeEnter:y,beforeLeave:F})},theirProps:{},defaultTag:s.Fragment,features:_t,visible:c==="visible",name:"Transition"})))}function co(e,t){let n=s.useContext(He)!==null,r=ke()!==null;return w.createElement(w.Fragment,null,!n&&r?w.createElement(nt,{ref:t,...e}):w.createElement(Bt,{ref:t,...e}))}let nt=H(so),Bt=H(uo),fo=H(co),ho=Object.assign(nt,{Child:fo,Root:nt});export{go as _,ho as a,bo as q};
