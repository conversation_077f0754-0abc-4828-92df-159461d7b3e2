var P,f,o_,E,$,G,i_,M={},l_=[],k_=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function k(_,e){for(var t in e)_[t]=e[t];return _}function u_(_){var e=_.parentNode;e&&e.removeChild(_)}function $_(_,e,t){var o,i,r,u={};for(r in e)r=="key"?o=e[r]:r=="ref"?i=e[r]:u[r]=e[r];if(arguments.length>2&&(u.children=arguments.length>3?P.call(arguments,2):t),typeof _=="function"&&_.defaultProps!=null)for(r in _.defaultProps)u[r]===void 0&&(u[r]=_.defaultProps[r]);return x(_,u,o,i,null)}function x(_,e,t,o,i){var r={type:_,props:e,key:t,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:i??++o_};return i==null&&f.vnode!=null&&f.vnode(r),r}function A_(){return{current:null}}function W(_){return _.children}function H_(_,e,t,o,i){var r;for(r in t)r==="children"||r==="key"||r in e||V(_,r,null,t[r],o);for(r in e)i&&typeof e[r]!="function"||r==="children"||r==="key"||r==="value"||r==="checked"||t[r]===e[r]||V(_,r,e[r],t[r],o)}function J(_,e,t){e[0]==="-"?_.setProperty(e,t??""):_[e]=t==null?"":typeof t!="number"||k_.test(e)?t:t+"px"}function V(_,e,t,o,i){var r;_:if(e==="style")if(typeof t=="string")_.style.cssText=t;else{if(typeof o=="string"&&(_.style.cssText=o=""),o)for(e in o)t&&e in t||J(_.style,e,"");if(t)for(e in t)o&&t[e]===o[e]||J(_.style,e,t[e])}else if(e[0]==="o"&&e[1]==="n")r=e!==(e=e.replace(/Capture$/,"")),e=e.toLowerCase()in _?e.toLowerCase().slice(2):e.slice(2),_.l||(_.l={}),_.l[e+r]=t,t?o||_.addEventListener(e,r?Q:K,r):_.removeEventListener(e,r?Q:K,r);else if(e!=="dangerouslySetInnerHTML"){if(i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e in _)try{_[e]=t??"";break _}catch{}typeof t=="function"||(t==null||t===!1&&e.indexOf("-")==-1?_.removeAttribute(e):_.setAttribute(e,t))}}function K(_){E=!0;try{return this.l[_.type+!1](f.event?f.event(_):_)}finally{E=!1}}function Q(_){E=!0;try{return this.l[_.type+!0](f.event?f.event(_):_)}finally{E=!1}}function F(_,e){this.props=_,this.context=e}function w(_,e){if(e==null)return _.__?w(_.__,_.__.__k.indexOf(_)+1):null;for(var t;e<_.__k.length;e++)if((t=_.__k[e])!=null&&t.__e!=null)return t.__e;return typeof _.type=="function"?w(_):null}function f_(_){var e,t;if((_=_.__)!=null&&_.__c!=null){for(_.__e=_.__c.base=null,e=0;e<_.__k.length;e++)if((t=_.__k[e])!=null&&t.__e!=null){_.__e=_.__c.base=t.__e;break}return f_(_)}}function E_(_){E?setTimeout(_):i_(_)}function X(_){(!_.__d&&(_.__d=!0)&&$.push(_)&&!O.__r++||G!==f.debounceRendering)&&((G=f.debounceRendering)||E_)(O)}function O(){var _,e,t,o,i,r,u,c;for($.sort(function(p,h){return p.__v.__b-h.__v.__b});_=$.shift();)_.__d&&(e=$.length,o=void 0,i=void 0,u=(r=(t=_).__v).__e,(c=t.__P)&&(o=[],(i=k({},r)).__v=r.__v+1,B(c,r,i,t.__n,c.ownerSVGElement!==void 0,r.__h!=null?[u]:null,o,u??w(r),r.__h),h_(o,r),r.__e!=u&&f_(r)),$.length>e&&$.sort(function(p,h){return p.__v.__b-h.__v.__b}));O.__r=0}function c_(_,e,t,o,i,r,u,c,p,h){var n,d,s,l,a,H,v,m=o&&o.__k||l_,b=m.length;for(t.__k=[],n=0;n<e.length;n++)if((l=t.__k[n]=(l=e[n])==null||typeof l=="boolean"?null:typeof l=="string"||typeof l=="number"||typeof l=="bigint"?x(null,l,null,null,l):Array.isArray(l)?x(W,{children:l},null,null,null):l.__b>0?x(l.type,l.props,l.key,l.ref?l.ref:null,l.__v):l)!=null){if(l.__=t,l.__b=t.__b+1,(s=m[n])===null||s&&l.key==s.key&&l.type===s.type)m[n]=void 0;else for(d=0;d<b;d++){if((s=m[d])&&l.key==s.key&&l.type===s.type){m[d]=void 0;break}s=null}B(_,l,s=s||M,i,r,u,c,p,h),a=l.__e,(d=l.ref)&&s.ref!=d&&(v||(v=[]),s.ref&&v.push(s.ref,null,l),v.push(d,l.__c||a,l)),a!=null?(H==null&&(H=a),typeof l.type=="function"&&l.__k===s.__k?l.__d=p=s_(l,p,_):p=a_(_,l,s,m,a,p),typeof t.type=="function"&&(t.__d=p)):p&&s.__e==p&&p.parentNode!=_&&(p=w(s))}for(t.__e=H,n=b;n--;)m[n]!=null&&(typeof t.type=="function"&&m[n].__e!=null&&m[n].__e==t.__d&&(t.__d=p_(o).nextSibling),v_(m[n],m[n]));if(v)for(n=0;n<v.length;n++)d_(v[n],v[++n],v[++n])}function s_(_,e,t){for(var o,i=_.__k,r=0;i&&r<i.length;r++)(o=i[r])&&(o.__=_,e=typeof o.type=="function"?s_(o,e,t):a_(t,o,o,i,o.__e,e));return e}function S_(_,e){return e=e||[],_==null||typeof _=="boolean"||(Array.isArray(_)?_.some(function(t){S_(t,e)}):e.push(_)),e}function a_(_,e,t,o,i,r){var u,c,p;if(e.__d!==void 0)u=e.__d,e.__d=void 0;else if(t==null||i!=r||i.parentNode==null)_:if(r==null||r.parentNode!==_)_.appendChild(i),u=null;else{for(c=r,p=0;(c=c.nextSibling)&&p<o.length;p+=1)if(c==i)break _;_.insertBefore(i,r),u=r}return u!==void 0?u:i.nextSibling}function p_(_){var e,t,o;if(_.type==null||typeof _.type=="string")return _.__e;if(_.__k){for(e=_.__k.length-1;e>=0;e--)if((t=_.__k[e])&&(o=p_(t)))return o}return null}function B(_,e,t,o,i,r,u,c,p){var h,n,d,s,l,a,H,v,m,b,C,S,z,A,T,g=e.type;if(e.constructor!==void 0)return null;t.__h!=null&&(p=t.__h,c=e.__e=t.__e,e.__h=null,r=[c]),(h=f.__b)&&h(e);try{_:if(typeof g=="function"){if(v=e.props,m=(h=g.contextType)&&o[h.__c],b=h?m?m.props.value:h.__:o,t.__c?H=(n=e.__c=t.__c).__=n.__E:("prototype"in g&&g.prototype.render?e.__c=n=new g(v,b):(e.__c=n=new F(v,b),n.constructor=g,n.render=w_),m&&m.sub(n),n.props=v,n.state||(n.state={}),n.context=b,n.__n=o,d=n.__d=!0,n.__h=[],n._sb=[]),n.__s==null&&(n.__s=n.state),g.getDerivedStateFromProps!=null&&(n.__s==n.state&&(n.__s=k({},n.__s)),k(n.__s,g.getDerivedStateFromProps(v,n.__s))),s=n.props,l=n.state,n.__v=e,d)g.getDerivedStateFromProps==null&&n.componentWillMount!=null&&n.componentWillMount(),n.componentDidMount!=null&&n.__h.push(n.componentDidMount);else{if(g.getDerivedStateFromProps==null&&v!==s&&n.componentWillReceiveProps!=null&&n.componentWillReceiveProps(v,b),!n.__e&&n.shouldComponentUpdate!=null&&n.shouldComponentUpdate(v,n.__s,b)===!1||e.__v===t.__v){for(e.__v!==t.__v&&(n.props=v,n.state=n.__s,n.__d=!1),e.__e=t.__e,e.__k=t.__k,e.__k.forEach(function(D){D&&(D.__=e)}),C=0;C<n._sb.length;C++)n.__h.push(n._sb[C]);n._sb=[],n.__h.length&&u.push(n);break _}n.componentWillUpdate!=null&&n.componentWillUpdate(v,n.__s,b),n.componentDidUpdate!=null&&n.__h.push(function(){n.componentDidUpdate(s,l,a)})}if(n.context=b,n.props=v,n.__P=_,S=f.__r,z=0,"prototype"in g&&g.prototype.render){for(n.state=n.__s,n.__d=!1,S&&S(e),h=n.render(n.props,n.state,n.context),A=0;A<n._sb.length;A++)n.__h.push(n._sb[A]);n._sb=[]}else do n.__d=!1,S&&S(e),h=n.render(n.props,n.state,n.context),n.state=n.__s;while(n.__d&&++z<25);n.state=n.__s,n.getChildContext!=null&&(o=k(k({},o),n.getChildContext())),d||n.getSnapshotBeforeUpdate==null||(a=n.getSnapshotBeforeUpdate(s,l)),T=h!=null&&h.type===W&&h.key==null?h.props.children:h,c_(_,Array.isArray(T)?T:[T],e,t,o,i,r,u,c,p),n.base=e.__e,e.__h=null,n.__h.length&&u.push(n),H&&(n.__E=n.__=null),n.__e=!1}else r==null&&e.__v===t.__v?(e.__k=t.__k,e.__e=t.__e):e.__e=x_(t.__e,e,t,o,i,r,u,p);(h=f.diffed)&&h(e)}catch(D){e.__v=null,(p||r!=null)&&(e.__e=c,e.__h=!!p,r[r.indexOf(c)]=null),f.__e(D,e,t)}}function h_(_,e){f.__c&&f.__c(e,_),_.some(function(t){try{_=t.__h,t.__h=[],_.some(function(o){o.call(t)})}catch(o){f.__e(o,t.__v)}})}function x_(_,e,t,o,i,r,u,c){var p,h,n,d=t.props,s=e.props,l=e.type,a=0;if(l==="svg"&&(i=!0),r!=null){for(;a<r.length;a++)if((p=r[a])&&"setAttribute"in p==!!l&&(l?p.localName===l:p.nodeType===3)){_=p,r[a]=null;break}}if(_==null){if(l===null)return document.createTextNode(s);_=i?document.createElementNS("http://www.w3.org/2000/svg",l):document.createElement(l,s.is&&s),r=null,c=!1}if(l===null)d===s||c&&_.data===s||(_.data=s);else{if(r=r&&P.call(_.childNodes),h=(d=t.props||M).dangerouslySetInnerHTML,n=s.dangerouslySetInnerHTML,!c){if(r!=null)for(d={},a=0;a<_.attributes.length;a++)d[_.attributes[a].name]=_.attributes[a].value;(n||h)&&(n&&(h&&n.__html==h.__html||n.__html===_.innerHTML)||(_.innerHTML=n&&n.__html||""))}if(H_(_,s,d,i,c),n)e.__k=[];else if(a=e.props.children,c_(_,Array.isArray(a)?a:[a],e,t,o,i&&l!=="foreignObject",r,u,r?r[0]:t.__k&&w(t,0),c),r!=null)for(a=r.length;a--;)r[a]!=null&&u_(r[a]);c||("value"in s&&(a=s.value)!==void 0&&(a!==_.value||l==="progress"&&!a||l==="option"&&a!==d.value)&&V(_,"value",a,d.value,!1),"checked"in s&&(a=s.checked)!==void 0&&a!==_.checked&&V(_,"checked",a,d.checked,!1))}return _}function d_(_,e,t){try{typeof _=="function"?_(e):_.current=e}catch(o){f.__e(o,t)}}function v_(_,e,t){var o,i;if(f.unmount&&f.unmount(_),(o=_.ref)&&(o.current&&o.current!==_.__e||d_(o,null,e)),(o=_.__c)!=null){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(r){f.__e(r,e)}o.base=o.__P=null,_.__c=void 0}if(o=_.__k)for(i=0;i<o.length;i++)o[i]&&v_(o[i],e,t||typeof _.type!="function");t||_.__e==null||u_(_.__e),_.__=_.__e=_.__d=void 0}function w_(_,e,t){return this.constructor(_,t)}function T_(_,e,t){var o,i,r;f.__&&f.__(_,e),i=(o=typeof t=="function")?null:t&&t.__k||e.__k,r=[],B(e,_=(!o&&t||e).__k=$_(W,null,[_]),i||M,M,e.ownerSVGElement!==void 0,!o&&t?[t]:i?null:e.firstChild?P.call(e.childNodes):null,r,!o&&t?t:i?i.__e:e.firstChild,o),h_(r,_)}function D_(_,e,t){var o,i,r,u=k({},_.props);for(r in e)r=="key"?o=e[r]:r=="ref"?i=e[r]:u[r]=e[r];return arguments.length>2&&(u.children=arguments.length>3?P.call(arguments,2):t),x(_.type,u,o||_.key,i||_.ref,null)}P=l_.slice,f={__e:function(_,e,t,o){for(var i,r,u;e=e.__;)if((i=e.__c)&&!i.__)try{if((r=i.constructor)&&r.getDerivedStateFromError!=null&&(i.setState(r.getDerivedStateFromError(_)),u=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(_,o||{}),u=i.__d),u)return i.__E=i}catch(c){_=c}throw _}},o_=0,E=!1,F.prototype.setState=function(_,e){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=k({},this.state),typeof _=="function"&&(_=_(k({},t),this.props)),_&&k(t,_),_!=null&&this.__v&&(e&&this._sb.push(e),X(this))},F.prototype.forceUpdate=function(_){this.__v&&(this.__e=!0,_&&this.__h.push(_),X(this))},F.prototype.render=W,$=[],i_=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,O.__r=0;var j,y,R,Y,N=0,y_=[],U=[],Z=f.__b,__=f.__r,e_=f.diffed,t_=f.__c,n_=f.unmount;function I(_,e){f.__h&&f.__h(y,_,N||e),N=0;var t=y.__H||(y.__H={__:[],__h:[]});return _>=t.__.length&&t.__.push({__V:U}),t.__[_]}function F_(_){return N=1,N_(b_,_)}function N_(_,e,t){var o=I(j++,2);if(o.t=_,!o.__c&&(o.__=[t?t(e):b_(void 0,e),function(r){var u=o.__N?o.__N[0]:o.__[0],c=o.t(u,r);u!==c&&(o.__N=[c,o.__[1]],o.__c.setState({}))}],o.__c=y,!y.u)){y.u=!0;var i=y.shouldComponentUpdate;y.shouldComponentUpdate=function(r,u,c){if(!o.__c.__H)return!0;var p=o.__c.__H.__.filter(function(n){return n.__c});if(p.every(function(n){return!n.__N}))return!i||i.call(this,r,u,c);var h=!1;return p.forEach(function(n){if(n.__N){var d=n.__[0];n.__=n.__N,n.__N=void 0,d!==n.__[0]&&(h=!0)}}),!(!h&&o.__c.props===r)&&(!i||i.call(this,r,u,c))}}return o.__N||o.__}function U_(_,e){var t=I(j++,3);!f.__s&&g_(t.__H,e)&&(t.__=_,t.i=e,y.__H.__h.push(t))}function L_(_){return N=5,m_(function(){return{current:_}},[])}function m_(_,e){var t=I(j++,7);return g_(t.__H,e)?(t.__V=_(),t.i=e,t.__h=_,t.__V):t.__}function M_(_,e){return N=8,m_(function(){return _},e)}function P_(){for(var _;_=y_.shift();)if(_.__P&&_.__H)try{_.__H.__h.forEach(L),_.__H.__h.forEach(q),_.__H.__h=[]}catch(e){_.__H.__h=[],f.__e(e,_.__v)}}f.__b=function(_){y=null,Z&&Z(_)},f.__r=function(_){__&&__(_),j=0;var e=(y=_.__c).__H;e&&(R===y?(e.__h=[],y.__h=[],e.__.forEach(function(t){t.__N&&(t.__=t.__N),t.__V=U,t.__N=t.i=void 0})):(e.__h.forEach(L),e.__h.forEach(q),e.__h=[])),R=y},f.diffed=function(_){e_&&e_(_);var e=_.__c;e&&e.__H&&(e.__H.__h.length&&(y_.push(e)!==1&&Y===f.requestAnimationFrame||((Y=f.requestAnimationFrame)||C_)(P_)),e.__H.__.forEach(function(t){t.i&&(t.__H=t.i),t.__V!==U&&(t.__=t.__V),t.i=void 0,t.__V=U})),R=y=null},f.__c=function(_,e){e.some(function(t){try{t.__h.forEach(L),t.__h=t.__h.filter(function(o){return!o.__||q(o)})}catch(o){e.some(function(i){i.__h&&(i.__h=[])}),e=[],f.__e(o,t.__v)}}),t_&&t_(_,e)},f.unmount=function(_){n_&&n_(_);var e,t=_.__c;t&&t.__H&&(t.__H.__.forEach(function(o){try{L(o)}catch(i){e=i}}),t.__H=void 0,e&&f.__e(e,t.__v))};var r_=typeof requestAnimationFrame=="function";function C_(_){var e,t=function(){clearTimeout(o),r_&&cancelAnimationFrame(e),setTimeout(_)},o=setTimeout(t,100);r_&&(e=requestAnimationFrame(t))}function L(_){var e=y,t=_.__c;typeof t=="function"&&(_.__c=void 0,t()),y=e}function q(_){var e=y;_.__c=_.__(),y=e}function g_(_,e){return!_||_.length!==e.length||e.some(function(t,o){return t!==_[o]})}function b_(_,e){return typeof e=="function"?e(_):e}function V_(_,e,t,o){var i=arguments.length,r=i<3?e:o===null?o=Object.getOwnPropertyDescriptor(e,t):o,u;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(_,e,t,o);else for(var c=_.length-1;c>=0;c--)(u=_[c])&&(r=(i<3?u(r):i>3?u(e,t,r):u(e,t))||r);return i>3&&r&&Object.defineProperty(e,t,r),r}export{T_ as D,D_ as F,M_ as T,V_ as _,W as a,m_ as b,L_ as c,A_ as d,U_ as h,S_ as j,F_ as p,F as x,$_ as y};
