import{r as X,R as vo}from"../vendor-df163860.js";import{s as An}from"../@emotion/serialize-358fdd7f.js";import{g as On,r as Nn,i as Vn}from"../@emotion/utils-8a8f62c5.js";import{G as yo,w as Dn,T as jn}from"../@emotion/react-4b83849f.js";var ai={exports:{}},It={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mn=Symbol.for("react.transitional.element"),Fn=Symbol.for("react.fragment");function si(e,t,o){var r=null;if(o!==void 0&&(r=""+o),t.key!==void 0&&(r=""+t.key),"key"in t){o={};for(var i in t)i!=="key"&&(o[i]=t[i])}else o=t;return t=o.ref,{$$typeof:Mn,type:e,key:r,ref:t!==void 0?t:null,props:o}}It.Fragment=Fn;It.jsx=si;It.jsxs=si;ai.exports=It;var G=ai.exports;const xe=(...e)=>e.filter(Boolean).map(t=>t.trim()).join(" ");function Ln(e,t){return`${e} returned \`undefined\`. Seems you forgot to wrap component within ${t}`}function rt(e={}){const{name:t,strict:o=!0,hookName:r="useContext",providerName:i="Provider",errorMessage:n,defaultValue:a}=e,s=X.createContext(a);s.displayName=t;function d(){var l;const c=X.useContext(s);if(!c&&o){const u=new Error(n??Ln(r,i));throw u.name="ContextError",(l=Error.captureStackTrace)==null||l.call(Error,u,d),u}return c}return[s.Provider,d,s]}function Hn(...e){return function(...o){e.forEach(r=>r==null?void 0:r(...o))}}const Gn=(...e)=>e.map(t=>{var o;return(o=t==null?void 0:t.trim)==null?void 0:o.call(t)}).filter(Boolean).join(" "),Yn=/^on[A-Z]/;function yt(...e){let t={};for(let o of e){for(let r in t){if(Yn.test(r)&&typeof t[r]=="function"&&typeof o[r]=="function"){t[r]=Hn(t[r],o[r]);continue}if(r==="className"||r==="class"){t[r]=Gn(t[r],o[r]);continue}if(r==="style"){t[r]=Object.assign({},t[r]??{},o[r]??{});continue}t[r]=o[r]!==void 0?o[r]:t[r]}for(let r in o)t[r]===void 0&&(t[r]=o[r])}return t}const qn=Object.freeze({}),Un=Object.freeze({});function Xn(e){var t=Object.create(null);return function(o){return t[o]===void 0&&(t[o]=e(o)),t[o]}}var Kn=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Zn=Xn(function(e){return Kn.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91}),Qn=function(t){return t()},li=vo["useInsertionEffect"]?vo["useInsertionEffect"]:!1,Jn=li||Qn,sg=li||X.useLayoutEffect;function ea(e,t){if(e!=null){if(typeof e=="function"){e(t);return}try{e.current=t}catch{throw new Error(`Cannot assign value '${t}' to ref '${e}'`)}}}function ta(...e){return t=>{e.forEach(o=>{ea(o,t)})}}function at(e){const t=Object.assign({},e);for(let o in t)t[o]===void 0&&delete t[o];return t}function oa(e){return e.default||e}const ce=e=>e!=null&&typeof e=="object"&&!Array.isArray(e),Se=e=>typeof e=="string",di=e=>typeof e=="function";function ra(e){var o;const t=X.version;return!Se(t)||t.startsWith("18.")?e==null?void 0:e.ref:(o=e==null?void 0:e.props)==null?void 0:o.ref}const[ia,zt]=rt({name:"ChakraContext",strict:!0,providerName:"<ChakraProvider />"});function lg(e){const{value:t,children:o}=e;return G.jsxs(ia,{value:t,children:[!t._config.disableLayers&&G.jsx(yo,{styles:t.layers.atRule}),G.jsx(yo,{styles:[t.getPreflightCss(),t.getGlobalCss(),t.getTokenCss()]}),o]})}const Ke=(e,t)=>{const o=Object.getOwnPropertyDescriptors(e),r=Object.keys(o),i=a=>{const s={};for(let d=0;d<a.length;d++){const c=a[d];o[c]&&(Object.defineProperty(s,c,o[c]),delete o[c])}return s},n=a=>i(Array.isArray(a)?a:r.filter(a));return[t].map(n).concat(i(r))},na=new Set(["htmlWidth","htmlHeight","htmlSize","htmlTranslate"]);function aa(e){return typeof e=="string"&&na.has(e)}function sa(e,t,o){const{css:r,isValidProperty:i}=zt(),{children:n,...a}=e,s=X.useMemo(()=>{const[g,p]=Ke(a,x=>o(x,t.variantKeys)),[m,f]=Ke(p,t.variantKeys),[S,k]=Ke(f,i);return{forwardedProps:g,variantProps:m,styleProps:S,elementProps:k}},[t.variantKeys,o,a,i]),{css:d,...c}=s.styleProps,l=X.useMemo(()=>{const g={...s.variantProps};return t.variantKeys.includes("colorPalette")||(g.colorPalette=a.colorPalette),t.variantKeys.includes("orientation")||(g.orientation=a.orientation),t(g)},[t,s.variantProps,a.colorPalette,a.orientation]);return{styles:X.useMemo(()=>r(l,...la(d),c),[r,l,d,c]),props:{...s.forwardedProps,...s.elementProps,children:n}}}const la=e=>(Array.isArray(e)?e:[e]).filter(Boolean).flat(),da=oa(Zn),ca=da,ua=e=>e!=="theme",ga=(e,t,o)=>{let r;if(t){const i=t.shouldForwardProp;r=e.__emotion_forwardProp&&i?n=>e.__emotion_forwardProp(n)&&i(n):i}return typeof r!="function"&&o&&(r=e.__emotion_forwardProp),r};let pa=typeof document<"u";const xo=({cache:e,serialized:t,isStringTag:o})=>{Nn(e,t,o);const r=Jn(()=>Vn(e,t,o));if(!pa&&r!==void 0){let i=t.name,n=t.next;for(;n!==void 0;)i=xe(i,n.name),n=n.next;return G.jsx("style",{"data-emotion":xe(e.key,i),dangerouslySetInnerHTML:{__html:r},nonce:e.sheet.nonce})}return null},ma=(e,t={},o={})=>{const r=e.__emotion_real===e,i=r&&e.__emotion_base||e;let n,a;o!==void 0&&(n=o.label,a=o.target);let s=[];const d=Dn((c,l,u)=>{var V;const{cva:g,isValidProperty:p}=zt(),m=t.__cva__?t:g(t),f=ha(e.__emotion_cva,m),S=W=>(L,P)=>W.includes(L)?!0:!(P!=null&&P.includes(L))&&!p(L);!o.shouldForwardProp&&o.forwardProps&&(o.shouldForwardProp=S(o.forwardProps));const k=(W,L)=>{const P=typeof e=="string"&&e.charCodeAt(0)>96?ca:ua,H=!(L!=null&&L.includes(W))&&!p(W);return P(W)&&H},x=ga(e,o,r)||k,I=X.useMemo(()=>Object.assign({},o.defaultProps,at(c)),[c]),{props:C,styles:_}=sa(I,f,x);let B="",R=[_],h=C;if(C.theme==null){h={};for(let W in C)h[W]=C[W];h.theme=X.useContext(jn)}typeof C.className=="string"?B=On(l.registered,R,C.className):C.className!=null&&(B=xe(B,C.className));const b=An(s.concat(R),l.registered,h);B=xe(B,`${l.key}-${b.name}`),a!==void 0&&(B=xe(B,a));const w=!x("as");let $=w&&C.as||i,z={};for(let W in C)if(!(w&&W==="as")){if(aa(W)){const L=W.replace("html","").toLowerCase();z[L]=C[W];continue}x(W)&&(z[W]=C[W])}z.className=B.trim(),z.ref=u;const N=o.forwardAsChild||((V=o.forwardProps)==null?void 0:V.includes("asChild"));if(C.asChild&&!N){const W=X.Children.only(C.children);$=W.type,z.children=null,Reflect.deleteProperty(z,"asChild"),z=yt(z,W.props),z.ref=ta(u,ra(W))}return z.as&&N?(z.as=void 0,G.jsxs(X.Fragment,{children:[G.jsx(xo,{cache:l,serialized:b,isStringTag:typeof $=="string"}),G.jsx($,{asChild:!0,...z,children:G.jsx(C.as,{children:z.children})})]})):G.jsxs(X.Fragment,{children:[G.jsx(xo,{cache:l,serialized:b,isStringTag:typeof $=="string"}),G.jsx($,{...z})]})});return d.displayName=n!==void 0?n:`chakra(${typeof i=="string"?i:i.displayName||i.name||"Component"})`,d.__emotion_real=d,d.__emotion_base=i,d.__emotion_forwardProp=o.shouldForwardProp,d.__emotion_cva=t,Object.defineProperty(d,"toString",{value(){return`.${a}`}}),d},$t=ma.bind(),Bt=new Map,fa=new Proxy($t,{apply(e,t,o){return $t(...o)},get(e,t){return Bt.has(t)||Bt.set(t,$t(t)),Bt.get(t)}}),Oe=fa,ha=(e,t)=>e&&!t?e:!e&&t?t:e.merge(t);function ba(e){const{key:t,recipe:o}=e,r=zt();return X.useMemo(()=>{const i=o||(t!=null?r.getRecipe(t):{});return r.cva(structuredClone(i))},[t,o,r])}const va=e=>e.charAt(0).toUpperCase()+e.slice(1);function ya(e){const{key:t,recipe:o}=e,r=va(t||o.className||"Component"),[i,n]=rt({strict:!1,name:`${r}PropsContext`,providerName:`${r}PropsContext`});function a(c){const{unstyled:l,...u}=c,g=ba({key:t,recipe:u.recipe||o}),[p,m]=g.splitVariantProps(u);return{styles:l?qn:g(p),className:g.className,props:m}}const s=(c,l)=>{const u=Oe(c,{},l),g=X.forwardRef((p,m)=>{const f=yt(n(),p),{styles:S,className:k,props:x}=a(f);return G.jsx(u,{...x,ref:m,css:[S,f.css],className:xe(k,f.className)})});return g.displayName=c.displayName||c.name,g};function d(){return i}return{withContext:s,PropsProvider:i,withPropsProvider:d,usePropsContext:n,useRecipeResult:a}}var ko=e=>Math.max(0,Math.min(1,e)),xa=(e,t)=>e.map((o,r)=>e[(Math.max(t,0)+r)%e.length]),Zt=e=>typeof e=="object"&&e!==null,ka=**********,Sa=1,wa=9,Ct=e=>Zt(e)&&e.nodeType===Sa&&typeof e.nodeName=="string",Ia=e=>Zt(e)&&e.nodeType===wa,za=e=>Zt(e)&&e===e.window,Ca=e=>Ct(e)?e.offsetWidth>0||e.offsetHeight>0||e.getClientRects().length>0:!1;function Ra(e){return Ia(e)?e:za(e)?e.document:(e==null?void 0:e.ownerDocument)??document}function Pa(e){let t=e.activeElement;for(;t!=null&&t.shadowRoot;){const o=t.shadowRoot.activeElement;if(o===t)break;t=o}return t}var ci=()=>typeof document<"u";function _a(){const e=navigator.userAgentData;return(e==null?void 0:e.platform)??navigator.platform}var Ta=e=>ci()&&e.test(_a()),Ea=e=>ci()&&e.test(navigator.vendor),Wa=()=>$a()&&Ea(/apple/i),$a=()=>Ta(/mac|iphone|ipad|ipod/i),Ba=e=>Ct(e)&&e.tagName==="IFRAME",ui="input:not([type='hidden']):not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], button:not([disabled]), [tabindex], iframe, object, embed, area[href], audio[controls], video[controls], [contenteditable]:not([contenteditable='false']), details > summary:first-of-type",gi=(e,t=!1)=>{if(!e)return[];const o=Array.from(e.querySelectorAll(ui));(t==!0||t=="if-empty"&&o.length===0)&&Ct(e)&&So(e)&&o.unshift(e);const i=o.filter(So);return i.forEach((n,a)=>{if(Ba(n)&&n.contentDocument){const s=n.contentDocument.body;i.splice(a,1,...gi(s))}}),i};function So(e){return!e||e.closest("[inert]")?!1:e.matches(ui)&&Ca(e)}function Vt(e,t){const{left:o,top:r,width:i,height:n}=t.getBoundingClientRect(),a={x:e.x-o,y:e.y-r},s={x:ko(a.x/i),y:ko(a.y/n)};function d(c={}){const{dir:l="ltr",orientation:u="horizontal",inverted:g}=c,p=typeof g=="object"?g.x:g,m=typeof g=="object"?g.y:g;return u==="horizontal"?l==="rtl"||p?1-s.x:s.x:m?1-s.y:s.y}return{offset:a,percent:s,getPercentValue:d}}function Re(e,t){return Array.from((e==null?void 0:e.querySelectorAll(t))??[])}function Aa(e,t){return(e==null?void 0:e.querySelector(t))??null}var Qt=e=>e.id;function Oa(e,t,o=Qt){return e.find(r=>o(r)===t)}function Jt(e,t,o=Qt){const r=Oa(e,t,o);return r?e.indexOf(r):-1}function pi(e,t,o=!0){let r=Jt(e,t);return r=o?(r+1)%e.length:Math.min(r+1,e.length-1),e[r]}function mi(e,t,o=!0){let r=Jt(e,t);return r===-1?o?e[e.length-1]:null:(r=o?(r-1+e.length)%e.length:Math.max(0,r-1),e[r])}function q(e){const t={getRootNode:o=>{var r;return((r=o.getRootNode)==null?void 0:r.call(o))??document},getDoc:o=>Ra(t.getRootNode(o)),getWin:o=>t.getDoc(o).defaultView??window,getActiveElement:o=>Pa(t.getRootNode(o)),isActiveElement:(o,r)=>r===t.getActiveElement(o),getById:(o,r)=>t.getRootNode(o).getElementById(r),setValue:(o,r)=>{if(o==null||r==null)return;const i=r.toString();o.value!==i&&(o.value=r.toString())}};return{...t,...e}}var Na=e=>e.split("").map(t=>{const o=t.charCodeAt(0);return o>0&&o<128?t:o>=128&&o<=255?`/x${o.toString(16)}`.replace("/","\\"):""}).join("").trim(),Va=e=>{var t;return Na(((t=e.dataset)==null?void 0:t.valuetext)??e.textContent??"")},Da=(e,t)=>e.trim().toLowerCase().startsWith(t.toLowerCase());function ja(e,t,o,r=Qt){const i=o?Jt(e,o,r):-1;let n=o?xa(e,i):e;return t.length===1&&(n=n.filter(s=>r(s)!==o)),n.find(s=>Da(Va(s),t))}function Ma(e,t){const{state:o,activeId:r,key:i,timeout:n=350,itemToId:a}=t,s=o.keysSoFar+i,c=s.length>1&&Array.from(s).every(m=>m===s[0])?s[0]:s;let l=e.slice();const u=ja(l,c,r,a);function g(){clearTimeout(o.timer),o.timer=-1}function p(m){o.keysSoFar=m,g(),m!==""&&(o.timer=+setTimeout(()=>{p(""),g()},n))}return p(s),u}var Fa=Object.assign(Ma,{defaultOptions:{keysSoFar:"",timer:-1},isValidEvent:La});function La(e){return e.key.length===1&&!e.ctrlKey&&!e.metaKey}const Ha=(e,t)=>{var d;if(!e||typeof e!="string")return{invalid:!0,value:e};const[o,r]=e.split("/");if(!o||!r||o==="currentBg")return{invalid:!0,value:o};const i=t(`colors.${o}`),n=(d=t.raw(`opacity.${r}`))==null?void 0:d.value;if(!n&&isNaN(Number(r)))return{invalid:!0,value:o};const a=n?Number(n)*100+"%":`${r}%`,s=i??o;return{invalid:!1,color:s,value:`color-mix(in srgb, ${s} ${a}, transparent)`}},j=e=>(t,o)=>{const r=o.utils.colorMix(t);if(r.invalid)return{[e]:t};const i="--mix-"+e;return{[i]:r.value,[e]:`var(${i}, ${r.color})`}};function Dt(e){if(e===null||typeof e!="object")return e;if(Array.isArray(e))return e.map(o=>Dt(o));const t=Object.create(Object.getPrototypeOf(e));for(const o of Object.keys(e))t[o]=Dt(e[o]);return t}function jt(e,t){if(t==null)return e;for(const o of Object.keys(t))if(!(t[o]===void 0||o==="__proto__"))if(!ce(e[o])&&ce(t[o]))Object.assign(e,{[o]:t[o]});else if(e[o]&&ce(t[o]))jt(e[o],t[o]);else if(Array.isArray(t[o])&&Array.isArray(e[o])){let r=0;for(;r<t[o].length;r++)ce(e[o][r])&&ce(t[o][r])?jt(e[o][r],t[o][r]):e[o][r]=t[o][r]}else Object.assign(e,{[o]:t[o]});return e}function et(e,...t){for(const o of t)jt(e,o);return e}const Ga=e=>e,Q=e=>e,T=e=>e,Ya=e=>e,qa=e=>e,fi=e=>e,Ua=e=>e,Xa=e=>e,Ka=e=>e;function hi(){const e=t=>t;return new Proxy(e,{get(){return e}})}const K=hi(),eo=hi(),bi=e=>e,vi=(...e)=>et({},...e.map(Dt)),Za=/[^a-zA-Z0-9_\u0081-\uffff-]/g;function Qa(e){return`${e}`.replace(Za,t=>`\\${t}`)}const Ja=/[A-Z]/g;function es(e){return e.replace(Ja,t=>`-${t.toLowerCase()}`)}function yi(e,t={}){const{fallback:o="",prefix:r=""}=t,i=es(["-",r,Qa(e)].filter(Boolean).join("-"));return{var:i,ref:`var(${i}${o?`, ${o}`:""})`}}const ts=e=>/^var\(--.+\)$/.test(e),J=(e,t)=>t!=null?`${e}(${t})`:t,Be=e=>{if(ts(e)||e==null)return e;const t=typeof e=="string"&&!e.endsWith("deg");return typeof e=="number"||t?`${e}deg`:e},wo=e=>({values:["outside","inside","mixed","none"],transform(t,{token:o}){const r=o("colors.colorPalette.focusRing");return{inside:{"--focus-ring-color":r,[e]:{outlineOffset:"0px",outlineWidth:"var(--focus-ring-width, 1px)",outlineColor:"var(--focus-ring-color)",outlineStyle:"var(--focus-ring-style, solid)",borderColor:"var(--focus-ring-color)"}},outside:{"--focus-ring-color":r,[e]:{outlineWidth:"var(--focus-ring-width, 2px)",outlineOffset:"var(--focus-ring-offset, 2px)",outlineStyle:"var(--focus-ring-style, solid)",outlineColor:"var(--focus-ring-color)"}},mixed:{"--focus-ring-color":r,[e]:{outlineWidth:"var(--focus-ring-width, 3px)",outlineStyle:"var(--focus-ring-style, solid)",outlineColor:"color-mix(in srgb, var(--focus-ring-color), transparent 60%)",borderColor:"var(--focus-ring-color)"}},none:{"--focus-ring-color":r,[e]:{outline:"none"}}}[t]??{}}}),os=j("borderColor"),Ce=e=>({transition:e,transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)",transitionDuration:"150ms"}),rs=Ga({hover:["@media (hover: hover)","&:is(:hover, [data-hover]):not(:disabled, [data-disabled])"],active:"&:is(:active, [data-active]):not(:disabled, [data-disabled], [data-state=open])",focus:"&:is(:focus, [data-focus])",focusWithin:"&:is(:focus-within, [data-focus-within])",focusVisible:"&:is(:focus-visible, [data-focus-visible])",disabled:"&:is(:disabled, [disabled], [data-disabled], [aria-disabled=true])",visited:"&:visited",target:"&:target",readOnly:"&:is([data-readonly], [aria-readonly=true], [readonly])",readWrite:"&:read-write",empty:"&:is(:empty, [data-empty])",checked:"&:is(:checked, [data-checked], [aria-checked=true], [data-state=checked])",enabled:"&:enabled",expanded:"&:is([aria-expanded=true], [data-expanded], [data-state=expanded])",highlighted:"&[data-highlighted]",complete:"&[data-complete]",incomplete:"&[data-incomplete]",dragging:"&[data-dragging]",before:"&::before",after:"&::after",firstLetter:"&::first-letter",firstLine:"&::first-line",marker:"&::marker",selection:"&::selection",file:"&::file-selector-button",backdrop:"&::backdrop",first:"&:first-of-type",last:"&:last-of-type",notFirst:"&:not(:first-of-type)",notLast:"&:not(:last-of-type)",only:"&:only-child",even:"&:nth-of-type(even)",odd:"&:nth-of-type(odd)",peerFocus:".peer:is(:focus, [data-focus]) ~ &",peerHover:".peer:is(:hover, [data-hover]):not(:disabled, [data-disabled]) ~ &",peerActive:".peer:is(:active, [data-active]):not(:disabled, [data-disabled]) ~ &",peerFocusWithin:".peer:focus-within ~ &",peerFocusVisible:".peer:is(:focus-visible, [data-focus-visible]) ~ &",peerDisabled:".peer:is(:disabled, [disabled], [data-disabled]) ~ &",peerChecked:".peer:is(:checked, [data-checked], [aria-checked=true], [data-state=checked]) ~ &",peerInvalid:".peer:is(:invalid, [data-invalid], [aria-invalid=true]) ~ &",peerExpanded:".peer:is([aria-expanded=true], [data-expanded], [data-state=expanded]) ~ &",peerPlaceholderShown:".peer:placeholder-shown ~ &",groupFocus:".group:is(:focus, [data-focus]) &",groupHover:".group:is(:hover, [data-hover]):not(:disabled, [data-disabled]) &",groupActive:".group:is(:active, [data-active]):not(:disabled, [data-disabled]) &",groupFocusWithin:".group:focus-within &",groupFocusVisible:".group:is(:focus-visible, [data-focus-visible]) &",groupDisabled:".group:is(:disabled, [disabled], [data-disabled]) &",groupChecked:".group:is(:checked, [data-checked], [aria-checked=true], [data-state=checked]) &",groupExpanded:".group:is([aria-expanded=true], [data-expanded], [data-state=expanded]) &",groupInvalid:".group:invalid &",indeterminate:"&:is(:indeterminate, [data-indeterminate], [aria-checked=mixed], [data-state=indeterminate])",required:"&:is([data-required], [aria-required=true])",valid:"&:is([data-valid], [data-state=valid])",invalid:"&:is([data-invalid], [aria-invalid=true], [data-state=invalid])",autofill:"&:autofill",inRange:"&:is(:in-range, [data-in-range])",outOfRange:"&:is(:out-of-range, [data-outside-range])",placeholder:"&::placeholder, &[data-placeholder]",placeholderShown:"&:is(:placeholder-shown, [data-placeholder-shown])",pressed:"&:is([aria-pressed=true], [data-pressed])",selected:"&:is([aria-selected=true], [data-selected])",grabbed:"&:is([aria-grabbed=true], [data-grabbed])",underValue:"&[data-state=under-value]",overValue:"&[data-state=over-value]",atValue:"&[data-state=at-value]",default:"&:default",optional:"&:optional",open:"&:is([open], [data-open], [data-state=open])",closed:"&:is([closed], [data-closed], [data-state=closed])",fullscreen:"&is(:fullscreen, [data-fullscreen])",loading:"&:is([data-loading], [aria-busy=true])",hidden:"&:is([hidden], [data-hidden])",current:"&[data-current]",currentPage:"&[aria-current=page]",currentStep:"&[aria-current=step]",today:"&[data-today]",unavailable:"&[data-unavailable]",rangeStart:"&[data-range-start]",rangeEnd:"&[data-range-end]",now:"&[data-now]",topmost:"&[data-topmost]",motionReduce:"@media (prefers-reduced-motion: reduce)",motionSafe:"@media (prefers-reduced-motion: no-preference)",print:"@media print",landscape:"@media (orientation: landscape)",portrait:"@media (orientation: portrait)",dark:".dark &, .dark .chakra-theme:not(.light) &",light:":root &, .light &",osDark:"@media (prefers-color-scheme: dark)",osLight:"@media (prefers-color-scheme: light)",highContrast:"@media (forced-colors: active)",lessContrast:"@media (prefers-contrast: less)",moreContrast:"@media (prefers-contrast: more)",ltr:"[dir=ltr] &",rtl:"[dir=rtl] &",scrollbar:"&::-webkit-scrollbar",scrollbarThumb:"&::-webkit-scrollbar-thumb",scrollbarTrack:"&::-webkit-scrollbar-track",horizontal:"&[data-orientation=horizontal]",vertical:"&[data-orientation=vertical]",icon:"& :where(svg)",starting:"@starting-style"}),Xe=yi("bg-currentcolor"),Io=e=>e===Xe.ref||e==="currentBg",D=e=>({...e("colors"),currentBg:Xe}),is=bi({conditions:rs,utilities:{background:{values:D,shorthand:["bg"],transform(e,t){if(Io(t.raw))return{background:Xe.ref};const o=j("background")(e,t);return{...o,[Xe.var]:o==null?void 0:o.background}}},backgroundColor:{values:D,shorthand:["bgColor"],transform(e,t){if(Io(t.raw))return{backgroundColor:Xe.ref};const o=j("backgroundColor")(e,t);return{...o,[Xe.var]:o==null?void 0:o.backgroundColor}}},backgroundSize:{shorthand:["bgSize"]},backgroundPosition:{shorthand:["bgPos"]},backgroundRepeat:{shorthand:["bgRepeat"]},backgroundAttachment:{shorthand:["bgAttachment"]},backgroundClip:{shorthand:["bgClip"],values:["text"],transform(e){return e==="text"?{color:"transparent",backgroundClip:"text"}:{backgroundClip:e}}},backgroundGradient:{shorthand:["bgGradient"],values(e){return{...e("gradients"),"to-t":"linear-gradient(to top, var(--gradient))","to-tr":"linear-gradient(to top right, var(--gradient))","to-r":"linear-gradient(to right, var(--gradient))","to-br":"linear-gradient(to bottom right, var(--gradient))","to-b":"linear-gradient(to bottom, var(--gradient))","to-bl":"linear-gradient(to bottom left, var(--gradient))","to-l":"linear-gradient(to left, var(--gradient))","to-tl":"linear-gradient(to top left, var(--gradient))"}},transform(e){return{"--gradient-stops":"var(--gradient-from), var(--gradient-to)","--gradient":"var(--gradient-via-stops, var(--gradient-stops))",backgroundImage:e}}},gradientFrom:{values:D,transform:j("--gradient-from")},gradientTo:{values:D,transform:j("--gradient-to")},gradientVia:{values:D,transform(e,t){return{...j("--gradient-via")(e,t),"--gradient-via-stops":"var(--gradient-from), var(--gradient-via), var(--gradient-to)"}}},backgroundImage:{values:"gradients",shorthand:["bgImg","bgImage"]},border:{values:"borders"},borderTop:{values:"borders"},borderLeft:{values:"borders"},borderBlockStart:{values:"borders"},borderRight:{values:"borders"},borderInlineEnd:{values:"borders"},borderBottom:{values:"borders"},borderBlockEnd:{values:"borders"},borderInlineStart:{values:"borders",shorthand:["borderStart"]},borderInline:{values:"borders",shorthand:["borderX"]},borderBlock:{values:"borders",shorthand:["borderY"]},borderColor:{values:D,transform:j("borderColor")},borderTopColor:{values:D,transform:j("borderTopColor")},borderBlockStartColor:{values:D,transform:j("borderBlockStartColor")},borderBottomColor:{values:D,transform:j("borderBottomColor")},borderBlockEndColor:{values:D,transform:j("borderBlockEndColor")},borderLeftColor:{values:D,transform:j("borderLeftColor")},borderInlineStartColor:{values:D,shorthand:["borderStartColor"],transform:j("borderInlineStartColor")},borderRightColor:{values:D,transform:j("borderRightColor")},borderInlineEndColor:{values:D,shorthand:["borderEndColor"],transform:j("borderInlineEndColor")},borderStyle:{values:"borderStyles"},borderTopStyle:{values:"borderStyles"},borderBlockStartStyle:{values:"borderStyles"},borderBottomStyle:{values:"borderStyles"},borderBlockEndStyle:{values:"borderStyles"},borderInlineStartStyle:{values:"borderStyles",shorthand:["borderStartStyle"]},borderInlineEndStyle:{values:"borderStyles",shorthand:["borderEndStyle"]},borderLeftStyle:{values:"borderStyles"},borderRightStyle:{values:"borderStyles"},borderRadius:{values:"radii",shorthand:["rounded"]},borderTopLeftRadius:{values:"radii",shorthand:["roundedTopLeft"]},borderStartStartRadius:{values:"radii",shorthand:["roundedStartStart","borderTopStartRadius"]},borderEndStartRadius:{values:"radii",shorthand:["roundedEndStart","borderBottomStartRadius"]},borderTopRightRadius:{values:"radii",shorthand:["roundedTopRight"]},borderStartEndRadius:{values:"radii",shorthand:["roundedStartEnd","borderTopEndRadius"]},borderEndEndRadius:{values:"radii",shorthand:["roundedEndEnd","borderBottomEndRadius"]},borderBottomLeftRadius:{values:"radii",shorthand:["roundedBottomLeft"]},borderBottomRightRadius:{values:"radii",shorthand:["roundedBottomRight"]},borderInlineStartRadius:{values:"radii",property:"borderRadius",shorthand:["roundedStart","borderStartRadius"],transform:e=>({borderStartStartRadius:e,borderEndStartRadius:e})},borderInlineEndRadius:{values:"radii",property:"borderRadius",shorthand:["roundedEnd","borderEndRadius"],transform:e=>({borderStartEndRadius:e,borderEndEndRadius:e})},borderTopRadius:{values:"radii",property:"borderRadius",shorthand:["roundedTop"],transform:e=>({borderTopLeftRadius:e,borderTopRightRadius:e})},borderBottomRadius:{values:"radii",property:"borderRadius",shorthand:["roundedBottom"],transform:e=>({borderBottomLeftRadius:e,borderBottomRightRadius:e})},borderLeftRadius:{values:"radii",property:"borderRadius",shorthand:["roundedLeft"],transform:e=>({borderTopLeftRadius:e,borderBottomLeftRadius:e})},borderRightRadius:{values:"radii",property:"borderRadius",shorthand:["roundedRight"],transform:e=>({borderTopRightRadius:e,borderBottomRightRadius:e})},borderWidth:{values:"borderWidths"},borderBlockStartWidth:{values:"borderWidths"},borderTopWidth:{values:"borderWidths"},borderBottomWidth:{values:"borderWidths"},borderBlockEndWidth:{values:"borderWidths"},borderRightWidth:{values:"borderWidths"},borderInlineWidth:{values:"borderWidths",shorthand:["borderXWidth"]},borderInlineStartWidth:{values:"borderWidths",shorthand:["borderStartWidth"]},borderInlineEndWidth:{values:"borderWidths",shorthand:["borderEndWidth"]},borderLeftWidth:{values:"borderWidths"},borderBlockWidth:{values:"borderWidths",shorthand:["borderYWidth"]},color:{values:D,transform:j("color")},fill:{values:D,transform:j("fill")},stroke:{values:D,transform:j("stroke")},accentColor:{values:D,transform:j("accentColor")},divideX:{values:{type:"string"},transform(e){return{"& > :not(style, [hidden]) ~ :not(style, [hidden])":{borderInlineStartWidth:e,borderInlineEndWidth:"0px"}}}},divideY:{values:{type:"string"},transform(e){return{"& > :not(style, [hidden]) ~ :not(style, [hidden])":{borderTopWidth:e,borderBottomWidth:"0px"}}}},divideColor:{values:D,transform(e,t){return{"& > :not(style, [hidden]) ~ :not(style, [hidden])":os(e,t)}}},divideStyle:{property:"borderStyle",transform(e){return{"& > :not(style, [hidden]) ~ :not(style, [hidden])":{borderStyle:e}}}},boxShadow:{values:"shadows",shorthand:["shadow"]},boxShadowColor:{values:D,transform:j("--shadow-color"),shorthand:["shadowColor"]},mixBlendMode:{shorthand:["blendMode"]},backgroundBlendMode:{shorthand:["bgBlendMode"]},opacity:{values:"opacity"},filter:{transform(e){return e!=="auto"?{filter:e}:{filter:"var(--blur) var(--brightness) var(--contrast) var(--grayscale) var(--hue-rotate) var(--invert) var(--saturate) var(--sepia) var(--drop-shadow)"}}},blur:{values:"blurs",transform:e=>({"--blur":J("blur",e)})},brightness:{transform:e=>({"--brightness":J("brightness",e)})},contrast:{transform:e=>({"--contrast":J("contrast",e)})},grayscale:{transform:e=>({"--grayscale":J("grayscale",e)})},hueRotate:{transform:e=>({"--hue-rotate":J("hue-rotate",Be(e))})},invert:{transform:e=>({"--invert":J("invert",e)})},saturate:{transform:e=>({"--saturate":J("saturate",e)})},sepia:{transform:e=>({"--sepia":J("sepia",e)})},dropShadow:{transform:e=>({"--drop-shadow":J("drop-shadow",e)})},backdropFilter:{transform(e){return e!=="auto"?{backdropFilter:e}:{backdropFilter:"var(--backdrop-blur) var(--backdrop-brightness) var(--backdrop-contrast) var(--backdrop-grayscale) var(--backdrop-hue-rotate) var(--backdrop-invert) var(--backdrop-opacity) var(--backdrop-saturate) var(--backdrop-sepia)"}}},backdropBlur:{values:"blurs",transform:e=>({"--backdrop-blur":J("blur",e)})},backdropBrightness:{transform:e=>({"--backdrop-brightness":J("brightness",e)})},backdropContrast:{transform:e=>({"--backdrop-contrast":J("contrast",e)})},backdropGrayscale:{transform:e=>({"--backdrop-grayscale":J("grayscale",e)})},backdropHueRotate:{transform:e=>({"--backdrop-hue-rotate":J("hue-rotate",Be(e))})},backdropInvert:{transform:e=>({"--backdrop-invert":J("invert",e)})},backdropOpacity:{transform:e=>({"--backdrop-opacity":J("opacity",e)})},backdropSaturate:{transform:e=>({"--backdrop-saturate":J("saturate",e)})},backdropSepia:{transform:e=>({"--backdrop-sepia":J("sepia",e)})},flexBasis:{values:"sizes"},gap:{values:"spacing"},rowGap:{values:"spacing",shorthand:["gapY"]},columnGap:{values:"spacing",shorthand:["gapX"]},flexDirection:{shorthand:["flexDir"]},gridGap:{values:"spacing"},gridColumnGap:{values:"spacing"},gridRowGap:{values:"spacing"},outlineColor:{values:D,transform:j("outlineColor")},focusRing:wo("&:is(:focus, [data-focus])"),focusVisibleRing:wo("&:is(:focus-visible, [data-focus-visible])"),focusRingColor:{values:D,transform:j("--focus-ring-color")},focusRingOffset:{values:"spacing",transform:e=>({"--focus-ring-offset":e})},focusRingWidth:{values:"borderWidths",property:"outlineWidth",transform:e=>({"--focus-ring-width":e})},focusRingStyle:{values:"borderStyles",property:"outlineStyle",transform:e=>({"--focus-ring-style":e})},aspectRatio:{values:"aspectRatios"},width:{values:"sizes",shorthand:["w"]},inlineSize:{values:"sizes"},height:{values:"sizes",shorthand:["h"]},blockSize:{values:"sizes"},boxSize:{values:"sizes",property:"width",transform:e=>({width:e,height:e})},minWidth:{values:"sizes",shorthand:["minW"]},minInlineSize:{values:"sizes"},minHeight:{values:"sizes",shorthand:["minH"]},minBlockSize:{values:"sizes"},maxWidth:{values:"sizes",shorthand:["maxW"]},maxInlineSize:{values:"sizes"},maxHeight:{values:"sizes",shorthand:["maxH"]},maxBlockSize:{values:"sizes"},hideFrom:{values:"breakpoints",transform:(e,{raw:t,token:o})=>({[o.raw(`breakpoints.${t}`)?`@breakpoint ${t}`:`@media screen and (min-width: ${e})`]:{display:"none"}})},hideBelow:{values:"breakpoints",transform(e,{raw:t,token:o}){return{[o.raw(`breakpoints.${t}`)?`@breakpoint ${t}Down`:`@media screen and (max-width: ${e})`]:{display:"none"}}}},overscrollBehavior:{shorthand:["overscroll"]},overscrollBehaviorX:{shorthand:["overscrollX"]},overscrollBehaviorY:{shorthand:["overscrollY"]},scrollbar:{values:["visible","hidden"],transform(e){switch(e){case"visible":return{msOverflowStyle:"auto",scrollbarWidth:"auto","&::-webkit-scrollbar":{display:"block"}};case"hidden":return{msOverflowStyle:"none",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}};default:return{}}}},scrollbarColor:{values:D,transform:j("scrollbarColor")},scrollbarGutter:{values:"spacing"},scrollbarWidth:{values:"sizes"},scrollMargin:{values:"spacing"},scrollMarginTop:{values:"spacing"},scrollMarginBottom:{values:"spacing"},scrollMarginLeft:{values:"spacing"},scrollMarginRight:{values:"spacing"},scrollMarginX:{values:"spacing",transform:e=>({scrollMarginLeft:e,scrollMarginRight:e})},scrollMarginY:{values:"spacing",transform:e=>({scrollMarginTop:e,scrollMarginBottom:e})},scrollPadding:{values:"spacing"},scrollPaddingTop:{values:"spacing"},scrollPaddingBottom:{values:"spacing"},scrollPaddingLeft:{values:"spacing"},scrollPaddingRight:{values:"spacing"},scrollPaddingInline:{values:"spacing",shorthand:["scrollPaddingX"]},scrollPaddingBlock:{values:"spacing",shorthand:["scrollPaddingY"]},scrollSnapType:{values:{none:"none",x:"x var(--scroll-snap-strictness)",y:"y var(--scroll-snap-strictness)",both:"both var(--scroll-snap-strictness)"}},scrollSnapStrictness:{values:["mandatory","proximity"],transform:e=>({"--scroll-snap-strictness":e})},scrollSnapMargin:{values:"spacing"},scrollSnapMarginTop:{values:"spacing"},scrollSnapMarginBottom:{values:"spacing"},scrollSnapMarginLeft:{values:"spacing"},scrollSnapMarginRight:{values:"spacing"},listStylePosition:{shorthand:["listStylePos"]},listStyleImage:{shorthand:["listStyleImg"]},position:{shorthand:["pos"]},zIndex:{values:"zIndex"},inset:{values:"spacing"},insetInline:{values:"spacing",shorthand:["insetX"]},insetBlock:{values:"spacing",shorthand:["insetY"]},top:{values:"spacing"},insetBlockStart:{values:"spacing"},bottom:{values:"spacing"},insetBlockEnd:{values:"spacing"},left:{values:"spacing"},right:{values:"spacing"},insetInlineStart:{values:"spacing",shorthand:["insetStart"]},insetInlineEnd:{values:"spacing",shorthand:["insetEnd"]},ring:{transform(e){return{"--ring-offset-shadow":"var(--ring-inset) 0 0 0 var(--ring-offset-width) var(--ring-offset-color)","--ring-shadow":"var(--ring-inset) 0 0 0 calc(var(--ring-width) + var(--ring-offset-width)) var(--ring-color)","--ring-width":e,boxShadow:"var(--ring-offset-shadow), var(--ring-shadow), var(--shadow, 0 0 #0000)"}}},ringColor:{values:D,transform:j("--ring-color")},ringOffset:{transform:e=>({"--ring-offset-width":e})},ringOffsetColor:{values:D,transform:j("--ring-offset-color")},ringInset:{transform:e=>({"--ring-inset":e})},margin:{values:"spacing",shorthand:["m"]},marginTop:{values:"spacing",shorthand:["mt"]},marginBlockStart:{values:"spacing",shorthand:["mt"]},marginRight:{values:"spacing",shorthand:["mr"]},marginBottom:{values:"spacing",shorthand:["mb"]},marginBlockEnd:{values:"spacing"},marginLeft:{values:"spacing",shorthand:["ml"]},marginInlineStart:{values:"spacing",shorthand:["ms","marginStart"]},marginInlineEnd:{values:"spacing",shorthand:["me","marginEnd"]},marginInline:{values:"spacing",shorthand:["mx","marginX"]},marginBlock:{values:"spacing",shorthand:["my","marginY"]},padding:{values:"spacing",shorthand:["p"]},paddingTop:{values:"spacing",shorthand:["pt"]},paddingRight:{values:"spacing",shorthand:["pr"]},paddingBottom:{values:"spacing",shorthand:["pb"]},paddingBlockStart:{values:"spacing"},paddingBlockEnd:{values:"spacing"},paddingLeft:{values:"spacing",shorthand:["pl"]},paddingInlineStart:{values:"spacing",shorthand:["ps","paddingStart"]},paddingInlineEnd:{values:"spacing",shorthand:["pe","paddingEnd"]},paddingInline:{values:"spacing",shorthand:["px","paddingX"]},paddingBlock:{values:"spacing",shorthand:["py","paddingY"]},textDecoration:{shorthand:["textDecor"]},textDecorationColor:{values:D,transform:j("textDecorationColor")},textShadow:{values:"shadows"},transform:{transform:e=>{let t=e;return e==="auto"&&(t="translateX(var(--translate-x, 0)) translateY(var(--translate-y, 0)) rotate(var(--rotate, 0)) scaleX(var(--scale-x, 1)) scaleY(var(--scale-y, 1)) skewX(var(--skew-x, 0)) skewY(var(--skew-y, 0))"),e==="auto-gpu"&&(t="translate3d(var(--translate-x, 0), var(--translate-y, 0), 0) rotate(var(--rotate, 0)) scaleX(var(--scale-x, 1)) scaleY(var(--scale-y, 1)) skewX(var(--skew-x, 0)) skewY(var(--skew-y, 0))"),{transform:t}}},skewX:{transform:e=>({"--skew-x":Be(e)})},skewY:{transform:e=>({"--skew-y":Be(e)})},scaleX:{transform:e=>({"--scale-x":e})},scaleY:{transform:e=>({"--scale-y":e})},scale:{transform(e){return e!=="auto"?{scale:e}:{scale:"var(--scale-x, 1) var(--scale-y, 1)"}}},spaceXReverse:{values:{type:"boolean"},transform(e){return{"& > :not(style, [hidden]) ~ :not(style, [hidden])":{"--space-x-reverse":e?"1":void 0}}}},spaceX:{property:"marginInlineStart",values:"spacing",transform:e=>({"& > :not(style, [hidden]) ~ :not(style, [hidden])":{"--space-x-reverse":"0",marginInlineStart:`calc(${e} * calc(1 - var(--space-x-reverse)))`,marginInlineEnd:`calc(${e} * var(--space-x-reverse))`}})},spaceYReverse:{values:{type:"boolean"},transform(e){return{"& > :not(style, [hidden]) ~ :not(style, [hidden])":{"--space-y-reverse":e?"1":void 0}}}},spaceY:{property:"marginTop",values:"spacing",transform:e=>({"& > :not(style, [hidden]) ~ :not(style, [hidden])":{"--space-y-reverse":"0",marginTop:`calc(${e} * calc(1 - var(--space-y-reverse)))`,marginBottom:`calc(${e} * var(--space-y-reverse))`}})},rotate:{transform(e){return e!=="auto"?{rotate:Be(e)}:{rotate:"var(--rotate-x, 0) var(--rotate-y, 0) var(--rotate-z, 0)"}}},rotateX:{transform(e){return{"--rotate-x":Be(e)}}},rotateY:{transform(e){return{"--rotate-y":Be(e)}}},translate:{transform(e){return e!=="auto"?{translate:e}:{translate:"var(--translate-x) var(--translate-y)"}}},translateX:{values:"spacing",transform:e=>({"--translate-x":e})},translateY:{values:"spacing",transform:e=>({"--translate-y":e})},transition:{values:["all","common","colors","opacity","position","backgrounds","size","shadow","transform"],transform(e){switch(e){case"all":return Ce("all");case"position":return Ce("left, right, top, bottom, inset-inline, inset-block");case"colors":return Ce("color, background-color, border-color, text-decoration-color, fill, stroke");case"opacity":return Ce("opacity");case"shadow":return Ce("box-shadow");case"transform":return Ce("transform");case"size":return Ce("width, height");case"backgrounds":return Ce("background, background-color, background-image, background-position");case"common":return Ce("color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter");default:return{transition:e}}}},transitionDuration:{values:"durations"},transitionProperty:{values:{common:"background-color, border-color, color, fill, stroke, opacity, box-shadow, translate, transform",colors:"background-color, border-color, color, fill, stroke",size:"width, height",position:"left, right, top, bottom, inset-inline, inset-block",background:"background, background-color, background-image, background-position"}},transitionTimingFunction:{values:"easings"},animation:{values:"animations"},animationDuration:{values:"durations"},animationDelay:{values:"durations"},animationTimingFunction:{values:"easings"},fontFamily:{values:"fonts"},fontSize:{values:"fontSizes"},fontWeight:{values:"fontWeights"},lineHeight:{values:"lineHeights"},letterSpacing:{values:"letterSpacings"},textIndent:{values:"spacing"},truncate:{values:{type:"boolean"},transform(e){return e===!0?{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}:{}}},lineClamp:{transform(e){return e==="none"?{WebkitLineClamp:"unset"}:{overflow:"hidden",display:"-webkit-box",WebkitLineClamp:e,WebkitBoxOrient:"vertical",textWrap:"wrap"}}},srOnly:{values:{type:"boolean"},transform(e){return ns[e]||{}}},debug:{values:{type:"boolean"},transform(e){return e?{outline:"1px solid blue !important","& > *":{outline:"1px solid red !important"}}:{}}},caretColor:{values:D,transform:j("caretColor")},cursor:{values:"cursor"}}}),ns={true:{position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},false:{position:"static",width:"auto",height:"auto",padding:"0",margin:"0",overflow:"visible",clip:"auto",whiteSpace:"normal"}};var as="",ss=as.split(","),ls="WebkitAppearance,WebkitBorderBefore,WebkitBorderBeforeColor,WebkitBorderBeforeStyle,WebkitBorderBeforeWidth,WebkitBoxReflect,WebkitLineClamp,WebkitMask,WebkitMaskAttachment,WebkitMaskClip,WebkitMaskComposite,WebkitMaskImage,WebkitMaskOrigin,WebkitMaskPosition,WebkitMaskPositionX,WebkitMaskPositionY,WebkitMaskRepeat,WebkitMaskRepeatX,WebkitMaskRepeatY,WebkitMaskSize,WebkitOverflowScrolling,WebkitTapHighlightColor,WebkitTextFillColor,WebkitTextStroke,WebkitTextStrokeColor,WebkitTextStrokeWidth,WebkitTouchCallout,WebkitUserModify,accentColor,alignContent,alignItems,alignSelf,alignTracks,all,animation,animationComposition,animationDelay,animationDirection,animationDuration,animationFillMode,animationIterationCount,animationName,animationPlayState,animationRange,animationRangeEnd,animationRangeStart,animationTimingFunction,animationTimeline,appearance,aspectRatio,azimuth,backdropFilter,backfaceVisibility,background,backgroundAttachment,backgroundBlendMode,backgroundClip,backgroundColor,backgroundImage,backgroundOrigin,backgroundPosition,backgroundPositionX,backgroundPositionY,backgroundRepeat,backgroundSize,blockSize,border,borderBlock,borderBlockColor,borderBlockStyle,borderBlockWidth,borderBlockEnd,borderBlockEndColor,borderBlockEndStyle,borderBlockEndWidth,borderBlockStart,borderBlockStartColor,borderBlockStartStyle,borderBlockStartWidth,borderBottom,borderBottomColor,borderBottomLeftRadius,borderBottomRightRadius,borderBottomStyle,borderBottomWidth,borderCollapse,borderColor,borderEndEndRadius,borderEndStartRadius,borderImage,borderImageOutset,borderImageRepeat,borderImageSlice,borderImageSource,borderImageWidth,borderInline,borderInlineEnd,borderInlineColor,borderInlineStyle,borderInlineWidth,borderInlineEndColor,borderInlineEndStyle,borderInlineEndWidth,borderInlineStart,borderInlineStartColor,borderInlineStartStyle,borderInlineStartWidth,borderLeft,borderLeftColor,borderLeftStyle,borderLeftWidth,borderRadius,borderRight,borderRightColor,borderRightStyle,borderRightWidth,borderSpacing,borderStartEndRadius,borderStartStartRadius,borderStyle,borderTop,borderTopColor,borderTopLeftRadius,borderTopRightRadius,borderTopStyle,borderTopWidth,borderWidth,bottom,boxAlign,boxDecorationBreak,boxDirection,boxFlex,boxFlexGroup,boxLines,boxOrdinalGroup,boxOrient,boxPack,boxShadow,boxSizing,breakAfter,breakBefore,breakInside,captionSide,caret,caretColor,caretShape,clear,clip,clipPath,color,colorScheme,columnCount,columnFill,columnGap,columnRule,columnRuleColor,columnRuleStyle,columnRuleWidth,columnSpan,columnWidth,columns,contain,containIntrinsicSize,containIntrinsicBlockSize,containIntrinsicHeight,containIntrinsicInlineSize,containIntrinsicWidth,container,containerName,containerType,content,contentVisibility,counterIncrement,counterReset,counterSet,cursor,direction,display,emptyCells,filter,flex,flexBasis,flexDirection,flexFlow,flexGrow,flexShrink,flexWrap,float,font,fontFamily,fontFeatureSettings,fontKerning,fontLanguageOverride,fontOpticalSizing,fontPalette,fontVariationSettings,fontSize,fontSizeAdjust,fontSmooth,fontStretch,fontStyle,fontSynthesis,fontSynthesisPosition,fontSynthesisSmallCaps,fontSynthesisStyle,fontSynthesisWeight,fontVariant,fontVariantAlternates,fontVariantCaps,fontVariantEastAsian,fontVariantEmoji,fontVariantLigatures,fontVariantNumeric,fontVariantPosition,fontWeight,forcedColorAdjust,gap,grid,gridArea,gridAutoColumns,gridAutoFlow,gridAutoRows,gridColumn,gridColumnEnd,gridColumnGap,gridColumnStart,gridGap,gridRow,gridRowEnd,gridRowGap,gridRowStart,gridTemplate,gridTemplateAreas,gridTemplateColumns,gridTemplateRows,hangingPunctuation,height,hyphenateCharacter,hyphenateLimitChars,hyphens,imageOrientation,imageRendering,imageResolution,imeMode,initialLetter,initialLetterAlign,inlineSize,inputSecurity,inset,insetBlock,insetBlockEnd,insetBlockStart,insetInline,insetInlineEnd,insetInlineStart,isolation,justifyContent,justifyItems,justifySelf,justifyTracks,left,letterSpacing,lineBreak,lineClamp,lineHeight,lineHeightStep,listStyle,listStyleImage,listStylePosition,listStyleType,margin,marginBlock,marginBlockEnd,marginBlockStart,marginBottom,marginInline,marginInlineEnd,marginInlineStart,marginLeft,marginRight,marginTop,marginTrim,mask,maskBorder,maskBorderMode,maskBorderOutset,maskBorderRepeat,maskBorderSlice,maskBorderSource,maskBorderWidth,maskClip,maskComposite,maskImage,maskMode,maskOrigin,maskPosition,maskRepeat,maskSize,maskType,masonryAutoFlow,mathDepth,mathShift,mathStyle,maxBlockSize,maxHeight,maxInlineSize,maxLines,maxWidth,minBlockSize,minHeight,minInlineSize,minWidth,mixBlendMode,objectFit,objectPosition,offset,offsetAnchor,offsetDistance,offsetPath,offsetPosition,offsetRotate,opacity,order,orphans,outline,outlineColor,outlineOffset,outlineStyle,outlineWidth,overflow,overflowAnchor,overflowBlock,overflowClipBox,overflowClipMargin,overflowInline,overflowWrap,overflowX,overflowY,overlay,overscrollBehavior,overscrollBehaviorBlock,overscrollBehaviorInline,overscrollBehaviorX,overscrollBehaviorY,padding,paddingBlock,paddingBlockEnd,paddingBlockStart,paddingBottom,paddingInline,paddingInlineEnd,paddingInlineStart,paddingLeft,paddingRight,paddingTop,page,pageBreakAfter,pageBreakBefore,pageBreakInside,paintOrder,perspective,perspectiveOrigin,placeContent,placeItems,placeSelf,pointerEvents,position,printColorAdjust,quotes,resize,right,rotate,rowGap,rubyAlign,rubyMerge,rubyPosition,scale,scrollbarColor,scrollbarGutter,scrollbarWidth,scrollBehavior,scrollMargin,scrollMarginBlock,scrollMarginBlockStart,scrollMarginBlockEnd,scrollMarginBottom,scrollMarginInline,scrollMarginInlineStart,scrollMarginInlineEnd,scrollMarginLeft,scrollMarginRight,scrollMarginTop,scrollPadding,scrollPaddingBlock,scrollPaddingBlockStart,scrollPaddingBlockEnd,scrollPaddingBottom,scrollPaddingInline,scrollPaddingInlineStart,scrollPaddingInlineEnd,scrollPaddingLeft,scrollPaddingRight,scrollPaddingTop,scrollSnapAlign,scrollSnapCoordinate,scrollSnapDestination,scrollSnapPointsX,scrollSnapPointsY,scrollSnapStop,scrollSnapType,scrollSnapTypeX,scrollSnapTypeY,scrollTimeline,scrollTimelineAxis,scrollTimelineName,shapeImageThreshold,shapeMargin,shapeOutside,tabSize,tableLayout,textAlign,textAlignLast,textCombineUpright,textDecoration,textDecorationColor,textDecorationLine,textDecorationSkip,textDecorationSkipInk,textDecorationStyle,textDecorationThickness,textEmphasis,textEmphasisColor,textEmphasisPosition,textEmphasisStyle,textIndent,textJustify,textOrientation,textOverflow,textRendering,textShadow,textSizeAdjust,textTransform,textUnderlineOffset,textUnderlinePosition,textWrap,timelineScope,top,touchAction,transform,transformBox,transformOrigin,transformStyle,transition,transitionBehavior,transitionDelay,transitionDuration,transitionProperty,transitionTimingFunction,translate,unicodeBidi,userSelect,verticalAlign,viewTimeline,viewTimelineAxis,viewTimelineInset,viewTimelineName,viewTransitionName,visibility,whiteSpace,whiteSpaceCollapse,widows,width,willChange,wordBreak,wordSpacing,wordWrap,writingMode,zIndex,zoom,alignmentBaseline,baselineShift,clipRule,colorInterpolation,colorRendering,dominantBaseline,fill,fillOpacity,fillRule,floodColor,floodOpacity,glyphOrientationVertical,lightingColor,marker,markerEnd,markerMid,markerStart,shapeRendering,stopColor,stopOpacity,stroke,strokeDasharray,strokeDashoffset,strokeLinecap,strokeLinejoin,strokeMiterlimit,strokeOpacity,strokeWidth,textAnchor,vectorEffect",ds=ls.split(",").concat(ss),cs=new Map(ds.map(e=>[e,!0]));function us(e){const t=Object.create(null);return o=>(t[o]===void 0&&(t[o]=e(o)),t[o])}var gs=/&|@/,ps=us(e=>cs.has(e)||e.startsWith("--")||gs.test(e));const ms=e=>e!=null;function Pe(e,t,o={}){const{stop:r,getKey:i}=o;function n(a,s=[]){if(ce(a)||Array.isArray(a)){const d={};for(const[c,l]of Object.entries(a)){const u=(i==null?void 0:i(c,l))??c,g=[...s,u];if(r!=null&&r(a,g))return t(a,s);const p=n(l,g);ms(p)&&(d[u]=p)}return d}return t(a,s)}return n(e)}function fs(e,t){return Array.isArray(e)?e.map(o=>t(o)):ce(e)?Pe(e,o=>t(o)):e!=null?t(e):e}function hs(e,t){const o={};return Pe(e,(r,i)=>{r&&(o[i.join(".")]=r.value)},{stop:t}),o}const me=e=>{const t=Object.create(null);function o(...r){const i=JSON.stringify(r);return t[i]===void 0&&(t[i]=e(...r)),t[i]}return o},xi=16,xt="px",to="em",it="rem";function ki(e=""){const t=new RegExp(String.raw`-?\d+(?:\.\d+|\d*)`),o=new RegExp(`${xt}|${to}|${it}`),r=e.match(new RegExp(`${t.source}(${o.source})`));return r==null?void 0:r[1]}function Si(e=""){if(typeof e=="number")return`${e}px`;const t=ki(e);if(!t||t===xt)return e;if(t===to||t===it)return`${parseFloat(e)*xi}${xt}`}function wi(e=""){const t=ki(e);if(!t||t===it)return e;if(t===to)return`${parseFloat(e)}${it}`;if(t===xt)return`${parseFloat(e)/xi}${it}`}const bs=e=>e.charAt(0).toUpperCase()+e.slice(1);function vs(e){const t=ys(e),o=Object.fromEntries(t);function r(g){return o[g]}function i(g){return Fe(r(g))}function n(){const g=Object.keys(o),p=xs(g),m=g.flatMap(f=>{const S=r(f),k=[`${f}Down`,Fe({max:ht(S.min)})],x=[f,Fe({min:S.min})],I=[`${f}Only`,i(f)];return[x,I,k]}).filter(([,f])=>f!=="").concat(p.map(([f,S])=>{const k=r(f),x=r(S);return[`${f}To${bs(S)}`,Fe({min:k.min,max:ht(x.min)})]}));return Object.fromEntries(m)}function a(){const g=n();return Object.fromEntries(Object.entries(g))}const s=a(),d=g=>s[g];function c(){return["base",...Object.keys(o)]}function l(g){return Fe({min:r(g).min})}function u(g){return Fe({max:ht(r(g).min)})}return{values:Object.values(o),only:i,keys:c,conditions:s,getCondition:d,up:l,down:u}}function ht(e){const t=parseFloat(Si(e)??"")-.04;return wi(`${t}px`)}function ys(e){return Object.entries(e).sort(([,o],[,r])=>parseInt(o,10)<parseInt(r,10)?-1:1).map(([o,r],i,n)=>{var s;let a=null;return i<=n.length-1&&(a=(s=n[i+1])==null?void 0:s[1]),a!=null&&(a=ht(a)),[o,{name:o,min:wi(r),max:a}]})}function xs(e){const t=[];return e.forEach((o,r)=>{let i=r;i++;let n=e[i];for(;n;)t.push([o,n]),i++,n=e[i]}),t}function Fe({min:e,max:t}){return e==null&&t==null?"":["@media screen",e&&`(min-width: ${e})`,t&&`(max-width: ${t})`].filter(Boolean).join(" and ")}const ks=(e,t)=>Object.fromEntries(Object.entries(e).map(([o,r])=>t(o,r))),Ss=e=>{const{breakpoints:t,conditions:o={}}=e,r=ks(o,(l,u)=>[`_${l}`,u]),i=Object.assign({},r,t.conditions);function n(){return Object.keys(i)}function a(l){return n().includes(l)||/^@|&|&$/.test(l)||l.startsWith("_")}function s(l){return l.filter(u=>u!=="base").sort((u,g)=>{const p=a(u),m=a(g);return p&&!m?1:!p&&m?-1:0})}function d(l){return l.startsWith("@breakpoint")?t.getCondition(l.replace("@breakpoint ","")):l}function c(l){return Reflect.get(i,l)||l}return{keys:n,sort:s,has:a,resolve:c,breakpoints:t.keys(),expandAtRule:d}},Ii=e=>({minMax:new RegExp(`(!?\\(\\s*min(-device-)?-${e})(.|
)+\\(\\s*max(-device)?-${e}`,"i"),min:new RegExp(`\\(\\s*min(-device)?-${e}`,"i"),maxMin:new RegExp(`(!?\\(\\s*max(-device)?-${e})(.|
)+\\(\\s*min(-device)?-${e}`,"i"),max:new RegExp(`\\(\\s*max(-device)?-${e}`,"i")}),ws=Ii("width"),Is=Ii("height"),zi=e=>({isMin:To(e.minMax,e.maxMin,e.min),isMax:To(e.maxMin,e.minMax,e.max)}),{isMin:Mt,isMax:zo}=zi(ws),{isMin:Ft,isMax:Co}=zi(Is),Ro=/print/i,Po=/^print$/i,zs=/(-?\d*\.?\d+)(ch|em|ex|px|rem)/,Cs=/(\d)/,ot=Number.MAX_VALUE,Rs={ch:8.8984375,em:16,rem:16,ex:8.296875,px:1};function _o(e){const t=zs.exec(e)||(Mt(e)||Ft(e)?Cs.exec(e):null);if(!t)return ot;if(t[0]==="0")return 0;const o=parseFloat(t[1]),r=t[2];return o*(Rs[r]||1)}function To(e,t,o){return r=>e.test(r)||!t.test(r)&&o.test(r)}function Ps(e,t){const o=Ro.test(e),r=Po.test(e),i=Ro.test(t),n=Po.test(t);return o&&i?!r&&n?1:r&&!n?-1:e.localeCompare(t):o?1:i?-1:null}const _s=me((e,t)=>{const o=Ps(e,t);if(o!==null)return o;const r=Mt(e)||Ft(e),i=zo(e)||Co(e),n=Mt(t)||Ft(t),a=zo(t)||Co(t);if(r&&a)return-1;if(i&&n)return 1;const s=_o(e),d=_o(t);return s===ot&&d===ot?e.localeCompare(t):s===ot?1:d===ot?-1:s!==d?s>d?i?-1:1:i?1:-1:e.localeCompare(t)});function Eo(e){return e.sort(([t],[o])=>_s(t,o))}function Ci(e){const t=[],o=[],r={};for(const[a,s]of Object.entries(e))a.startsWith("@media")?t.push([a,s]):a.startsWith("@container")?o.push([a,s]):ce(s)?r[a]=Ci(s):r[a]=s;const i=Eo(t),n=Eo(o);return{...r,...Object.fromEntries(i),...Object.fromEntries(n)}}const Ri=/\s*!(important)?/i,Ts=e=>Se(e)?Ri.test(e):!1,Es=e=>Se(e)?e.replace(Ri,"").trim():e;function Pi(e){const{transform:t,conditions:o,normalize:r}=e,i=Bs(e);return me((...n)=>{const a=i(...n),s=r(a),d=Object.create(null);return Pe(s,(c,l)=>{const u=Ts(c);if(c==null)return;const[g,...p]=o.sort(l).map(o.resolve);u&&(c=Es(c));let m=t(g,c)??Object.create(null);m=Pe(m,f=>Se(f)&&u?`${f} !important`:f,{getKey:f=>o.expandAtRule(f)}),Ws(d,p.flat(),m)}),Ci(d)})}function Ws(e,t,o){let r=e;for(const i of t)i&&(r[i]||(r[i]=Object.create(null)),r=r[i]);et(r,o)}function $s(...e){return e.filter(t=>ce(t)&&Object.keys(at(t)).length>0)}function Bs(e){function t(o){const r=$s(...o);return r.length===1?r:r.map(i=>e.normalize(i))}return me((...o)=>et({},...t(o)))}function _i(e,t=[]){const o=Object.assign({},e);for(const r of t)r in o&&delete o[r];return o}const As=(...e)=>{const t=e.filter(Boolean);return Array.from(new Set(t))},Ti=e=>({base:{},variants:{},defaultVariants:{},compoundVariants:[],...e});function Os(e){const{css:t,conditions:o,normalize:r,layers:i}=e;function n(s={}){const{base:d,variants:c,defaultVariants:l,compoundVariants:u}=Ti(s),g=Pi({conditions:o,normalize:r,transform(x,I){var C;return(C=c[x])==null?void 0:C[I]}}),p=(x={})=>{const I=r({...l,...at(x)});let C={...d};et(C,g(I));const _=a(u,I);return i.wrap("recipes",t(C,_))},m=Object.keys(c),f=x=>{const I=_i(x,["recipe"]),[C,_]=Ke(I,m);return m.includes("colorPalette")||(C.colorPalette=x.colorPalette||l.colorPalette),m.includes("orientation")&&(_.orientation=x.orientation),[C,_]},S=Object.fromEntries(Object.entries(c).map(([x,I])=>[x,Object.keys(I)]));return Object.assign(x=>t(p(x)),{className:s.className,__cva__:!0,variantMap:S,variantKeys:m,raw:p,config:s,splitVariantProps:f,merge(x){return n(Ns(e)(this,x))}})}function a(s,d){let c={};return s.forEach(l=>{Object.entries(l).every(([g,p])=>g==="css"?!0:(Array.isArray(p)?p:[p]).some(f=>d[g]===f))&&(c=t(c,l.css))}),c}return n}function Ns(e){const{css:t}=e;return function(r,i){const n=Ti(i.config),a=As(r.variantKeys,Object.keys(i.variants)),s=t(r.base,n.base),d=Object.fromEntries(a.map(g=>[g,t(r.config.variants[g],n.variants[g])])),c=et(r.config.defaultVariants,n.defaultVariants),l=[...r.compoundVariants,...n.compoundVariants];return{className:xe(r.className,i.className),base:s,variants:d,defaultVariants:c,compoundVariants:l}}}const Vs={reset:"reset",base:"base",tokens:"tokens",recipes:"recipes"},Wo={reset:0,base:1,tokens:2,recipes:3};function Ds(e){const t=e.layers??Vs,r=Object.values(t).sort((i,n)=>Wo[i]-Wo[n]);return{names:r,atRule:`@layer ${r.join(", ")};`,wrap(i,n){return e.disableLayers?n:{[`@layer ${t[i]}`]:n}}}}function js(e){const{utility:t,normalize:o}=e,{hasShorthand:r,resolveShorthand:i}=t;return function(n){return Pe(n,o,{stop:a=>Array.isArray(a),getKey:r?i:void 0})}}function Ms(e){const{preflight:t}=e;if(!t)return{};const{scope:o="",level:r="parent"}=ce(t)?t:{};let i="";o&&r==="parent"?i=`${o} `:o&&r==="element"&&(i=`&${o}`);const n={"*":{margin:"0px",padding:"0px",font:"inherit",wordWrap:"break-word",WebkitTapHighlightColor:"transparent"},"*, *::before, *::after, *::backdrop":{boxSizing:"border-box",borderWidth:"0px",borderStyle:"solid",borderColor:"var(--global-color-border, currentColor)"},hr:{height:"0px",color:"inherit",borderTopWidth:"1px"},body:{minHeight:"100dvh",position:"relative"},img:{borderStyle:"none"},"img, svg, video, canvas, audio, iframe, embed, object":{display:"block",verticalAlign:"middle"},iframe:{border:"none"},"img, video":{maxWidth:"100%",height:"auto"},"p, h1, h2, h3, h4, h5, h6":{overflowWrap:"break-word"},"ol, ul":{listStyle:"none"},"code, kbd, pre, samp":{fontSize:"1em"},"button, [type='button'], [type='reset'], [type='submit']":{WebkitAppearance:"button",backgroundColor:"transparent",backgroundImage:"none"},"button, input, optgroup, select, textarea":{color:"inherit"},"button, select":{textTransform:"none"},table:{textIndent:"0px",borderColor:"inherit",borderCollapse:"collapse"},"*::placeholder":{opacity:"unset",color:"#9ca3af",userSelect:"none"},textarea:{resize:"vertical"},summary:{display:"list-item"},small:{fontSize:"80%"},"sub, sup":{fontSize:"75%",lineHeight:0,position:"relative",verticalAlign:"baseline"},sub:{bottom:"-0.25em"},sup:{top:"-0.5em"},dialog:{padding:"0px"},a:{color:"inherit",textDecoration:"inherit"},"abbr:where([title])":{textDecoration:"underline dotted"},"b, strong":{fontWeight:"bolder"},"code, kbd, samp, pre":{fontSize:"1em","--font-mono-fallback":"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New'",fontFamily:"var(--global-font-mono, var(--font-mono-fallback))"},'input[type="text"], input[type="email"], input[type="search"], input[type="password"]':{WebkitAppearance:"none",MozAppearance:"none"},"input[type='search']":{WebkitAppearance:"textfield",outlineOffset:"-2px"},"::-webkit-search-decoration, ::-webkit-search-cancel-button":{WebkitAppearance:"none"},"::-webkit-file-upload-button":{WebkitAppearance:"button",font:"inherit"},'input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button':{height:"auto"},"input[type='number']":{MozAppearance:"textfield"},":-moz-ui-invalid":{boxShadow:"none"},":-moz-focusring":{outline:"auto"},"[hidden]:where(:not([hidden='until-found']))":{display:"none !important"}},a={[o||"html"]:{lineHeight:1.5,"--font-fallback":"ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",WebkitTextSizeAdjust:"100%",WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",textRendering:"optimizeLegibility",touchAction:"manipulation",MozTabSize:"4",tabSize:"4",fontFamily:"var(--global-font-body, var(--font-fallback))"}};if(r==="element"){const s=Object.entries(n).reduce((d,[c,l])=>(d[c]={[i]:l},d),{});Object.assign(a,s)}else i?a[i]=n:Object.assign(a,n);return a}function Fs(e){const{conditions:t,isValidProperty:o}=e;return function(i){return Pe(i,n=>n,{getKey:(n,a)=>ce(a)&&!t.has(n)&&!o(n)?Ls(n).map(s=>"&"+s).join(", "):n})}}function Ls(e){const t=[];let o=0,r="",i=!1;for(let n=0;n<e.length;n++){const a=e[n];if(a==="\\"&&!i){i=!0,r+=a;continue}if(i){i=!1,r+=a;continue}a==="("?o++:a===")"&&o--,a===","&&o===0?(t.push(r.trim()),r=""):r+=a}return r&&t.push(r.trim()),t}const Hs=(e={})=>{const t=i=>{var n;return{base:((n=e.base)==null?void 0:n[i])??{},variants:{},defaultVariants:e.defaultVariants??{},compoundVariants:e.compoundVariants?Gs(e.compoundVariants,i):[]}},r=(e.slots??[]).map(i=>[i,t(i)]);for(const[i,n]of Object.entries(e.variants??{}))for(const[a,s]of Object.entries(n))r.forEach(([d,c])=>{var l;(l=c.variants)[i]??(l[i]={}),c.variants[i][a]=s[d]??{}});return Object.fromEntries(r)},Gs=(e,t)=>e.filter(o=>o.css[t]).map(o=>({...o,css:o.css[t]}));function Ys(e){const{cva:t}=e;return function(r={}){const i=Object.entries(Hs(r)).map(([u,g])=>[u,t(g)]);function n(u){const g=i.map(([p,m])=>[p,m(u)]);return Object.fromEntries(g)}const a=r.variants??{},s=Object.keys(a);function d(u){var f;const g=_i(u,["recipe"]),[p,m]=Ke(g,s);return s.includes("colorPalette")||(p.colorPalette=u.colorPalette||((f=r.defaultVariants)==null?void 0:f.colorPalette)),s.includes("orientation")&&(m.orientation=u.orientation),[p,m]}const c=Object.fromEntries(Object.entries(a).map(([u,g])=>[u,Object.keys(g)]));let l={};return r.className&&(l=Object.fromEntries(r.slots.map(u=>[u,`${r.className}__${u}`]))),Object.assign(n,{variantMap:c,variantKeys:s,splitVariantProps:d,classNameMap:l})}}const qs=()=>e=>Array.from(new Set(e)),Us=/([\0-\x1f\x7f]|^-?\d)|^-$|^-|[^\x80-\uFFFF\w-]/g,Xs=function(e,t){return t?e==="\0"?"�":e==="-"&&e.length===1?"\\-":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16):"\\"+e},Ei=e=>(e+"").replace(Us,Xs),Wi=(e,t)=>{let o="",r=0,i="char",n="",a="";const s=[];for(;r<e.length;){const d=e[r];if(d==="{"){const l=e.indexOf("}",r);if(l===-1)break;const u=e.slice(r+1,l),g=t(u);o+=g??u,r=l+1;continue}if(i==="token"&&d===","){e[r]===""&&r++,i="fallback",s.push(i);const l=t(n);l!=null&&l.endsWith(")")&&(o+=l.slice(0,-1)),n="",a="";continue}if(i==="fallback"&&a+d===", var("){const g=Ks(e.slice(r+1))+r+1,p=e.slice(r+1,g);if(g===-1)break;o+=", var("+p+")",r=g+1,i=s.pop()??i,a="";continue}if(i==="token"||i==="fallback"){if(r++,d===")"){i=s.pop()??i??"char",a+=d;const l=n&&(t(n)??Ei(n));if(a){if(a=a.slice(1).trim(),!a.startsWith("token(")&&a.endsWith(")")&&(a=a.slice(0,-1)),a.includes("token(")){const g=Wi(a,t);g&&(a=g.slice(0,-1))}else if(a){const g=t(a);g&&(a=g)}}const u=o.at(-1);a?u!=null&&u.trim()?o+=l.slice(0,-1)+(", "+a+")"):o+=a:o+=l||")",n="",a="",i="char";continue}i==="token"&&(n+=d),i==="fallback"&&(a+=d);continue}const c=e.indexOf("token(",r);if(c!==-1){const l=c+6;o+=e.slice(r,c),r=l,i="token",s.push(i);continue}o+=d,r++}return o},Ks=e=>{let t=0;const o=["("];for(;t<e.length;){const r=e[t];if(r==="(")o.push(r);else if(r===")"&&(o.pop(),o.length===0))return t;t++}return t};function $i(e){const t={};return e.forEach((o,r)=>{o instanceof Map?t[r]=Object.fromEntries(o):t[r]=o}),t}const Bi=/({([^}]*)})/g,Zs=/[{}]/g,Qs=/\w+\.\w+/,Ai=e=>{if(!Se(e))return[];const t=e.match(Bi);return t?t.map(o=>o.replace(Zs,"")).map(o=>o.trim()):[]},Js=e=>Bi.test(e);function Oi(e){var o,r,i;if(!((o=e.extensions)!=null&&o.references))return((i=(r=e.extensions)==null?void 0:r.cssVar)==null?void 0:i.ref)??e.value;const t=e.extensions.references??{};return e.value=Object.keys(t).reduce((n,a)=>{const s=t[a];if(s.extensions.conditions)return n;const d=Oi(s);return n.replace(`{${a}}`,d)},e.value),delete e.extensions.references,e.value}function Ni(e){return ce(e)&&e.reference?e.reference:String(e)}const Rt=(e,...t)=>t.map(Ni).join(` ${e} `).replace(/calc/g,""),$o=(...e)=>`calc(${Rt("+",...e)})`,Bo=(...e)=>`calc(${Rt("-",...e)})`,Lt=(...e)=>`calc(${Rt("*",...e)})`,Ao=(...e)=>`calc(${Rt("/",...e)})`,Oo=e=>{const t=Ni(e);return t!=null&&!Number.isNaN(parseFloat(t))?String(t).startsWith("-")?String(t).slice(1):`-${t}`:Lt(t,-1)},qe=Object.assign(e=>({add:(...t)=>qe($o(e,...t)),subtract:(...t)=>qe(Bo(e,...t)),multiply:(...t)=>qe(Lt(e,...t)),divide:(...t)=>qe(Ao(e,...t)),negate:()=>qe(Oo(e)),toString:()=>e.toString()}),{add:$o,subtract:Bo,multiply:Lt,divide:Ao,negate:Oo}),el={enforce:"pre",transform(e){const{prefix:t,allTokens:o,formatCssVar:r,formatTokenName:i,registerToken:n}=e;o.filter(({extensions:s})=>s.category==="spacing").forEach(s=>{const d=s.path.slice(),c=r(d,t);if(Se(s.value)&&s.value==="0rem")return;const l=structuredClone(s);Object.assign(l.extensions,{negative:!0,prop:`-${s.extensions.prop}`,originalPath:d}),l.value=qe.negate(c.ref);const u=l.path[l.path.length-1];u!=null&&(l.path[l.path.length-1]=`-${u}`),l.path&&(l.name=i(l.path)),n(l)})}},tl=new Set(["spacing","sizes","borderWidths","fontSizes","radii"]),ol={enforce:"post",transform(e){e.allTokens.filter(o=>tl.has(o.extensions.category)&&!o.extensions.negative).forEach(o=>{Object.assign(o.extensions,{pixelValue:Si(o.value)})})}},rl={enforce:"post",transform(e){const{allTokens:t,registerToken:o,formatTokenName:r}=e,i=t.filter(({extensions:s})=>s.category==="colors"),n=new Map,a=new Map;i.forEach(s=>{const{colorPalette:d}=s.extensions;d&&(d.keys.forEach(c=>{n.set(r(c),c)}),d.roots.forEach(c=>{var g;const l=r(c),u=a.get(l)||[];if(u.push(s),a.set(l,u),s.extensions.default&&c.length===1){const p=(g=d.keys[0])==null?void 0:g.filter(Boolean);if(!p.length)return;const m=c.concat(p);n.set(r(m),[])}}))}),n.forEach(s=>{const d=["colors","colorPalette",...s].filter(Boolean),c=r(d),l=r(d.slice(1));o({name:c,value:c,originalValue:c,path:d,extensions:{condition:"base",originalPath:d,category:"colors",prop:l,virtual:!0}},"pre")})}},il={enforce:"post",transform(e){e.allTokens=e.allTokens.filter(t=>t.value!=="")}},nl=[el,rl,ol,il],al={type:"extensions",enforce:"pre",name:"tokens/css-var",transform(e,t){const{prefix:o,formatCssVar:r}=t,{negative:i,originalPath:n}=e.extensions,a=i?n:e.path;return{cssVar:r(a.filter(Boolean),o)}}},sl={enforce:"post",type:"value",name:"tokens/conditionals",transform(e,t){const{prefix:o,formatCssVar:r}=t,i=Ai(e.value);return i.length&&i.forEach(n=>{const a=r(n.split("."),o);e.value=e.value.replace(`{${a.ref}}`,a)}),e.value}},ll={type:"extensions",enforce:"pre",name:"tokens/colors/colorPalette",match(e){return e.extensions.category==="colors"&&!e.extensions.virtual},transform(e,t){let o=e.path.slice();if(o.pop(),o.shift(),o.length===0){const s=[...e.path];s.shift(),o=s}if(o.length===0)return{};const r=o.reduce((s,d,c,l)=>{const u=l.slice(0,c+1);return s.push(u),s},[]),i=o[0],n=t.formatTokenName(o),a=e.path.slice(e.path.indexOf(i)+1).reduce((s,d,c,l)=>(s.push(l.slice(c)),s),[]);return a.length===0&&a.push([""]),{colorPalette:{value:n,roots:r,keys:a}}}},dl=[al,sl,ll],No=e=>ce(e)&&Object.prototype.hasOwnProperty.call(e,"value");function cl(e){return e?{breakpoints:fs(e,t=>({value:t})),sizes:Object.fromEntries(Object.entries(e).map(([t,o])=>[`breakpoint-${t}`,{value:o}]))}:{breakpoints:{},sizes:{}}}function ul(e){const{prefix:t="",tokens:o={},semanticTokens:r={},breakpoints:i={}}=e,n=y=>y.join("."),a=(y,v)=>yi(y.join("-"),{prefix:v}),s=[],d=new Map,c=new Map,l=new Map,u=new Map,g=new Map,p=new Map,m=new Map,f=new Map,S=[];function k(y,v){s.push(y),d.set(y.name,y),v&&f.forEach(A=>{A.enforce===v&&pe(A,y)})}const x=cl(i),I=at({...o,breakpoints:x.breakpoints,sizes:{...o.sizes,...x.sizes}});function C(){Pe(I,(y,v)=>{const A=v.includes("DEFAULT");v=Vo(v);const F=v[0],Z=n(v),te=Se(y)?{value:y}:y,ze={value:te.value,originalValue:te.value,name:Z,path:v,extensions:{condition:"base",originalPath:v,category:F,prop:n(v.slice(1))}};A&&(ze.extensions.default=!0),k(ze)},{stop:No}),Pe(r,(y,v)=>{const A=v.includes("DEFAULT");v=Vi(Vo(v));const F=v[0],Z=n(v),te=Se(y.value)?{value:{base:y.value}}:y,ze={value:te.value.base||"",originalValue:te.value.base||"",name:Z,path:v,extensions:{originalPath:v,category:F,conditions:te.value,condition:"base",prop:n(v.slice(1))}};A&&(ze.extensions.default=!0),k(ze)},{stop:No})}function _(y){return d.get(y)}function B(y){const{condition:v}=y.extensions;v&&(c.has(v)||c.set(v,new Set),c.get(v).add(y))}function R(y){const{category:v,prop:A}=y.extensions;v&&(m.has(v)||m.set(v,new Map),m.get(v).set(A,y))}function h(y){const{condition:v,negative:A,virtual:F,cssVar:Z}=y.extensions;A||F||!v||!Z||(l.has(v)||l.set(v,new Map),l.get(v).set(Z.var,y.value))}function b(y){const{category:v,prop:A,cssVar:F,negative:Z}=y.extensions;if(!v)return;p.has(v)||p.set(v,new Map);const te=Z?y.extensions.conditions?y.originalValue:y.value:F.ref;p.get(v).set(A,te),g.set([v,A].join("."),te)}function w(y){const{colorPalette:v,virtual:A,default:F}=y.extensions;!v||A||v.roots.forEach(Z=>{var mo;const te=n(Z);u.has(te)||u.set(te,new Map);const ze=pl([...y.path],[...Z]),ut=n(ze),Me=_(ut);if(!Me||!Me.extensions.cssVar)return;const{var:Wn}=Me.extensions.cssVar;if(u.get(te).set(Wn,y.extensions.cssVar.ref),F&&Z.length===1){const $n=n(["colors","colorPalette"]),fo=_($n);if(!fo)return;const Bn=n(y.path),ho=_(Bn);if(!ho)return;const bo=(mo=v.keys[0])==null?void 0:mo.filter(Boolean);if(!bo.length)return;const Wt=n(Z.concat(bo));u.has(Wt)||u.set(Wt,new Map),u.get(Wt).set(fo.extensions.cssVar.var,ho.extensions.cssVar.ref)}})}let $={};function z(){s.forEach(y=>{B(y),R(y),h(y),b(y),w(y)}),$=$i(p)}const N=(y,v)=>{var Me;if(!y||typeof y!="string")return{invalid:!0,value:y};const[A,F]=y.split("/");if(!A||!F)return{invalid:!0,value:A};const Z=v(A),te=(Me=_(`opacity.${F}`))==null?void 0:Me.value;if(!te&&isNaN(Number(F)))return{invalid:!0,value:A};const ze=te?Number(te)*100+"%":`${F}%`,ut=Z??A;return{invalid:!1,color:ut,value:`color-mix(in srgb, ${ut} ${ze}, transparent)`}},V=me((y,v)=>g.get(y)??v),W=me(y=>$[y]||null),L=me(y=>Wi(y,v=>{if(!v)return;if(v.includes("/")){const F=N(v,Z=>V(Z));if(F.invalid)throw new Error("Invalid color mix at "+v+": "+F.value);return F.value}const A=V(v);return A||(Qs.test(v)?Ei(v):v)})),P={prefix:t,allTokens:s,tokenMap:d,registerToken:k,getByName:_,formatTokenName:n,formatCssVar:a,flatMap:g,cssVarMap:l,categoryMap:m,colorPaletteMap:u,getVar:V,getCategoryValues:W,expandReferenceInValue:L};function H(...y){y.forEach(v=>{f.set(v.name,v)})}function Y(...y){S.push(...y)}function pe(y,v){if(v.extensions.references||di(y.match)&&!y.match(v))return;const F=(Z=>y.transform(Z,P))(v);switch(!0){case y.type==="extensions":Object.assign(v.extensions,F);break;case y.type==="value":v.value=F;break;default:v[y.type]=F;break}}function Te(y){S.forEach(v=>{v.enforce===y&&v.transform(P)})}function De(y){f.forEach(v=>{v.enforce===y&&s.forEach(A=>{pe(v,A)})})}function ct(){s.forEach(y=>{const v=gl(y);!v||v.length===0||v.forEach(A=>{k(A)})})}function je(y){return Ai(y).map(A=>_(A)).filter(Boolean)}function _n(){s.forEach(y=>{if(!Js(y.value))return;const v=je(y.value);y.extensions.references=v.reduce((A,F)=>(A[F.name]=F,A),{})})}function Tn(){s.forEach(y=>{Oi(y)})}function En(){Te("pre"),De("pre"),ct(),_n(),Tn(),Te("post"),De("post"),z()}return C(),H(...dl),Y(...nl),En(),P}function Vo(e){return e[0]==="DEFAULT"?e:e.filter(t=>t!=="DEFAULT")}function Vi(e){return e.filter(t=>t!=="base")}function gl(e){if(!e.extensions.conditions)return;const{conditions:t}=e.extensions,o=[];return Pe(t,(r,i)=>{const n=Vi(i);if(!n.length)return;const a=structuredClone(e);a.value=r,a.extensions.condition=n.join(":"),o.push(a)}),o}function pl(e,t){const o=e.findIndex((r,i)=>t.every((n,a)=>e[i+a]===n));return o===-1||(e.splice(o,t.length),e.splice(o,0,"colorPalette")),e}qs()(["aspectRatios","zIndex","opacity","colors","fonts","fontSizes","fontWeights","lineHeights","letterSpacings","sizes","shadows","spacing","radii","cursor","borders","borderWidths","borderStyles","durations","easings","animations","blurs","gradients","breakpoints","assets"]);function ml(e){return Object.fromEntries(Object.entries(e).map(([t,o])=>[t,o]))}function fl(e){const t=ml(e.config),o=e.tokens,r=new Map,i=new Map;function n(h,b){t[h]=b,a(h,b)}const a=(h,b)=>{const w=f(b);w&&(i.set(h,w),u(h,b))},s=()=>{for(const[h,b]of Object.entries(t))b&&a(h,b)},d=()=>{for(const[h,b]of Object.entries(t)){const{shorthand:w}=b??{};if(!w)continue;(Array.isArray(w)?w:[w]).forEach(z=>r.set(z,h))}},c=()=>{const h=$i(o.colorPaletteMap);n("colorPalette",{values:Object.keys(h),transform:me(b=>h[b])})},l=new Map,u=(h,b)=>{if(!b)return;const w=f(b,z=>`type:Tokens["${z}"]`);if(typeof w=="object"&&w.type){l.set(h,new Set([`type:${w.type}`]));return}if(w){const z=new Set(Object.keys(w));l.set(h,z)}const $=l.get(h)??new Set;b.property&&l.set(h,$.add(`CssProperties["${b.property}"]`))},g=()=>{for(const[h,b]of Object.entries(t))b&&u(h,b)},p=(h,b)=>{const w=l.get(h)??new Set;l.set(h,new Set([...w,...b]))},m=()=>{const h=new Map;for(const[b,w]of l.entries()){if(w.size===0){h.set(b,["string"]);continue}const $=Array.from(w).map(z=>z.startsWith("CssProperties")?z:z.startsWith("type:")?z.replace("type:",""):JSON.stringify(z));h.set(b,$)}return h},f=(h,b)=>{const{values:w}=h,$=z=>{const N=b==null?void 0:b(z);return N?{[N]:N}:void 0};return Se(w)?($==null?void 0:$(w))??o.getCategoryValues(w)??{}:Array.isArray(w)?w.reduce((z,N)=>(z[N]=N,z),{}):di(w)?w(b?$:o.getCategoryValues):w},S=me((h,b)=>({[h]:h.startsWith("--")?o.getVar(b,b):b})),k=Object.assign(o.getVar,{raw:h=>o.getByName(h)}),x=me((h,b)=>{var V;const w=_(h);Se(b)&&!b.includes("_EMO_")&&(b=o.expandReferenceInValue(b));const $=t[w];if(!$)return S(w,b);const z=(V=i.get(w))==null?void 0:V[b];if(!$.transform)return S(h,z??b);const N=W=>Ha(W,k);return $.transform(z??b,{raw:b,token:k,utils:{colorMix:N}})});function I(){d(),c(),s(),g()}I();const C=r.size>0,_=me(h=>r.get(h)??h);return{keys:()=>[...Array.from(r.keys()),...Object.keys(t)],hasShorthand:C,transform:x,shorthands:r,resolveShorthand:_,register:n,getTypes:m,addPropertyType:p}}function hl(...e){const t=vi(...e),{theme:o={},utilities:r={},globalCss:i={},cssVarsRoot:n=":where(:root, :host)",cssVarsPrefix:a="chakra",preflight:s}=t,d=Ds(t),c=ul({breakpoints:o.breakpoints,tokens:o.tokens,semanticTokens:o.semanticTokens,prefix:a}),l=vs(o.breakpoints??{}),u=Ss({conditions:t.conditions??{},breakpoints:l}),g=fl({config:r,tokens:c});function p(){const{textStyles:P,layerStyles:H,animationStyles:Y}=o,pe=at({textStyle:P,layerStyle:H,animationStyle:Y});for(const[Te,De]of Object.entries(pe)){const ct=hs(De??{},je=>ce(je)&&"value"in je);g.register(Te,{values:Object.keys(ct),transform(je){return I(ct[je])}})}}p(),g.addPropertyType("animationName",Object.keys(o.keyframes??{}));const m=new Set(["css",...g.keys(),...u.keys()]),f=me(P=>m.has(P)||ps(P)),S=P=>Array.isArray(P)?P.reduce((H,Y,pe)=>{const Te=u.breakpoints[pe];return Y!=null&&(H[Te]=Y),H},{}):P,k=js({utility:g,normalize:S}),x=Fs({conditions:u,isValidProperty:f}),I=Pi({transform:g.transform,conditions:u,normalize:k}),C=Os({css:I,conditions:u,normalize:k,layers:d}),_=Ys({cva:C});function B(){const P={};for(const[H,Y]of c.cssVarMap.entries()){const pe=Object.fromEntries(Y);if(Object.keys(pe).length===0)continue;const Te=H==="base"?n:u.resolve(H),De=I(x({[Te]:pe}));et(P,De)}return d.wrap("tokens",P)}function R(){const P=Object.fromEntries(Object.entries(o.keyframes??{}).map(([Y,pe])=>[`@keyframes ${Y}`,pe])),H=Object.assign({},P,I(x(i)));return d.wrap("base",H)}function h(P){return Ke(P,f)}function b(){const P=Ms({preflight:s});return d.wrap("reset",P)}const w=bl(c),$=(P,H)=>{var Y;return((Y=w.get(P))==null?void 0:Y.value)||H};$.var=(P,H)=>{var Y;return((Y=w.get(P))==null?void 0:Y.variable)||H};function z(P,H){var Y;return((Y=o.recipes)==null?void 0:Y[P])??H}function N(P,H){var Y;return((Y=o.slotRecipes)==null?void 0:Y[P])??H}function V(P){return Object.hasOwnProperty.call(o.recipes??{},P)}function W(P){return Object.hasOwnProperty.call(o.slotRecipes??{},P)}function L(P){return V(P)||W(P)}return{$$chakra:!0,_config:t,breakpoints:l,tokens:c,conditions:u,utility:g,token:$,properties:m,layers:d,isValidProperty:f,splitCssProps:h,normalizeValue:S,getTokenCss:B,getGlobalCss:R,getPreflightCss:b,css:I,cva:C,sva:_,getRecipe:z,getSlotRecipe:N,hasRecipe:L,isRecipe:V,isSlotRecipe:W}}function bl(e){const t=new Map;return e.allTokens.forEach(o=>{const{cssVar:r,virtual:i,conditions:n}=o.extensions,a=n||i?r.ref:o.value;t.set(o.name,{value:a,variable:r.ref})}),t}const vl={sm:"480px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},At="var(--chakra-empty,/*!*/ /*!*/)",yl=qa({"*":{fontFeatureSettings:'"cv11"',"--ring-inset":At,"--ring-offset-width":"0px","--ring-offset-color":"#fff","--ring-color":"rgba(66, 153, 225, 0.6)","--ring-offset-shadow":"0 0 #0000","--ring-shadow":"0 0 #0000",...Object.fromEntries(["brightness","contrast","grayscale","hue-rotate","invert","saturate","sepia","drop-shadow"].map(e=>[`--${e}`,At])),...Object.fromEntries(["blur","brightness","contrast","grayscale","hue-rotate","invert","opacity","saturate","sepia"].map(e=>[`--backdrop-${e}`,At])),"--global-font-mono":"fonts.mono","--global-font-body":"fonts.body","--global-color-border":"colors.border"},html:{color:"fg",bg:"bg",lineHeight:"1.5",colorPalette:"gray"},"*::placeholder":{color:"fg.muted/80"},"*::selection":{bg:"colorPalette.muted/80"}}),xl=Ka({"fill.muted":{value:{background:"colorPalette.muted",color:"colorPalette.fg"}},"fill.subtle":{value:{background:"colorPalette.subtle",color:"colorPalette.fg"}},"fill.surface":{value:{background:"colorPalette.subtle",color:"colorPalette.fg",boxShadow:"0 0 0px 1px var(--shadow-color)",boxShadowColor:"colorPalette.muted"}},"fill.solid":{value:{background:"colorPalette.solid",color:"colorPalette.contrast"}},"outline.subtle":{value:{color:"colorPalette.fg",boxShadow:"inset 0 0 0px 1px var(--shadow-color)",boxShadowColor:"colorPalette.subtle"}},"outline.solid":{value:{borderWidth:"1px",borderColor:"colorPalette.solid",color:"colorPalette.fg"}},"indicator.bottom":{value:{position:"relative","--indicator-color-fallback":"colors.colorPalette.solid",_before:{content:'""',position:"absolute",bottom:"var(--indicator-offset-y, 0)",insetInline:"var(--indicator-offset-x, 0)",height:"var(--indicator-thickness, 2px)",background:"var(--indicator-color, var(--indicator-color-fallback))"}}},"indicator.top":{value:{position:"relative","--indicator-color-fallback":"colors.colorPalette.solid",_before:{content:'""',position:"absolute",top:"var(--indicator-offset-y, 0)",insetInline:"var(--indicator-offset-x, 0)",height:"var(--indicator-thickness, 2px)",background:"var(--indicator-color, var(--indicator-color-fallback))"}}},"indicator.start":{value:{position:"relative","--indicator-color-fallback":"colors.colorPalette.solid",_before:{content:'""',position:"absolute",insetInlineStart:"var(--indicator-offset-x, 0)",insetBlock:"var(--indicator-offset-y, 0)",width:"var(--indicator-thickness, 2px)",background:"var(--indicator-color, var(--indicator-color-fallback))"}}},"indicator.end":{value:{position:"relative","--indicator-color-fallback":"colors.colorPalette.solid",_before:{content:'""',position:"absolute",insetInlineEnd:"var(--indicator-offset-x, 0)",insetBlock:"var(--indicator-offset-y, 0)",width:"var(--indicator-thickness, 2px)",background:"var(--indicator-color, var(--indicator-color-fallback))"}}},disabled:{value:{opacity:"0.5",cursor:"not-allowed"}},none:{value:{}}}),kl=Xa({"slide-fade-in":{value:{transformOrigin:"var(--transform-origin)","&[data-placement^=top]":{animationName:"slide-from-bottom, fade-in"},"&[data-placement^=bottom]":{animationName:"slide-from-top, fade-in"},"&[data-placement^=left]":{animationName:"slide-from-right, fade-in"},"&[data-placement^=right]":{animationName:"slide-from-left, fade-in"}}},"slide-fade-out":{value:{transformOrigin:"var(--transform-origin)","&[data-placement^=top]":{animationName:"slide-to-bottom, fade-out"},"&[data-placement^=bottom]":{animationName:"slide-to-top, fade-out"},"&[data-placement^=left]":{animationName:"slide-to-right, fade-out"},"&[data-placement^=right]":{animationName:"slide-to-left, fade-out"}}},"scale-fade-in":{value:{transformOrigin:"var(--transform-origin)",animationName:"scale-in, fade-in"}},"scale-fade-out":{value:{transformOrigin:"var(--transform-origin)",animationName:"scale-out, fade-out"}}}),oo=Q({className:"chakra-badge",base:{display:"inline-flex",alignItems:"center",borderRadius:"l2",gap:"1",fontWeight:"medium",fontVariantNumeric:"tabular-nums",whiteSpace:"nowrap",userSelect:"none"},variants:{variant:{solid:{bg:"colorPalette.solid",color:"colorPalette.contrast"},subtle:{bg:"colorPalette.subtle",color:"colorPalette.fg"},outline:{color:"colorPalette.fg",shadow:"inset 0 0 0px 1px var(--shadow-color)",shadowColor:"colorPalette.muted"},surface:{bg:"colorPalette.subtle",color:"colorPalette.fg",shadow:"inset 0 0 0px 1px var(--shadow-color)",shadowColor:"colorPalette.muted"},plain:{color:"colorPalette.fg"}},size:{xs:{textStyle:"2xs",px:"1",minH:"4"},sm:{textStyle:"xs",px:"1.5",minH:"5"},md:{textStyle:"sm",px:"2",minH:"6"},lg:{textStyle:"sm",px:"2.5",minH:"7"}}},defaultVariants:{variant:"subtle",size:"sm"}}),Sl=Q({className:"chakra-button",base:{display:"inline-flex",appearance:"none",alignItems:"center",justifyContent:"center",userSelect:"none",position:"relative",borderRadius:"l2",whiteSpace:"nowrap",verticalAlign:"middle",borderWidth:"1px",borderColor:"transparent",cursor:"button",flexShrink:"0",outline:"0",lineHeight:"1.2",isolation:"isolate",fontWeight:"medium",transitionProperty:"common",transitionDuration:"moderate",focusVisibleRing:"outside",_disabled:{layerStyle:"disabled"},_icon:{flexShrink:"0"}},variants:{size:{"2xs":{h:"6",minW:"6",textStyle:"xs",px:"2",gap:"1",_icon:{width:"3.5",height:"3.5"}},xs:{h:"8",minW:"8",textStyle:"xs",px:"2.5",gap:"1",_icon:{width:"4",height:"4"}},sm:{h:"9",minW:"9",px:"3.5",textStyle:"sm",gap:"2",_icon:{width:"4",height:"4"}},md:{h:"10",minW:"10",textStyle:"sm",px:"4",gap:"2",_icon:{width:"5",height:"5"}},lg:{h:"11",minW:"11",textStyle:"md",px:"5",gap:"3",_icon:{width:"5",height:"5"}},xl:{h:"12",minW:"12",textStyle:"md",px:"5",gap:"2.5",_icon:{width:"5",height:"5"}},"2xl":{h:"16",minW:"16",textStyle:"lg",px:"7",gap:"3",_icon:{width:"6",height:"6"}}},variant:{solid:{bg:"colorPalette.solid",color:"colorPalette.contrast",_hover:{bg:"colorPalette.solid/90"},_expanded:{bg:"colorPalette.solid/90"}},subtle:{bg:"colorPalette.subtle",color:"colorPalette.fg",_hover:{bg:"colorPalette.muted"},_expanded:{bg:"colorPalette.muted"}},surface:{bg:"colorPalette.subtle",color:"colorPalette.fg",shadow:"0 0 0px 1px var(--shadow-color)",shadowColor:"colorPalette.muted",_hover:{bg:"colorPalette.muted"},_expanded:{bg:"colorPalette.muted"}},outline:{borderWidth:"1px",borderColor:"colorPalette.muted",color:"colorPalette.fg",_hover:{bg:"colorPalette.subtle"},_expanded:{bg:"colorPalette.subtle"}},ghost:{color:"colorPalette.fg",_hover:{bg:"colorPalette.subtle"},_expanded:{bg:"colorPalette.subtle"}},plain:{color:"colorPalette.fg"}}},defaultVariants:{size:"md",variant:"solid"}}),oe=Q({className:"chakra-checkmark",base:{display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:"0",color:"white",borderWidth:"1px",borderColor:"transparent",borderRadius:"l1",focusVisibleRing:"outside",_icon:{boxSize:"full"},_invalid:{colorPalette:"red",borderColor:"border.error"},_disabled:{opacity:"0.5"}},variants:{size:{xs:{boxSize:"3"},sm:{boxSize:"4"},md:{boxSize:"5",p:"0.5"},lg:{boxSize:"6",p:"0.5"}},variant:{solid:{borderColor:"border","&:is([data-state=checked], [data-state=indeterminate])":{bg:"colorPalette.solid",color:"colorPalette.contrast",borderColor:"colorPalette.solid"}},outline:{borderColor:"border","&:is([data-state=checked], [data-state=indeterminate])":{color:"colorPalette.fg",borderColor:"colorPalette.solid"}},subtle:{bg:"colorPalette.muted",borderColor:"colorPalette.muted","&:is([data-state=checked], [data-state=indeterminate])":{color:"colorPalette.fg"}},plain:{"&:is([data-state=checked], [data-state=indeterminate])":{color:"colorPalette.fg"}},inverted:{borderColor:"border",color:"colorPalette.fg","&:is([data-state=checked], [data-state=indeterminate])":{borderColor:"colorPalette.solid"}}}},defaultVariants:{variant:"solid",size:"md"}}),{variants:wl,defaultVariants:Il}=oo,zl=Q({className:"chakra-code",base:{fontFamily:"mono",alignItems:"center",display:"inline-flex",borderRadius:"l2"},variants:wl,defaultVariants:Il}),Di=Q({className:"color-swatch",base:{boxSize:"var(--swatch-size)",shadow:"inset 0 0 0 1px rgba(0, 0, 0, 0.1)","--checker-size":"8px","--checker-bg":"colors.bg","--checker-fg":"colors.bg.emphasized",background:"linear-gradient(var(--color), var(--color)), repeating-conic-gradient(var(--checker-fg) 0%, var(--checker-fg) 25%, var(--checker-bg) 0%, var(--checker-bg) 50%) 0% 50% / var(--checker-size) var(--checker-size) !important",display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:"0"},variants:{size:{"2xs":{"--swatch-size":"sizes.3.5"},xs:{"--swatch-size":"sizes.4"},sm:{"--swatch-size":"sizes.4.5"},md:{"--swatch-size":"sizes.5"},lg:{"--swatch-size":"sizes.6"},xl:{"--swatch-size":"sizes.7"},"2xl":{"--swatch-size":"sizes.8"},inherit:{"--swatch-size":"inherit"},full:{"--swatch-size":"100%"}},shape:{square:{borderRadius:"none"},circle:{borderRadius:"full"},rounded:{borderRadius:"l1"}}},defaultVariants:{size:"md",shape:"rounded"}}),Cl=Q({className:"chakra-container",base:{position:"relative",maxWidth:"8xl",w:"100%",mx:"auto",px:{base:"4",md:"6",lg:"8"}},variants:{centerContent:{true:{display:"flex",flexDirection:"column",alignItems:"center"}},fluid:{true:{maxWidth:"full"}}}}),Rl=Q({className:"chakra-heading",base:{fontFamily:"heading",fontWeight:"semibold"},variants:{size:{xs:{textStyle:"xs"},sm:{textStyle:"sm"},md:{textStyle:"md"},lg:{textStyle:"lg"},xl:{textStyle:"xl"},"2xl":{textStyle:"2xl"},"3xl":{textStyle:"3xl"},"4xl":{textStyle:"4xl"},"5xl":{textStyle:"5xl"},"6xl":{textStyle:"6xl"},"7xl":{textStyle:"7xl"}}},defaultVariants:{size:"xl"}}),Pl=Q({className:"chakra-icon",base:{display:"inline-block",lineHeight:"1em",flexShrink:"0",color:"currentcolor",verticalAlign:"middle"},variants:{size:{inherit:{},xs:{boxSize:"3"},sm:{boxSize:"4"},md:{boxSize:"5"},lg:{boxSize:"6"},xl:{boxSize:"7"},"2xl":{boxSize:"8"}}},defaultVariants:{size:"inherit"}}),U=Q({className:"chakra-input",base:{width:"100%",minWidth:"0",outline:"0",position:"relative",appearance:"none",textAlign:"start",borderRadius:"l2",_disabled:{layerStyle:"disabled"},height:"var(--input-height)",minW:"var(--input-height)","--focus-color":"colors.colorPalette.focusRing","--error-color":"colors.border.error",_invalid:{focusRingColor:"var(--error-color)",borderColor:"var(--error-color)"}},variants:{size:{"2xs":{textStyle:"xs",px:"2","--input-height":"sizes.7"},xs:{textStyle:"xs",px:"2","--input-height":"sizes.8"},sm:{textStyle:"sm",px:"2.5","--input-height":"sizes.9"},md:{textStyle:"sm",px:"3","--input-height":"sizes.10"},lg:{textStyle:"md",px:"4","--input-height":"sizes.11"},xl:{textStyle:"md",px:"4.5","--input-height":"sizes.12"},"2xl":{textStyle:"lg",px:"5","--input-height":"sizes.16"}},variant:{outline:{bg:"transparent",borderWidth:"1px",borderColor:"border",focusVisibleRing:"inside"},subtle:{borderWidth:"1px",borderColor:"transparent",bg:"bg.muted",focusVisibleRing:"inside"},flushed:{bg:"transparent",borderBottomWidth:"1px",borderBottomColor:"border",borderRadius:"0",px:"0",_focusVisible:{borderColor:"var(--focus-color)",boxShadow:"0px 1px 0px 0px var(--focus-color)"}}}},defaultVariants:{size:"md",variant:"outline"}}),_l=Q({className:"chakra-input-addon",base:{flex:"0 0 auto",width:"auto",display:"flex",alignItems:"center",whiteSpace:"nowrap",alignSelf:"stretch",borderRadius:"l2"},variants:{size:U.variants.size,variant:{outline:{borderWidth:"1px",borderColor:"border",bg:"bg.muted"},subtle:{borderWidth:"1px",borderColor:"transparent",bg:"bg.emphasized"},flushed:{borderBottom:"1px solid",borderColor:"inherit",borderRadius:"0",px:"0",bg:"transparent"}}},defaultVariants:{size:"md",variant:"outline"}}),Tl=Q({className:"chakra-kbd",base:{display:"inline-flex",alignItems:"center",fontWeight:"medium",fontFamily:"mono",flexShrink:"0",whiteSpace:"nowrap",wordSpacing:"-0.5em",userSelect:"none",px:"1",borderRadius:"l2"},variants:{variant:{raised:{bg:"colorPalette.subtle",color:"colorPalette.fg",borderWidth:"1px",borderBottomWidth:"2px",borderColor:"colorPalette.muted"},outline:{borderWidth:"1px",color:"colorPalette.fg"},subtle:{bg:"colorPalette.muted",color:"colorPalette.fg"},plain:{color:"colorPalette.fg"}},size:{sm:{textStyle:"xs",height:"4.5"},md:{textStyle:"sm",height:"5"},lg:{textStyle:"md",height:"6"}}},defaultVariants:{size:"md",variant:"raised"}}),El=Q({className:"chakra-link",base:{display:"inline-flex",alignItems:"center",outline:"none",gap:"1.5",cursor:"pointer",borderRadius:"l1",focusRing:"outside"},variants:{variant:{underline:{color:"colorPalette.fg",textDecoration:"underline",textUnderlineOffset:"3px",textDecorationColor:"currentColor/20"},plain:{color:"colorPalette.fg",_hover:{textDecoration:"underline",textUnderlineOffset:"3px",textDecorationColor:"currentColor/20"}}}},defaultVariants:{variant:"plain"}}),Wl=Q({className:"chakra-mark",base:{bg:"transparent",color:"inherit",whiteSpace:"nowrap"},variants:{variant:{subtle:{bg:"colorPalette.subtle",color:"inherit"},solid:{bg:"colorPalette.solid",color:"colorPalette.contrast"},text:{fontWeight:"medium"},plain:{}}}}),re=Q({className:"chakra-radiomark",base:{display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:0,verticalAlign:"top",color:"white",borderWidth:"1px",borderColor:"transparent",borderRadius:"full",cursor:"radio",_focusVisible:{outline:"2px solid",outlineColor:"colorPalette.focusRing",outlineOffset:"2px"},_invalid:{colorPalette:"red",borderColor:"red.500"},_disabled:{opacity:"0.5",cursor:"disabled"},"& .dot":{height:"100%",width:"100%",borderRadius:"full",bg:"currentColor",scale:"0.4"}},variants:{variant:{solid:{borderWidth:"1px",borderColor:"border",_checked:{bg:"colorPalette.solid",color:"colorPalette.contrast",borderColor:"colorPalette.solid"}},subtle:{borderWidth:"1px",bg:"colorPalette.muted",borderColor:"colorPalette.muted",color:"transparent",_checked:{color:"colorPalette.fg"}},outline:{borderWidth:"1px",borderColor:"inherit",_checked:{color:"colorPalette.fg",borderColor:"colorPalette.solid"},"& .dot":{scale:"0.6"}},inverted:{bg:"bg",borderWidth:"1px",borderColor:"inherit",_checked:{color:"colorPalette.solid",borderColor:"currentcolor"}}},size:{xs:{boxSize:"3"},sm:{boxSize:"4"},md:{boxSize:"5"},lg:{boxSize:"6"}}},defaultVariants:{variant:"solid",size:"md"}}),$l=Q({className:"chakra-separator",base:{display:"block",borderColor:"border"},variants:{variant:{solid:{borderStyle:"solid"},dashed:{borderStyle:"dashed"},dotted:{borderStyle:"dotted"}},orientation:{vertical:{height:"100%",borderInlineStartWidth:"var(--separator-thickness)"},horizontal:{width:"100%",borderTopWidth:"var(--separator-thickness)"}},size:{xs:{"--separator-thickness":"0.5px"},sm:{"--separator-thickness":"1px"},md:{"--separator-thickness":"2px"},lg:{"--separator-thickness":"3px"}}},defaultVariants:{size:"sm",variant:"solid",orientation:"horizontal"}}),Bl=Q({className:"chakra-skeleton",base:{},variants:{loading:{true:{borderRadius:"l2",boxShadow:"none",backgroundClip:"padding-box",cursor:"default",color:"transparent",pointerEvents:"none",userSelect:"none",flexShrink:"0","&::before, &::after, *":{visibility:"hidden"}},false:{background:"unset",animation:"fade-in var(--fade-duration, 0.1s) ease-out !important"}},variant:{pulse:{background:"bg.emphasized",animation:"pulse",animationDuration:"var(--duration, 1.2s)"},shine:{"--animate-from":"200%","--animate-to":"-200%","--start-color":"colors.bg.muted","--end-color":"colors.bg.emphasized",backgroundImage:"linear-gradient(270deg,var(--start-color),var(--end-color),var(--end-color),var(--start-color))",backgroundSize:"400% 100%",animation:"bg-position var(--duration, 5s) ease-in-out infinite"},none:{animation:"none"}}},defaultVariants:{variant:"pulse",loading:!0}}),Al=Q({className:"chakra-skip-nav",base:{display:"inline-flex",bg:"bg.panel",padding:"2.5",borderRadius:"l2",fontWeight:"semibold",focusVisibleRing:"outside",textStyle:"sm",userSelect:"none",border:"0",height:"1px",width:"1px",margin:"-1px",outline:"0",overflow:"hidden",position:"absolute",clip:"rect(0 0 0 0)",_focusVisible:{clip:"auto",width:"auto",height:"auto",position:"fixed",top:"6",insetStart:"6"}}}),Ol=Q({className:"chakra-spinner",base:{display:"inline-block",borderColor:"currentColor",borderStyle:"solid",borderWidth:"2px",borderRadius:"full",width:"var(--spinner-size)",height:"var(--spinner-size)",animation:"spin",animationDuration:"slowest","--spinner-track-color":"transparent",borderBottomColor:"var(--spinner-track-color)",borderInlineStartColor:"var(--spinner-track-color)"},variants:{size:{inherit:{"--spinner-size":"1em"},xs:{"--spinner-size":"sizes.3"},sm:{"--spinner-size":"sizes.4"},md:{"--spinner-size":"sizes.5"},lg:{"--spinner-size":"sizes.8"},xl:{"--spinner-size":"sizes.10"}}},defaultVariants:{size:"md"}}),Nl=Q({className:"chakra-textarea",base:{width:"100%",minWidth:"0",outline:"0",position:"relative",appearance:"none",textAlign:"start",borderRadius:"l2",_disabled:{layerStyle:"disabled"},"--focus-color":"colors.colorPalette.focusRing","--error-color":"colors.border.error",_invalid:{focusRingColor:"var(--error-color)",borderColor:"var(--error-color)"}},variants:{size:{xs:{textStyle:"xs",px:"2",py:"1.5",scrollPaddingBottom:"1.5"},sm:{textStyle:"sm",px:"2.5",py:"2",scrollPaddingBottom:"2"},md:{textStyle:"sm",px:"3",py:"2",scrollPaddingBottom:"2"},lg:{textStyle:"md",px:"4",py:"3",scrollPaddingBottom:"3"},xl:{textStyle:"md",px:"4.5",py:"3.5",scrollPaddingBottom:"3.5"}},variant:{outline:{bg:"transparent",borderWidth:"1px",borderColor:"border",focusVisibleRing:"inside"},subtle:{borderWidth:"1px",borderColor:"transparent",bg:"bg.muted",focusVisibleRing:"inside"},flushed:{bg:"transparent",borderBottomWidth:"1px",borderBottomColor:"border",borderRadius:"0",px:"0",_focusVisible:{borderColor:"var(--focus-color)",boxShadow:"0px 1px 0px 0px var(--focus-color)"}}}},defaultVariants:{size:"md",variant:"outline"}}),Vl={badge:oo,button:Sl,code:zl,container:Cl,heading:Rl,input:U,inputAddon:_l,kbd:Tl,link:El,mark:Wl,separator:$l,skeleton:Bl,skipNavLink:Al,spinner:Ol,textarea:Nl,icon:Pl,checkmark:oe,radiomark:re,colorSwatch:Di},Dl=eo.colors({bg:{DEFAULT:{value:{_light:"{colors.white}",_dark:"{colors.black}"}},subtle:{value:{_light:"{colors.gray.50}",_dark:"{colors.gray.950}"}},muted:{value:{_light:"{colors.gray.100}",_dark:"{colors.gray.900}"}},emphasized:{value:{_light:"{colors.gray.200}",_dark:"{colors.gray.800}"}},inverted:{value:{_light:"{colors.black}",_dark:"{colors.white}"}},panel:{value:{_light:"{colors.white}",_dark:"{colors.gray.950}"}},error:{value:{_light:"{colors.red.50}",_dark:"{colors.red.950}"}},warning:{value:{_light:"{colors.orange.50}",_dark:"{colors.orange.950}"}},success:{value:{_light:"{colors.green.50}",_dark:"{colors.green.950}"}},info:{value:{_light:"{colors.blue.50}",_dark:"{colors.blue.950}"}}},fg:{DEFAULT:{value:{_light:"{colors.black}",_dark:"{colors.gray.50}"}},muted:{value:{_light:"{colors.gray.600}",_dark:"{colors.gray.400}"}},subtle:{value:{_light:"{colors.gray.400}",_dark:"{colors.gray.500}"}},inverted:{value:{_light:"{colors.gray.50}",_dark:"{colors.black}"}},error:{value:{_light:"{colors.red.500}",_dark:"{colors.red.400}"}},warning:{value:{_light:"{colors.orange.600}",_dark:"{colors.orange.300}"}},success:{value:{_light:"{colors.green.600}",_dark:"{colors.green.300}"}},info:{value:{_light:"{colors.blue.600}",_dark:"{colors.blue.300}"}}},border:{DEFAULT:{value:{_light:"{colors.gray.200}",_dark:"{colors.gray.800}"}},muted:{value:{_light:"{colors.gray.100}",_dark:"{colors.gray.900}"}},subtle:{value:{_light:"{colors.gray.50}",_dark:"{colors.gray.950}"}},emphasized:{value:{_light:"{colors.gray.300}",_dark:"{colors.gray.700}"}},inverted:{value:{_light:"{colors.gray.800}",_dark:"{colors.gray.200}"}},error:{value:{_light:"{colors.red.500}",_dark:"{colors.red.400}"}},warning:{value:{_light:"{colors.orange.500}",_dark:"{colors.orange.400}"}},success:{value:{_light:"{colors.green.500}",_dark:"{colors.green.400}"}},info:{value:{_light:"{colors.blue.500}",_dark:"{colors.blue.400}"}}},gray:{contrast:{value:{_light:"{colors.white}",_dark:"{colors.black}"}},fg:{value:{_light:"{colors.gray.800}",_dark:"{colors.gray.200}"}},subtle:{value:{_light:"{colors.gray.100}",_dark:"{colors.gray.900}"}},muted:{value:{_light:"{colors.gray.200}",_dark:"{colors.gray.800}"}},emphasized:{value:{_light:"{colors.gray.300}",_dark:"{colors.gray.700}"}},solid:{value:{_light:"{colors.gray.900}",_dark:"{colors.white}"}},focusRing:{value:{_light:"{colors.gray.800}",_dark:"{colors.gray.200}"}}},red:{contrast:{value:{_light:"white",_dark:"white"}},fg:{value:{_light:"{colors.red.700}",_dark:"{colors.red.300}"}},subtle:{value:{_light:"{colors.red.100}",_dark:"{colors.red.900}"}},muted:{value:{_light:"{colors.red.200}",_dark:"{colors.red.800}"}},emphasized:{value:{_light:"{colors.red.300}",_dark:"{colors.red.700}"}},solid:{value:{_light:"{colors.red.600}",_dark:"{colors.red.600}"}},focusRing:{value:{_light:"{colors.red.600}",_dark:"{colors.red.600}"}}},orange:{contrast:{value:{_light:"white",_dark:"black"}},fg:{value:{_light:"{colors.orange.700}",_dark:"{colors.orange.300}"}},subtle:{value:{_light:"{colors.orange.100}",_dark:"{colors.orange.900}"}},muted:{value:{_light:"{colors.orange.200}",_dark:"{colors.orange.800}"}},emphasized:{value:{_light:"{colors.orange.300}",_dark:"{colors.orange.700}"}},solid:{value:{_light:"{colors.orange.600}",_dark:"{colors.orange.500}"}},focusRing:{value:{_light:"{colors.orange.600}",_dark:"{colors.orange.500}"}}},green:{contrast:{value:{_light:"white",_dark:"white"}},fg:{value:{_light:"{colors.green.700}",_dark:"{colors.green.300}"}},subtle:{value:{_light:"{colors.green.100}",_dark:"{colors.green.900}"}},muted:{value:{_light:"{colors.green.200}",_dark:"{colors.green.800}"}},emphasized:{value:{_light:"{colors.green.300}",_dark:"{colors.green.700}"}},solid:{value:{_light:"{colors.green.600}",_dark:"{colors.green.600}"}},focusRing:{value:{_light:"{colors.green.600}",_dark:"{colors.green.600}"}}},blue:{contrast:{value:{_light:"white",_dark:"white"}},fg:{value:{_light:"{colors.blue.700}",_dark:"{colors.blue.300}"}},subtle:{value:{_light:"{colors.blue.100}",_dark:"{colors.blue.900}"}},muted:{value:{_light:"{colors.blue.200}",_dark:"{colors.blue.800}"}},emphasized:{value:{_light:"{colors.blue.300}",_dark:"{colors.blue.700}"}},solid:{value:{_light:"{colors.blue.600}",_dark:"{colors.blue.600}"}},focusRing:{value:{_light:"{colors.blue.600}",_dark:"{colors.blue.600}"}}},yellow:{contrast:{value:{_light:"black",_dark:"black"}},fg:{value:{_light:"{colors.yellow.800}",_dark:"{colors.yellow.300}"}},subtle:{value:{_light:"{colors.yellow.100}",_dark:"{colors.yellow.900}"}},muted:{value:{_light:"{colors.yellow.200}",_dark:"{colors.yellow.800}"}},emphasized:{value:{_light:"{colors.yellow.300}",_dark:"{colors.yellow.700}"}},solid:{value:{_light:"{colors.yellow.300}",_dark:"{colors.yellow.300}"}},focusRing:{value:{_light:"{colors.yellow.300}",_dark:"{colors.yellow.300}"}}},teal:{contrast:{value:{_light:"white",_dark:"white"}},fg:{value:{_light:"{colors.teal.700}",_dark:"{colors.teal.300}"}},subtle:{value:{_light:"{colors.teal.100}",_dark:"{colors.teal.900}"}},muted:{value:{_light:"{colors.teal.200}",_dark:"{colors.teal.800}"}},emphasized:{value:{_light:"{colors.teal.300}",_dark:"{colors.teal.700}"}},solid:{value:{_light:"{colors.teal.600}",_dark:"{colors.teal.600}"}},focusRing:{value:{_light:"{colors.teal.600}",_dark:"{colors.teal.600}"}}},purple:{contrast:{value:{_light:"white",_dark:"white"}},fg:{value:{_light:"{colors.purple.700}",_dark:"{colors.purple.300}"}},subtle:{value:{_light:"{colors.purple.100}",_dark:"{colors.purple.900}"}},muted:{value:{_light:"{colors.purple.200}",_dark:"{colors.purple.800}"}},emphasized:{value:{_light:"{colors.purple.300}",_dark:"{colors.purple.700}"}},solid:{value:{_light:"{colors.purple.600}",_dark:"{colors.purple.600}"}},focusRing:{value:{_light:"{colors.purple.600}",_dark:"{colors.purple.600}"}}},pink:{contrast:{value:{_light:"white",_dark:"white"}},fg:{value:{_light:"{colors.pink.700}",_dark:"{colors.pink.300}"}},subtle:{value:{_light:"{colors.pink.100}",_dark:"{colors.pink.900}"}},muted:{value:{_light:"{colors.pink.200}",_dark:"{colors.pink.800}"}},emphasized:{value:{_light:"{colors.pink.300}",_dark:"{colors.pink.700}"}},solid:{value:{_light:"{colors.pink.600}",_dark:"{colors.pink.600}"}},focusRing:{value:{_light:"{colors.pink.600}",_dark:"{colors.pink.600}"}}},cyan:{contrast:{value:{_light:"white",_dark:"white"}},fg:{value:{_light:"{colors.cyan.700}",_dark:"{colors.cyan.300}"}},subtle:{value:{_light:"{colors.cyan.100}",_dark:"{colors.cyan.900}"}},muted:{value:{_light:"{colors.cyan.200}",_dark:"{colors.cyan.800}"}},emphasized:{value:{_light:"{colors.cyan.300}",_dark:"{colors.cyan.700}"}},solid:{value:{_light:"{colors.cyan.600}",_dark:"{colors.cyan.600}"}},focusRing:{value:{_light:"{colors.cyan.600}",_dark:"{colors.cyan.600}"}}}}),jl=eo.radii({l1:{value:"{radii.xs}"},l2:{value:"{radii.sm}"},l3:{value:"{radii.md}"}}),Ml=eo.shadows({xs:{value:{_light:"0px 1px 2px {colors.gray.900/10}, 0px 0px 1px {colors.gray.900/20}",_dark:"0px 1px 1px {black/64}, 0px 0px 1px inset {colors.gray.300/20}"}},sm:{value:{_light:"0px 2px 4px {colors.gray.900/10}, 0px 0px 1px {colors.gray.900/30}",_dark:"0px 2px 4px {black/64}, 0px 0px 1px inset {colors.gray.300/30}"}},md:{value:{_light:"0px 4px 8px {colors.gray.900/10}, 0px 0px 1px {colors.gray.900/30}",_dark:"0px 4px 8px {black/64}, 0px 0px 1px inset {colors.gray.300/30}"}},lg:{value:{_light:"0px 8px 16px {colors.gray.900/10}, 0px 0px 1px {colors.gray.900/30}",_dark:"0px 8px 16px {black/64}, 0px 0px 1px inset {colors.gray.300/30}"}},xl:{value:{_light:"0px 16px 24px {colors.gray.900/10}, 0px 0px 1px {colors.gray.900/30}",_dark:"0px 16px 24px {black/64}, 0px 0px 1px inset {colors.gray.300/30}"}},"2xl":{value:{_light:"0px 24px 40px {colors.gray.900/16}, 0px 0px 1px {colors.gray.900/30}",_dark:"0px 24px 40px {black/64}, 0px 0px 1px inset {colors.gray.300/30}"}},inner:{value:{_light:"inset 0 2px 4px 0 {black/5}",_dark:"inset 0 2px 4px 0 black"}},inset:{value:{_light:"inset 0 0 0 1px {black/5}",_dark:"inset 0 0 0 1px {colors.gray.300/5}"}}});var E=(e,t=[])=>({parts:(...o)=>{if(Fl(t))return E(e,o);throw new Error("createAnatomy().parts(...) should only be called once. Did you mean to use .extendWith(...) ?")},extendWith:(...o)=>E(e,[...t,...o]),rename:o=>E(o,t),keys:()=>t,build:()=>[...new Set(t)].reduce((o,r)=>Object.assign(o,{[r]:{selector:[`&[data-scope="${Le(e)}"][data-part="${Le(r)}"]`,`& [data-scope="${Le(e)}"][data-part="${Le(r)}"]`].join(", "),attrs:{"data-scope":Le(e),"data-part":Le(r)}}}),{})}),Le=e=>e.replace(/([A-Z])([A-Z])/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[\s_]+/g,"-").toLowerCase(),Fl=e=>e.length===0,ji=e=>e[0],Mi=e=>e[e.length-1],Ll=Function.prototype.toString;Ll.call(Object);var{floor:Hl,abs:dg,round:Fi,min:Gl,max:Yl,pow:cg,sign:ug}=Math,ql=e=>Number.isNaN(e),ro=e=>ql(e)?0:e,Ul=(e,t)=>(e%t+t)%t,Xl=(e,t,o)=>Fi((ro(e)-t)/o)*o+t,Kl=(e,t,o)=>Gl(Yl(ro(e),t),o),Zl=(e,t,o)=>(ro(e)-t)/(o-t),Ql=(e,t,o,r)=>Kl(Xl(e*(o-t)+t,t,r),t,o),Ot=(e,t)=>typeof t=="number"?Hl(e*t+.5)/t:Fi(e),Ht=(e,t)=>{const[o,r]=e,[i,n]=t;return a=>o===r||i===n?i:i+(n-i)/(r-o)*(a-o)};const Jl=Symbol(),Do=Object.getPrototypeOf,Gt=new WeakMap,ed=e=>e&&(Gt.has(e)?Gt.get(e):Do(e)===Object.prototype||Do(e)===Array.prototype),td=e=>ed(e)&&e[Jl]||null,jo=(e,t=!0)=>{Gt.set(e,t)};function od(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global}function Li(e,t){const o=od();return o?(o[e]||(o[e]=t()),o[e]):t()}var bt=Li("__zag__refSet",()=>new WeakSet),rd=e=>typeof e=="object"&&e!==null&&"$$typeof"in e&&"props"in e,id=e=>typeof e=="object"&&e!==null&&"__v_isVNode"in e,nd=e=>typeof e=="object"&&e!==null&&"nodeType"in e&&typeof e.nodeName=="string",ad=e=>rd(e)||id(e)||nd(e),Yt=e=>e!==null&&typeof e=="object",Mo=e=>Yt(e)&&!bt.has(e)&&(Array.isArray(e)||!(Symbol.iterator in e))&&!ad(e)&&!(e instanceof WeakMap)&&!(e instanceof WeakSet)&&!(e instanceof Error)&&!(e instanceof Number)&&!(e instanceof Date)&&!(e instanceof String)&&!(e instanceof RegExp)&&!(e instanceof ArrayBuffer)&&!(e instanceof Promise),Ue=Li("__zag__proxyStateMap",()=>new WeakMap),sd=(e=Object.is,t=(s,d)=>new Proxy(s,d),o=new WeakMap,r=(s,d)=>{const c=o.get(s);if((c==null?void 0:c[0])===d)return c[1];const l=Array.isArray(s)?[]:Object.create(Object.getPrototypeOf(s));return jo(l,!0),o.set(s,[d,l]),Reflect.ownKeys(s).forEach(u=>{const g=Reflect.get(s,u);bt.has(g)?(jo(g,!1),l[u]=g):Ue.has(g)?l[u]=dd(g):l[u]=g}),Object.freeze(l)},i=new WeakMap,n=[1,1],a=s=>{if(!Yt(s))throw new Error("object required");const d=i.get(s);if(d)return d;let c=n[0];const l=new Set,u=(R,h=++n[0])=>{c!==h&&(c=h,l.forEach(b=>b(R,h)))};let g=n[1];const p=(R=++n[1])=>(g!==R&&!l.size&&(g=R,f.forEach(([h])=>{const b=h[1](R);b>c&&(c=b)})),c),m=R=>(h,b)=>{const w=[...h];w[1]=[R,...w[1]],u(w,b)},f=new Map,S=(R,h)=>{if(l.size){const b=h[3](m(R));f.set(R,[h,b])}else f.set(R,[h])},k=R=>{var b;const h=f.get(R);h&&(f.delete(R),(b=h[1])==null||b.call(h))},x=R=>(l.add(R),l.size===1&&f.forEach(([b,w],$)=>{const z=b[3](m($));f.set($,[b,z])}),()=>{l.delete(R),l.size===0&&f.forEach(([b,w],$)=>{w&&(w(),f.set($,[b]))})}),I=Array.isArray(s)?[]:Object.create(Object.getPrototypeOf(s)),_=t(I,{deleteProperty(R,h){const b=Reflect.get(R,h);k(h);const w=Reflect.deleteProperty(R,h);return w&&u(["delete",[h],b]),w},set(R,h,b,w){var V;const $=Reflect.has(R,h),z=Reflect.get(R,h,w);if($&&(e(z,b)||i.has(b)&&e(z,i.get(b))))return!0;k(h),Yt(b)&&(b=td(b)||b);let N=b;if(!((V=Object.getOwnPropertyDescriptor(R,h))!=null&&V.set)){!Ue.has(b)&&Mo(b)&&(N=Hi(b));const W=!bt.has(N)&&Ue.get(N);W&&S(h,W)}return Reflect.set(R,h,N,w),u(["set",[h],b,z]),!0}});i.set(s,_);const B=[I,p,r,x];return Ue.set(_,B),Reflect.ownKeys(s).forEach(R=>{const h=Object.getOwnPropertyDescriptor(s,R);h.get||h.set?Object.defineProperty(I,R,h):_[R]=s[R]}),_})=>[a,Ue,bt,e,t,Mo,o,r,i,n],[ld]=sd();function Hi(e={}){return ld(e)}function dd(e){const t=Ue.get(e),[o,r,i]=t;return i(o,r())}var O=()=>e=>Array.from(new Set(e)),Gi=E("accordion").parts("root","item","itemTrigger","itemContent","itemIndicator");Gi.build();var be=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`accordion:${e.id}`},getItemId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.item)==null?void 0:r.call(o,t))??`accordion:${e.id}:item:${t}`},getItemContentId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemContent)==null?void 0:r.call(o,t))??`accordion:${e.id}:content:${t}`},getItemTriggerId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemTrigger)==null?void 0:r.call(o,t))??`accordion:${e.id}:trigger:${t}`},getRootEl:e=>be.getById(e,be.getRootId(e)),getTriggerEls:e=>{const o=`[aria-controls][data-ownedby='${CSS.escape(be.getRootId(e))}']:not([disabled])`;return Re(be.getRootEl(e),o)},getFirstTriggerEl:e=>ji(be.getTriggerEls(e)),getLastTriggerEl:e=>Mi(be.getTriggerEls(e)),getNextTriggerEl:(e,t)=>pi(be.getTriggerEls(e),be.getItemTriggerId(e,t)),getPrevTriggerEl:(e,t)=>mi(be.getTriggerEls(e),be.getItemTriggerId(e,t))});O()(["collapsible","dir","disabled","getRootNode","id","ids","multiple","onFocusChange","onValueChange","orientation","value"]);O()(["value","disabled"]);var Yi=E("collapsible").parts("root","trigger","content");Yi.build();var He=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`collapsible:${e.id}`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`collapsible:${e.id}:content`},getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`collapsible:${e.id}:trigger`},getRootEl:e=>He.getById(e,He.getRootId(e)),getContentEl:e=>He.getById(e,He.getContentId(e)),getTriggerEl:e=>He.getById(e,He.getTriggerId(e))});O()(["dir","disabled","getRootNode","id","ids","onExitComplete","onOpenChange","open.controlled","open"]);const Qe=Math.min,Ae=Math.max,kt=Math.round,gt=Math.floor,ke=e=>({x:e,y:e}),cd={left:"right",right:"left",bottom:"top",top:"bottom"},ud={start:"end",end:"start"};function qt(e,t,o){return Ae(e,Qe(t,o))}function st(e,t){return typeof e=="function"?e(t):e}function Ne(e){return e.split("-")[0]}function lt(e){return e.split("-")[1]}function qi(e){return e==="x"?"y":"x"}function io(e){return e==="y"?"height":"width"}function Pt(e){return["top","bottom"].includes(Ne(e))?"y":"x"}function no(e){return qi(Pt(e))}function gd(e,t,o){o===void 0&&(o=!1);const r=lt(e),i=no(e),n=io(i);let a=i==="x"?r===(o?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[n]>t.floating[n]&&(a=St(a)),[a,St(a)]}function pd(e){const t=St(e);return[Ut(e),t,Ut(t)]}function Ut(e){return e.replace(/start|end/g,t=>ud[t])}function md(e,t,o){const r=["left","right"],i=["right","left"],n=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return o?t?i:r:t?r:i;case"left":case"right":return t?n:a;default:return[]}}function fd(e,t,o,r){const i=lt(e);let n=md(Ne(e),o==="start",r);return i&&(n=n.map(a=>a+"-"+i),t&&(n=n.concat(n.map(Ut)))),n}function St(e){return e.replace(/left|right|bottom|top/g,t=>cd[t])}function hd(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ui(e){return typeof e!="number"?hd(e):{top:e,right:e,bottom:e,left:e}}function wt(e){const{x:t,y:o,width:r,height:i}=e;return{width:r,height:i,top:o,left:t,right:t+r,bottom:o+i,x:t,y:o}}function Fo(e,t,o){let{reference:r,floating:i}=e;const n=Pt(t),a=no(t),s=io(a),d=Ne(t),c=n==="y",l=r.x+r.width/2-i.width/2,u=r.y+r.height/2-i.height/2,g=r[s]/2-i[s]/2;let p;switch(d){case"top":p={x:l,y:r.y-i.height};break;case"bottom":p={x:l,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:u};break;case"left":p={x:r.x-i.width,y:u};break;default:p={x:r.x,y:r.y}}switch(lt(t)){case"start":p[a]-=g*(o&&c?-1:1);break;case"end":p[a]+=g*(o&&c?-1:1);break}return p}const bd=async(e,t,o)=>{const{placement:r="bottom",strategy:i="absolute",middleware:n=[],platform:a}=o,s=n.filter(Boolean),d=await(a.isRTL==null?void 0:a.isRTL(t));let c=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:l,y:u}=Fo(c,r,d),g=r,p={},m=0;for(let f=0;f<s.length;f++){const{name:S,fn:k}=s[f],{x,y:I,data:C,reset:_}=await k({x:l,y:u,initialPlacement:r,placement:g,strategy:i,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});l=x??l,u=I??u,p={...p,[S]:{...p[S],...C}},_&&m<=50&&(m++,typeof _=="object"&&(_.placement&&(g=_.placement),_.rects&&(c=_.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:i}):_.rects),{x:l,y:u}=Fo(c,g,d)),f=-1)}return{x:l,y:u,placement:g,strategy:i,middlewareData:p}};async function Xi(e,t){var o;t===void 0&&(t={});const{x:r,y:i,platform:n,rects:a,elements:s,strategy:d}=e,{boundary:c="clippingAncestors",rootBoundary:l="viewport",elementContext:u="floating",altBoundary:g=!1,padding:p=0}=st(t,e),m=Ui(p),S=s[g?u==="floating"?"reference":"floating":u],k=wt(await n.getClippingRect({element:(o=await(n.isElement==null?void 0:n.isElement(S)))==null||o?S:S.contextElement||await(n.getDocumentElement==null?void 0:n.getDocumentElement(s.floating)),boundary:c,rootBoundary:l,strategy:d})),x=u==="floating"?{...a.floating,x:r,y:i}:a.reference,I=await(n.getOffsetParent==null?void 0:n.getOffsetParent(s.floating)),C=await(n.isElement==null?void 0:n.isElement(I))?await(n.getScale==null?void 0:n.getScale(I))||{x:1,y:1}:{x:1,y:1},_=wt(n.convertOffsetParentRelativeRectToViewportRelativeRect?await n.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:x,offsetParent:I,strategy:d}):x);return{top:(k.top-_.top+m.top)/C.y,bottom:(_.bottom-k.bottom+m.bottom)/C.y,left:(k.left-_.left+m.left)/C.x,right:(_.right-k.right+m.right)/C.x}}const vd=e=>({name:"arrow",options:e,async fn(t){const{x:o,y:r,placement:i,rects:n,platform:a,elements:s,middlewareData:d}=t,{element:c,padding:l=0}=st(e,t)||{};if(c==null)return{};const u=Ui(l),g={x:o,y:r},p=no(i),m=io(p),f=await a.getDimensions(c),S=p==="y",k=S?"top":"left",x=S?"bottom":"right",I=S?"clientHeight":"clientWidth",C=n.reference[m]+n.reference[p]-g[p]-n.floating[m],_=g[p]-n.reference[p],B=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c));let R=B?B[I]:0;(!R||!await(a.isElement==null?void 0:a.isElement(B)))&&(R=s.floating[I]||n.floating[m]);const h=C/2-_/2,b=R/2-f[m]/2-1,w=Qe(u[k],b),$=Qe(u[x],b),z=w,N=R-f[m]-$,V=R/2-f[m]/2+h,W=qt(z,V,N),L=!d.arrow&&lt(i)!=null&&V!==W&&n.reference[m]/2-(V<z?w:$)-f[m]/2<0,P=L?V<z?V-z:V-N:0;return{[p]:g[p]+P,data:{[p]:W,centerOffset:V-W-P,...L&&{alignmentOffset:P}},reset:L}}}),yd=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var o,r;const{placement:i,middlewareData:n,rects:a,initialPlacement:s,platform:d,elements:c}=t,{mainAxis:l=!0,crossAxis:u=!0,fallbackPlacements:g,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:f=!0,...S}=st(e,t);if((o=n.arrow)!=null&&o.alignmentOffset)return{};const k=Ne(i),x=Ne(s)===s,I=await(d.isRTL==null?void 0:d.isRTL(c.floating)),C=g||(x||!f?[St(s)]:pd(s));!g&&m!=="none"&&C.push(...fd(s,f,m,I));const _=[s,...C],B=await Xi(t,S),R=[];let h=((r=n.flip)==null?void 0:r.overflows)||[];if(l&&R.push(B[k]),u){const z=gd(i,a,I);R.push(B[z[0]],B[z[1]])}if(h=[...h,{placement:i,overflows:R}],!R.every(z=>z<=0)){var b,w;const z=(((b=n.flip)==null?void 0:b.index)||0)+1,N=_[z];if(N)return{data:{index:z,overflows:h},reset:{placement:N}};let V=(w=h.filter(W=>W.overflows[0]<=0).sort((W,L)=>W.overflows[1]-L.overflows[1])[0])==null?void 0:w.placement;if(!V)switch(p){case"bestFit":{var $;const W=($=h.map(L=>[L.placement,L.overflows.filter(P=>P>0).reduce((P,H)=>P+H,0)]).sort((L,P)=>L[1]-P[1])[0])==null?void 0:$[0];W&&(V=W);break}case"initialPlacement":V=s;break}if(i!==V)return{reset:{placement:V}}}return{}}}};async function xd(e,t){const{placement:o,platform:r,elements:i}=e,n=await(r.isRTL==null?void 0:r.isRTL(i.floating)),a=Ne(o),s=lt(o),d=Pt(o)==="y",c=["left","top"].includes(a)?-1:1,l=n&&d?-1:1,u=st(t,e);let{mainAxis:g,crossAxis:p,alignmentAxis:m}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...u};return s&&typeof m=="number"&&(p=s==="end"?m*-1:m),d?{x:p*l,y:g*c}:{x:g*c,y:p*l}}const kd=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var o,r;const{x:i,y:n,placement:a,middlewareData:s}=t,d=await xd(t,e);return a===((o=s.offset)==null?void 0:o.placement)&&(r=s.arrow)!=null&&r.alignmentOffset?{}:{x:i+d.x,y:n+d.y,data:{...d,placement:a}}}}},Sd=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:o,y:r,placement:i}=t,{mainAxis:n=!0,crossAxis:a=!1,limiter:s={fn:S=>{let{x:k,y:x}=S;return{x:k,y:x}}},...d}=st(e,t),c={x:o,y:r},l=await Xi(t,d),u=Pt(Ne(i)),g=qi(u);let p=c[g],m=c[u];if(n){const S=g==="y"?"top":"left",k=g==="y"?"bottom":"right",x=p+l[S],I=p-l[k];p=qt(x,p,I)}if(a){const S=u==="y"?"top":"left",k=u==="y"?"bottom":"right",x=m+l[S],I=m-l[k];m=qt(x,m,I)}const f=s.fn({...t,[g]:p,[u]:m});return{...f,data:{x:f.x-o,y:f.y-r}}}}};function _t(){return typeof window<"u"}function tt(e){return Ki(e)?(e.nodeName||"").toLowerCase():"#document"}function ue(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Ie(e){var t;return(t=(Ki(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ki(e){return _t()?e instanceof Node||e instanceof ue(e).Node:!1}function fe(e){return _t()?e instanceof Element||e instanceof ue(e).Element:!1}function we(e){return _t()?e instanceof HTMLElement||e instanceof ue(e).HTMLElement:!1}function Lo(e){return!_t()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof ue(e).ShadowRoot}function dt(e){const{overflow:t,overflowX:o,overflowY:r,display:i}=he(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+o)&&!["inline","contents"].includes(i)}function wd(e){return["table","td","th"].includes(tt(e))}function Tt(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function ao(e){const t=so(),o=fe(e)?he(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>o[r]?o[r]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!t&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!t&&(o.filter?o.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(o.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(o.contain||"").includes(r))}function Id(e){let t=$e(e);for(;we(t)&&!Je(t);){if(ao(t))return t;if(Tt(t))return null;t=$e(t)}return null}function so(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Je(e){return["html","body","#document"].includes(tt(e))}function he(e){return ue(e).getComputedStyle(e)}function Et(e){return fe(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function $e(e){if(tt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Lo(e)&&e.host||Ie(e);return Lo(t)?t.host:t}function Zi(e){const t=$e(e);return Je(t)?e.ownerDocument?e.ownerDocument.body:e.body:we(t)&&dt(t)?t:Zi(t)}function nt(e,t,o){var r;t===void 0&&(t=[]),o===void 0&&(o=!0);const i=Zi(e),n=i===((r=e.ownerDocument)==null?void 0:r.body),a=ue(i);if(n){const s=Xt(a);return t.concat(a,a.visualViewport||[],dt(i)?i:[],s&&o?nt(s):[])}return t.concat(i,nt(i,[],o))}function Xt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Qi(e){const t=he(e);let o=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=we(e),n=i?e.offsetWidth:o,a=i?e.offsetHeight:r,s=kt(o)!==n||kt(r)!==a;return s&&(o=n,r=a),{width:o,height:r,$:s}}function lo(e){return fe(e)?e:e.contextElement}function Ze(e){const t=lo(e);if(!we(t))return ke(1);const o=t.getBoundingClientRect(),{width:r,height:i,$:n}=Qi(t);let a=(n?kt(o.width):o.width)/r,s=(n?kt(o.height):o.height)/i;return(!a||!Number.isFinite(a))&&(a=1),(!s||!Number.isFinite(s))&&(s=1),{x:a,y:s}}const zd=ke(0);function Ji(e){const t=ue(e);return!so()||!t.visualViewport?zd:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Cd(e,t,o){return t===void 0&&(t=!1),!o||t&&o!==ue(e)?!1:t}function Ve(e,t,o,r){t===void 0&&(t=!1),o===void 0&&(o=!1);const i=e.getBoundingClientRect(),n=lo(e);let a=ke(1);t&&(r?fe(r)&&(a=Ze(r)):a=Ze(e));const s=Cd(n,o,r)?Ji(n):ke(0);let d=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,l=i.width/a.x,u=i.height/a.y;if(n){const g=ue(n),p=r&&fe(r)?ue(r):r;let m=g,f=Xt(m);for(;f&&r&&p!==m;){const S=Ze(f),k=f.getBoundingClientRect(),x=he(f),I=k.left+(f.clientLeft+parseFloat(x.paddingLeft))*S.x,C=k.top+(f.clientTop+parseFloat(x.paddingTop))*S.y;d*=S.x,c*=S.y,l*=S.x,u*=S.y,d+=I,c+=C,m=ue(f),f=Xt(m)}}return wt({width:l,height:u,x:d,y:c})}function co(e,t){const o=Et(e).scrollLeft;return t?t.left+o:Ve(Ie(e)).left+o}function en(e,t,o){o===void 0&&(o=!1);const r=e.getBoundingClientRect(),i=r.left+t.scrollLeft-(o?0:co(e,r)),n=r.top+t.scrollTop;return{x:i,y:n}}function Rd(e){let{elements:t,rect:o,offsetParent:r,strategy:i}=e;const n=i==="fixed",a=Ie(r),s=t?Tt(t.floating):!1;if(r===a||s&&n)return o;let d={scrollLeft:0,scrollTop:0},c=ke(1);const l=ke(0),u=we(r);if((u||!u&&!n)&&((tt(r)!=="body"||dt(a))&&(d=Et(r)),we(r))){const p=Ve(r);c=Ze(r),l.x=p.x+r.clientLeft,l.y=p.y+r.clientTop}const g=a&&!u&&!n?en(a,d,!0):ke(0);return{width:o.width*c.x,height:o.height*c.y,x:o.x*c.x-d.scrollLeft*c.x+l.x+g.x,y:o.y*c.y-d.scrollTop*c.y+l.y+g.y}}function Pd(e){return Array.from(e.getClientRects())}function _d(e){const t=Ie(e),o=Et(e),r=e.ownerDocument.body,i=Ae(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),n=Ae(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-o.scrollLeft+co(e);const s=-o.scrollTop;return he(r).direction==="rtl"&&(a+=Ae(t.clientWidth,r.clientWidth)-i),{width:i,height:n,x:a,y:s}}function Td(e,t){const o=ue(e),r=Ie(e),i=o.visualViewport;let n=r.clientWidth,a=r.clientHeight,s=0,d=0;if(i){n=i.width,a=i.height;const c=so();(!c||c&&t==="fixed")&&(s=i.offsetLeft,d=i.offsetTop)}return{width:n,height:a,x:s,y:d}}function Ed(e,t){const o=Ve(e,!0,t==="fixed"),r=o.top+e.clientTop,i=o.left+e.clientLeft,n=we(e)?Ze(e):ke(1),a=e.clientWidth*n.x,s=e.clientHeight*n.y,d=i*n.x,c=r*n.y;return{width:a,height:s,x:d,y:c}}function Ho(e,t,o){let r;if(t==="viewport")r=Td(e,o);else if(t==="document")r=_d(Ie(e));else if(fe(t))r=Ed(t,o);else{const i=Ji(e);r={x:t.x-i.x,y:t.y-i.y,width:t.width,height:t.height}}return wt(r)}function tn(e,t){const o=$e(e);return o===t||!fe(o)||Je(o)?!1:he(o).position==="fixed"||tn(o,t)}function Wd(e,t){const o=t.get(e);if(o)return o;let r=nt(e,[],!1).filter(s=>fe(s)&&tt(s)!=="body"),i=null;const n=he(e).position==="fixed";let a=n?$e(e):e;for(;fe(a)&&!Je(a);){const s=he(a),d=ao(a);!d&&s.position==="fixed"&&(i=null),(n?!d&&!i:!d&&s.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||dt(a)&&!d&&tn(e,a))?r=r.filter(l=>l!==a):i=s,a=$e(a)}return t.set(e,r),r}function $d(e){let{element:t,boundary:o,rootBoundary:r,strategy:i}=e;const a=[...o==="clippingAncestors"?Tt(t)?[]:Wd(t,this._c):[].concat(o),r],s=a[0],d=a.reduce((c,l)=>{const u=Ho(t,l,i);return c.top=Ae(u.top,c.top),c.right=Qe(u.right,c.right),c.bottom=Qe(u.bottom,c.bottom),c.left=Ae(u.left,c.left),c},Ho(t,s,i));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function Bd(e){const{width:t,height:o}=Qi(e);return{width:t,height:o}}function Ad(e,t,o){const r=we(t),i=Ie(t),n=o==="fixed",a=Ve(e,!0,n,t);let s={scrollLeft:0,scrollTop:0};const d=ke(0);if(r||!r&&!n)if((tt(t)!=="body"||dt(i))&&(s=Et(t)),r){const g=Ve(t,!0,n,t);d.x=g.x+t.clientLeft,d.y=g.y+t.clientTop}else i&&(d.x=co(i));const c=i&&!r&&!n?en(i,s):ke(0),l=a.left+s.scrollLeft-d.x-c.x,u=a.top+s.scrollTop-d.y-c.y;return{x:l,y:u,width:a.width,height:a.height}}function Nt(e){return he(e).position==="static"}function Go(e,t){if(!we(e)||he(e).position==="fixed")return null;if(t)return t(e);let o=e.offsetParent;return Ie(e)===o&&(o=o.ownerDocument.body),o}function on(e,t){const o=ue(e);if(Tt(e))return o;if(!we(e)){let i=$e(e);for(;i&&!Je(i);){if(fe(i)&&!Nt(i))return i;i=$e(i)}return o}let r=Go(e,t);for(;r&&wd(r)&&Nt(r);)r=Go(r,t);return r&&Je(r)&&Nt(r)&&!ao(r)?o:r||Id(e)||o}const Od=async function(e){const t=this.getOffsetParent||on,o=this.getDimensions,r=await o(e.floating);return{reference:Ad(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Nd(e){return he(e).direction==="rtl"}const Vd={convertOffsetParentRelativeRectToViewportRelativeRect:Rd,getDocumentElement:Ie,getClippingRect:$d,getOffsetParent:on,getElementRects:Od,getClientRects:Pd,getDimensions:Bd,getScale:Ze,isElement:fe,isRTL:Nd};function Dd(e,t){let o=null,r;const i=Ie(e);function n(){var s;clearTimeout(r),(s=o)==null||s.disconnect(),o=null}function a(s,d){s===void 0&&(s=!1),d===void 0&&(d=1),n();const{left:c,top:l,width:u,height:g}=e.getBoundingClientRect();if(s||t(),!u||!g)return;const p=gt(l),m=gt(i.clientWidth-(c+u)),f=gt(i.clientHeight-(l+g)),S=gt(c),x={rootMargin:-p+"px "+-m+"px "+-f+"px "+-S+"px",threshold:Ae(0,Qe(1,d))||1};let I=!0;function C(_){const B=_[0].intersectionRatio;if(B!==d){if(!I)return a();B?a(!1,B):r=setTimeout(()=>{a(!1,1e-7)},1e3)}I=!1}try{o=new IntersectionObserver(C,{...x,root:i.ownerDocument})}catch{o=new IntersectionObserver(C,x)}o.observe(e)}return a(!0),n}function gg(e,t,o,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:n=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:d=!1}=r,c=lo(e),l=i||n?[...c?nt(c):[],...nt(t)]:[];l.forEach(k=>{i&&k.addEventListener("scroll",o,{passive:!0}),n&&k.addEventListener("resize",o)});const u=c&&s?Dd(c,o):null;let g=-1,p=null;a&&(p=new ResizeObserver(k=>{let[x]=k;x&&x.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var I;(I=p)==null||I.observe(t)})),o()}),c&&!d&&p.observe(c),p.observe(t));let m,f=d?Ve(e):null;d&&S();function S(){const k=Ve(e);f&&(k.x!==f.x||k.y!==f.y||k.width!==f.width||k.height!==f.height)&&o(),f=k,m=requestAnimationFrame(S)}return o(),()=>{var k;l.forEach(x=>{i&&x.removeEventListener("scroll",o),n&&x.removeEventListener("resize",o)}),u==null||u(),(k=p)==null||k.disconnect(),p=null,d&&cancelAnimationFrame(m)}}const pg=kd,mg=Sd,fg=yd,hg=vd,bg=(e,t,o)=>{const r=new Map,i={platform:Vd,...o},n={...i.platform,_c:r};return bd(e,t,{...i,platform:n})};var rn=e=>e.ownerDocument.defaultView||window;function jd(e,t){const{type:o="HTMLInputElement",property:r="value"}=t,i=rn(e)[o].prototype;return Object.getOwnPropertyDescriptor(i,r)??{}}function Md(e,t,o={}){var i;(i=jd(e,o).set)==null||i.call(e,t),e.setAttribute("value",t)}function nn(e,t){const{value:o,bubbles:r=!0}=t;if(!e)return;const i=rn(e);e instanceof i.HTMLInputElement&&(Md(e,`${o}`),e.dispatchEvent(new i.Event("input",{bubbles:r})))}var an=E("color-picker",["root","label","control","trigger","positioner","content","area","areaThumb","valueText","areaBackground","channelSlider","channelSliderLabel","channelSliderTrack","channelSliderThumb","channelSliderValueText","channelInput","transparencyGrid","swatchGroup","swatchTrigger","swatchIndicator","swatch","eyeDropperTrigger","formatTrigger","formatSelect"]);an.build();var M=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`color-picker:${e.id}`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`color-picker:${e.id}:label`},getHiddenInputId:e=>{var t;return((t=e.ids)==null?void 0:t.hiddenInput)??`color-picker:${e.id}:hidden-input`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`color-picker:${e.id}:control`},getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`color-picker:${e.id}:trigger`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`color-picker:${e.id}:content`},getPositionerId:e=>{var t;return((t=e.ids)==null?void 0:t.positioner)??`color-picker:${e.id}:positioner`},getFormatSelectId:e=>{var t;return((t=e.ids)==null?void 0:t.formatSelect)??`color-picker:${e.id}:format-select`},getAreaId:e=>{var t;return((t=e.ids)==null?void 0:t.area)??`color-picker:${e.id}:area`},getAreaGradientId:e=>{var t;return((t=e.ids)==null?void 0:t.areaGradient)??`color-picker:${e.id}:area-gradient`},getAreaThumbId:e=>{var t;return((t=e.ids)==null?void 0:t.areaThumb)??`color-picker:${e.id}:area-thumb`},getChannelSliderTrackId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.channelSliderTrack)==null?void 0:r.call(o,t))??`color-picker:${e.id}:slider-track:${t}`},getChannelSliderThumbId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.channelSliderThumb)==null?void 0:r.call(o,t))??`color-picker:${e.id}:slider-thumb:${t}`},getContentEl:e=>M.getById(e,M.getContentId(e)),getAreaThumbEl:e=>M.getById(e,M.getAreaThumbId(e)),getChannelSliderThumbEl:(e,t)=>M.getById(e,M.getChannelSliderThumbId(e,t)),getChannelInputEl:(e,t)=>{const o=`input[data-channel="${t}"]`;return[...Re(M.getContentEl(e),o),...Re(M.getControlEl(e),o)]},getFormatSelectEl:e=>M.getById(e,M.getFormatSelectId(e)),getHiddenInputEl:e=>M.getById(e,M.getHiddenInputId(e)),getAreaEl:e=>M.getById(e,M.getAreaId(e)),getAreaValueFromPoint(e,t){const o=M.getAreaEl(e);if(!o)return;const{percent:r}=Vt(t,o);return r},getControlEl:e=>M.getById(e,M.getControlId(e)),getTriggerEl:e=>M.getById(e,M.getTriggerId(e)),getPositionerEl:e=>M.getById(e,M.getPositionerId(e)),getChannelSliderTrackEl:(e,t)=>M.getById(e,M.getChannelSliderTrackId(e,t)),getChannelSliderValueFromPoint(e,t,o){const r=M.getChannelSliderTrackEl(e,o);if(!r)return;const{percent:i}=Vt(t,r);return i},getChannelInputEls:e=>[...Re(M.getContentEl(e),"input[data-channel]"),...Re(M.getControlEl(e),"input[data-channel]")]}),uo=E("dialog").parts("trigger","backdrop","positioner","content","title","description","closeTrigger");uo.build();var ae=q({getPositionerId:e=>{var t;return((t=e.ids)==null?void 0:t.positioner)??`dialog:${e.id}:positioner`},getBackdropId:e=>{var t;return((t=e.ids)==null?void 0:t.backdrop)??`dialog:${e.id}:backdrop`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`dialog:${e.id}:content`},getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`dialog:${e.id}:trigger`},getTitleId:e=>{var t;return((t=e.ids)==null?void 0:t.title)??`dialog:${e.id}:title`},getDescriptionId:e=>{var t;return((t=e.ids)==null?void 0:t.description)??`dialog:${e.id}:description`},getCloseTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.closeTrigger)??`dialog:${e.id}:close`},getContentEl:e=>ae.getById(e,ae.getContentId(e)),getPositionerEl:e=>ae.getById(e,ae.getPositionerId(e)),getBackdropEl:e=>ae.getById(e,ae.getBackdropId(e)),getTriggerEl:e=>ae.getById(e,ae.getTriggerId(e)),getTitleEl:e=>ae.getById(e,ae.getTitleId(e)),getDescriptionEl:e=>ae.getById(e,ae.getDescriptionId(e)),getCloseTriggerEl:e=>ae.getById(e,ae.getCloseTriggerId(e))});O()(["aria-label","closeOnEscape","closeOnInteractOutside","dir","finalFocusEl","getRootNode","getRootNode","id","id","ids","initialFocusEl","modal","onEscapeKeyDown","onFocusOutside","onInteractOutside","onOpenChange","onPointerDownOutside","open.controlled","open","persistentElements","preventScroll","restoreFocus","role","trapFocus"]);var sn=E("editable").parts("root","area","label","preview","input","editTrigger","submitTrigger","cancelTrigger","control");sn.build();var ve=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`editable:${e.id}`},getAreaId:e=>{var t;return((t=e.ids)==null?void 0:t.area)??`editable:${e.id}:area`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`editable:${e.id}:label`},getPreviewId:e=>{var t;return((t=e.ids)==null?void 0:t.preview)??`editable:${e.id}:preview`},getInputId:e=>{var t;return((t=e.ids)==null?void 0:t.input)??`editable:${e.id}:input`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`editable:${e.id}:control`},getSubmitTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.submitTrigger)??`editable:${e.id}:submit`},getCancelTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.cancelTrigger)??`editable:${e.id}:cancel`},getEditTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.editTrigger)??`editable:${e.id}:edit`},getInputEl:e=>ve.getById(e,ve.getInputId(e)),getPreviewEl:e=>ve.getById(e,ve.getPreviewId(e)),getSubmitTriggerEl:e=>ve.getById(e,ve.getSubmitTriggerId(e)),getCancelTriggerEl:e=>ve.getById(e,ve.getCancelTriggerId(e)),getEditTriggerEl:e=>ve.getById(e,ve.getEditTriggerId(e))});O()(["activationMode","autoResize","dir","disabled","finalFocusEl","form","getRootNode","id","ids","invalid","maxLength","name","onEditChange","onFocusOutside","onInteractOutside","onPointerDownOutside","onValueChange","onValueCommit","onValueRevert","placeholder","readOnly","required","selectOnFocus","edit","edit.controlled","submitMode","translations","value"]);const ln=E("field").parts("root","errorText","helperText","input","label","select","textarea","requiredIndicator");ln.build();const dn=E("fieldset").parts("root","errorText","helperText","legend");dn.build();var cn=E("file-upload").parts("root","dropzone","item","itemDeleteTrigger","itemGroup","itemName","itemPreview","itemPreviewImage","itemSizeText","label","trigger","clearTrigger");cn.build();var Ge=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`file:${e.id}`},getDropzoneId:e=>{var t;return((t=e.ids)==null?void 0:t.dropzone)??`file:${e.id}:dropzone`},getHiddenInputId:e=>{var t;return((t=e.ids)==null?void 0:t.hiddenInput)??`file:${e.id}:input`},getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`file:${e.id}:trigger`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`file:${e.id}:label`},getItemId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.item)==null?void 0:r.call(o,t))??`file:${e.id}:item:${t}`},getItemNameId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemName)==null?void 0:r.call(o,t))??`file:${e.id}:item-name:${t}`},getItemSizeTextId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemSizeText)==null?void 0:r.call(o,t))??`file:${e.id}:item-size:${t}`},getItemPreviewId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemPreview)==null?void 0:r.call(o,t))??`file:${e.id}:item-preview:${t}`},getRootEl:e=>Ge.getById(e,Ge.getRootId(e)),getHiddenInputEl:e=>Ge.getById(e,Ge.getHiddenInputId(e)),getDropzoneEl:e=>Ge.getById(e,Ge.getDropzoneId(e))});O()(["accept","allowDrop","capture","dir","directory","disabled","getRootNode","id","ids","locale","maxFiles","maxFileSize","minFileSize","name","invalid","onFileAccept","onFileReject","onFileChange","preventDocumentDrop","required","translations","validate"]);O()(["file"]);var un=E("menu").parts("arrow","arrowTip","content","contextTrigger","indicator","item","itemGroup","itemGroupLabel","itemIndicator","itemText","positioner","separator","trigger","triggerItem");un.build();var ie=q({getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`menu:${e.id}:trigger`},getContextTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.contextTrigger)??`menu:${e.id}:ctx-trigger`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`menu:${e.id}:content`},getArrowId:e=>{var t;return((t=e.ids)==null?void 0:t.arrow)??`menu:${e.id}:arrow`},getPositionerId:e=>{var t;return((t=e.ids)==null?void 0:t.positioner)??`menu:${e.id}:popper`},getGroupId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.group)==null?void 0:r.call(o,t))??`menu:${e.id}:group:${t}`},getGroupLabelId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.groupLabel)==null?void 0:r.call(o,t))??`menu:${e.id}:group-label:${t}`},getContentEl:e=>ie.getById(e,ie.getContentId(e)),getPositionerEl:e=>ie.getById(e,ie.getPositionerId(e)),getTriggerEl:e=>ie.getById(e,ie.getTriggerId(e)),getHighlightedItemEl:e=>e.highlightedValue?ie.getById(e,e.highlightedValue):null,getArrowEl:e=>ie.getById(e,ie.getArrowId(e)),getElements:e=>{const o=`[role^="menuitem"][data-ownedby=${CSS.escape(ie.getContentId(e))}]:not([data-disabled])`;return Re(ie.getContentEl(e),o)},getFirstEl:e=>ji(ie.getElements(e)),getLastEl:e=>Mi(ie.getElements(e)),getNextEl:(e,t)=>pi(ie.getElements(e),e.highlightedValue,t??e.loopFocus),getPrevEl:(e,t)=>mi(ie.getElements(e),e.highlightedValue,t??e.loopFocus),getElemByKey:(e,t)=>Fa(ie.getElements(e),{state:e.typeaheadState,key:t,activeId:e.highlightedValue}),isTargetDisabled:e=>Ct(e)&&(e.dataset.disabled===""||e.hasAttribute("disabled")),isTriggerItem:e=>{var t;return!!((t=e==null?void 0:e.getAttribute("role"))!=null&&t.startsWith("menuitem"))&&!!(e!=null&&e.hasAttribute("aria-controls"))},getOptionFromItemEl(e){return{id:e.id,name:e.dataset.name,value:e.dataset.value,valueText:e.dataset.valueText,type:e.dataset.type}}});O()(["anchorPoint","aria-label","closeOnSelect","composite","dir","getRootNode","highlightedValue","id","ids","loopFocus","navigate","onEscapeKeyDown","onFocusOutside","onHighlightChange","onInteractOutside","onOpenChange","onPointerDownOutside","onSelect","open.controlled","open","positioning","typeahead"]);O()(["closeOnSelect","disabled","value","valueText"]);O()(["htmlFor"]);O()(["id"]);O()(["disabled","valueText","closeOnSelect","type","value","checked","onCheckedChange"]);var gn=E("popover").parts("arrow","arrowTip","anchor","trigger","indicator","positioner","content","title","description","closeTrigger");gn.build();var se=q({getAnchorId:e=>{var t;return((t=e.ids)==null?void 0:t.anchor)??`popover:${e.id}:anchor`},getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`popover:${e.id}:trigger`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`popover:${e.id}:content`},getPositionerId:e=>{var t;return((t=e.ids)==null?void 0:t.positioner)??`popover:${e.id}:popper`},getArrowId:e=>{var t;return((t=e.ids)==null?void 0:t.arrow)??`popover:${e.id}:arrow`},getTitleId:e=>{var t;return((t=e.ids)==null?void 0:t.title)??`popover:${e.id}:title`},getDescriptionId:e=>{var t;return((t=e.ids)==null?void 0:t.description)??`popover:${e.id}:desc`},getCloseTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.closeTrigger)??`popover:${e.id}:close`},getAnchorEl:e=>se.getById(e,se.getAnchorId(e)),getTriggerEl:e=>se.getById(e,se.getTriggerId(e)),getContentEl:e=>se.getById(e,se.getContentId(e)),getPositionerEl:e=>se.getById(e,se.getPositionerId(e)),getTitleEl:e=>se.getById(e,se.getTitleId(e)),getDescriptionEl:e=>se.getById(e,se.getDescriptionId(e)),getFocusableEls:e=>gi(se.getContentEl(e)),getFirstFocusableEl:e=>se.getFocusableEls(e)[0]});O()(["autoFocus","closeOnEscape","closeOnInteractOutside","dir","getRootNode","id","ids","initialFocusEl","modal","onEscapeKeyDown","onFocusOutside","onInteractOutside","onOpenChange","onPointerDownOutside","open.controlled","open","persistentElements","portalled","positioning"]);var go=E("radio-group").parts("root","label","item","itemText","itemControl","indicator");go.build();var ne=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`radio-group:${e.id}`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`radio-group:${e.id}:label`},getItemId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.item)==null?void 0:r.call(o,t))??`radio-group:${e.id}:radio:${t}`},getItemHiddenInputId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemHiddenInput)==null?void 0:r.call(o,t))??`radio-group:${e.id}:radio:input:${t}`},getItemControlId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemControl)==null?void 0:r.call(o,t))??`radio-group:${e.id}:radio:control:${t}`},getItemLabelId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemLabel)==null?void 0:r.call(o,t))??`radio-group:${e.id}:radio:label:${t}`},getIndicatorId:e=>{var t;return((t=e.ids)==null?void 0:t.indicator)??`radio-group:${e.id}:indicator`},getRootEl:e=>ne.getById(e,ne.getRootId(e)),getItemHiddenInputEl:(e,t)=>ne.getById(e,ne.getItemHiddenInputId(e,t)),getIndicatorEl:e=>ne.getById(e,ne.getIndicatorId(e)),getFirstEnabledInputEl:e=>{var t;return(t=ne.getRootEl(e))==null?void 0:t.querySelector("input:not(:disabled)")},getFirstEnabledAndCheckedInputEl:e=>{var t;return(t=ne.getRootEl(e))==null?void 0:t.querySelector("input:not(:disabled):checked")},getInputEls:e=>{const o=`input[type=radio][data-ownedby='${CSS.escape(ne.getRootId(e))}']:not([disabled])`;return Re(ne.getRootEl(e),o)},getActiveRadioEl:e=>{if(e.value)return ne.getById(e,ne.getItemId(e,e.value))},getOffsetRect:e=>({left:(e==null?void 0:e.offsetLeft)??0,top:(e==null?void 0:e.offsetTop)??0,width:(e==null?void 0:e.offsetWidth)??0,height:(e==null?void 0:e.offsetHeight)??0}),getRectById:(e,t)=>{const o=ne.getById(e,ne.getItemId(e,t));if(o)return ne.resolveRect(ne.getOffsetRect(o))},resolveRect:e=>({width:`${e.width}px`,height:`${e.height}px`,left:`${e.left}px`,top:`${e.top}px`})});O()(["dir","disabled","form","getRootNode","id","ids","name","onValueChange","orientation","readOnly","value"]);O()(["value","disabled","invalid"]);var pn=E("rating-group").parts("root","label","item","control");pn.build();var Ee=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`rating:${e.id}`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`rating:${e.id}:label`},getHiddenInputId:e=>{var t;return((t=e.ids)==null?void 0:t.hiddenInput)??`rating:${e.id}:input`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`rating:${e.id}:control`},getItemId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.item)==null?void 0:r.call(o,t))??`rating:${e.id}:item:${t}`},getRootEl:e=>Ee.getById(e,Ee.getRootId(e)),getControlEl:e=>Ee.getById(e,Ee.getControlId(e)),getRadioEl:(e,t=e.value)=>{const o=`[role=radio][aria-posinset='${Math.ceil(t)}']`;return Aa(Ee.getControlEl(e),o)},getHiddenInputEl:e=>Ee.getById(e,Ee.getHiddenInputId(e)),dispatchChangeEvent:e=>{const t=Ee.getHiddenInputEl(e);t&&nn(t,{value:e.value})}});O()(["allowHalf","autoFocus","count","dir","disabled","form","getRootNode","id","ids","name","onHoverChange","onValueChange","required","readOnly","translations","value"]);O()(["index"]);var mn=E("select").parts("label","positioner","trigger","indicator","clearTrigger","item","itemText","itemIndicator","itemGroup","itemGroupLabel","list","content","root","control","valueText");mn.build();var le=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`select:${e.id}`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`select:${e.id}:content`},getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`select:${e.id}:trigger`},getClearTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.clearTrigger)??`select:${e.id}:clear-trigger`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`select:${e.id}:label`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`select:${e.id}:control`},getItemId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.item)==null?void 0:r.call(o,t))??`select:${e.id}:option:${t}`},getHiddenSelectId:e=>{var t;return((t=e.ids)==null?void 0:t.hiddenSelect)??`select:${e.id}:select`},getPositionerId:e=>{var t;return((t=e.ids)==null?void 0:t.positioner)??`select:${e.id}:positioner`},getItemGroupId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemGroup)==null?void 0:r.call(o,t))??`select:${e.id}:optgroup:${t}`},getItemGroupLabelId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.itemGroupLabel)==null?void 0:r.call(o,t))??`select:${e.id}:optgroup-label:${t}`},getHiddenSelectEl:e=>le.getById(e,le.getHiddenSelectId(e)),getContentEl:e=>le.getById(e,le.getContentId(e)),getControlEl:e=>le.getById(e,le.getControlId(e)),getTriggerEl:e=>le.getById(e,le.getTriggerId(e)),getClearTriggerEl:e=>le.getById(e,le.getClearTriggerId(e)),getPositionerEl:e=>le.getById(e,le.getPositionerId(e)),getHighlightedOptionEl(e){return e.highlightedValue?le.getById(e,le.getItemId(e,e.highlightedValue)):null}});O()(["closeOnSelect","collection","dir","disabled","deselectable","form","getRootNode","highlightedValue","id","ids","invalid","loopFocus","multiple","name","onFocusOutside","onHighlightChange","onInteractOutside","onOpenChange","onPointerDownOutside","onValueChange","open.controlled","open","composite","positioning","required","readOnly","scrollToIndexFn","value"]);O()(["item","persistFocus"]);O()(["id"]);O()(["htmlFor"]);var fn=E("slider").parts("root","label","thumb","valueText","track","range","control","markerGroup","marker","draggingIndicator");fn.build();function Fd(e){const t=e[0],o=e[e.length-1];return[t,o]}function Ld(e){const[t,o]=Fd(e.valuePercent);if(e.valuePercent.length===1){if(e.origin==="center"){const r=e.valuePercent[0]<50,i=r?`${e.valuePercent[0]}%`:"50%",n=r?"50%":`${100-e.valuePercent[0]}%`;return{start:i,end:n}}return{start:"0%",end:`${100-o}%`}}return{start:`${t}%`,end:`${100-o}%`}}function Hd(e){return e.isVertical?{position:"absolute",bottom:"var(--slider-range-start)",top:"var(--slider-range-end)"}:{position:"absolute",[e.isRtl?"right":"left"]:"var(--slider-range-start)",[e.isRtl?"left":"right"]:"var(--slider-range-end)"}}function Gd(e){const{height:t=0}=e.thumbSize??{},o=Ht([e.min,e.max],[-t/2,t/2]);return parseFloat(o(e.value).toFixed(2))}function Yd(e){const{width:t=0}=e.thumbSize??{};if(e.isRtl){const r=Ht([e.max,e.min],[-t/2,t/2]);return-1*parseFloat(r(e.value).toFixed(2))}const o=Ht([e.min,e.max],[-t/2,t/2]);return parseFloat(o(e.value).toFixed(2))}function qd(e,t){if(e.thumbAlignment==="center")return`${t}%`;const o=e.isVertical?Gd(e):Yd(e);return`calc(${t}% - ${o}px)`}function hn(e){let t=Zl(e.value,e.min,e.max)*100;return qd(e,t)}function bn(e){let t="visible";return e.thumbAlignment==="contain"&&!e.hasMeasuredThumbSize&&(t="hidden"),t}function Ud(e,t){const o=e.isVertical?"bottom":"insetInlineStart";return{visibility:bn(e),position:"absolute",transform:"var(--slider-thumb-transform)",[o]:`var(--slider-thumb-offset-${t})`}}function Xd(){return{touchAction:"none",userSelect:"none",WebkitUserSelect:"none",position:"relative"}}function Kd(e){const t=Ld(e);return{...e.value.reduce((r,i,n)=>{const a=hn({...e,value:i});return{...r,[`--slider-thumb-offset-${n}`]:a}},{}),"--slider-thumb-transform":e.isVertical?"translateY(50%)":e.isRtl?"translateX(50%)":"translateX(-50%)","--slider-range-start":t.start,"--slider-range-end":t.end}}function Zd(e,t){return{visibility:bn(e),position:"absolute",pointerEvents:"none",[e.isHorizontal?"insetInlineStart":"bottom"]:hn({...e,value:t}),translate:"var(--tx) var(--ty)","--tx":e.isHorizontal?e.isRtl?"50%":"-50%":"0%","--ty":e.isHorizontal?"0%":"50%"}}function Qd(){return{userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none",position:"relative"}}var Jd={getRootStyle:Kd,getControlStyle:Xd,getThumbStyle:Ud,getRangeStyle:Hd,getMarkerStyle:Zd,getMarkerGroupStyle:Qd},de=q({...Jd,getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`slider:${e.id}`},getThumbId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.thumb)==null?void 0:r.call(o,t))??`slider:${e.id}:thumb:${t}`},getHiddenInputId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.hiddenInput)==null?void 0:r.call(o,t))??`slider:${e.id}:input:${t}`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`slider:${e.id}:control`},getTrackId:e=>{var t;return((t=e.ids)==null?void 0:t.track)??`slider:${e.id}:track`},getRangeId:e=>{var t;return((t=e.ids)==null?void 0:t.range)??`slider:${e.id}:range`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`slider:${e.id}:label`},getValueTextId:e=>{var t;return((t=e.ids)==null?void 0:t.valueText)??`slider:${e.id}:value-text`},getMarkerId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.marker)==null?void 0:r.call(o,t))??`slider:${e.id}:marker:${t}`},getRootEl:e=>de.getById(e,de.getRootId(e)),getThumbEl:(e,t)=>de.getById(e,de.getThumbId(e,t)),getHiddenInputEl:(e,t)=>de.getById(e,de.getHiddenInputId(e,t)),getControlEl:e=>de.getById(e,de.getControlId(e)),getElements:e=>Re(de.getControlEl(e),"[role=slider]"),getFirstEl:e=>de.getElements(e)[0],getRangeEl:e=>de.getById(e,de.getRangeId(e)),getValueFromPoint(e,t){const o=de.getControlEl(e);if(!o)return;const i=Vt(t,o).getPercentValue({orientation:e.orientation,dir:e.dir,inverted:{y:!0}});return Ql(i,e.min,e.max,e.step)},dispatchChangeEvent(e){Array.from(e.value).forEach((o,r)=>{const i=de.getHiddenInputEl(e,r);i&&nn(i,{value:o})})}});O()(["aria-label","aria-labelledby","dir","disabled","form","getAriaValueText","getRootNode","id","ids","invalid","max","min","minStepsBetweenThumbs","name","onFocusChange","onValueChange","onValueChangeEnd","orientation","origin","readOnly","step","thumbAlignment","thumbAlignment","thumbSize","value"]);O()(["index","name"]);var vn=E("switch").parts("root","label","control","thumb");vn.build();var pt=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`switch:${e.id}`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`switch:${e.id}:label`},getThumbId:e=>{var t;return((t=e.ids)==null?void 0:t.thumb)??`switch:${e.id}:thumb`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`switch:${e.id}:control`},getHiddenInputId:e=>{var t;return((t=e.ids)==null?void 0:t.hiddenInput)??`switch:${e.id}:input`},getRootEl:e=>pt.getById(e,pt.getRootId(e)),getHiddenInputEl:e=>pt.getById(e,pt.getHiddenInputId(e))});O()(["checked","dir","disabled","form","getRootNode","id","ids","invalid","label","name","onCheckedChange","readOnly","required","value"]);var yn=E("avatar").parts("root","image","fallback");yn.build();var mt=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`avatar:${e.id}`},getImageId:e=>{var t;return((t=e.ids)==null?void 0:t.image)??`avatar:${e.id}:image`},getFallbackId:e=>{var t;return((t=e.ids)==null?void 0:t.fallback)??`avatar:${e.id}:fallback`},getRootEl:e=>mt.getById(e,mt.getRootId(e)),getImageEl:e=>mt.getById(e,mt.getImageId(e))});O()(["dir","id","ids","onStatusChange","getRootNode"]);var xn=E("checkbox").parts("root","label","control","indicator");xn.build();var ft=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`checkbox:${e.id}`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`checkbox:${e.id}:label`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`checkbox:${e.id}:control`},getHiddenInputId:e=>{var t;return((t=e.ids)==null?void 0:t.hiddenInput)??`checkbox:${e.id}:input`},getRootEl:e=>ft.getById(e,ft.getRootId(e)),getHiddenInputEl:e=>ft.getById(e,ft.getHiddenInputId(e))});O()(["checked","dir","disabled","form","getRootNode","id","ids","invalid","name","onCheckedChange","readOnly","required","value"]);const ec=xn.extendWith("group"),tc=an.extendWith("view");var kn=E("hoverCard").parts("arrow","arrowTip","trigger","positioner","content");kn.build();var Ye=q({getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`hover-card:${e.id}:trigger`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`hover-card:${e.id}:content`},getPositionerId:e=>{var t;return((t=e.ids)==null?void 0:t.positioner)??`hover-card:${e.id}:popper`},getArrowId:e=>{var t;return((t=e.ids)==null?void 0:t.arrow)??`hover-card:${e.id}:arrow`},getTriggerEl:e=>Ye.getById(e,Ye.getTriggerId(e)),getContentEl:e=>Ye.getById(e,Ye.getContentId(e)),getPositionerEl:e=>Ye.getById(e,Ye.getPositionerId(e))});O()(["closeDelay","dir","getRootNode","id","ids","onOpenChange","open.controlled","open","openDelay","positioning"]);var Sn=E("numberInput").parts("root","label","input","control","valueText","incrementTrigger","decrementTrigger","scrubber");Sn.build();var ee=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`number-input:${e.id}`},getInputId:e=>{var t;return((t=e.ids)==null?void 0:t.input)??`number-input:${e.id}:input`},getIncrementTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.incrementTrigger)??`number-input:${e.id}:inc`},getDecrementTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.decrementTrigger)??`number-input:${e.id}:dec`},getScrubberId:e=>{var t;return((t=e.ids)==null?void 0:t.scrubber)??`number-input:${e.id}:scrubber`},getCursorId:e=>`number-input:${e.id}:cursor`,getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`number-input:${e.id}:label`},getInputEl:e=>ee.getById(e,ee.getInputId(e)),getIncrementTriggerEl:e=>ee.getById(e,ee.getIncrementTriggerId(e)),getDecrementTriggerEl:e=>ee.getById(e,ee.getDecrementTriggerId(e)),getScrubberEl:e=>ee.getById(e,ee.getScrubberId(e)),getCursorEl:e=>ee.getDoc(e).getElementById(ee.getCursorId(e)),getPressedTriggerEl:(e,t=e.hint)=>{let o=null;return t==="increment"&&(o=ee.getIncrementTriggerEl(e)),t==="decrement"&&(o=ee.getDecrementTriggerEl(e)),o},setupVirtualCursor(e){if(!Wa())return ee.createVirtualCursor(e),()=>{var t;(t=ee.getCursorEl(e))==null||t.remove()}},preventTextSelection(e){const t=ee.getDoc(e),o=t.documentElement,r=t.body;return r.style.pointerEvents="none",o.style.userSelect="none",o.style.cursor="ew-resize",()=>{r.style.pointerEvents="",o.style.userSelect="",o.style.cursor="",o.style.length||o.removeAttribute("style"),r.style.length||r.removeAttribute("style")}},getMousemoveValue(e,t){const o=ee.getWin(e),r=Ot(t.movementX,o.devicePixelRatio),i=Ot(t.movementY,o.devicePixelRatio);let n=r>0?"increment":r<0?"decrement":null;e.isRtl&&n==="increment"&&(n="decrement"),e.isRtl&&n==="decrement"&&(n="increment");const a={x:e.scrubberCursorPoint.x+r,y:e.scrubberCursorPoint.y+i},s=o.innerWidth,d=Ot(7.5,o.devicePixelRatio);return a.x=Ul(a.x+d,s)-d,{hint:n,point:a}},createVirtualCursor(e){const t=ee.getDoc(e),o=t.createElement("div");o.className="scrubber--cursor",o.id=ee.getCursorId(e),Object.assign(o.style,{width:"15px",height:"15px",position:"fixed",pointerEvents:"none",left:"0px",top:"0px",zIndex:ka,transform:e.scrubberCursorPoint?`translate3d(${e.scrubberCursorPoint.x}px, ${e.scrubberCursorPoint.y}px, 0px)`:void 0,willChange:"transform"}),o.innerHTML=`
        <svg width="46" height="15" style="left: -15.5px; position: absolute; top: 0; filter: drop-shadow(rgba(0, 0, 0, 0.4) 0px 1px 1.1px);">
          <g transform="translate(2 3)">
            <path fill-rule="evenodd" d="M 15 4.5L 15 2L 11.5 5.5L 15 9L 15 6.5L 31 6.5L 31 9L 34.5 5.5L 31 2L 31 4.5Z" style="stroke-width: 2px; stroke: white;"></path>
            <path fill-rule="evenodd" d="M 15 4.5L 15 2L 11.5 5.5L 15 9L 15 6.5L 31 6.5L 31 9L 34.5 5.5L 31 2L 31 4.5Z"></path>
          </g>
        </svg>`,t.body.appendChild(o)}}),wn=E("pinInput").parts("root","label","input","control");wn.build();var ye=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`pin-input:${e.id}`},getInputId:(e,t)=>{var o,r;return((r=(o=e.ids)==null?void 0:o.input)==null?void 0:r.call(o,t))??`pin-input:${e.id}:${t}`},getHiddenInputId:e=>{var t;return((t=e.ids)==null?void 0:t.hiddenInput)??`pin-input:${e.id}:hidden`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`pin-input:${e.id}:label`},getControlId:e=>{var t;return((t=e.ids)==null?void 0:t.control)??`pin-input:${e.id}:control`},getRootEl:e=>ye.getById(e,ye.getRootId(e)),getInputEls:e=>{const o=`input[data-ownedby=${CSS.escape(ye.getRootId(e))}]`;return Re(ye.getRootEl(e),o)},getInputEl:(e,t)=>ye.getById(e,ye.getInputId(e,t)),getFocusedInputEl:e=>ye.getInputEls(e)[e.focusedIndex],getFirstInputEl:e=>ye.getInputEls(e)[0],getHiddenInputEl:e=>ye.getById(e,ye.getHiddenInputId(e))}),po=E("progress").parts("root","label","track","range","valueText","view","circle","circleTrack","circleRange");po.build();q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`progress-${e.id}`},getTrackId:e=>{var t;return((t=e.ids)==null?void 0:t.track)??`progress-${e.id}-track`},getLabelId:e=>{var t;return((t=e.ids)==null?void 0:t.label)??`progress-${e.id}-label`},getCircleId:e=>{var t;return((t=e.ids)==null?void 0:t.circle)??`progress-${e.id}-circle`}});O()(["dir","getRootNode","id","ids","max","min","orientation","translations","value","onValueChange"]);var In=E("qr-code").parts("root","frame","pattern","overlay","downloadTrigger");In.build();var Yo=q({getRootId:e=>{var t;return((t=e.ids)==null?void 0:t.root)??`qrcode:${e.id}:root`},getFrameId:e=>{var t;return((t=e.ids)==null?void 0:t.frame)??`qrcode:${e.id}:frame`},getFrameEl:e=>Yo.getById(e,Yo.getFrameId(e))});O()(["ids","value","id","encoding","dir","getRootNode","onValueChange"]);const zn=go.rename("segment-group");zn.build();var Cn=E("tooltip").parts("trigger","arrow","arrowTip","positioner","content");Cn.build();var We=q({getTriggerId:e=>{var t;return((t=e.ids)==null?void 0:t.trigger)??`tooltip:${e.id}:trigger`},getContentId:e=>{var t;return((t=e.ids)==null?void 0:t.content)??`tooltip:${e.id}:content`},getArrowId:e=>{var t;return((t=e.ids)==null?void 0:t.arrow)??`tooltip:${e.id}:arrow`},getPositionerId:e=>{var t;return((t=e.ids)==null?void 0:t.positioner)??`tooltip:${e.id}:popper`},getTriggerEl:e=>We.getById(e,We.getTriggerId(e)),getContentEl:e=>We.getById(e,We.getContentId(e)),getPositionerEl:e=>We.getById(e,We.getPositionerId(e)),getArrowEl:e=>We.getById(e,We.getArrowId(e))});Hi({id:null,prevId:null,setId(e){this.prevId=this.id,this.id=e}});O()(["aria-label","closeDelay","closeOnEscape","closeOnPointerDown","closeOnScroll","closeOnClick","dir","disabled","getRootNode","id","ids","interactive","onOpenChange","open.controlled","open","openDelay","positioning"]);const oc=Gi.extendWith("itemBody"),rc=E("action-bar").parts("positioner","content","separator","selectionTrigger","closeTrigger"),ic=E("alert").parts("title","description","root","indicator","content"),nc=E("breadcrumb").parts("link","currentLink","item","list","root","ellipsis","separator"),ac=E("blockquote").parts("root","icon","content","caption"),sc=E("card").parts("root","header","body","footer","title","description"),lc=E("checkbox-card",["root","control","label","description","addon","indicator","content"]),dc=E("data-list").parts("root","item","itemLabel","itemValue"),cc=uo.extendWith("header","body","footer","backdrop"),uc=uo.extendWith("header","body","footer","backdrop"),gc=sn.extendWith("textarea"),pc=E("empty-state",["root","content","indicator","title","description"]),mc=ln.extendWith("requiredIndicator"),fc=dn.extendWith("content"),hc=cn.extendWith("itemContent","dropzoneContent"),bc=E("list").parts("root","item","indicator"),vc=un.extendWith("itemCommand"),yc=E("select").parts("root","field","indicator"),xc=gn.extendWith("header","body","footer"),Rn=go.extendWith("itemAddon","itemIndicator"),kc=Rn.extendWith("itemContent","itemDescription"),Sc=pn.extendWith("itemIndicator"),wc=mn.extendWith("indicatorGroup"),Ic=fn.extendWith("markerIndicator"),zc=E("stat").parts("root","label","helpText","valueText","valueUnit","indicator"),Cc=E("status").parts("root","indicator"),Rc=E("steps",["root","list","item","trigger","indicator","separator","content","title","description","nextTrigger","prevTrigger","progress"]),Pc=vn.extendWith("indicator"),_c=E("table").parts("root","header","body","row","columnHeader","cell","footer","caption"),Tc=E("toast").parts("root","title","description","indicator","closeTrigger","actionTrigger"),Ec=E("tabs").parts("root","trigger","list","content","contentGroup","indicator"),Wc=E("tag").parts("root","label","closeTrigger","startElement","endElement"),$c=E("timeline").parts("root","item","content","separator","indicator","connector","title","description"),Bc=T({className:"chakra-accordion",slots:oc.keys(),base:{root:{width:"full","--accordion-radius":"radii.l2"},item:{overflowAnchor:"none"},itemTrigger:{display:"flex",alignItems:"center",width:"full",outline:"0",gap:"3",fontWeight:"medium",borderRadius:"var(--accordion-radius)",_focusVisible:{outline:"2px solid",outlineColor:"colorPalette.focusRing"},_disabled:{layerStyle:"disabled"}},itemBody:{pt:"var(--accordion-padding-y)",pb:"calc(var(--accordion-padding-y) * 2)"},itemContent:{overflow:"hidden",borderRadius:"var(--accordion-radius)",_open:{animationName:"expand-height, fade-in",animationDuration:"moderate"},_closed:{animationName:"collapse-height, fade-out",animationDuration:"moderate"}},itemIndicator:{transition:"rotate 0.2s",transformOrigin:"center",color:"fg.subtle",_open:{rotate:"180deg"},_icon:{width:"1.2em",height:"1.2em"}}},variants:{variant:{outline:{item:{borderBottomWidth:"1px"}},subtle:{itemTrigger:{px:"var(--accordion-padding-x)"},itemContent:{px:"var(--accordion-padding-x)"},item:{borderRadius:"var(--accordion-radius)",_open:{bg:"colorPalette.subtle"}}},enclosed:{root:{borderWidth:"1px",borderRadius:"var(--accordion-radius)",divideY:"1px",overflow:"hidden"},itemTrigger:{px:"var(--accordion-padding-x)"},itemContent:{px:"var(--accordion-padding-x)"},item:{_open:{bg:"bg.subtle"}}},plain:{}},size:{sm:{root:{"--accordion-padding-x":"spacing.3","--accordion-padding-y":"spacing.2"},itemTrigger:{textStyle:"sm",py:"var(--accordion-padding-y)"}},md:{root:{"--accordion-padding-x":"spacing.4","--accordion-padding-y":"spacing.2"},itemTrigger:{textStyle:"md",py:"var(--accordion-padding-y)"}},lg:{root:{"--accordion-padding-x":"spacing.4.5","--accordion-padding-y":"spacing.2.5"},itemTrigger:{textStyle:"lg",py:"var(--accordion-padding-y)"}}}},defaultVariants:{size:"md",variant:"outline"}}),Ac=T({className:"chakra-action-bar",slots:rc.keys(),base:{positioner:{position:"fixed",display:"flex",justifyContent:"center",pointerEvents:"none",insetInline:"0",top:"unset",bottom:"calc(env(safe-area-inset-bottom) + 20px)"},content:{bg:"bg.panel",shadow:"md",display:"flex",alignItems:"center",gap:"3",borderRadius:"l3",py:"2.5",px:"3",pointerEvents:"auto",translate:"calc(-1 * var(--scrollbar-width) / 2) 0px",_open:{animationName:"slide-from-bottom, fade-in",animationDuration:"moderate"},_closed:{animationName:"slide-to-bottom, fade-out",animationDuration:"faster"}},separator:{width:"1px",height:"5",bg:"border"},selectionTrigger:{display:"inline-flex",alignItems:"center",gap:"2",alignSelf:"stretch",textStyle:"sm",px:"4",py:"1",borderRadius:"l2",borderWidth:"1px",borderStyle:"dashed"}}}),Oc=T({slots:ic.keys(),className:"chakra-alert",base:{root:{width:"full",display:"flex",alignItems:"flex-start",position:"relative",borderRadius:"l3"},title:{fontWeight:"medium"},description:{display:"inline"},indicator:{display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:"0",width:"1em",height:"1em",_icon:{boxSize:"full"}},content:{display:"flex",flex:"1",gap:"1"}},variants:{status:{info:{root:{colorPalette:"blue"}},warning:{root:{colorPalette:"orange"}},success:{root:{colorPalette:"green"}},error:{root:{colorPalette:"red"}},neutral:{root:{colorPalette:"gray"}}},inline:{true:{content:{display:"inline-flex",flexDirection:"row",alignItems:"center"}},false:{content:{display:"flex",flexDirection:"column"}}},variant:{subtle:{root:{bg:"colorPalette.subtle",color:"colorPalette.fg"}},surface:{root:{bg:"colorPalette.subtle",color:"colorPalette.fg",shadow:"inset 0 0 0px 1px var(--shadow-color)",shadowColor:"colorPalette.muted"},indicator:{color:"colorPalette.fg"}},outline:{root:{color:"colorPalette.fg",shadow:"inset 0 0 0px 1px var(--shadow-color)",shadowColor:"colorPalette.muted"},indicator:{color:"colorPalette.fg"}},solid:{root:{bg:"colorPalette.solid",color:"colorPalette.contrast"},indicator:{color:"colorPalette.contrast"}}},size:{sm:{root:{gap:"2",px:"3",py:"3",textStyle:"xs"},indicator:{textStyle:"lg"}},md:{root:{gap:"3",px:"4",py:"4",textStyle:"sm"},indicator:{textStyle:"xl"}},lg:{root:{gap:"3",px:"4",py:"4",textStyle:"md"},indicator:{textStyle:"2xl"}}}},defaultVariants:{status:"info",variant:"subtle",size:"md",inline:!1}}),Nc=T({slots:yn.keys(),className:"chakra-avatar",base:{root:{display:"inline-flex",alignItems:"center",justifyContent:"center",fontWeight:"medium",position:"relative",verticalAlign:"top",flexShrink:"0",userSelect:"none",width:"var(--avatar-size)",height:"var(--avatar-size)",fontSize:"var(--avatar-font-size)",borderRadius:"var(--avatar-radius)","&[data-group-item]":{borderWidth:"2px",borderColor:"bg"}},image:{width:"100%",height:"100%",objectFit:"cover",borderRadius:"var(--avatar-radius)"},fallback:{lineHeight:"1",textTransform:"uppercase",fontWeight:"medium",fontSize:"var(--avatar-font-size)",borderRadius:"var(--avatar-radius)"}},variants:{size:{full:{root:{"--avatar-size":"100%","--avatar-font-size":"100%"}},"2xs":{root:{"--avatar-font-size":"fontSizes.2xs","--avatar-size":"sizes.6"}},xs:{root:{"--avatar-font-size":"fontSizes.xs","--avatar-size":"sizes.8"}},sm:{root:{"--avatar-font-size":"fontSizes.sm","--avatar-size":"sizes.9"}},md:{root:{"--avatar-font-size":"fontSizes.md","--avatar-size":"sizes.10"}},lg:{root:{"--avatar-font-size":"fontSizes.md","--avatar-size":"sizes.11"}},xl:{root:{"--avatar-font-size":"fontSizes.lg","--avatar-size":"sizes.12"}},"2xl":{root:{"--avatar-font-size":"fontSizes.xl","--avatar-size":"sizes.16"}}},variant:{solid:{root:{bg:"colorPalette.solid",color:"colorPalette.contrast"}},subtle:{root:{bg:"colorPalette.muted",color:"colorPalette.fg"}},outline:{root:{color:"colorPalette.fg",borderWidth:"1px",borderColor:"colorPalette.muted"}}},shape:{square:{},rounded:{root:{"--avatar-radius":"radii.l3"}},full:{root:{"--avatar-radius":"radii.full"}}},borderless:{true:{root:{"&[data-group-item]":{borderWidth:"0px"}}}}},defaultVariants:{size:"md",shape:"full",variant:"subtle"}}),Vc=T({className:"chakra-blockquote",slots:ac.keys(),base:{root:{position:"relative",display:"flex",flexDirection:"column",gap:"2"},caption:{textStyle:"sm",color:"fg.muted"},icon:{boxSize:"5"}},variants:{justify:{start:{root:{alignItems:"flex-start",textAlign:"start"}},center:{root:{alignItems:"center",textAlign:"center"}},end:{root:{alignItems:"flex-end",textAlign:"end"}}},variant:{subtle:{root:{paddingX:"5",borderStartWidth:"4px",borderStartColor:"colorPalette.muted"},icon:{color:"colorPalette.fg"}},solid:{root:{paddingX:"5",borderStartWidth:"4px",borderStartColor:"colorPalette.solid"},icon:{color:"colorPalette.solid"}},plain:{root:{paddingX:"5"},icon:{color:"colorPalette.solid"}}}},defaultVariants:{variant:"subtle",justify:"start"}}),Dc=T({className:"chakra-breadcrumb",slots:nc.keys(),base:{list:{display:"flex",alignItems:"center",wordBreak:"break-word",color:"fg.muted"},link:{outline:"0",textDecoration:"none",borderRadius:"l1",focusRing:"outside",display:"inline-flex",alignItems:"center",gap:"2"},item:{display:"inline-flex",alignItems:"center"},separator:{color:"fg.muted",opacity:"0.8",_icon:{boxSize:"1em"}},ellipsis:{display:"inline-flex",alignItems:"center",justifyContent:"center",_icon:{boxSize:"1em"}}},variants:{variant:{underline:{link:{color:"colorPalette.fg",textDecoration:"underline",textUnderlineOffset:"0.2em",textDecorationColor:"colorPalette.muted"},currentLink:{color:"colorPalette.fg"}},plain:{link:{color:"fg.muted",_hover:{color:"fg"}},currentLink:{color:"fg"}}},size:{sm:{list:{gap:"1",textStyle:"xs"}},md:{list:{gap:"1.5",textStyle:"sm"}},lg:{list:{gap:"2",textStyle:"md"}}}},defaultVariants:{variant:"plain",size:"md"}}),jc=T({className:"chakra-card",slots:sc.keys(),base:{root:{display:"flex",flexDirection:"column",position:"relative",minWidth:"0",wordWrap:"break-word",borderRadius:"l3",color:"fg",textAlign:"start"},title:{fontWeight:"semibold"},description:{color:"fg.muted",fontSize:"sm"},header:{paddingInline:"var(--card-padding)",paddingTop:"var(--card-padding)",display:"flex",flexDirection:"column",gap:"1.5"},body:{padding:"var(--card-padding)",flex:"1",display:"flex",flexDirection:"column"},footer:{display:"flex",alignItems:"center",gap:"2",paddingInline:"var(--card-padding)",paddingBottom:"var(--card-padding)"}},variants:{size:{sm:{root:{"--card-padding":"spacing.4"},title:{textStyle:"md"}},md:{root:{"--card-padding":"spacing.6"},title:{textStyle:"lg"}},lg:{root:{"--card-padding":"spacing.7"},title:{textStyle:"xl"}}},variant:{elevated:{root:{bg:"bg.panel",boxShadow:"md"}},outline:{root:{bg:"bg.panel",borderWidth:"1px",borderColor:"border"}},subtle:{root:{bg:"bg.muted"}}}},defaultVariants:{variant:"outline",size:"md"}});var Zo,Qo,Jo,er,tr,or,rr,ir,nr,ar,sr,lr,dr,cr;const Mc=T({slots:ec.keys(),className:"chakra-checkbox",base:{root:{display:"inline-flex",gap:"2",alignItems:"center",verticalAlign:"top",position:"relative"},control:oe.base,label:{fontWeight:"medium",userSelect:"none",_disabled:{opacity:"0.5"}}},variants:{size:{xs:{root:{gap:"1.5"},label:{textStyle:"xs"},control:(Qo=(Zo=oe.variants)==null?void 0:Zo.size)==null?void 0:Qo.xs},sm:{root:{gap:"2"},label:{textStyle:"sm"},control:(er=(Jo=oe.variants)==null?void 0:Jo.size)==null?void 0:er.sm},md:{root:{gap:"2.5"},label:{textStyle:"sm"},control:(or=(tr=oe.variants)==null?void 0:tr.size)==null?void 0:or.md},lg:{root:{gap:"3"},label:{textStyle:"md"},control:(ir=(rr=oe.variants)==null?void 0:rr.size)==null?void 0:ir.lg}},variant:{outline:{control:(ar=(nr=oe.variants)==null?void 0:nr.variant)==null?void 0:ar.outline},solid:{control:(lr=(sr=oe.variants)==null?void 0:sr.variant)==null?void 0:lr.solid},subtle:{control:(cr=(dr=oe.variants)==null?void 0:dr.variant)==null?void 0:cr.subtle}}},defaultVariants:{variant:"solid",size:"md"}});var ur,gr,pr,mr,fr,hr,br;const Fc=T({slots:lc.keys(),className:"chakra-checkbox-card",base:{root:{display:"flex",flexDirection:"column",userSelect:"none",position:"relative",borderRadius:"l2",flex:"1",focusVisibleRing:"outside",_disabled:{opacity:"0.8",borderColor:"border.subtle"},_invalid:{outline:"2px solid",outlineColor:"border.error"}},control:{display:"inline-flex",flex:"1",position:"relative",borderRadius:"inherit",justifyContent:"var(--checkbox-card-justify)",alignItems:"var(--checkbox-card-align)"},label:{fontWeight:"medium",display:"flex",alignItems:"center",gap:"2",_disabled:{opacity:"0.5"}},description:{opacity:"0.64",textStyle:"sm"},addon:{_disabled:{opacity:"0.5"}},indicator:oe.base,content:{display:"flex",flexDirection:"column",flex:"1",gap:"1",justifyContent:"var(--checkbox-card-justify)",alignItems:"var(--checkbox-card-align)"}},variants:{size:{sm:{root:{textStyle:"sm"},control:{padding:"3",gap:"1.5"},addon:{px:"3",py:"1.5",borderTopWidth:"1px"},indicator:(ur=oe.variants)==null?void 0:ur.size.sm},md:{root:{textStyle:"sm"},control:{padding:"4",gap:"2.5"},addon:{px:"4",py:"2",borderTopWidth:"1px"},indicator:(gr=oe.variants)==null?void 0:gr.size.md},lg:{root:{textStyle:"md"},control:{padding:"4",gap:"3.5"},addon:{px:"4",py:"2",borderTopWidth:"1px"},indicator:(pr=oe.variants)==null?void 0:pr.size.lg}},variant:{surface:{root:{borderWidth:"1px",borderColor:"border",_checked:{bg:"colorPalette.subtle",color:"colorPalette.fg",borderColor:"colorPalette.muted"},_disabled:{bg:"bg.muted"}},indicator:(mr=oe.variants)==null?void 0:mr.variant.solid},subtle:{root:{bg:"bg.muted"},control:{_checked:{bg:"colorPalette.muted",color:"colorPalette.fg"}},indicator:(fr=oe.variants)==null?void 0:fr.variant.plain},outline:{root:{borderWidth:"1px",borderColor:"border",_checked:{boxShadow:"0 0 0 1px var(--shadow-color)",boxShadowColor:"colorPalette.solid",borderColor:"colorPalette.solid"}},indicator:(hr=oe.variants)==null?void 0:hr.variant.solid},solid:{root:{borderWidth:"1px",_checked:{bg:"colorPalette.solid",color:"colorPalette.contrast",borderColor:"colorPalette.solid"}},indicator:(br=oe.variants)==null?void 0:br.variant.inverted}},justify:{start:{root:{"--checkbox-card-justify":"flex-start"}},end:{root:{"--checkbox-card-justify":"flex-end"}},center:{root:{"--checkbox-card-justify":"center"}}},align:{start:{root:{"--checkbox-card-align":"flex-start"},content:{textAlign:"start"}},end:{root:{"--checkbox-card-align":"flex-end"},content:{textAlign:"end"}},center:{root:{"--checkbox-card-align":"center"},content:{textAlign:"center"}}},orientation:{vertical:{control:{flexDirection:"column"}},horizontal:{control:{flexDirection:"row"}}}},defaultVariants:{size:"md",variant:"outline",align:"start",orientation:"horizontal"}}),Lc=T({slots:Yi.keys(),className:"chakra-collapsible",base:{content:{overflow:"hidden",_open:{animationName:"expand-height, fade-in",animationDuration:"moderate"},_closed:{animationName:"collapse-height, fade-out",animationDuration:"moderate"}}}});var vr,yr,xr,kr,Sr,wr,Ir,zr,Cr,Rr,Pr,_r,Tr,Er,Wr,$r,Br,Ar;const Hc=T({className:"colorPicker",slots:tc.keys(),base:{root:{display:"flex",flexDirection:"column",gap:"1.5"},label:{color:"fg",fontWeight:"medium",textStyle:"sm"},valueText:{textAlign:"start"},control:{display:"flex",alignItems:"center",flexDirection:"row",gap:"2",position:"relative"},swatchTrigger:{display:"flex",alignItems:"center",justifyContent:"center"},trigger:{display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"row",flexShrink:"0",gap:"2",textStyle:"sm",minH:"var(--input-height)",minW:"var(--input-height)",px:"1",rounded:"l2",_disabled:{opacity:"0.5"},"--focus-color":"colors.colorPalette.focusRing","&:focus-visible":{borderColor:"var(--focus-color)",outline:"1px solid var(--focus-color)"},"&[data-fit-content]":{"--input-height":"unset",px:"0",border:"0"}},content:{display:"flex",flexDirection:"column",bg:"bg.panel",borderRadius:"l3",boxShadow:"lg",width:"64",p:"4",gap:"3",zIndex:"dropdown",_open:{animationStyle:"slide-fade-in",animationDuration:"fast"},_closed:{animationStyle:"slide-fade-out",animationDuration:"faster"}},area:{height:"180px",borderRadius:"l2",overflow:"hidden"},areaThumb:{borderRadius:"full",height:"var(--thumb-size)",width:"var(--thumb-size)",borderWidth:"2px",borderColor:"white",shadow:"sm",focusVisibleRing:"mixed",focusRingColor:"white"},areaBackground:{height:"full"},channelSlider:{borderRadius:"l2",flex:"1"},channelSliderTrack:{height:"var(--slider-height)",borderRadius:"inherit",boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.1)"},swatchGroup:{display:"flex",flexDirection:"row",flexWrap:"wrap",gap:"2"},swatch:{...Di.base,borderRadius:"l1"},swatchIndicator:{color:"white",rounded:"full"},channelSliderThumb:{borderRadius:"full",height:"var(--thumb-size)",width:"var(--thumb-size)",borderWidth:"2px",borderColor:"white",shadow:"sm",transform:"translate(-50%, -50%)",focusVisibleRing:"outside",focusRingOffset:"1px"},channelInput:{...U.base,"&::-webkit-inner-spin-button, &::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:0}},formatSelect:{textStyle:"xs",textTransform:"uppercase",borderWidth:"1px",minH:"6",focusRing:"inside",rounded:"l2"},transparencyGrid:{borderRadius:"l2"},view:{display:"flex",flexDirection:"column",gap:"2"}},variants:{size:{"2xs":{channelInput:(yr=(vr=U.variants)==null?void 0:vr.size)==null?void 0:yr["2xs"],swatch:{"--swatch-size":"sizes.4.5"},trigger:{"--input-height":"sizes.7"},area:{"--thumb-size":"sizes.3"},channelSlider:{"--slider-height":"sizes.3","--thumb-size":"sizes.3"}},xs:{channelInput:(kr=(xr=U.variants)==null?void 0:xr.size)==null?void 0:kr.xs,swatch:{"--swatch-size":"sizes.5"},trigger:{"--input-height":"sizes.8"},area:{"--thumb-size":"sizes.3.5"},channelSlider:{"--slider-height":"sizes.3.5","--thumb-size":"sizes.3.5"}},sm:{channelInput:(wr=(Sr=U.variants)==null?void 0:Sr.size)==null?void 0:wr.sm,swatch:{"--swatch-size":"sizes.6"},trigger:{"--input-height":"sizes.9"},area:{"--thumb-size":"sizes.3.5"},channelSlider:{"--slider-height":"sizes.3.5","--thumb-size":"sizes.3.5"}},md:{channelInput:(zr=(Ir=U.variants)==null?void 0:Ir.size)==null?void 0:zr.md,swatch:{"--swatch-size":"sizes.7"},trigger:{"--input-height":"sizes.10"},area:{"--thumb-size":"sizes.3.5"},channelSlider:{"--slider-height":"sizes.3.5","--thumb-size":"sizes.3.5"}},lg:{channelInput:(Rr=(Cr=U.variants)==null?void 0:Cr.size)==null?void 0:Rr.lg,swatch:{"--swatch-size":"sizes.7"},trigger:{"--input-height":"sizes.11"},area:{"--thumb-size":"sizes.3.5"},channelSlider:{"--slider-height":"sizes.3.5","--thumb-size":"sizes.3.5"}},xl:{channelInput:(_r=(Pr=U.variants)==null?void 0:Pr.size)==null?void 0:_r.xl,swatch:{"--swatch-size":"sizes.8"},trigger:{"--input-height":"sizes.12"},area:{"--thumb-size":"sizes.3.5"},channelSlider:{"--slider-height":"sizes.3.5","--thumb-size":"sizes.3.5"}},"2xl":{channelInput:(Er=(Tr=U.variants)==null?void 0:Tr.size)==null?void 0:Er["2xl"],swatch:{"--swatch-size":"sizes.10"},trigger:{"--input-height":"sizes.16"},area:{"--thumb-size":"sizes.3.5"},channelSlider:{"--slider-height":"sizes.3.5","--thumb-size":"sizes.3.5"}}},variant:{outline:{channelInput:($r=(Wr=U.variants)==null?void 0:Wr.variant)==null?void 0:$r.outline,trigger:{borderWidth:"1px"}},subtle:{channelInput:(Ar=(Br=U.variants)==null?void 0:Br.variant)==null?void 0:Ar.subtle,trigger:{borderWidth:"1px",borderColor:"transparent",bg:"bg.muted"}}}},defaultVariants:{size:"md",variant:"outline"}}),Gc=T({slots:dc.keys(),className:"chakra-data-list",base:{itemLabel:{display:"flex",alignItems:"center",gap:"1"},itemValue:{display:"flex",minWidth:"0",flex:"1"}},variants:{orientation:{horizontal:{root:{display:"flex",flexDirection:"column"},item:{display:"inline-flex",alignItems:"center",gap:"4"},itemLabel:{minWidth:"120px"}},vertical:{root:{display:"flex",flexDirection:"column"},item:{display:"flex",flexDirection:"column",gap:"1"}}},size:{sm:{root:{gap:"3"},item:{textStyle:"xs"}},md:{root:{gap:"4"},item:{textStyle:"sm"}},lg:{root:{gap:"5"},item:{textStyle:"md"}}},variant:{subtle:{itemLabel:{color:"fg.muted"}},bold:{itemLabel:{fontWeight:"medium"},itemValue:{color:"fg.muted"}}}},defaultVariants:{size:"md",orientation:"vertical",variant:"subtle"}}),Yc=T({slots:cc.keys(),className:"chakra-dialog",base:{backdrop:{bg:"blackAlpha.500",pos:"fixed",left:0,top:0,w:"100vw",h:"100dvh",zIndex:"modal",_open:{animationName:"fade-in",animationDuration:"slow"},_closed:{animationName:"fade-out",animationDuration:"moderate"}},positioner:{display:"flex",width:"100vw",height:"100dvh",position:"fixed",left:0,top:0,"--dialog-z-index":"zIndex.modal",zIndex:"calc(var(--dialog-z-index) + var(--layer-index, 0))",justifyContent:"center",overscrollBehaviorY:"none"},content:{display:"flex",flexDirection:"column",position:"relative",width:"100%",outline:0,borderRadius:"l3",textStyle:"sm",my:"var(--dialog-margin, var(--dialog-base-margin))","--dialog-z-index":"zIndex.modal",zIndex:"calc(var(--dialog-z-index) + var(--layer-index, 0))",bg:"bg.panel",boxShadow:"lg",_open:{animationDuration:"moderate"},_closed:{animationDuration:"faster"}},header:{flex:0,px:"6",pt:"6",pb:"4"},body:{flex:"1",px:"6",pt:"2",pb:"6"},footer:{display:"flex",alignItems:"center",justifyContent:"flex-end",gap:"3",px:"6",pt:"2",pb:"4"},title:{textStyle:"lg",fontWeight:"semibold"},description:{color:"fg.muted"}},variants:{placement:{center:{positioner:{alignItems:"center"},content:{"--dialog-base-margin":"auto",mx:"auto"}},top:{positioner:{alignItems:"flex-start"},content:{"--dialog-base-margin":"spacing.16",mx:"auto"}},bottom:{positioner:{alignItems:"flex-end"},content:{"--dialog-base-margin":"spacing.16",mx:"auto"}}},scrollBehavior:{inside:{positioner:{overflow:"hidden"},content:{maxH:"calc(100% - 7.5rem)"},body:{overflow:"auto"}},outside:{positioner:{overflow:"auto",pointerEvents:"auto"}}},size:{xs:{content:{maxW:"sm"}},sm:{content:{maxW:"md"}},md:{content:{maxW:"lg"}},lg:{content:{maxW:"2xl"}},xl:{content:{maxW:"4xl"}},cover:{positioner:{padding:"10"},content:{width:"100%",height:"100%","--dialog-margin":"0"}},full:{content:{maxW:"100vw",minH:"100vh","--dialog-margin":"0",borderRadius:"0"}}},motionPreset:{scale:{content:{_open:{animationName:"scale-in, fade-in"},_closed:{animationName:"scale-out, fade-out"}}},"slide-in-bottom":{content:{_open:{animationName:"slide-from-bottom, fade-in"},_closed:{animationName:"slide-to-bottom, fade-out"}}},"slide-in-top":{content:{_open:{animationName:"slide-from-top, fade-in"},_closed:{animationName:"slide-to-top, fade-out"}}},"slide-in-left":{content:{_open:{animationName:"slide-from-left, fade-in"},_closed:{animationName:"slide-to-left, fade-out"}}},"slide-in-right":{content:{_open:{animationName:"slide-from-right, fade-in"},_closed:{animationName:"slide-to-right, fade-out"}}},none:{}}},defaultVariants:{size:"md",scrollBehavior:"outside",placement:"top",motionPreset:"scale"}}),qc=T({slots:uc.keys(),className:"chakra-drawer",base:{backdrop:{bg:"blackAlpha.500",pos:"fixed",insetInlineStart:0,top:0,w:"100vw",h:"100dvh",zIndex:"modal",_open:{animationName:"fade-in",animationDuration:"slow"},_closed:{animationName:"fade-out",animationDuration:"moderate"}},positioner:{display:"flex",width:"100vw",height:"100dvh",position:"fixed",insetInlineStart:0,top:0,zIndex:"modal",overscrollBehaviorY:"none"},content:{display:"flex",flexDirection:"column",position:"relative",width:"100%",outline:0,zIndex:"modal",textStyle:"sm",maxH:"100dvh",color:"inherit",bg:"bg.panel",boxShadow:"lg",_open:{animationDuration:"slowest",animationTimingFunction:"ease-in-smooth"},_closed:{animationDuration:"slower",animationTimingFunction:"ease-in-smooth"}},header:{flex:0,px:"6",pt:"6",pb:"4"},body:{px:"6",py:"2",flex:"1",overflow:"auto"},footer:{display:"flex",alignItems:"center",justifyContent:"flex-end",gap:"3",px:"6",pt:"2",pb:"4"},title:{textStyle:"lg",fontWeight:"semibold"},description:{color:"fg.muted"}},variants:{size:{xs:{content:{maxW:"xs"}},sm:{content:{maxW:"md"}},md:{content:{maxW:"lg"}},lg:{content:{maxW:"2xl"}},xl:{content:{maxW:"4xl"}},full:{content:{maxW:"100vw",h:"100dvh"}}},placement:{start:{positioner:{justifyContent:"flex-start"},content:{_open:{animationName:{base:"slide-from-left-full, fade-in",_rtl:"slide-from-right-full, fade-in"}},_closed:{animationName:{base:"slide-to-left-full, fade-out",_rtl:"slide-to-right-full, fade-out"}}}},end:{positioner:{justifyContent:"flex-end"},content:{_open:{animationName:{base:"slide-from-right-full, fade-in",_rtl:"slide-from-left-full, fade-in"}},_closed:{animationName:{base:"slide-to-right-full, fade-out",_rtl:"slide-to-right-full, fade-out"}}}},top:{positioner:{alignItems:"flex-start"},content:{maxW:"100%",_open:{animationName:"slide-from-top-full, fade-in"},_closed:{animationName:"slide-to-top-full, fade-out"}}},bottom:{positioner:{alignItems:"flex-end"},content:{maxW:"100%",_open:{animationName:"slide-from-bottom-full, fade-in"},_closed:{animationName:"slide-to-bottom-full, fade-out"}}}},contained:{true:{positioner:{padding:"4"},content:{borderRadius:"l3"}}}},defaultVariants:{size:"xs",placement:"end"}}),qo=fi({fontSize:"inherit",fontWeight:"inherit",textAlign:"inherit",bg:"transparent",borderRadius:"l2"}),Uc=T({slots:gc.keys(),className:"chakra-editable",base:{root:{display:"inline-flex",alignItems:"center",position:"relative",gap:"1.5",width:"full"},preview:{...qo,py:"1",px:"1",display:"inline-flex",alignItems:"center",transitionProperty:"common",transitionDuration:"normal",cursor:"text",_hover:{bg:"bg.muted"},_disabled:{userSelect:"none"}},input:{...qo,outline:"0",py:"1",px:"1",transitionProperty:"common",transitionDuration:"normal",width:"full",focusVisibleRing:"inside",focusRingWidth:"2px",_placeholder:{opacity:.6}},control:{display:"inline-flex",alignItems:"center",gap:"1.5"}},variants:{size:{sm:{root:{textStyle:"sm"},preview:{minH:"8"},input:{minH:"8"}},md:{root:{textStyle:"sm"},preview:{minH:"9"},input:{minH:"9"}},lg:{root:{textStyle:"md"},preview:{minH:"10"},input:{minH:"10"}}}},defaultVariants:{size:"md"}}),Xc=T({slots:pc.keys(),className:"chakra-empty-state",base:{root:{width:"full"},content:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},indicator:{display:"flex",alignItems:"center",justifyContent:"center",color:"fg.subtle",_icon:{boxSize:"1em"}},title:{fontWeight:"semibold"},description:{textStyle:"sm",color:"fg.muted"}},variants:{size:{sm:{root:{px:"4",py:"6"},title:{textStyle:"md"},content:{gap:"4"},indicator:{textStyle:"2xl"}},md:{root:{px:"8",py:"12"},title:{textStyle:"lg"},content:{gap:"6"},indicator:{textStyle:"4xl"}},lg:{root:{px:"12",py:"16"},title:{textStyle:"xl"},content:{gap:"8"},indicator:{textStyle:"6xl"}}}},defaultVariants:{size:"md"}}),Kc=T({className:"chakra-field",slots:mc.keys(),base:{requiredIndicator:{color:"fg.error",lineHeight:"1"},root:{display:"flex",width:"100%",position:"relative",gap:"1.5"},label:{display:"flex",alignItems:"center",textAlign:"start",textStyle:"sm",fontWeight:"medium",gap:"1",userSelect:"none",_disabled:{opacity:"0.5"}},errorText:{display:"inline-flex",alignItems:"center",fontWeight:"medium",gap:"1",color:"fg.error",textStyle:"xs"},helperText:{color:"fg.muted",textStyle:"xs"}},variants:{orientation:{vertical:{root:{flexDirection:"column",alignItems:"flex-start"}},horizontal:{root:{flexDirection:"row",alignItems:"center",justifyContent:"space-between"},label:{flex:"0 0 var(--field-label-width, 80px)"}}}},defaultVariants:{orientation:"vertical"}}),Zc=T({className:"fieldset",slots:fc.keys(),base:{root:{display:"flex",flexDirection:"column",width:"full"},content:{display:"flex",flexDirection:"column",width:"full"},legend:{color:"fg",fontWeight:"medium",_disabled:{opacity:"0.5"}},helperText:{color:"fg.muted",textStyle:"sm"},errorText:{display:"inline-flex",alignItems:"center",color:"fg.error",gap:"2",fontWeight:"medium",textStyle:"sm"}},variants:{size:{sm:{root:{spaceY:"2"},content:{gap:"1.5"},legend:{textStyle:"sm"}},md:{root:{spaceY:"4"},content:{gap:"4"},legend:{textStyle:"sm"}},lg:{root:{spaceY:"6"},content:{gap:"4"},legend:{textStyle:"md"}}}},defaultVariants:{size:"md"}}),Qc=T({className:"chakra-file-upload",slots:hc.keys(),base:{root:{display:"flex",flexDirection:"column",gap:"4",width:"100%",alignItems:"flex-start"},label:{fontWeight:"medium",textStyle:"sm"},dropzone:{background:"bg",borderRadius:"l3",borderWidth:"2px",borderStyle:"dashed",display:"flex",alignItems:"center",flexDirection:"column",gap:"4",justifyContent:"center",minHeight:"2xs",px:"3",py:"2",transition:"backgrounds",focusVisibleRing:"outside",_hover:{bg:"bg.subtle"},_dragging:{bg:"colorPalette.subtle",borderStyle:"solid",borderColor:"colorPalette.solid"}},dropzoneContent:{display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",gap:"1",textStyle:"sm"},item:{textStyle:"sm",animationName:"fade-in",animationDuration:"moderate",background:"bg",borderRadius:"l2",borderWidth:"1px",width:"100%",display:"flex",alignItems:"center",gap:"3",p:"4"},itemGroup:{width:"100%",display:"flex",flexDirection:"column",gap:"3"},itemName:{color:"fg",fontWeight:"medium",lineClamp:"1"},itemContent:{display:"flex",flexDirection:"column",gap:"0.5",flex:"1"},itemSizeText:{color:"fg.muted",textStyle:"xs"},itemDeleteTrigger:{alignSelf:"flex-start"},itemPreviewImage:{width:"10",height:"10",objectFit:"scale-down"}},defaultVariants:{}}),Jc=T({className:"chakra-hover-card",slots:kn.keys(),base:{content:{position:"relative",display:"flex",flexDirection:"column",textStyle:"sm","--hovercard-bg":"colors.bg.panel",bg:"var(--hovercard-bg)",boxShadow:"lg",maxWidth:"80",borderRadius:"l3",zIndex:"popover",transformOrigin:"var(--transform-origin)",outline:"0",_open:{animationStyle:"slide-fade-in",animationDuration:"fast"},_closed:{animationStyle:"slide-fade-out",animationDuration:"faster"}},arrow:{"--arrow-size":"sizes.3","--arrow-background":"var(--hovercard-bg)"},arrowTip:{borderTopWidth:"0.5px",borderInlineStartWidth:"0.5px"}},variants:{size:{xs:{content:{padding:"3"}},sm:{content:{padding:"4"}},md:{content:{padding:"5"}},lg:{content:{padding:"6"}}}},defaultVariants:{size:"md"}}),eu=T({className:"chakra-list",slots:bc.keys(),base:{root:{display:"flex",flexDirection:"column",gap:"var(--list-gap)","& :where(ul, ol)":{marginTop:"var(--list-gap)"}},item:{whiteSpace:"normal",display:"list-item"},indicator:{marginEnd:"2",minHeight:"1lh",flexShrink:0,display:"inline-block",verticalAlign:"middle"}},variants:{variant:{marker:{root:{listStyle:"revert",listStylePosition:"inside"},item:{_marker:{color:"fg.subtle"}}},plain:{item:{alignItems:"flex-start",display:"inline-flex"}}},align:{center:{item:{alignItems:"center"}},start:{item:{alignItems:"flex-start"}},end:{item:{alignItems:"flex-end"}}}},defaultVariants:{variant:"marker"}}),tu=T({className:"chakra-menu",slots:vc.keys(),base:{content:{outline:0,bg:"bg.panel",boxShadow:"lg",color:"fg",maxHeight:"var(--available-height)","--menu-z-index":"zIndex.dropdown",zIndex:"calc(var(--menu-z-index) + var(--layer-index, 0))",borderRadius:"l2",overflow:"hidden",overflowY:"auto",_open:{animationStyle:"slide-fade-in",animationDuration:"fast"},_closed:{animationStyle:"slide-fade-out",animationDuration:"faster"}},item:{textDecoration:"none",color:"fg",userSelect:"none",borderRadius:"l1",width:"100%",display:"flex",cursor:"menuitem",alignItems:"center",textAlign:"start",position:"relative",flex:"0 0 auto",outline:0,_disabled:{layerStyle:"disabled"}},itemText:{flex:"1"},itemGroupLabel:{px:"2",py:"1.5",fontWeight:"semibold",textStyle:"sm"},indicator:{display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:"0"},itemCommand:{opacity:"0.6",textStyle:"xs",ms:"auto",ps:"4",letterSpacing:"widest"},separator:{height:"1px",bg:"bg.muted",my:"1",mx:"-1"}},variants:{variant:{subtle:{item:{_highlighted:{bg:"bg.emphasized/60"}}},solid:{item:{_highlighted:{bg:"colorPalette.solid",color:"colorPalette.contrast"}}}},size:{sm:{content:{minW:"8rem",padding:"1"},item:{gap:"1",textStyle:"xs",py:"1",px:"1.5"}},md:{content:{minW:"8rem",padding:"1.5"},item:{gap:"2",textStyle:"sm",py:"1.5",px:"2"}}}},defaultVariants:{size:"md",variant:"subtle"}}),vt=T({className:"chakra-select",slots:wc.keys(),base:{root:{display:"flex",flexDirection:"column",gap:"1.5",width:"full"},trigger:{display:"flex",alignItems:"center",justifyContent:"space-between",width:"full",minH:"var(--select-trigger-height)",px:"var(--select-trigger-padding-x)",borderRadius:"l2",userSelect:"none",textAlign:"start",focusVisibleRing:"inside",_placeholderShown:{color:"fg.muted"},_disabled:{layerStyle:"disabled"},_invalid:{borderColor:"border.error"}},indicatorGroup:{display:"flex",alignItems:"center",gap:"1",pos:"absolute",right:"0",top:"0",bottom:"0",px:"var(--select-trigger-padding-x)",pointerEvents:"none"},indicator:{display:"flex",alignItems:"center",justifyContent:"center",color:{base:"fg.muted",_disabled:"fg.subtle",_invalid:"fg.error"}},content:{background:"bg.panel",display:"flex",flexDirection:"column",zIndex:"dropdown",borderRadius:"l2",outline:0,maxH:"96",overflowY:"auto",boxShadow:"md",_open:{animationStyle:"slide-fade-in",animationDuration:"fast"},_closed:{animationStyle:"slide-fade-out",animationDuration:"fastest"}},item:{position:"relative",userSelect:"none",display:"flex",alignItems:"center",gap:"2",cursor:"option",justifyContent:"space-between",flex:"1",textAlign:"start",borderRadius:"l1",_highlighted:{bg:"bg.emphasized/60"},_disabled:{pointerEvents:"none",opacity:"0.5"},_icon:{width:"4",height:"4"}},control:{pos:"relative"},itemText:{flex:"1"},itemGroup:{_first:{mt:"0"}},itemGroupLabel:{py:"1",fontWeight:"medium"},label:{fontWeight:"medium",userSelect:"none",textStyle:"sm",_disabled:{layerStyle:"disabled"}},valueText:{lineClamp:"1",maxW:"80%"}},variants:{variant:{outline:{trigger:{bg:"transparent",borderWidth:"1px",borderColor:"border",_expanded:{borderColor:"border.emphasized"}}},subtle:{trigger:{borderWidth:"1px",borderColor:"transparent",bg:"bg.muted"}}},size:{xs:{root:{"--select-trigger-height":"sizes.8","--select-trigger-padding-x":"spacing.2"},content:{p:"1",gap:"1",textStyle:"xs"},trigger:{textStyle:"xs",gap:"1"},item:{py:"1",px:"2"},itemGroupLabel:{py:"1",px:"2"},indicator:{_icon:{width:"3.5",height:"3.5"}}},sm:{root:{"--select-trigger-height":"sizes.9","--select-trigger-padding-x":"spacing.2.5"},content:{p:"1",textStyle:"sm"},trigger:{textStyle:"sm",gap:"1"},indicator:{_icon:{width:"4",height:"4"}},item:{py:"1",px:"1.5"},itemGroup:{mt:"1"},itemGroupLabel:{py:"1",px:"1.5"}},md:{root:{"--select-trigger-height":"sizes.10","--select-trigger-padding-x":"spacing.3"},content:{p:"1",textStyle:"sm"},itemGroup:{mt:"1.5"},item:{py:"1.5",px:"2"},itemIndicator:{display:"flex",alignItems:"center",justifyContent:"center"},itemGroupLabel:{py:"1.5",px:"2"},trigger:{textStyle:"sm",gap:"2"},indicator:{_icon:{width:"4",height:"4"}}},lg:{root:{"--select-trigger-height":"sizes.12","--select-trigger-padding-x":"spacing.4"},content:{p:"1.5",textStyle:"md"},itemGroup:{mt:"2"},item:{py:"2",px:"3"},itemGroupLabel:{py:"2",px:"3"},trigger:{textStyle:"md",py:"3",gap:"2"},indicator:{_icon:{width:"5",height:"5"}}}}},defaultVariants:{size:"md",variant:"outline"}});var Or,Nr;const ou=T({className:"chakra-native-select",slots:yc.keys(),base:{root:{height:"fit-content",display:"flex",width:"100%",position:"relative"},field:{width:"100%",minWidth:"0",outline:"0",appearance:"none",borderRadius:"l2",_disabled:{layerStyle:"disabled"},_invalid:{borderColor:"border.error"},focusVisibleRing:"inside",lineHeight:"normal","& > option, & > optgroup":{bg:"inherit"}},indicator:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)",height:"100%",color:"fg.muted",_disabled:{opacity:"0.5"},_invalid:{color:"fg.error"},_icon:{width:"1em",height:"1em"}}},variants:{variant:{outline:{field:(Or=vt.variants)==null?void 0:Or.variant.outline.trigger},subtle:{field:(Nr=vt.variants)==null?void 0:Nr.variant.subtle.trigger},plain:{field:{bg:"transparent",color:"fg",focusRingWidth:"2px"}}},size:{xs:{field:{textStyle:"xs",ps:"2",pe:"6",height:"6"},indicator:{textStyle:"sm",insetEnd:"1.5"}},sm:{field:{textStyle:"sm",ps:"2.5",pe:"8",height:"8"},indicator:{textStyle:"md",insetEnd:"2"}},md:{field:{textStyle:"sm",ps:"3",pe:"8",height:"10"},indicator:{textStyle:"lg",insetEnd:"2"}},lg:{field:{textStyle:"md",ps:"4",pe:"8",height:"11"},indicator:{textStyle:"xl",insetEnd:"3"}},xl:{field:{textStyle:"md",ps:"4.5",pe:"10",height:"12"},indicator:{textStyle:"xl",insetEnd:"3"}}}},defaultVariants:vt.defaultVariants});function Kt(e,t){const o={};for(const r in e){const i=t(r,e[r]);o[i[0]]=i[1]}return o}const Uo=fi({display:"flex",justifyContent:"center",alignItems:"center",flex:"1",userSelect:"none",cursor:"button",lineHeight:"1",color:"fg.muted","--stepper-base-radius":"radii.l1","--stepper-radius":"calc(var(--stepper-base-radius) + 1px)",_icon:{boxSize:"1em"},_disabled:{opacity:"0.5"},_hover:{bg:"bg.muted"},_active:{bg:"bg.emphasized"}}),ru=T({className:"chakra-number-input",slots:Sn.keys(),base:{root:{position:"relative",zIndex:"0",isolation:"isolate"},input:{...U.base,verticalAlign:"top",pe:"calc(var(--stepper-width) + 0.5rem)"},control:{display:"flex",flexDirection:"column",position:"absolute",top:"0",insetEnd:"0px",margin:"1px",width:"var(--stepper-width)",height:"calc(100% - 2px)",zIndex:"1",borderStartWidth:"1px",divideY:"1px"},incrementTrigger:{...Uo,borderTopEndRadius:"var(--stepper-radius)"},decrementTrigger:{...Uo,borderBottomEndRadius:"var(--stepper-radius)"},valueText:{fontWeight:"medium",fontFeatureSettings:"pnum",fontVariantNumeric:"proportional-nums"}},variants:{size:{xs:{input:U.variants.size.xs,control:{fontSize:"2xs","--stepper-width":"sizes.4"}},sm:{input:U.variants.size.sm,control:{fontSize:"xs","--stepper-width":"sizes.5"}},md:{input:U.variants.size.md,control:{fontSize:"sm","--stepper-width":"sizes.6"}},lg:{input:U.variants.size.lg,control:{fontSize:"sm","--stepper-width":"sizes.6"}}},variant:Kt(U.variants.variant,(e,t)=>[e,{input:t}])},defaultVariants:{size:"md",variant:"outline"}}),{variants:Xo,defaultVariants:iu}=U,nu=T({className:"chakra-pin-input",slots:wn.keys(),base:{input:{...U.base,textAlign:"center",width:"var(--input-height)"}},variants:{size:Kt(Xo.size,(e,t)=>[e,{input:t}]),variant:Kt(Xo.variant,(e,t)=>[e,{input:t}])},defaultVariants:iu}),au=T({className:"chakra-popover",slots:xc.keys(),base:{content:{position:"relative",display:"flex",flexDirection:"column",textStyle:"sm","--popover-bg":"colors.bg.panel",bg:"var(--popover-bg)",boxShadow:"lg","--popover-size":"sizes.xs","--popover-mobile-size":"calc(100dvw - 1rem)",width:{base:"min(var(--popover-mobile-size), var(--popover-size))",sm:"var(--popover-size)"},borderRadius:"l3","--popover-z-index":"zIndex.popover",zIndex:"calc(var(--popover-z-index) + var(--layer-index, 0))",outline:"0",transformOrigin:"var(--transform-origin)",maxHeight:"var(--available-height)",_open:{animationStyle:"scale-fade-in",animationDuration:"fast"},_closed:{animationStyle:"scale-fade-out",animationDuration:"faster"}},header:{paddingInline:"var(--popover-padding)",paddingTop:"var(--popover-padding)"},body:{padding:"var(--popover-padding)",flex:"1"},footer:{display:"flex",alignItems:"center",paddingInline:"var(--popover-padding)",paddingBottom:"var(--popover-padding)"},arrow:{"--arrow-size":"sizes.3","--arrow-background":"var(--popover-bg)"},arrowTip:{borderTopWidth:"1px",borderInlineStartWidth:"1px"}},variants:{size:{xs:{content:{"--popover-padding":"spacing.3"}},sm:{content:{"--popover-padding":"spacing.4"}},md:{content:{"--popover-padding":"spacing.5"}},lg:{content:{"--popover-padding":"spacing.6"}}}},defaultVariants:{size:"md"}}),su=T({slots:po.keys(),className:"chakra-progress",base:{root:{textStyle:"sm",position:"relative"},track:{overflow:"hidden",position:"relative"},range:{display:"flex",alignItems:"center",justifyContent:"center",transitionProperty:"width, height",transitionDuration:"slow",height:"100%",bgColor:"var(--track-color)",_indeterminate:{"--animate-from-x":"-40%","--animate-to-x":"100%",position:"absolute",willChange:"left",minWidth:"50%",animation:"position 1s ease infinite normal none running",backgroundImage:"linear-gradient(to right, transparent 0%, var(--track-color) 50%, transparent 100%)"}},label:{display:"inline-flex",fontWeight:"medium",alignItems:"center",gap:"1"},valueText:{textStyle:"xs",lineHeight:"1",fontWeight:"medium"}},variants:{variant:{outline:{track:{shadow:"inset",bgColor:"bg.muted"},range:{bgColor:"colorPalette.solid"}},subtle:{track:{bgColor:"colorPalette.muted"},range:{bgColor:"colorPalette.solid/72"}}},shape:{square:{},rounded:{track:{borderRadius:"l1"}},full:{track:{borderRadius:"full"}}},striped:{true:{range:{backgroundImage:"linear-gradient(45deg, var(--stripe-color) 25%, transparent 25%, transparent 50%, var(--stripe-color) 50%, var(--stripe-color) 75%, transparent 75%, transparent)",backgroundSize:"var(--stripe-size) var(--stripe-size)","--stripe-size":"1rem","--stripe-color":{_light:"rgba(255, 255, 255, 0.3)",_dark:"rgba(0, 0, 0, 0.3)"}}}},animated:{true:{range:{"--animate-from":"var(--stripe-size)",animation:"bg-position 1s linear infinite"}}},size:{xs:{track:{h:"1.5"}},sm:{track:{h:"2"}},md:{track:{h:"2.5"}},lg:{track:{h:"3"}},xl:{track:{h:"4"}}}},defaultVariants:{variant:"outline",size:"md",shape:"rounded"}}),lu=T({className:"chakra-progress-circle",slots:po.keys(),base:{root:{display:"inline-flex",textStyle:"sm",position:"relative"},circle:{_indeterminate:{animation:"spin 2s linear infinite"}},circleTrack:{"--track-color":"colors.colorPalette.muted",stroke:"var(--track-color)"},circleRange:{stroke:"colorPalette.solid",transitionProperty:"stroke-dasharray",transitionDuration:"0.6s",_indeterminate:{animation:"circular-progress 1.5s linear infinite"}},label:{display:"inline-flex"},valueText:{lineHeight:"1",fontWeight:"medium",letterSpacing:"tight",fontVariantNumeric:"tabular-nums"}},variants:{size:{xs:{circle:{"--size":"24px","--thickness":"4px"},valueText:{textStyle:"2xs"}},sm:{circle:{"--size":"32px","--thickness":"5px"},valueText:{textStyle:"2xs"}},md:{circle:{"--size":"40px","--thickness":"6px"},valueText:{textStyle:"xs"}},lg:{circle:{"--size":"48px","--thickness":"7px"},valueText:{textStyle:"sm"}},xl:{circle:{"--size":"64px","--thickness":"8px"},valueText:{textStyle:"sm"}}}},defaultVariants:{size:"md"}}),du=T({slots:In.keys(),className:"chakra-qr-code",base:{root:{position:"relative",width:"fit-content","--qr-code-overlay-size":"calc(var(--qr-code-size) / 3)"},frame:{width:"var(--qr-code-size)",height:"var(--qr-code-size)",fill:"currentColor"},overlay:{display:"flex",alignItems:"center",justifyContent:"center",width:"var(--qr-code-overlay-size)",height:"var(--qr-code-overlay-size)",padding:"1",bg:"bg",rounded:"l1"}},variants:{size:{"2xs":{root:{"--qr-code-size":"40px"}},xs:{root:{"--qr-code-size":"64px"}},sm:{root:{"--qr-code-size":"80px"}},md:{root:{"--qr-code-size":"120px"}},lg:{root:{"--qr-code-size":"160px"}},xl:{root:{"--qr-code-size":"200px"}},"2xl":{root:{"--qr-code-size":"240px"}},full:{root:{"--qr-code-size":"100%"}}}},defaultVariants:{size:"md"}});var Vr,Dr,jr,Mr,Fr,Lr,Hr;const cu=T({className:"chakra-radio-card",slots:kc.keys(),base:{root:{display:"flex",flexDirection:"column",gap:"1.5",isolation:"isolate"},item:{flex:"1",display:"flex",flexDirection:"column",userSelect:"none",position:"relative",borderRadius:"l2",_focus:{bg:"colorPalette.muted/20"},_disabled:{opacity:"0.8",borderColor:"border.disabled"},_checked:{zIndex:"1"}},label:{display:"inline-flex",fontWeight:"medium",textStyle:"sm",_disabled:{opacity:"0.5"}},itemText:{fontWeight:"medium"},itemDescription:{opacity:"0.64",textStyle:"sm"},itemControl:{display:"inline-flex",flex:"1",pos:"relative",rounded:"inherit",justifyContent:"var(--radio-card-justify)",alignItems:"var(--radio-card-align)",_disabled:{bg:"bg.muted"}},itemIndicator:re.base,itemAddon:{roundedBottom:"inherit",_disabled:{color:"fg.muted"}},itemContent:{display:"flex",flexDirection:"column",flex:"1",gap:"1",justifyContent:"var(--radio-card-justify)",alignItems:"var(--radio-card-align)"}},variants:{size:{sm:{item:{textStyle:"sm"},itemControl:{padding:"3",gap:"1.5"},itemAddon:{px:"3",py:"1.5",borderTopWidth:"1px"},itemIndicator:(Vr=re.variants)==null?void 0:Vr.size.sm},md:{item:{textStyle:"sm"},itemControl:{padding:"4",gap:"2.5"},itemAddon:{px:"4",py:"2",borderTopWidth:"1px"},itemIndicator:(Dr=re.variants)==null?void 0:Dr.size.md},lg:{item:{textStyle:"md"},itemControl:{padding:"4",gap:"3.5"},itemAddon:{px:"4",py:"2",borderTopWidth:"1px"},itemIndicator:(jr=re.variants)==null?void 0:jr.size.lg}},variant:{surface:{item:{borderWidth:"1px",_checked:{bg:"colorPalette.subtle",color:"colorPalette.fg",borderColor:"colorPalette.muted"}},itemIndicator:(Mr=re.variants)==null?void 0:Mr.variant.solid},subtle:{item:{bg:"bg.muted"},itemControl:{_checked:{bg:"colorPalette.muted",color:"colorPalette.fg"}},itemIndicator:(Fr=re.variants)==null?void 0:Fr.variant.outline},outline:{item:{borderWidth:"1px",_checked:{boxShadow:"0 0 0 1px var(--shadow-color)",boxShadowColor:"colorPalette.solid",borderColor:"colorPalette.solid"}},itemIndicator:(Lr=re.variants)==null?void 0:Lr.variant.solid},solid:{item:{borderWidth:"1px",_checked:{bg:"colorPalette.solid",color:"colorPalette.contrast",borderColor:"colorPalette.solid"}},itemIndicator:(Hr=re.variants)==null?void 0:Hr.variant.inverted}},justify:{start:{item:{"--radio-card-justify":"flex-start"}},end:{item:{"--radio-card-justify":"flex-end"}},center:{item:{"--radio-card-justify":"center"}}},align:{start:{item:{"--radio-card-align":"flex-start"},itemControl:{textAlign:"start"}},end:{item:{"--radio-card-align":"flex-end"},itemControl:{textAlign:"end"}},center:{item:{"--radio-card-align":"center"},itemControl:{textAlign:"center"}}},orientation:{vertical:{itemControl:{flexDirection:"column"}},horizontal:{itemControl:{flexDirection:"row"}}}},defaultVariants:{size:"md",variant:"outline",align:"start",orientation:"horizontal"}});var Gr,Yr,qr,Ur,Xr,Kr,Zr,Qr,Jr,ei,ti,oi,ri,ii;const uu=T({className:"chakra-radio-group",slots:Rn.keys(),base:{item:{display:"inline-flex",alignItems:"center",position:"relative",fontWeight:"medium",_disabled:{cursor:"disabled"}},itemControl:re.base,label:{userSelect:"none",textStyle:"sm",_disabled:{opacity:"0.5"}}},variants:{variant:{outline:{itemControl:(Yr=(Gr=re.variants)==null?void 0:Gr.variant)==null?void 0:Yr.outline},subtle:{itemControl:(Ur=(qr=re.variants)==null?void 0:qr.variant)==null?void 0:Ur.subtle},solid:{itemControl:(Kr=(Xr=re.variants)==null?void 0:Xr.variant)==null?void 0:Kr.solid}},size:{xs:{item:{textStyle:"xs",gap:"1.5"},itemControl:(Qr=(Zr=re.variants)==null?void 0:Zr.size)==null?void 0:Qr.xs},sm:{item:{textStyle:"sm",gap:"2"},itemControl:(ei=(Jr=re.variants)==null?void 0:Jr.size)==null?void 0:ei.sm},md:{item:{textStyle:"sm",gap:"2.5"},itemControl:(oi=(ti=re.variants)==null?void 0:ti.size)==null?void 0:oi.md},lg:{item:{textStyle:"md",gap:"3"},itemControl:(ii=(ri=re.variants)==null?void 0:ri.size)==null?void 0:ii.lg}}},defaultVariants:{size:"md",variant:"solid"}}),gu=T({className:"chakra-rating-group",slots:Sc.keys(),base:{root:{display:"inline-flex"},control:{display:"inline-flex",alignItems:"center"},item:{display:"inline-flex",alignItems:"center",justifyContent:"center",userSelect:"none"},itemIndicator:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:"1em",height:"1em",position:"relative",_icon:{stroke:"currentColor",width:"100%",height:"100%",display:"inline-block",flexShrink:0,position:"absolute",left:0,top:0},"& [data-bg]":{color:"bg.emphasized"},"& [data-fg]":{color:"transparent"},"&[data-highlighted]:not([data-half])":{"& [data-fg]":{color:"colorPalette.solid"}},"&[data-half]":{"& [data-fg]":{color:"colorPalette.solid",clipPath:"inset(0 50% 0 0)"}}}},variants:{size:{xs:{item:{textStyle:"sm"}},sm:{item:{textStyle:"md"}},md:{item:{textStyle:"xl"}},lg:{item:{textStyle:"2xl"}}}},defaultVariants:{size:"md"}}),pu=T({className:"chakra-segment-group",slots:zn.keys(),base:{root:{"--segment-radius":"radii.l2",borderRadius:"l2",display:"inline-flex",boxShadow:"inset",minW:"max-content",textAlign:"center",position:"relative",isolation:"isolate",bg:"bg.muted"},item:{display:"flex",alignItems:"center",userSelect:"none",fontSize:"sm",position:"relative",color:"fg",borderRadius:"var(--segment-radius)",_disabled:{opacity:"0.5"},"&:has(input:focus-visible)":{focusRing:"outside"},_before:{content:'""',position:"absolute",insetInlineStart:0,insetBlock:"1.5",bg:"border",width:"1px",transition:"opacity 0.2s"},"& + &[data-state=checked], &[data-state=checked] + &, &:first-of-type":{_before:{opacity:"0"}},"&[data-state=checked][data-ssr]":{shadow:"sm",bg:"bg",borderRadius:"var(--segment-radius)"}},indicator:{shadow:"sm",pos:"absolute",bg:{_light:"bg",_dark:"bg.emphasized"},width:"var(--width)",height:"var(--height)",top:"var(--top)",left:"var(--left)",zIndex:-1,borderRadius:"var(--segment-radius)"}},variants:{size:{xs:{root:{height:"6"},item:{textStyle:"xs",px:"3",gap:"1"}},sm:{root:{height:"8"},item:{textStyle:"sm",px:"4",gap:"2"}},md:{root:{height:"10"},item:{textStyle:"sm",px:"4",gap:"2"}},lg:{root:{height:"10"},item:{textStyle:"md",px:"5",gap:"3"}}}},defaultVariants:{size:"md"}}),mu=T({className:"chakra-slider",slots:Ic.keys(),base:{root:{display:"flex",flexDirection:"column",gap:"1",textStyle:"sm",position:"relative",isolation:"isolate",touchAction:"none"},label:{fontWeight:"medium",textStyle:"sm"},control:{display:"inline-flex",alignItems:"center",position:"relative"},track:{overflow:"hidden",borderRadius:"full",flex:"1"},range:{width:"inherit",height:"inherit",_disabled:{bg:"border.emphasized!"}},markerGroup:{position:"absolute!",zIndex:"1"},marker:{"--marker-bg":{base:"white",_underValue:"colors.bg"},display:"flex",alignItems:"center",gap:"calc(var(--slider-thumb-size) / 2)",color:"fg.muted",textStyle:"xs"},markerIndicator:{width:"var(--slider-marker-size)",height:"var(--slider-marker-size)",borderRadius:"full",bg:"var(--marker-bg)"},thumb:{width:"var(--slider-thumb-size)",height:"var(--slider-thumb-size)",display:"flex",alignItems:"center",justifyContent:"center",outline:0,zIndex:"2",borderRadius:"full",_focusVisible:{ring:"2px",ringColor:"colorPalette.focusRing",ringOffset:"2px",ringOffsetColor:"bg"}}},variants:{size:{sm:{root:{"--slider-thumb-size":"sizes.4","--slider-track-size":"sizes.1.5","--slider-marker-center":"6px","--slider-marker-size":"sizes.1","--slider-marker-inset":"3px"}},md:{root:{"--slider-thumb-size":"sizes.5","--slider-track-size":"sizes.2","--slider-marker-center":"8px","--slider-marker-size":"sizes.1","--slider-marker-inset":"4px"}},lg:{root:{"--slider-thumb-size":"sizes.6","--slider-track-size":"sizes.2.5","--slider-marker-center":"9px","--slider-marker-size":"sizes.1.5","--slider-marker-inset":"5px"}}},variant:{outline:{track:{shadow:"inset",bg:"bg.emphasized/72"},range:{bg:"colorPalette.solid"},thumb:{borderWidth:"2px",borderColor:"colorPalette.solid",bg:"bg",_disabled:{bg:"border.emphasized",borderColor:"border.emphasized"}}},solid:{track:{bg:"colorPalette.subtle",_disabled:{bg:"bg.muted"}},range:{bg:"colorPalette.solid"},thumb:{bg:"colorPalette.solid",_disabled:{bg:"border.emphasized"}}}},orientation:{vertical:{root:{display:"inline-flex"},control:{flexDirection:"column",height:"100%",minWidth:"var(--slider-thumb-size)","&[data-has-mark-label]":{marginEnd:"4"}},track:{width:"var(--slider-track-size)"},thumb:{left:"50%",translate:"-50% 0"},markerGroup:{insetStart:"var(--slider-marker-center)",insetBlock:"var(--slider-marker-inset)"},marker:{flexDirection:"row"}},horizontal:{control:{flexDirection:"row",width:"100%",minHeight:"var(--slider-thumb-size)","&[data-has-mark-label]":{marginBottom:"4"}},track:{height:"var(--slider-track-size)"},thumb:{top:"50%",translate:"0 -50%"},markerGroup:{top:"var(--slider-marker-center)",insetInline:"var(--slider-marker-inset)"},marker:{flexDirection:"column"}}}},defaultVariants:{size:"md",variant:"outline",orientation:"horizontal"}}),fu=T({className:"chakra-stat",slots:zc.keys(),base:{root:{display:"flex",flexDirection:"column",gap:"1",position:"relative",flex:"1"},label:{display:"inline-flex",gap:"1.5",alignItems:"center",color:"fg.muted",textStyle:"sm"},helpText:{color:"fg.muted",textStyle:"xs"},valueUnit:{color:"fg.muted",textStyle:"xs",fontWeight:"initial",letterSpacing:"initial"},valueText:{verticalAlign:"baseline",fontWeight:"semibold",letterSpacing:"tight",fontFeatureSettings:"pnum",fontVariantNumeric:"proportional-nums",display:"inline-flex",gap:"1"},indicator:{display:"inline-flex",alignItems:"center",justifyContent:"center",marginEnd:1,"& :where(svg)":{w:"1em",h:"1em"},"&[data-type=up]":{color:"fg.success"},"&[data-type=down]":{color:"fg.error"}}},variants:{size:{sm:{valueText:{textStyle:"xl"}},md:{valueText:{textStyle:"2xl"}},lg:{valueText:{textStyle:"3xl"}}}},defaultVariants:{size:"md"}}),hu=T({className:"chakra-status",slots:Cc.keys(),base:{root:{display:"inline-flex",alignItems:"center",gap:"2"},indicator:{width:"0.64em",height:"0.64em",flexShrink:0,borderRadius:"full",forcedColorAdjust:"none",bg:"colorPalette.solid"}},variants:{size:{sm:{root:{textStyle:"xs"}},md:{root:{textStyle:"sm"}},lg:{root:{textStyle:"md"}}}},defaultVariants:{size:"md"}}),bu=T({className:"chakra-steps",slots:Rc.keys(),base:{root:{display:"flex",width:"full"},list:{display:"flex",justifyContent:"space-between","--steps-gutter":"spacing.3","--steps-thickness":"2px"},title:{fontWeight:"medium",color:"fg"},description:{color:"fg.muted"},separator:{bg:"border",flex:"1"},indicator:{display:"flex",justifyContent:"center",alignItems:"center",flexShrink:"0",borderRadius:"full",fontWeight:"medium",width:"var(--steps-size)",height:"var(--steps-size)",_icon:{flexShrink:"0",width:"var(--steps-icon-size)",height:"var(--steps-icon-size)"}},item:{position:"relative",display:"flex",flex:"1 0 0","&:last-of-type":{flex:"initial","& [data-part=separator]":{display:"none"}}},trigger:{display:"flex",alignItems:"center",gap:"3",textAlign:"start",focusVisibleRing:"outside",borderRadius:"l2"},content:{focusVisibleRing:"outside"}},variants:{orientation:{vertical:{root:{flexDirection:"row",height:"100%"},list:{flexDirection:"column",alignItems:"flex-start"},separator:{position:"absolute",width:"var(--steps-thickness)",height:"100%",maxHeight:"calc(100% - var(--steps-size) - var(--steps-gutter) * 2)",top:"calc(var(--steps-size) + var(--steps-gutter))",insetStart:"calc(var(--steps-size) / 2 - 1px)"},item:{alignItems:"flex-start"}},horizontal:{root:{flexDirection:"column",width:"100%"},list:{flexDirection:"row",alignItems:"center"},separator:{width:"100%",height:"var(--steps-thickness)",marginX:"var(--steps-gutter)"},item:{alignItems:"center"}}},variant:{solid:{indicator:{_incomplete:{borderWidth:"var(--steps-thickness)"},_current:{bg:"colorPalette.muted",borderWidth:"var(--steps-thickness)",borderColor:"colorPalette.solid",color:"colorPalette.fg"},_complete:{bg:"colorPalette.solid",borderColor:"colorPalette.solid",color:"colorPalette.contrast"}},separator:{_complete:{bg:"colorPalette.solid"}}},subtle:{indicator:{_incomplete:{bg:"bg.muted"},_current:{bg:"colorPalette.muted",color:"colorPalette.fg"},_complete:{bg:"colorPalette.emphasized",color:"colorPalette.fg"}},separator:{_complete:{bg:"colorPalette.emphasized"}}}},size:{xs:{root:{gap:"2.5"},list:{"--steps-size":"sizes.6","--steps-icon-size":"sizes.3.5",textStyle:"xs"},title:{textStyle:"sm"}},sm:{root:{gap:"3"},list:{"--steps-size":"sizes.8","--steps-icon-size":"sizes.4",textStyle:"xs"},title:{textStyle:"sm"}},md:{root:{gap:"4"},list:{"--steps-size":"sizes.10","--steps-icon-size":"sizes.4",textStyle:"sm"},title:{textStyle:"sm"}},lg:{root:{gap:"6"},list:{"--steps-size":"sizes.11","--steps-icon-size":"sizes.5",textStyle:"md"},title:{textStyle:"md"}}}},defaultVariants:{size:"md",variant:"solid",orientation:"horizontal"}}),vu=T({slots:Pc.keys(),className:"chakra-switch",base:{root:{display:"inline-flex",gap:"2.5",alignItems:"center",position:"relative",verticalAlign:"middle","--switch-diff":"calc(var(--switch-width) - var(--switch-height))","--switch-x":{base:"var(--switch-diff)",_rtl:"calc(var(--switch-diff) * -1)"}},label:{lineHeight:"1",userSelect:"none",fontSize:"sm",fontWeight:"medium",_disabled:{opacity:"0.5"}},indicator:{position:"absolute",height:"var(--switch-height)",width:"var(--switch-height)",fontSize:"var(--switch-indicator-font-size)",fontWeight:"medium",flexShrink:0,userSelect:"none",display:"grid",placeContent:"center",transition:"inset-inline-start 0.12s ease",insetInlineStart:"calc(var(--switch-x) - 2px)",_checked:{insetInlineStart:"2px"}},control:{display:"inline-flex",gap:"0.5rem",flexShrink:0,justifyContent:"flex-start",cursor:"switch",borderRadius:"full",position:"relative",width:"var(--switch-width)",height:"var(--switch-height)",_disabled:{opacity:"0.5",cursor:"not-allowed"},_invalid:{outline:"2px solid",outlineColor:"border.error",outlineOffset:"2px"}},thumb:{display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,transitionProperty:"translate",transitionDuration:"fast",borderRadius:"inherit",_checked:{translate:"var(--switch-x) 0"}}},variants:{variant:{solid:{control:{borderRadius:"full",bg:"bg.emphasized",focusVisibleRing:"outside",_checked:{bg:"colorPalette.solid"}},thumb:{bg:"white",width:"var(--switch-height)",height:"var(--switch-height)",scale:"0.8",boxShadow:"sm",_checked:{bg:"colorPalette.contrast"}}},raised:{control:{borderRadius:"full",height:"calc(var(--switch-height) / 2)",bg:"bg.muted",boxShadow:"inset",_checked:{bg:"colorPalette.solid/60"}},thumb:{width:"var(--switch-height)",height:"var(--switch-height)",position:"relative",top:"calc(var(--switch-height) * -0.25)",bg:"white",boxShadow:"xs",focusVisibleRing:"outside",_checked:{bg:"colorPalette.solid"}}}},size:{xs:{root:{"--switch-width":"sizes.6","--switch-height":"sizes.3","--switch-indicator-font-size":"fontSizes.xs"}},sm:{root:{"--switch-width":"sizes.8","--switch-height":"sizes.4","--switch-indicator-font-size":"fontSizes.xs"}},md:{root:{"--switch-width":"sizes.10","--switch-height":"sizes.5","--switch-indicator-font-size":"fontSizes.sm"}},lg:{root:{"--switch-width":"sizes.12","--switch-height":"sizes.6","--switch-indicator-font-size":"fontSizes.md"}}}},defaultVariants:{variant:"solid",size:"md"}}),yu=T({className:"chakra-table",slots:_c.keys(),base:{root:{fontVariantNumeric:"lining-nums tabular-nums",borderCollapse:"collapse",width:"full",textAlign:"start",verticalAlign:"top"},row:{_selected:{bg:"colorPalette.subtle"}},cell:{textAlign:"start",alignItems:"center"},columnHeader:{fontWeight:"medium",textAlign:"start",color:"fg"},caption:{fontWeight:"medium",textStyle:"xs"},footer:{fontWeight:"medium"}},variants:{interactive:{true:{body:{"& tr":{_hover:{bg:"colorPalette.subtle"}}}}},stickyHeader:{true:{header:{"& :where(tr)":{top:"var(--table-sticky-offset, 0)",position:"sticky",zIndex:1}}}},striped:{true:{row:{"&:nth-of-type(odd) td":{bg:"bg.muted"}}}},showColumnBorder:{true:{columnHeader:{"&:not(:last-of-type)":{borderInlineEndWidth:"1px"}},cell:{"&:not(:last-of-type)":{borderInlineEndWidth:"1px"}}}},variant:{line:{columnHeader:{borderBottomWidth:"1px"},cell:{borderBottomWidth:"1px"},row:{bg:"bg"}},outline:{root:{boxShadow:"0 0 0 1px {colors.border}",overflow:"hidden"},columnHeader:{borderBottomWidth:"1px"},header:{bg:"bg.muted"},row:{"&:not(:last-of-type)":{borderBottomWidth:"1px"}},footer:{borderTopWidth:"1px"}}},size:{sm:{root:{textStyle:"sm"},columnHeader:{px:"2",py:"2"},cell:{px:"2",py:"2"}},md:{root:{textStyle:"sm"},columnHeader:{px:"3",py:"3"},cell:{px:"3",py:"3"}},lg:{root:{textStyle:"md"},columnHeader:{px:"4",py:"3"},cell:{px:"4",py:"3"}}}},defaultVariants:{variant:"line",size:"md"}}),xu=T({slots:Ec.keys(),className:"chakra-tabs",base:{root:{"--tabs-trigger-radius":"radii.l2",position:"relative",_horizontal:{display:"block"},_vertical:{display:"flex"}},list:{display:"inline-flex",position:"relative",isolation:"isolate","--tabs-indicator-shadow":"shadows.xs","--tabs-indicator-bg":"colors.bg",minH:"var(--tabs-height)",_horizontal:{flexDirection:"row"},_vertical:{flexDirection:"column"}},trigger:{outline:"0",minW:"var(--tabs-height)",height:"var(--tabs-height)",display:"flex",alignItems:"center",fontWeight:"medium",position:"relative",cursor:"button",gap:"2",_focusVisible:{zIndex:1,outline:"2px solid",outlineColor:"colorPalette.focusRing"},_disabled:{cursor:"not-allowed",opacity:.5}},content:{focusVisibleRing:"inside",_horizontal:{width:"100%",pt:"var(--tabs-content-padding)"},_vertical:{height:"100%",ps:"var(--tabs-content-padding)"}},indicator:{width:"var(--width)",height:"var(--height)",borderRadius:"var(--tabs-indicator-radius)",bg:"var(--tabs-indicator-bg)",shadow:"var(--tabs-indicator-shadow)",zIndex:-1}},variants:{fitted:{true:{list:{display:"flex"},trigger:{flex:1,textAlign:"center",justifyContent:"center"}}},justify:{start:{list:{justifyContent:"flex-start"}},center:{list:{justifyContent:"center"}},end:{list:{justifyContent:"flex-end"}}},size:{sm:{root:{"--tabs-height":"sizes.9","--tabs-content-padding":"spacing.3"},trigger:{py:"1",px:"3",textStyle:"sm"}},md:{root:{"--tabs-height":"sizes.10","--tabs-content-padding":"spacing.4"},trigger:{py:"2",px:"4",textStyle:"sm"}},lg:{root:{"--tabs-height":"sizes.11","--tabs-content-padding":"spacing.4.5"},trigger:{py:"2",px:"4.5",textStyle:"md"}}},variant:{line:{list:{display:"flex",borderColor:"border",_horizontal:{borderBottomWidth:"1px"},_vertical:{borderEndWidth:"1px"}},trigger:{color:"fg.muted",_disabled:{_active:{bg:"initial"}},_selected:{color:"fg",_horizontal:{layerStyle:"indicator.bottom","--indicator-offset-y":"-1px","--indicator-color":"colors.colorPalette.solid"},_vertical:{layerStyle:"indicator.end","--indicator-offset-x":"-1px"}}}},subtle:{trigger:{borderRadius:"var(--tabs-trigger-radius)",color:"fg.muted",_selected:{bg:"colorPalette.subtle",color:"colorPalette.fg"}}},enclosed:{list:{bg:"bg.muted",padding:"1",borderRadius:"l3",minH:"calc(var(--tabs-height) - 4px)"},trigger:{justifyContent:"center",color:"fg.muted",borderRadius:"var(--tabs-trigger-radius)",_selected:{bg:"bg",color:"colorPalette.fg",shadow:"xs"}}},outline:{list:{"--line-thickness":"1px","--line-offset":"calc(var(--line-thickness) * -1)",borderColor:"border",display:"flex",_horizontal:{_before:{content:'""',position:"absolute",bottom:"0px",width:"100%",borderBottomWidth:"var(--line-thickness)",borderBottomColor:"border"}},_vertical:{_before:{content:'""',position:"absolute",insetInline:"var(--line-offset)",height:"calc(100% - calc(var(--line-thickness) * 2))",borderEndWidth:"var(--line-thickness)",borderEndColor:"border"}}},trigger:{color:"fg.muted",borderWidth:"1px",borderColor:"transparent",_selected:{bg:"currentBg",color:"colorPalette.fg"},_horizontal:{borderTopRadius:"var(--tabs-trigger-radius)",marginBottom:"var(--line-offset)",marginEnd:{_notLast:"var(--line-offset)"},_selected:{borderColor:"border",borderBottomColor:"transparent"}},_vertical:{borderStartRadius:"var(--tabs-trigger-radius)",marginEnd:"var(--line-offset)",marginBottom:{_notLast:"var(--line-offset)"},_selected:{borderColor:"border",borderEndColor:"transparent"}}}},plain:{trigger:{color:"fg.muted",_selected:{color:"colorPalette.fg"},borderRadius:"var(--tabs-trigger-radius)","&[data-selected][data-ssr]":{bg:"var(--tabs-indicator-bg)",shadow:"var(--tabs-indicator-shadow)",borderRadius:"var(--tabs-indicator-radius)"}}}}},defaultVariants:{size:"md",variant:"line"}});var ni;const ge=(ni=oo.variants)==null?void 0:ni.variant,ku=T({slots:Wc.keys(),className:"chakra-tag",base:{root:{display:"inline-flex",alignItems:"center",verticalAlign:"top",maxWidth:"100%",userSelect:"none",borderRadius:"l2",focusVisibleRing:"outside"},label:{lineClamp:"1"},closeTrigger:{display:"flex",alignItems:"center",justifyContent:"center",outline:"0",borderRadius:"l1",color:"currentColor",focusVisibleRing:"inside",focusRingWidth:"2px"},startElement:{flexShrink:0,boxSize:"var(--tag-element-size)",ms:"var(--tag-element-offset)","&:has([data-scope=avatar])":{boxSize:"var(--tag-avatar-size)",ms:"calc(var(--tag-element-offset) * 1.5)"},_icon:{boxSize:"100%"}},endElement:{flexShrink:0,boxSize:"var(--tag-element-size)",me:"var(--tag-element-offset)",_icon:{boxSize:"100%"},"&:has(button)":{ms:"calc(var(--tag-element-offset) * -1)"}}},variants:{size:{sm:{root:{px:"1.5",minH:"4.5",gap:"1","--tag-avatar-size":"spacing.3","--tag-element-size":"spacing.3","--tag-element-offset":"-2px"},label:{textStyle:"xs"}},md:{root:{px:"1.5",minH:"5",gap:"1","--tag-avatar-size":"spacing.3.5","--tag-element-size":"spacing.3.5","--tag-element-offset":"-2px"},label:{textStyle:"xs"}},lg:{root:{px:"2",minH:"6",gap:"1.5","--tag-avatar-size":"spacing.4.5","--tag-element-size":"spacing.4","--tag-element-offset":"-3px"},label:{textStyle:"sm"}},xl:{root:{px:"2.5",minH:"8",gap:"1.5","--tag-avatar-size":"spacing.6","--tag-element-size":"spacing.4.5","--tag-element-offset":"-4px"},label:{textStyle:"sm"}}},variant:{subtle:{root:ge==null?void 0:ge.subtle},solid:{root:ge==null?void 0:ge.solid},outline:{root:ge==null?void 0:ge.outline},surface:{root:ge==null?void 0:ge.surface}}},defaultVariants:{size:"md",variant:"surface"}}),Su=T({slots:$c.keys(),className:"chakra-timeline",base:{root:{display:"flex",flexDirection:"column",width:"full","--timeline-thickness":"1px","--timeline-gutter":"4px"},item:{display:"flex",position:"relative",alignItems:"flex-start",flexShrink:0,gap:"4",_last:{"& :where(.chakra-timeline__separator)":{display:"none"}}},separator:{position:"absolute",borderStartWidth:"var(--timeline-thickness)",ms:"calc(-1 * var(--timeline-thickness) / 2)",insetInlineStart:"calc(var(--timeline-indicator-size) / 2)",insetBlock:"0",borderColor:"border"},indicator:{outline:"2px solid {colors.bg}",position:"relative",flexShrink:"0",boxSize:"var(--timeline-indicator-size)",fontSize:"var(--timeline-font-size)",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:"full",fontWeight:"medium"},connector:{alignSelf:"stretch",position:"relative"},content:{pb:"6",display:"flex",flexDirection:"column",width:"full",gap:"2"},title:{display:"flex",fontWeight:"medium",flexWrap:"wrap",gap:"1.5",alignItems:"center",mt:"var(--timeline-margin)"},description:{color:"fg.muted",textStyle:"xs"}},variants:{variant:{subtle:{indicator:{bg:"colorPalette.muted"}},solid:{indicator:{bg:"colorPalette.solid",color:"colorPalette.contrast"}},outline:{indicator:{bg:"currentBg",borderWidth:"1px",borderColor:"colorPalette.muted"}},plain:{}},size:{sm:{root:{"--timeline-indicator-size":"sizes.4","--timeline-font-size":"fontSizes.2xs"},title:{textStyle:"xs"}},md:{root:{"--timeline-indicator-size":"sizes.5","--timeline-font-size":"fontSizes.xs"},title:{textStyle:"sm"}},lg:{root:{"--timeline-indicator-size":"sizes.6","--timeline-font-size":"fontSizes.xs"},title:{mt:"0.5",textStyle:"sm"}},xl:{root:{"--timeline-indicator-size":"sizes.8","--timeline-font-size":"fontSizes.sm"},title:{mt:"1.5",textStyle:"sm"}}}},defaultVariants:{size:"md",variant:"solid"}}),wu=T({slots:Tc.keys(),className:"chakra-toast",base:{root:{width:"full",display:"flex",alignItems:"flex-start",position:"relative",gap:"3",py:"4",ps:"4",pe:"6",borderRadius:"l2",translate:"var(--x) var(--y)",scale:"var(--scale)",zIndex:"var(--z-index)",height:"var(--height)",opacity:"var(--opacity)",willChange:"translate, opacity, scale",transition:"translate 400ms, scale 400ms, opacity 400ms, height 400ms, box-shadow 200ms",transitionTimingFunction:"cubic-bezier(0.21, 1.02, 0.73, 1)",_closed:{transition:"translate 400ms, scale 400ms, opacity 200ms",transitionTimingFunction:"cubic-bezier(0.06, 0.71, 0.55, 1)"},bg:"bg.panel",color:"fg",boxShadow:"xl","--toast-trigger-bg":"colors.bg.muted","&[data-type=warning]":{bg:"orange.solid",color:"orange.contrast","--toast-trigger-bg":"{white/10}","--toast-border-color":"{white/40}"},"&[data-type=success]":{bg:"green.solid",color:"green.contrast","--toast-trigger-bg":"{white/10}","--toast-border-color":"{white/40}"},"&[data-type=error]":{bg:"red.solid",color:"red.contrast","--toast-trigger-bg":"{white/10}","--toast-border-color":"{white/40}"}},title:{fontWeight:"medium",textStyle:"sm",marginEnd:"2"},description:{display:"inline",textStyle:"sm",opacity:"0.8"},indicator:{flexShrink:"0",boxSize:"5"},actionTrigger:{textStyle:"sm",fontWeight:"medium",height:"8",px:"3",borderRadius:"l2",alignSelf:"center",borderWidth:"1px",borderColor:"var(--toast-border-color, inherit)",transition:"background 200ms",_hover:{bg:"var(--toast-trigger-bg)"}},closeTrigger:{position:"absolute",top:"1",insetEnd:"1",padding:"1",display:"inline-flex",alignItems:"center",justifyContent:"center",color:"{currentColor/60}",borderRadius:"l2",textStyle:"md",transition:"background 200ms",_icon:{boxSize:"1em"}}}}),Iu=T({slots:Cn.keys(),className:"chakra-tooltip",base:{content:{"--tooltip-bg":"colors.bg.inverted",bg:"var(--tooltip-bg)",color:"fg.inverted",px:"2.5",py:"1",borderRadius:"l2",fontWeight:"medium",textStyle:"xs",boxShadow:"md",maxW:"xs",zIndex:"tooltip",transformOrigin:"var(--transform-origin)",_open:{animationStyle:"scale-fade-in",animationDuration:"fast"},_closed:{animationStyle:"scale-fade-out",animationDuration:"fast"}},arrow:{"--arrow-size":"sizes.2","--arrow-background":"var(--tooltip-bg)"},arrowTip:{borderTopWidth:"1px",borderInlineStartWidth:"1px",borderColor:"var(--tooltip-bg)"}}}),zu={accordion:Bc,actionBar:Ac,alert:Oc,avatar:Nc,blockquote:Vc,breadcrumb:Dc,card:jc,checkbox:Mc,checkboxCard:Fc,collapsible:Lc,dataList:Gc,dialog:Yc,drawer:qc,editable:Uc,emptyState:Xc,field:Kc,fieldset:Zc,fileUpload:Qc,hoverCard:Jc,list:eu,menu:tu,nativeSelect:ou,numberInput:ru,pinInput:nu,popover:au,progress:su,progressCircle:lu,radioCard:cu,radioGroup:uu,ratingGroup:gu,segmentGroup:pu,select:vt,slider:mu,stat:fu,steps:bu,switch:vu,table:yu,tabs:xu,tag:ku,toast:wu,tooltip:Iu,status:hu,timeline:Su,colorPicker:Hc,qrCode:du},Cu=Ua({"2xs":{value:{fontSize:"2xs",lineHeight:"0.75rem"}},xs:{value:{fontSize:"xs",lineHeight:"1rem"}},sm:{value:{fontSize:"sm",lineHeight:"1.25rem"}},md:{value:{fontSize:"md",lineHeight:"1.5rem"}},lg:{value:{fontSize:"lg",lineHeight:"1.75rem"}},xl:{value:{fontSize:"xl",lineHeight:"1.875rem"}},"2xl":{value:{fontSize:"2xl",lineHeight:"2rem"}},"3xl":{value:{fontSize:"3xl",lineHeight:"2.375rem"}},"4xl":{value:{fontSize:"4xl",lineHeight:"2.75rem",letterSpacing:"-0.025em"}},"5xl":{value:{fontSize:"5xl",lineHeight:"3.75rem",letterSpacing:"-0.025em"}},"6xl":{value:{fontSize:"6xl",lineHeight:"4.5rem",letterSpacing:"-0.025em"}},"7xl":{value:{fontSize:"7xl",lineHeight:"5.75rem",letterSpacing:"-0.025em"}},none:{value:{}}}),Ru=K.animations({spin:{value:"spin 1s linear infinite"},ping:{value:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite"},pulse:{value:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite"},bounce:{value:"bounce 1s infinite"}}),Pu=K.aspectRatios({square:{value:"1 / 1"},landscape:{value:"4 / 3"},portrait:{value:"3 / 4"},wide:{value:"16 / 9"},ultrawide:{value:"18 / 5"},golden:{value:"1.618 / 1"}}),_u=K.blurs({none:{value:" "},sm:{value:"4px"},md:{value:"8px"},lg:{value:"12px"},xl:{value:"16px"},"2xl":{value:"24px"},"3xl":{value:"40px"},"4xl":{value:"64px"}}),Tu=K.borders({xs:{value:"0.5px solid"},sm:{value:"1px solid"},md:{value:"2px solid"},lg:{value:"4px solid"},xl:{value:"8px solid"}}),Eu=K.colors({transparent:{value:"transparent"},current:{value:"currentColor"},black:{value:"#09090B"},white:{value:"#FFFFFF"},whiteAlpha:{50:{value:"rgba(255, 255, 255, 0.04)"},100:{value:"rgba(255, 255, 255, 0.06)"},200:{value:"rgba(255, 255, 255, 0.08)"},300:{value:"rgba(255, 255, 255, 0.16)"},400:{value:"rgba(255, 255, 255, 0.24)"},500:{value:"rgba(255, 255, 255, 0.36)"},600:{value:"rgba(255, 255, 255, 0.48)"},700:{value:"rgba(255, 255, 255, 0.64)"},800:{value:"rgba(255, 255, 255, 0.80)"},900:{value:"rgba(255, 255, 255, 0.92)"},950:{value:"rgba(255, 255, 255, 0.95)"}},blackAlpha:{50:{value:"rgba(0, 0, 0, 0.04)"},100:{value:"rgba(0, 0, 0, 0.06)"},200:{value:"rgba(0, 0, 0, 0.08)"},300:{value:"rgba(0, 0, 0, 0.16)"},400:{value:"rgba(0, 0, 0, 0.24)"},500:{value:"rgba(0, 0, 0, 0.36)"},600:{value:"rgba(0, 0, 0, 0.48)"},700:{value:"rgba(0, 0, 0, 0.64)"},800:{value:"rgba(0, 0, 0, 0.80)"},900:{value:"rgba(0, 0, 0, 0.92)"},950:{value:"rgba(0, 0, 0, 0.95)"}},gray:{50:{value:"#fafafa"},100:{value:"#f4f4f5"},200:{value:"#e4e4e7"},300:{value:"#d4d4d8"},400:{value:"#a1a1aa"},500:{value:"#71717a"},600:{value:"#52525b"},700:{value:"#3f3f46"},800:{value:"#27272a"},900:{value:"#18181b"},950:{value:"#111111"}},red:{50:{value:"#fef2f2"},100:{value:"#fee2e2"},200:{value:"#fecaca"},300:{value:"#fca5a5"},400:{value:"#f87171"},500:{value:"#ef4444"},600:{value:"#dc2626"},700:{value:"#991919"},800:{value:"#511111"},900:{value:"#300c0c"},950:{value:"#1f0808"}},orange:{50:{value:"#fff7ed"},100:{value:"#ffedd5"},200:{value:"#fed7aa"},300:{value:"#fdba74"},400:{value:"#fb923c"},500:{value:"#f97316"},600:{value:"#ea580c"},700:{value:"#92310a"},800:{value:"#6c2710"},900:{value:"#3b1106"},950:{value:"#220a04"}},yellow:{50:{value:"#fefce8"},100:{value:"#fef9c3"},200:{value:"#fef08a"},300:{value:"#fde047"},400:{value:"#facc15"},500:{value:"#eab308"},600:{value:"#ca8a04"},700:{value:"#845209"},800:{value:"#713f12"},900:{value:"#422006"},950:{value:"#281304"}},green:{50:{value:"#f0fdf4"},100:{value:"#dcfce7"},200:{value:"#bbf7d0"},300:{value:"#86efac"},400:{value:"#4ade80"},500:{value:"#22c55e"},600:{value:"#16a34a"},700:{value:"#116932"},800:{value:"#124a28"},900:{value:"#042713"},950:{value:"#03190c"}},teal:{50:{value:"#f0fdfa"},100:{value:"#ccfbf1"},200:{value:"#99f6e4"},300:{value:"#5eead4"},400:{value:"#2dd4bf"},500:{value:"#14b8a6"},600:{value:"#0d9488"},700:{value:"#0c5d56"},800:{value:"#114240"},900:{value:"#032726"},950:{value:"#021716"}},blue:{50:{value:"#eff6ff"},100:{value:"#dbeafe"},200:{value:"#bfdbfe"},300:{value:"#a3cfff"},400:{value:"#60a5fa"},500:{value:"#3b82f6"},600:{value:"#2563eb"},700:{value:"#173da6"},800:{value:"#1a3478"},900:{value:"#14204a"},950:{value:"#0c142e"}},cyan:{50:{value:"#ecfeff"},100:{value:"#cffafe"},200:{value:"#a5f3fc"},300:{value:"#67e8f9"},400:{value:"#22d3ee"},500:{value:"#06b6d4"},600:{value:"#0891b2"},700:{value:"#0c5c72"},800:{value:"#134152"},900:{value:"#072a38"},950:{value:"#051b24"}},purple:{50:{value:"#faf5ff"},100:{value:"#f3e8ff"},200:{value:"#e9d5ff"},300:{value:"#d8b4fe"},400:{value:"#c084fc"},500:{value:"#a855f7"},600:{value:"#9333ea"},700:{value:"#641ba3"},800:{value:"#4a1772"},900:{value:"#2f0553"},950:{value:"#1a032e"}},pink:{50:{value:"#fdf2f8"},100:{value:"#fce7f3"},200:{value:"#fbcfe8"},300:{value:"#f9a8d4"},400:{value:"#f472b6"},500:{value:"#ec4899"},600:{value:"#db2777"},700:{value:"#a41752"},800:{value:"#6d0e34"},900:{value:"#45061f"},950:{value:"#2c0514"}}}),Wu=K.cursor({button:{value:"pointer"},checkbox:{value:"default"},disabled:{value:"not-allowed"},menuitem:{value:"default"},option:{value:"default"},radio:{value:"default"},slider:{value:"default"},switch:{value:"pointer"}}),$u=K.durations({fastest:{value:"50ms"},faster:{value:"100ms"},fast:{value:"150ms"},moderate:{value:"200ms"},slow:{value:"300ms"},slower:{value:"400ms"},slowest:{value:"500ms"}}),Bu=K.easings({"ease-in":{value:"cubic-bezier(0.42, 0, 1, 1)"},"ease-out":{value:"cubic-bezier(0, 0, 0.58, 1)"},"ease-in-out":{value:"cubic-bezier(0.42, 0, 0.58, 1)"},"ease-in-smooth":{value:"cubic-bezier(0.32, 0.72, 0, 1)"}}),Au=K.fontSizes({"2xs":{value:"0.625rem"},xs:{value:"0.75rem"},sm:{value:"0.875rem"},md:{value:"1rem"},lg:{value:"1.125rem"},xl:{value:"1.25rem"},"2xl":{value:"1.5rem"},"3xl":{value:"1.875rem"},"4xl":{value:"2.25rem"},"5xl":{value:"3rem"},"6xl":{value:"3.75rem"},"7xl":{value:"4.5rem"},"8xl":{value:"6rem"},"9xl":{value:"8rem"}}),Ou=K.fontWeights({thin:{value:"100"},extralight:{value:"200"},light:{value:"300"},normal:{value:"400"},medium:{value:"500"},semibold:{value:"600"},bold:{value:"700"},extrabold:{value:"800"},black:{value:"900"}}),Ko='-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',Nu=K.fonts({heading:{value:`Inter, ${Ko}`},body:{value:`Inter, ${Ko}`},mono:{value:'SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace'}}),Vu=Ya({spin:{"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}},pulse:{"50%":{opacity:"0.5"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}},"bg-position":{from:{backgroundPosition:"var(--animate-from, 1rem) 0"},to:{backgroundPosition:"var(--animate-to, 0) 0"}},position:{from:{insetInlineStart:"var(--animate-from-x)",insetBlockStart:"var(--animate-from-y)"},to:{insetInlineStart:"var(--animate-to-x)",insetBlockStart:"var(--animate-to-y)"}},"circular-progress":{"0%":{strokeDasharray:"1, 400",strokeDashoffset:"0"},"50%":{strokeDasharray:"400, 400",strokeDashoffset:"-100%"},"100%":{strokeDasharray:"400, 400",strokeDashoffset:"-260%"}},"expand-height":{from:{height:"0"},to:{height:"var(--height)"}},"collapse-height":{from:{height:"var(--height)"},to:{height:"0"}},"expand-width":{from:{width:"0"},to:{width:"var(--width)"}},"collapse-width":{from:{height:"var(--width)"},to:{height:"0"}},"fade-in":{from:{opacity:0},to:{opacity:1}},"fade-out":{from:{opacity:1},to:{opacity:0}},"slide-from-left-full":{from:{translate:"-100% 0"},to:{translate:"0 0"}},"slide-from-right-full":{from:{translate:"100% 0"},to:{translate:"0 0"}},"slide-from-top-full":{from:{translate:"0 -100%"},to:{translate:"0 0"}},"slide-from-bottom-full":{from:{translate:"0 100%"},to:{translate:"0 0"}},"slide-to-left-full":{from:{translate:"0 0"},to:{translate:"-100% 0"}},"slide-to-right-full":{from:{translate:"0 0"},to:{translate:"100% 0"}},"slide-to-top-full":{from:{translate:"0 0"},to:{translate:"0 -100%"}},"slide-to-bottom-full":{from:{translate:"0 0"},to:{translate:"0 100%"}},"slide-from-top":{"0%":{translate:"0 -0.5rem"},to:{translate:"0"}},"slide-from-bottom":{"0%":{translate:"0 0.5rem"},to:{translate:"0"}},"slide-from-left":{"0%":{translate:"-0.5rem 0"},to:{translate:"0"}},"slide-from-right":{"0%":{translate:"0.5rem 0"},to:{translate:"0"}},"slide-to-top":{"0%":{translate:"0"},to:{translate:"0 -0.5rem"}},"slide-to-bottom":{"0%":{translate:"0"},to:{translate:"0 0.5rem"}},"slide-to-left":{"0%":{translate:"0"},to:{translate:"-0.5rem 0"}},"slide-to-right":{"0%":{translate:"0"},to:{translate:"0.5rem 0"}},"scale-in":{from:{scale:"0.95"},to:{scale:"1"}},"scale-out":{from:{scale:"1"},to:{scale:"0.95"}}}),Du=K.letterSpacings({tighter:{value:"-0.05em"},tight:{value:"-0.025em"},wide:{value:"0.025em"},wider:{value:"0.05em"},widest:{value:"0.1em"}}),ju=K.lineHeights({shorter:{value:1.25},short:{value:1.375},moderate:{value:1.5},tall:{value:1.625},taller:{value:2}}),Mu=K.radii({none:{value:"0"},"2xs":{value:"0.0625rem"},xs:{value:"0.125rem"},sm:{value:"0.25rem"},md:{value:"0.375rem"},lg:{value:"0.5rem"},xl:{value:"0.75rem"},"2xl":{value:"1rem"},"3xl":{value:"1.5rem"},"4xl":{value:"2rem"},full:{value:"9999px"}}),Pn=K.spacing({.5:{value:"0.125rem"},1:{value:"0.25rem"},1.5:{value:"0.375rem"},2:{value:"0.5rem"},2.5:{value:"0.625rem"},3:{value:"0.75rem"},3.5:{value:"0.875rem"},4:{value:"1rem"},4.5:{value:"1.125rem"},5:{value:"1.25rem"},6:{value:"1.5rem"},7:{value:"1.75rem"},8:{value:"2rem"},9:{value:"2.25rem"},10:{value:"2.5rem"},11:{value:"2.75rem"},12:{value:"3rem"},14:{value:"3.5rem"},16:{value:"4rem"},20:{value:"5rem"},24:{value:"6rem"},28:{value:"7rem"},32:{value:"8rem"},36:{value:"9rem"},40:{value:"10rem"},44:{value:"11rem"},48:{value:"12rem"},52:{value:"13rem"},56:{value:"14rem"},60:{value:"15rem"},64:{value:"16rem"},72:{value:"18rem"},80:{value:"20rem"},96:{value:"24rem"}}),Fu=K.sizes({"3xs":{value:"14rem"},"2xs":{value:"16rem"},xs:{value:"20rem"},sm:{value:"24rem"},md:{value:"28rem"},lg:{value:"32rem"},xl:{value:"36rem"},"2xl":{value:"42rem"},"3xl":{value:"48rem"},"4xl":{value:"56rem"},"5xl":{value:"64rem"},"6xl":{value:"72rem"},"7xl":{value:"80rem"},"8xl":{value:"90rem"}}),Lu=K.sizes({max:{value:"max-content"},min:{value:"min-content"},fit:{value:"fit-content"},prose:{value:"60ch"},full:{value:"100%"},dvh:{value:"100dvh"},svh:{value:"100svh"},lvh:{value:"100lvh"},dvw:{value:"100dvw"},svw:{value:"100svw"},lvw:{value:"100lvw"},vw:{value:"100vw"},vh:{value:"100vh"}}),Hu=K.sizes({"1/2":{value:"50%"},"1/3":{value:"33.333333%"},"2/3":{value:"66.666667%"},"1/4":{value:"25%"},"3/4":{value:"75%"},"1/5":{value:"20%"},"2/5":{value:"40%"},"3/5":{value:"60%"},"4/5":{value:"80%"},"1/6":{value:"16.666667%"},"2/6":{value:"33.333333%"},"3/6":{value:"50%"},"4/6":{value:"66.666667%"},"5/6":{value:"83.333333%"},"1/12":{value:"8.333333%"},"2/12":{value:"16.666667%"},"3/12":{value:"25%"},"4/12":{value:"33.333333%"},"5/12":{value:"41.666667%"},"6/12":{value:"50%"},"7/12":{value:"58.333333%"},"8/12":{value:"66.666667%"},"9/12":{value:"75%"},"10/12":{value:"83.333333%"},"11/12":{value:"91.666667%"}}),Gu=K.sizes({...Fu,...Pn,...Hu,...Lu}),Yu=K.zIndex({hide:{value:-1},base:{value:0},docked:{value:10},dropdown:{value:1e3},sticky:{value:1100},banner:{value:1200},overlay:{value:1300},modal:{value:1400},popover:{value:1500},skipNav:{value:1600},toast:{value:1700},tooltip:{value:1800},max:{value:**********}}),qu=bi({preflight:!0,cssVarsPrefix:"chakra",cssVarsRoot:":where(html, .chakra-theme)",globalCss:yl,theme:{breakpoints:vl,keyframes:Vu,tokens:{aspectRatios:Pu,animations:Ru,blurs:_u,borders:Tu,colors:Eu,durations:$u,easings:Bu,fonts:Nu,fontSizes:Au,fontWeights:Ou,letterSpacings:Du,lineHeights:ju,radii:Mu,spacing:Pn,sizes:Gu,zIndex:Yu,cursor:Wu},semanticTokens:{colors:Dl,shadows:Ml,radii:jl},recipes:Vl,slotRecipes:zu,textStyles:Cu,layerStyles:xl,animationStyles:kl}}),Uu=vi(is,qu),vg=hl(Uu);function Xu(e){const{key:t,recipe:o}=e,r=zt();return X.useMemo(()=>{const i=o||(t!=null?r.getSlotRecipe(t):{});return r.sva(structuredClone(i))},[t,o,r])}const Ku=e=>e.charAt(0).toUpperCase()+e.slice(1),Zu=e=>{const{key:t,recipe:o}=e,r=Ku(t||o.className||"Component"),[i,n]=rt({name:`${r}StylesContext`,errorMessage:`use${r}Styles returned is 'undefined'. Seems you forgot to wrap the components in "<${r}.Root />" `}),[a,s]=rt({name:`${r}ClassNameContext`,errorMessage:`use${r}ClassNames returned is 'undefined'. Seems you forgot to wrap the components in "<${r}.Root />" `,strict:!1}),[d,c]=rt({strict:!1,name:`${r}PropsContext`,providerName:`${r}PropsContext`,defaultValue:{}});function l(m){const{unstyled:f,...S}=m,k=Xu({key:t,recipe:S.recipe||o}),[x,I]=k.splitVariantProps(S);return{styles:f?Un:k(x),classNames:k.classNameMap,props:I}}function u(m,f={}){const{defaultProps:S}=f,k=x=>{const I=yt(S,c(),x),{styles:C,classNames:_,props:B}=l(I);return G.jsx(i,{value:C,children:G.jsx(a,{value:_,children:G.jsx(m,{...B})})})};return k.displayName=m.displayName||m.name,k}return{StylesProvider:i,ClassNamesProvider:a,PropsProvider:d,usePropsContext:c,useRecipeResult:l,withProvider:(m,f,S)=>{const{defaultProps:k,...x}=S??{},I=Oe(m,{},x),C=X.forwardRef((_,B)=>{var N;const R=yt(k??{},c(),_),{styles:h,props:b,classNames:w}=l(R),$=w[f],z=G.jsx(i,{value:h,children:G.jsx(a,{value:w,children:G.jsx(I,{ref:B,...b,css:[h[f],R.css],className:xe(R.className,$)})})});return((N=S==null?void 0:S.wrapElement)==null?void 0:N.call(S,z,R))??z});return C.displayName=m.displayName||m.name,C},withContext:(m,f,S)=>{const k=Oe(m,{},S),x=X.forwardRef((I,C)=>{const _=n(),B=s(),R=B==null?void 0:B[f];return G.jsx(k,{...I,css:[f?_[f]:void 0,I.css],ref:C,className:xe(I.className,R)})});return x.displayName=m.displayName||m.name,x},withRootProvider:u,useStyles:n,useClassNames:s}},Qu=Oe("div",{base:{display:"flex",alignItems:"center",justifyContent:"center"},variants:{inline:{true:{display:"inline-flex"}}}});Qu.displayName="Center";const yg=X.forwardRef(function(t,o){const{direction:r,align:i,justify:n,wrap:a,basis:s,grow:d,shrink:c,inline:l,...u}=t;return G.jsx(Oe.div,{ref:o,...u,css:{display:l?"inline-flex":"flex",flexDirection:r,alignItems:i,justifyContent:n,flexWrap:a,flexBasis:s,flexGrow:d,flexShrink:c,...t.css}})}),{StylesProvider:Ju,ClassNamesProvider:eg,useRecipeResult:tg,withContext:_e,useStyles:xg,PropsProvider:kg}=Zu({key:"table"}),Sg=X.forwardRef(function({native:t,...o},r){const{styles:i,props:n,classNames:a}=tg(o),s=X.useMemo(()=>t?{...i.root,"& thead":i.header,"& tbody":i.body,"& tfoot":i.footer,"& thead th":i.columnHeader,"& tr":i.row,"& td":i.cell,"& caption":i.caption}:i.root,[i,t]);return G.jsx(eg,{value:a,children:G.jsx(Ju,{value:i,children:G.jsx(Oe.table,{ref:r,...n,css:[s,o.css],className:xe(a==null?void 0:a.root,o.className)})})})}),wg=_e("tr","row"),Ig=Oe("div",{base:{display:"block",whiteSpace:"nowrap",WebkitOverflowScrolling:"touch",overflow:"auto",maxWidth:"100%"}}),zg=_e("thead","header");_e("tfoot","footer");const Cg=_e("th","columnHeader"),Rg=_e("td","cell");_e("caption","caption",{defaultProps:{captionSide:"bottom"}});const Pg=_e("tbody","body");_e("colgroup");_e("col");const{withContext:og,PropsProvider:_g}=ya({key:"text"}),Tg=og("p");export{Qu as C,yg as F,Ig as T,gg as a,hg as b,bg as c,Sg as d,zg as e,fg as f,wg as g,Cg as h,Tg as i,G as j,Pg as k,Rg as l,lg as m,vg as n,pg as o,mg as s,sg as u};
