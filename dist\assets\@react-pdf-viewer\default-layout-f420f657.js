import{r as Se,b as dr}from"../vendor-df163860.js";import{l as Pe}from"./core-9b83754d.js";var Ye={},on={exports:{}},an={},$e=Pe;function fr(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var pe=fr(Se),pr=function(e){var t=e.files,r=pe.useRef(),o=pe.useContext($e.LocalizationContext).l10n,n=pe.useContext($e.ThemeContext).direction===$e.TextDirection.RightToLeft,a=pe.useRef([]),i=o&&o.attachment?o.attachment.clickToDownload:"Click to download",c=function(s){var g=r.current,f=[].slice.call(g.getElementsByClassName("rpv-attachment__item"));if(f.length!==0){f.forEach(function(d){return d.setAttribute("tabindex","-1")});var b=document.activeElement,m=f[Math.min(f.length-1,Math.max(0,s(f,b)))];m.setAttribute("tabindex","0"),m.focus()}};return $e.useIsomorphicLayoutEffect(function(){var s=r.current;if(s){var g=[].slice.call(s.getElementsByClassName("rpv-attachment__item"));if(a.current=g,g.length>0){var f=g[0];f.focus(),f.setAttribute("tabindex","0")}}},[]),pe.createElement("div",{"data-testid":"attachment__list",className:$e.classNames({"rpv-attachment__list":!0,"rpv-attachment__list--rtl":n}),ref:r,tabIndex:-1,onKeyDown:function(s){switch(s.key){case"ArrowDown":s.preventDefault(),c(function(g,f){return g.indexOf(f)+1});break;case"ArrowUp":s.preventDefault(),c(function(g,f){return g.indexOf(f)-1});break;case"End":s.preventDefault(),c(function(g,f){return g.length-1});break;case"Home":s.preventDefault(),c(function(g,f){return 0})}}},t.map(function(s){return pe.createElement("button",{className:"rpv-attachment__item",key:s.fileName,tabIndex:-1,title:i,type:"button",onClick:function(){return g=s.fileName,f=s.data,b=typeof f=="string"?"":URL.createObjectURL(new Blob([f],{type:""})),(m=document.createElement("a")).style.display="none",m.href=b||g,m.setAttribute("download",function(d){var h=d.split("/").pop();return h?h.split("#")[0].split("?")[0]:d}(g)),document.body.appendChild(m),m.click(),document.body.removeChild(m),void(b&&URL.revokeObjectURL(b));var g,f,b,m}},s.fileName)}))},gr=function(e){var t=e.doc,r=pe.useContext($e.LocalizationContext).l10n,o=pe.useContext($e.ThemeContext).direction===$e.TextDirection.RightToLeft,n=r&&r.attachment?r.attachment.noAttachment:"There is no attachment",a=pe.useState({files:[],isLoaded:!1}),i=a[0],c=a[1];return pe.useEffect(function(){t.getAttachments().then(function(s){var g=s?Object.keys(s).map(function(f){return{data:s[f].content,fileName:s[f].filename}}):[];c({files:g,isLoaded:!0})})},[t]),i.isLoaded?i.files.length===0?pe.createElement("div",{"data-testid":"attachment__empty",className:$e.classNames({"rpv-attachment__empty":!0,"rpv-attachment__empty--rtl":o})},n):pe.createElement(pr,{files:i.files}):pe.createElement($e.Spinner,null)},vr=function(e){var t=e.store,r=pe.useState(t.get("doc")),o=r[0],n=r[1],a=function(i){n(i)};return pe.useEffect(function(){return t.subscribe("doc",a),function(){t.unsubscribe("doc",a)}},[]),o?pe.createElement(gr,{doc:o}):pe.createElement("div",{className:"rpv-attachment__loader"},pe.createElement($e.Spinner,null))};an.attachmentPlugin=function(){var e=pe.useMemo(function(){return $e.createStore({})},[]);return{onDocumentLoad:function(t){e.update("doc",t.doc)},Attachments:function(){return pe.createElement(vr,{store:e})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */on.exports=an;var hr=on.exports,cn={exports:{}},lt={},je=Pe;function br(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var et,B=br(Se),_t=function(){return _t=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},_t.apply(this,arguments)},ln=function(){return B.createElement(je.Icon,{size:16},B.createElement("path",{d:"M6.427,8.245A.5.5,0,0,1,6.862,7.5H17.138a.5.5,0,0,1,.435.749l-5.139,9a.5.5,0,0,1-.868,0Z"}))},un=function(){return B.createElement(je.Icon,{size:16},B.createElement("path",{d:"M9.248,17.572a.5.5,0,0,1-.748-.434V6.862a.5.5,0,0,1,.748-.434l8.992,5.138a.5.5,0,0,1,0,.868Z"}))},Er=function(e){var t=e.bookmark,r=e.depth,o=e.doc,n=e.index,a=e.isBookmarkExpanded,i=e.numberOfSiblings,c=e.pathFromRoot,s=e.renderBookmarkItem,g=e.store,f=c?"".concat(c,".").concat(n):"".concat(n),b=B.useMemo(function(){return function(V){var x=V.count,M=V.items;if(x>=0)return!1;var O=M.length;if(O===0)return!1;for(var X=M.concat([]);X.length>0;){var w=X.shift(),D=w.items;w.count&&D&&w.count>0&&D.length>0&&(O+=D.length,X=X.concat(D))}return Math.abs(x)===O}(t)},[t]),m=g.get("bookmarkExpandedMap"),d=a?a({bookmark:t,doc:o,depth:r,index:n}):m.has(f)?m.get(f):!b,h=B.useState(d),l=h[0],u=h[1],v=t.items&&t.items.length>0,p=function(){var V=!l;g.updateCurrentValue("bookmarkExpandedMap",function(x){return x.set(f,V)}),u(V)},E=function(){var V=t.dest,x=g.get("jumpToDestination");je.getDestination(o,V).then(function(M){x&&x(_t({label:t.title},M))})},S=function(){v&&t.dest&&E()},j=function(){!v&&t.dest&&E()},A=function(V,x){return B.createElement("div",{className:"rpv-bookmark__item",style:{paddingLeft:"".concat(1.25*r,"rem")},onClick:V},x)},H=function(V,x){return v?B.createElement("span",{className:"rpv-bookmark__toggle","data-testid":"bookmark__toggle-".concat(r,"-").concat(n),onClick:p},l?V:x):B.createElement("span",{className:"rpv-bookmark__toggle"})},J=function(V){return t.url?B.createElement("a",{className:"rpv-bookmark__title",href:t.url,rel:"noopener noreferrer nofollow",target:t.newWindow?"_blank":""},t.title):B.createElement("div",{className:"rpv-bookmark__title","aria-label":t.title,onClick:V},t.title)};return B.createElement("li",{"aria-expanded":l?"true":"false","aria-label":t.title,"aria-level":r+1,"aria-posinset":n+1,"aria-setsize":i,role:"treeitem",tabIndex:-1},s?s({bookmark:t,depth:r,hasSubItems:v,index:n,isExpanded:l,path:f,defaultRenderItem:A,defaultRenderTitle:J,defaultRenderToggle:H,onClickItem:j,onClickTitle:S,onToggleSubItems:p}):A(j,B.createElement(B.Fragment,null,H(B.createElement(ln,null),B.createElement(un,null)),J(S))),v&&l&&B.createElement(sn,{bookmarks:t.items,depth:r+1,doc:o,isBookmarkExpanded:a,isRoot:!1,pathFromRoot:f,renderBookmarkItem:s,store:g}))},sn=function(e){var t=e.bookmarks,r=e.depth,o=r===void 0?0:r,n=e.doc,a=e.isBookmarkExpanded,i=e.isRoot,c=e.pathFromRoot,s=e.renderBookmarkItem,g=e.store;return B.createElement("ul",{className:"rpv-bookmark__list",role:i?"tree":"group",tabIndex:-1},t.map(function(f,b){return B.createElement(Er,{bookmark:f,depth:o,doc:n,index:b,isBookmarkExpanded:a,key:b,numberOfSiblings:t.length,pathFromRoot:c,renderBookmarkItem:s,store:g})}))};(function(e){e[e.Collapse=0]="Collapse",e[e.Expand=1]="Expand"})(et||(et={}));var kr=function(e){var t=e.bookmarks,r=e.doc,o=e.isBookmarkExpanded,n=e.renderBookmarkItem,a=e.store,i=B.useRef(),c=function(b){var m=i.current;if(m&&b.target instanceof HTMLElement&&m.contains(b.target))switch(b.key){case"ArrowDown":b.preventDefault(),g(function(d,h){return d.indexOf(h)+1});break;case"ArrowLeft":b.preventDefault(),f(et.Collapse);break;case"ArrowRight":b.preventDefault(),f(et.Expand);break;case"ArrowUp":b.preventDefault,g(function(d,h){return d.indexOf(h)-1});break;case"End":b.preventDefault(),g(function(d,h){return d.length-1});break;case" ":case"Enter":case"Space":b.preventDefault(),s();break;case"Home":b.preventDefault(),g(function(d,h){return 0})}},s=function(){var b=document.activeElement.closest(".rpv-bookmark__item").querySelector(".rpv-bookmark__title");b&&b.click()},g=function(b){var m=i.current,d=[].slice.call(m.getElementsByClassName("rpv-bookmark__item"));if(d.length!==0){var h=document.activeElement,l=d[Math.min(d.length-1,Math.max(0,b(d,h)))];h.setAttribute("tabindex","-1"),l.setAttribute("tabindex","0"),l.focus()}},f=function(b){var m=i.current;if([].slice.call(m.getElementsByClassName("rpv-bookmark__item")).length!==0){var d=document.activeElement.closest(".rpv-bookmark__item"),h=b===et.Collapse?"true":"false";if(d&&d.parentElement.getAttribute("aria-expanded")===h){var l=d.querySelector(".rpv-bookmark__toggle");l&&l.click()}}};return B.useEffect(function(){return document.addEventListener("keydown",c),function(){document.removeEventListener("keydown",c)}},[]),B.useEffect(function(){var b=i.current;if(b){var m=[].slice.call(b.getElementsByClassName("rpv-bookmark__item"));m.length>0&&(m[0].focus(),m[0].setAttribute("tabindex","0"))}},[]),B.createElement("div",{ref:i},B.createElement(sn,{bookmarks:t,depth:0,doc:r,isBookmarkExpanded:o,isRoot:!0,pathFromRoot:"",renderBookmarkItem:n,store:a}))},Cr=function(e){var t=e.doc,r=e.isBookmarkExpanded,o=e.renderBookmarkItem,n=e.store,a=B.useContext(je.LocalizationContext).l10n,i=B.useContext(je.ThemeContext).direction===je.TextDirection.RightToLeft,c=B.useState({isLoaded:!1,items:[]}),s=c[0],g=c[1];return B.useEffect(function(){g({isLoaded:!1,items:[]}),t.getOutline().then(function(f){g({isLoaded:!0,items:f||[]})})},[t]),s.isLoaded?s.items.length===0?B.createElement("div",{"data-testid":"bookmark__empty",className:je.classNames({"rpv-bookmark__empty":!0,"rpv-bookmark__empty--rtl":i})},a&&a.bookmark?a.bookmark.noBookmark:"There is no bookmark"):B.createElement("div",{"data-testid":"bookmark__container",className:je.classNames({"rpv-bookmark__container":!0,"rpv-bookmark__container--rtl":i})},B.createElement(kr,{bookmarks:s.items,doc:t,isBookmarkExpanded:r,renderBookmarkItem:o,store:n})):B.createElement("div",{className:"rpv-bookmark__loader"},B.createElement(je.Spinner,null))},_r=function(e){var t=e.isBookmarkExpanded,r=e.renderBookmarkItem,o=e.store,n=B.useState(o.get("doc")),a=n[0],i=n[1],c=function(s){i(s)};return B.useEffect(function(){return o.subscribe("doc",c),function(){o.unsubscribe("doc",c)}},[]),a?B.createElement(Cr,{doc:a,isBookmarkExpanded:t,renderBookmarkItem:r,store:o}):B.createElement("div",{className:"rpv-bookmark__loader"},B.createElement(je.Spinner,null))};lt.DownArrowIcon=ln,lt.RightArrowIcon=un,lt.bookmarkPlugin=function(){var e=B.useMemo(function(){return je.createStore({bookmarkExpandedMap:new Map})},[]);return{install:function(t){e.update("jumpToDestination",t.jumpToDestination)},onDocumentLoad:function(t){e.update("doc",t.doc)},Bookmarks:function(t){return B.createElement(_r,{isBookmarkExpanded:t==null?void 0:t.isBookmarkExpanded,renderBookmarkItem:t==null?void 0:t.renderBookmarkItem,store:e})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */cn.exports=lt;var Sr=cn.exports,mn={exports:{}},dn={};(function(e){var t=Pe;function r(l){var u=Object.create(null);return l&&Object.keys(l).forEach(function(v){if(v!=="default"){var p=Object.getOwnPropertyDescriptor(l,v);Object.defineProperty(u,v,p.get?p:{enumerable:!0,get:function(){return l[v]}})}}),u.default=l,Object.freeze(u)}var o,n=r(Se);e.ThumbnailDirection=void 0,(o=e.ThumbnailDirection||(e.ThumbnailDirection={})).Horizontal="Horizontal",o.Vertical="Vertical";var a=function(){return a=Object.assign||function(l){for(var u,v=1,p=arguments.length;v<p;v++)for(var E in u=arguments[v])Object.prototype.hasOwnProperty.call(u,E)&&(l[E]=u[E]);return l},a.apply(this,arguments)},i=function(l){var u=l.doc,v=l.getPageIndex,p=l.renderSpinner,E=l.store,S=l.width,j=u.numPages,A=v?v({numPages:j}):0,H=Math.max(0,Math.min(A,j-1)),J=E.get("pagesRotation")||new Map,V=J.has(H)?J.get(H):0,x=n.useState(""),M=x[0],O=x[1],X=t.useIsMounted(),w=n.useRef(),D=n.useState(E.get("rotation")||0),Z=D[0],Q=D[1],oe=n.useState(V),Y=oe[0],z=oe[1],re=n.useState(!1),le=re[0],ee=re[1],K=function(ae){var Ee=ae.has(H)?ae.get(H):0;z(Ee)},Me=function(ae){Q(ae)},se=t.useIntersectionObserver({onVisibilityChanged:function(ae){ee(ae.isVisible)}});return n.useEffect(function(){if(le){var ae=se.current;ae&&(O(""),t.getPage(u,H).then(function(Ee){var de=Ee.getViewport({scale:1}),Te=(de.rotation+Z+Y)%360,Ne=Math.abs(Z+Y)%180==0,Oe=Ne?de.width:de.height,we=Ne?de.height:de.width,be=document.createElement("canvas"),xe=be.getContext("2d",{alpha:!1}),Re=ae.clientWidth,Le=ae.clientHeight,F=S?S/Oe:Math.min(Re/Oe,Le/we),I=F*Oe,fe=F*we;be.height=fe,be.width=I,be.style.opacity="0";var ye=Ee.getViewport({rotation:Te,scale:F});w.current=Ee.render({canvasContext:xe,viewport:ye}),w.current.promise.then(function(){X.current&&O(be.toDataURL()),be.width=0,be.height=0},function(){})}))}},[Y,le]),n.useEffect(function(){return E.subscribe("pagesRotation",K),E.subscribe("rotation",Me),function(){E.unsubscribe("pagesRotation",K),E.unsubscribe("rotation",Me)}},[]),n.useEffect(function(){return function(){var ae;(ae=w.current)===null||ae===void 0||ae.cancel()}},[]),n.createElement("div",{ref:se,className:"rpv-thumbnail__cover-inner","data-testid":"thumbnail__cover-inner"},M?n.createElement("img",{className:"rpv-thumbnail__cover-image","data-testid":"thumbnail__cover-image",src:M}):n.createElement("div",{className:"rpv-thumbnail__cover-loader","data-testid":"thumbnail__cover-loader"},p?p():n.createElement(t.Spinner,null)))},c=function(l){var u=l.getPageIndex,v=l.renderSpinner,p=l.store,E=l.width,S=n.useState(p.get("doc")),j=S[0],A=S[1],H=function(J){A(J)};return n.useEffect(function(){return p.subscribe("doc",H),function(){p.unsubscribe("doc",H)}},[]),n.createElement("div",{className:"rpv-thumbnail__cover"},j?n.createElement(i,{doc:j,getPageIndex:u,renderSpinner:v,store:p,width:E}):n.createElement("div",{className:"rpv-thumbnail__cover-loader"},v?v():n.createElement(t.Spinner,null)))},s=function(){return n.createElement(t.Spinner,null)},g=n.createContext({renderSpinner:s}),f=function(l){var u=l.children,v=l.doc,p=t.useIsMounted(),E=n.useState({loading:!0,labels:[]}),S=E[0],j=E[1];return n.useEffect(function(){v.getPageLabels().then(function(A){p.current&&j({loading:!1,labels:A||[]})})},[v.loadingTask.docId]),S.loading?n.createElement(n.Fragment,null):u(S.labels)},b=function(l){var u=l.page,v=l.pageHeight,p=l.pageIndex,E=l.pageWidth,S=l.rotation,j=l.thumbnailHeight,A=l.thumbnailWidth,H=l.onRenderCompleted,J=n.useContext(t.LocalizationContext).l10n,V=n.useRef(),x=n.useState(""),M=x[0],O=x[1],X=J&&J.thumbnail?J.thumbnail.thumbnailLabel:"Thumbnail of page {{pageIndex}}";return n.useEffect(function(){var w=V.current;w&&w.cancel();var D=document.createElement("canvas"),Z=D.getContext("2d",{alpha:!1}),Q=A,oe=Q/(E/v),Y=Q/E;D.height=oe,D.width=Q,D.style.height="".concat(oe,"px"),D.style.width="".concat(Q,"px");var z=u.getViewport({rotation:S,scale:Y});return V.current=u.render({canvasContext:Z,viewport:z}),V.current.promise.then(function(){O(D.toDataURL()),H(p)},function(){H(p)}),function(){var re;(re=V.current)===null||re===void 0||re.cancel()}},[S]),M?n.createElement("img",{"aria-label":X.replace("{{pageIndex}}","".concat(p+1)),src:M,height:"".concat(j,"px"),width:"".concat(A,"px")}):n.useContext(g).renderSpinner()},m=function(l){var u=l.doc,v=l.pageHeight,p=l.pageIndex,E=l.pageRotation,S=l.pageWidth,j=l.rotation,A=l.shouldRender,H=l.thumbnailWidth,J=l.onRenderCompleted,V=l.onVisibilityChanged,x=t.useIsMounted(),M=n.useState({height:v,page:null,viewportRotation:0,width:S}),O=M[0],X=M[1],w=O.page,D=O.height,Z=O.width,Q=Z/D,oe=Math.abs(j+E)%180==0,Y=oe?H:H/Q,z=oe?H/Q:H;n.useEffect(function(){A&&t.getPage(u,p).then(function(ee){var K=ee.getViewport({scale:1});x.current&&X({height:K.height,page:ee,viewportRotation:K.rotation,width:K.width})})},[A]);var re=(O.viewportRotation+j+E)%360,le=t.useIntersectionObserver({onVisibilityChanged:function(ee){V(p,ee)}});return n.createElement("div",{className:"rpv-thumbnail__container","data-testid":"thumbnail__container-".concat(p),ref:le,style:{height:"".concat(z,"px"),width:"".concat(Y,"px")}},w?n.createElement(b,{page:w,pageHeight:oe?D:Z,pageIndex:p,pageWidth:oe?Z:D,rotation:re,thumbnailHeight:z,thumbnailWidth:Y,onRenderCompleted:J}):n.useContext(g).renderSpinner())},d=function(l){var u=l.currentPage,v=l.doc,p=l.labels,E=l.pagesRotation,S=l.pageHeight,j=l.pageWidth,A=l.renderCurrentPageLabel,H=l.renderThumbnailItem,J=l.rotatedPage,V=l.rotation,x=l.thumbnailDirection,M=l.thumbnailWidth,O=l.viewMode,X=l.onJumpToPage,w=l.onRotatePage,D=v.numPages,Z=v.loadingTask.docId,Q=n.useRef(null),oe=n.useRef([]),Y=n.useState(u),z=Y[0],re=Y[1],le=n.useContext(t.ThemeContext).direction===t.TextDirection.RightToLeft,ee=n.useState(-1),K=ee[0],Me=ee[1],se=t.useIsMounted(),ae=t.usePrevious(O),Ee=n.useRef(!1),de=t.useRenderQueue({doc:v}),Te=n.useMemo(function(){return Array(D).fill(0).map(function(F,I){return I})},[Z]),Ne=n.useMemo(function(){switch(O){case t.ViewMode.DualPage:return t.chunk(Te,2);case t.ViewMode.DualPageWithCover:return[[Te[0]]].concat(t.chunk(Te.slice(1),2));case t.ViewMode.SinglePage:default:return t.chunk(Te,1)}},[Z,O]),Oe=function(){if(Q.current){var F=oe.current,I=z+1;I<F.length&&(z>=0&&F[z].setAttribute("tabindex","-1"),re(I))}},we=function(){if(Q.current){var F=oe.current,I=z-1;I>=0&&(z>=0&&F[z].setAttribute("tabindex","-1"),re(I))}},be=function(){z>=0&&z<D&&X(z)};t.useIsomorphicLayoutEffect(function(){var F=Q.current;F&&(oe.current=Array.from(F.querySelectorAll(".rpv-thumbnail__item")))},[O]),n.useEffect(function(){var F=oe.current;if(!(F.length===0||z<0||z>F.length)){var I=F[z];I.setAttribute("tabindex","0"),I.focus()}},[z]),t.useIsomorphicLayoutEffect(function(){var F=Q.current,I=oe.current;if(!(!F||I.length===0||u<0||u>I.length)){var fe=I[u].closest(".rpv-thumbnail__items");fe&&(x===e.ThumbnailDirection.Vertical?function(ye,G){var ke=ye.getBoundingClientRect().top-G.getBoundingClientRect().top,Be=ye.clientHeight,Ve=G.clientHeight;ke<0?G.scrollTop+=ke:ke+Be<=Ve||(G.scrollTop+=ke+Be-Ve)}(fe,F):function(ye,G){var ke=ye.getBoundingClientRect().left-G.getBoundingClientRect().left,Be=ye.clientWidth,Ve=G.clientWidth;ke<0?G.scrollLeft+=ke:ke+Be<=Ve||(G.scrollLeft+=ke+Be-Ve)}(fe,F))}},[u,x]);var xe=n.useCallback(function(F){se.current&&(de.markRendered(F),Ee.current=!1,Le())},[Z]),Re=n.useCallback(function(F,I){I.isVisible?de.setVisibility(F,I.ratio):de.setOutOfRange(F),Le()},[Z]),Le=n.useCallback(function(){if(!Ee.current){var F=de.getHighestPriorityPage();F>-1&&(de.markRendering(F),Ee.current=!0,Me(F))}},[Z]);return n.useEffect(function(){J>=0&&(de.markRendering(J),Ee.current=!0,Me(J))},[Z,J]),t.useIsomorphicLayoutEffect(function(){ae!==O&&(de.markNotRendered(),Le())},[O]),n.createElement("div",{ref:Q,"data-testid":"thumbnail__list",className:t.classNames({"rpv-thumbnail__list":!0,"rpv-thumbnail__list--horizontal":x===e.ThumbnailDirection.Horizontal,"rpv-thumbnail__list--rtl":le,"rpv-thumbnail__list--vertical":x===e.ThumbnailDirection.Vertical}),onKeyDown:function(F){switch(F.key){case"ArrowDown":Oe();break;case"ArrowUp":we();break;case"Enter":be()}}},Ne.map(function(F,I){var fe=!1;switch(O){case t.ViewMode.DualPage:fe=u===2*I||u===2*I+1;break;case t.ViewMode.DualPageWithCover:fe=u===0&&I===0||I>0&&u===2*I-1||I>0&&u===2*I;break;case t.ViewMode.SinglePage:default:fe=u===I}return n.createElement("div",{className:t.classNames({"rpv-thumbnail__items":!0,"rpv-thumbnail__items--dual":O===t.ViewMode.DualPage,"rpv-thumbnail__items--dual-cover":O===t.ViewMode.DualPageWithCover,"rpv-thumbnail__items--single":O===t.ViewMode.SinglePage,"rpv-thumbnail__items--selected":fe}),key:"".concat(I,"___").concat(O)},F.map(function(ye){return function(G){var ke=O===t.ViewMode.DualPageWithCover&&(G===0||D%2==0&&G===D-1),Be="".concat(v.loadingTask.docId,"___").concat(G),Ve=p.length===D?p[G]:"".concat(G+1),ct=A?A({currentPage:u,pageIndex:G,numPages:D,pageLabel:Ve}):Ve,Ct=E.has(G)?E.get(G):0,Ut=n.createElement(m,{doc:v,pageHeight:S,pageIndex:G,pageRotation:Ct,pageWidth:j,rotation:V,shouldRender:K===G,thumbnailWidth:M,onRenderCompleted:xe,onVisibilityChanged:Re});return H?H({currentPage:u,key:Be,numPages:D,pageIndex:G,renderPageLabel:n.createElement(n.Fragment,null,ct),renderPageThumbnail:Ut,onJumpToPage:function(){return X(G)},onRotatePage:function(mr){return w(G,mr)}}):n.createElement("div",{key:Be},n.createElement("div",{className:t.classNames({"rpv-thumbnail__item":!0,"rpv-thumbnail__item--dual-even":O===t.ViewMode.DualPage&&G%2==0,"rpv-thumbnail__item--dual-odd":O===t.ViewMode.DualPage&&G%2==1,"rpv-thumbnail__item--dual-cover":ke,"rpv-thumbnail__item--dual-cover-even":O===t.ViewMode.DualPageWithCover&&!ke&&G%2==0,"rpv-thumbnail__item--dual-cover-odd":O===t.ViewMode.DualPageWithCover&&!ke&&G%2==1,"rpv-thumbnail__item--single":O===t.ViewMode.SinglePage,"rpv-thumbnail__item--selected":u===G}),role:"button",tabIndex:u===G?0:-1,onClick:function(){return X(G)}},Ut),n.createElement("div",{"data-testid":"thumbnail__label-".concat(G),className:"rpv-thumbnail__label"},ct))}(ye)}))}))},h=function(l){var u=l.renderCurrentPageLabel,v=l.renderThumbnailItem,p=l.store,E=l.thumbnailDirection,S=l.thumbnailWidth,j=n.useState(p.get("doc")),A=j[0],H=j[1],J=n.useState(p.get("currentPage")||0),V=J[0],x=J[1],M=n.useState(p.get("pageHeight")||0),O=M[0],X=M[1],w=n.useState(p.get("pageWidth")||0),D=w[0],Z=w[1],Q=n.useState(p.get("rotation")||0),oe=Q[0],Y=Q[1],z=n.useState(p.get("pagesRotation")||new Map),re=z[0],le=z[1],ee=n.useState(p.get("rotatedPage")||-1),K=ee[0],Me=ee[1],se=n.useState(p.get("viewMode")),ae=se[0],Ee=se[1],de=function(I){x(I)},Te=function(I){H(I)},Ne=function(I){X(I)},Oe=function(I){Z(I)},we=function(I){Y(I)},be=function(I){le(I)},xe=function(I){Me(I)},Re=function(I){Ee(I)},Le=function(I){var fe=p.get("jumpToPage");fe&&fe(I)},F=function(I,fe){p.get("rotatePage")(I,fe)};return n.useEffect(function(){return p.subscribe("doc",Te),p.subscribe("pageHeight",Ne),p.subscribe("pageWidth",Oe),p.subscribe("rotatedPage",xe),p.subscribe("rotation",we),p.subscribe("pagesRotation",be),p.subscribe("viewMode",Re),function(){p.unsubscribe("doc",Te),p.unsubscribe("pageHeight",Ne),p.unsubscribe("pageWidth",Oe),p.unsubscribe("rotatedPage",xe),p.unsubscribe("rotation",we),p.unsubscribe("pagesRotation",be),p.unsubscribe("viewMode",Re)}},[]),t.useIsomorphicLayoutEffect(function(){return p.subscribe("currentPage",de),function(){p.unsubscribe("currentPage",de)}},[]),A?n.createElement(t.LazyRender,{testId:"thumbnail__list-container",attrs:{className:"rpv-thumbnail__list-container"}},n.createElement(f,{doc:A},function(I){return n.createElement(d,{currentPage:V,doc:A,labels:I,pagesRotation:re,pageHeight:O,pageWidth:D,renderCurrentPageLabel:u,renderThumbnailItem:v,rotatedPage:K,rotation:oe,thumbnailDirection:E,thumbnailWidth:S,viewMode:ae,onJumpToPage:Le,onRotatePage:F})})):n.createElement("div",{"data-testid":"thumbnail-list__loader",className:"rpv-thumbnail__loader"},n.useContext(g).renderSpinner())};e.thumbnailPlugin=function(l){var u=n.useMemo(function(){return t.createStore({rotatePage:function(){},viewMode:t.ViewMode.SinglePage})},[]),v=n.useState(""),p=v[0],E=v[1];return{install:function(S){u.update("jumpToPage",S.jumpToPage),u.update("rotatePage",S.rotatePage)},onDocumentLoad:function(S){E(S.doc.loadingTask.docId),u.update("doc",S.doc)},onViewerStateChange:function(S){return u.update("currentPage",S.pageIndex),u.update("pagesRotation",S.pagesRotation),u.update("pageHeight",S.pageHeight),u.update("pageWidth",S.pageWidth),u.update("rotation",S.rotation),u.update("rotatedPage",S.rotatedPage),u.update("viewMode",S.viewMode),S},Cover:function(S){return n.createElement(c,a({},S,{renderSpinner:l==null?void 0:l.renderSpinner,store:u}))},Thumbnails:n.useCallback(function(S){return n.createElement(g.Provider,{value:{renderSpinner:(l==null?void 0:l.renderSpinner)||s}},n.createElement(h,{renderCurrentPageLabel:l==null?void 0:l.renderCurrentPageLabel,renderThumbnailItem:S==null?void 0:S.renderThumbnailItem,store:u,thumbnailDirection:(S==null?void 0:S.thumbnailDirection)||e.ThumbnailDirection.Vertical,thumbnailWidth:(l==null?void 0:l.thumbnailWidth)||100}))},[p])}}})(dn);/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */mn.exports=dn;var Pr=mn.exports,fn={exports:{}},ut={},pn={exports:{}},gn={};(function(e){var t=Pe;function r(h){var l=Object.create(null);return h&&Object.keys(h).forEach(function(u){if(u!=="default"){var v=Object.getOwnPropertyDescriptor(h,u);Object.defineProperty(l,u,v.get?v:{enumerable:!0,get:function(){return h[u]}})}}),l.default=h,Object.freeze(l)}var o,n=r(Se),a=function(){return n.createElement(t.Icon,{size:16},n.createElement("path",{d:`M11.5,5.5v-2C11.5,2.672,12.172,2,13,2s1.5,0.672,1.5,1.5v2 M14.5,11.5v-6C14.5,4.672,15.172,4,16,4
            c0.828,0,1.5,0.672,1.5,1.5v3 M17.5,13V8.5C17.5,7.672,18.172,7,19,7s1.5,0.672,1.5,1.5v10c0,2.761-2.239,5-5,5h-3.335
            c-1.712-0.001-3.305-0.876-4.223-2.321C6.22,18.467,4.083,14,4.083,14c-0.378-0.545-0.242-1.292,0.303-1.67
            c0.446-0.309,1.044-0.281,1.458,0.07L8.5,15.5v-10C8.5,4.672,9.172,4,10,4s1.5,0.672,1.5,1.5v6`}))},i=function(){return i=Object.assign||function(h){for(var l,u=1,v=arguments.length;u<v;u++)for(var p in l=arguments[u])Object.prototype.hasOwnProperty.call(l,p)&&(h[p]=l[p]);return h},i.apply(this,arguments)};e.SelectionMode=void 0,(o=e.SelectionMode||(e.SelectionMode={})).Hand="Hand",o.Text="Text";var c=function(){return n.createElement(t.Icon,{size:16},n.createElement("path",{d:`M13.675,11.671l2.941-2.941c0.195-0.196,0.195-0.512-0.001-0.707C16.563,7.971,16.5,7.931,16.43,7.906
            L4.168,3.527C3.908,3.434,3.622,3.57,3.529,3.83c-0.039,0.109-0.039,0.228,0,0.336l4.379,12.262
            c0.093,0.26,0.379,0.396,0.639,0.303c0.07-0.025,0.133-0.065,0.185-0.117l2.943-2.943l6.146,6.146c0.195,0.195,0.512,0.195,0.707,0
            l1.293-1.293c0.195-0.195,0.195-0.512,0-0.707L13.675,11.671z`}))},s=function(h){var l=h.children,u=h.mode,v=h.onClick,p=n.useContext(t.LocalizationContext).l10n,E="",S=n.createElement(c,null);switch(u){case e.SelectionMode.Hand:E=p&&p.selectionMode?p.selectionMode.handTool:"Hand tool",S=n.createElement(a,null);break;case e.SelectionMode.Text:default:E=p&&p.selectionMode?p.selectionMode.textSelectionTool:"Text selection tool",S=n.createElement(c,null)}return l({icon:S,label:E,onClick:v})},g={left:0,top:8},f=function(h){var l=h.isSelected,u=h.mode,v=h.onClick,p="";switch(u){case e.SelectionMode.Hand:p="selection-mode__hand-button";break;case e.SelectionMode.Text:default:p="selection-mode__text-button"}return n.createElement(s,{mode:u,onClick:v},function(E){return n.createElement(t.Tooltip,{ariaControlsSuffix:"selection-mode-switch",position:t.Position.BottomCenter,target:n.createElement(t.MinimalButton,{ariaLabel:E.label,isSelected:l,testId:p,onClick:E.onClick},E.icon),content:function(){return E.label},offset:g})})},b=function(h){var l=h.children,u=h.mode,v=h.store,p=u===v.get("selectionMode");return(l||function(E){return n.createElement(f,{isSelected:p,mode:E.mode,onClick:E.onClick})})({isSelected:p,mode:u,onClick:function(){return v.update("selectionMode",u)}})},m=function(h){var l=h.isSelected,u=h.mode,v=h.onClick,p="";switch(u){case e.SelectionMode.Hand:p="selection-mode__hand-menu";break;case e.SelectionMode.Text:default:p="selection-mode__text-menu"}return n.createElement(s,{mode:u,onClick:v},function(E){return n.createElement(t.MenuItem,{checked:l,icon:E.icon,testId:p,onClick:E.onClick},E.label)})},d=function(h){var l=h.store,u=n.useRef(null),v=n.useState(e.SelectionMode.Text),p=v[0],E=v[1],S=n.useRef({top:0,left:0,x:0,y:0}),j=function(x){var M=u.current;M&&(M.scrollTop=S.current.top-(x.clientY-S.current.y),M.scrollLeft=S.current.left-(x.clientX-S.current.x))},A=function(){var x=u.current;x&&(x.classList.add("rpv-selection-mode__grab"),x.classList.remove("rpv-selection-mode__grabbing"),document.removeEventListener("mousemove",j),document.removeEventListener("mouseup",A))},H=function(x){var M=u.current;M&&p!==e.SelectionMode.Text&&(M.classList.remove("rpv-selection-mode__grab"),M.classList.add("rpv-selection-mode__grabbing"),x.preventDefault(),x.stopPropagation(),S.current={left:M.scrollLeft,top:M.scrollTop,x:x.clientX,y:x.clientY},document.addEventListener("mousemove",j),document.addEventListener("mouseup",A))},J=function(x){u.current=x()},V=function(x){E(x)};return n.useEffect(function(){var x=u.current;if(x)return p===e.SelectionMode.Hand?x.classList.add("rpv-selection-mode__grab"):x.classList.remove("rpv-selection-mode__grab"),x.addEventListener("mousedown",H),function(){x.removeEventListener("mousedown",H)}},[p]),n.useEffect(function(){return l.subscribe("getPagesContainer",J),l.subscribe("selectionMode",V),function(){l.unsubscribe("getPagesContainer",J),l.unsubscribe("selectionMode",V)}},[]),n.createElement(n.Fragment,null)};e.HandToolIcon=a,e.TextSelectionIcon=c,e.selectionModePlugin=function(h){var l=n.useMemo(function(){return t.createStore()},[]),u=function(v){return n.createElement(b,i({},v,{store:l}))};return{install:function(v){l.update("selectionMode",h&&h.selectionMode?h.selectionMode:e.SelectionMode.Text),l.update("getPagesContainer",v.getPagesContainer)},renderViewer:function(v){var p=v.slot;return p.subSlot&&p.subSlot.children&&(p.subSlot.children=n.createElement(n.Fragment,null,n.createElement(d,{store:l}),p.subSlot.children)),p},SwitchSelectionMode:u,SwitchSelectionModeButton:function(v){return n.createElement(u,{mode:v.mode},function(p){return n.createElement(f,{isSelected:p.isSelected,mode:p.mode,onClick:function(){p.onClick()}})})},SwitchSelectionModeMenuItem:function(v){return n.createElement(u,{mode:v.mode},function(p){return n.createElement(m,{isSelected:p.isSelected,mode:p.mode,onClick:function(){p.onClick(),v.onClick()}})})}}}})(gn);/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */pn.exports=gn;var Mr=pn.exports,vn={exports:{}},st={},te=Pe;function wr(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var T=wr(Se),Tt=function(){return T.createElement(te.Icon,{size:16},T.createElement("path",{d:"M11.5 23.499L11.5 14.499"}),T.createElement("path",{d:"M7.5 18.499L11.5 14.499 15.5 18.499"}),T.createElement("path",{d:"M11.5 1.499L11.5 10.499"}),T.createElement("path",{d:"M7.5 6.499L11.5 10.499 15.5 6.499"}),T.createElement("path",{d:"M20.5 12.499L1.5 12.499"}))},Lt=function(){return T.createElement(te.Icon,{size:16},T.createElement("path",{d:"M0.5 12L23.5 12"}),T.createElement("path",{d:"M11.5 1L11.5 23"}),T.createElement("path",{d:"M8.5 4L11.5 1 14.5 4"}),T.createElement("path",{d:"M20.5 9L23.5 12 20.5 15"}),T.createElement("path",{d:"M3.5 15L0.5 12 3.5 9"}),T.createElement("path",{d:"M14.5 20L11.5 23 8.5 20"}))},pt=function(){return pt=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},pt.apply(this,arguments)},xr={left:0,top:8},hn=function(e){var t=e.enableShortcuts,r=e.onClick,o=T.useContext(te.LocalizationContext).l10n,n=o&&o.fullScreen?o.fullScreen.enterFullScreen:"Full screen",a=t?te.isMac()?"Meta+Ctrl+F":"F11":"";return T.createElement(te.Tooltip,{ariaControlsSuffix:"full-screen-enter",position:te.Position.BottomCenter,target:T.createElement(te.MinimalButton,{ariaKeyShortcuts:a,ariaLabel:n,isDisabled:!te.isFullScreenEnabled(),testId:"full-screen__enter-button",onClick:r},T.createElement(Lt,null)),content:function(){return n},offset:xr})},yr={left:0,top:8},Ir=function(e){var t=e.onClick,r=T.useContext(te.LocalizationContext).l10n,o=r&&r.fullScreen?r.fullScreen.exitFullScreen:"Exit full screen";return T.createElement(te.Tooltip,{ariaControlsSuffix:"full-screen-exit",position:te.Position.BottomCenter,target:T.createElement(te.MinimalButton,{ariaKeyShortcuts:"Esc",ariaLabel:o,testId:"full-screen__exit-button-with-tooltip",onClick:t},T.createElement(Tt,null)),content:function(){return o},offset:yr})},Ot=function(e,t){var r=T.useState(t.get("fullScreenMode")),o=r[0],n=r[1],a=T.useCallback(function(i){n(i)},[]);return T.useEffect(function(){return t.subscribe("fullScreenMode",a),function(){t.unsubscribe("fullScreenMode",a)}},[]),{enterFullScreen:function(){var i=t.get("getPagesContainer");if(i){var c=e(i());t.get("enterFullScreenMode")(c)}},exitFullScreen:function(){t.get("exitFullScreenMode")()},isFullScreen:o===te.FullScreenMode.Entering||o===te.FullScreenMode.EnteredCompletely}},Tr=function(e){var t=e.children,r=e.enableShortcuts,o=e.getFullScreenTarget,n=e.store,a=Ot(o,n),i=a.enterFullScreen,c=a.exitFullScreen,s=a.isFullScreen;return(t||function(g){return s?T.createElement(Ir,{onClick:g.onClick}):T.createElement(hn,{enableShortcuts:r,onClick:g.onClick})})({onClick:s?c:i})},Lr=function(e){var t=e.onClick,r=T.useContext(te.LocalizationContext).l10n,o=r&&r.fullScreen?r.fullScreen.enterFullScreen:"Full screen";return T.createElement(te.MenuItem,{icon:T.createElement(Lt,null),isDisabled:!te.isFullScreenEnabled(),testId:"full-screen__enter-menu",onClick:t},o)},Or=function(e){var t=e.onClick,r=T.useContext(te.LocalizationContext).l10n,o=T.useContext(te.ThemeContext).direction===te.TextDirection.RightToLeft,n=r&&r.fullScreen?r.fullScreen.exitFullScreen:"Exit full screen";return T.createElement("div",{className:te.classNames({"rpv-full-screen__exit-button":!0,"rpv-full-screen__exit-button--ltr":!o,"rpv-full-screen__exit-button--rtl":o})},T.createElement(te.MinimalButton,{ariaLabel:n,testId:"full-screen__exit-button",onClick:t},T.createElement(Tt,null)))},Dr=function(e){var t=e.children,r=e.getFullScreenTarget,o=e.store,n=Ot(r,o),a=n.enterFullScreen,i=n.exitFullScreen,c=n.isFullScreen;return c&&(t||function(s){return T.createElement(Or,{onClick:s.onClick})})({onClick:c?i:a})},$r=function(e){var t=e.store,r=e.onEnterFullScreen,o=e.onExitFullScreen,n=T.useState(t.get("fullScreenMode")),a=n[0],i=n[1],c=T.useCallback(function(s){i(s)},[]);return T.useEffect(function(){switch(a){case te.FullScreenMode.EnteredCompletely:r(t.get("zoom"));break;case te.FullScreenMode.Exited:o(t.get("zoom"))}},[a]),T.useEffect(function(){return t.subscribe("fullScreenMode",c),function(){t.unsubscribe("fullScreenMode",c)}},[]),(a===te.FullScreenMode.Entering||a===te.FullScreenMode.Entered)&&T.createElement("div",{className:"rpv-full-screen__overlay"},T.createElement(te.Spinner,null))},Nr=function(e){var t=e.containerRef,r=e.getFullScreenTarget,o=e.store,n=Ot(r,o).enterFullScreen,a=function(i){if(!i.shiftKey&&!i.altKey&&(te.isMac()?i.metaKey&&i.ctrlKey&&i.key==="f":i.key==="F11")){var c=t.current;c&&document.activeElement&&c.contains(document.activeElement)&&(i.preventDefault(),n())}};return T.useEffect(function(){if(t.current)return document.addEventListener("keydown",a),function(){document.removeEventListener("keydown",a)}},[t.current]),T.createElement(T.Fragment,null)};st.ExitFullScreenIcon=Tt,st.FullScreenIcon=Lt,st.fullScreenPlugin=function(e){var t=(e==null?void 0:e.getFullScreenTarget)||function(i){return i},r=T.useMemo(function(){return Object.assign({},{enableShortcuts:!0,onEnterFullScreen:function(){},onExitFullScreen:function(){}},e)},[]),o=T.useMemo(function(){return te.createStore({enterFullScreenMode:function(){},exitFullScreenMode:function(){},fullScreenMode:te.FullScreenMode.Normal,zoom:function(){}})},[]),n=function(i){return T.createElement(Tr,pt({},i,{enableShortcuts:r.enableShortcuts,getFullScreenTarget:t,store:o}))},a=function(){return T.createElement(Dr,{getFullScreenTarget:t,store:o},e==null?void 0:e.renderExitFullScreenButton)};return{install:function(i){o.update("enterFullScreenMode",i.enterFullScreenMode),o.update("exitFullScreenMode",i.exitFullScreenMode),o.update("getPagesContainer",i.getPagesContainer),o.update("zoom",i.zoom)},onViewerStateChange:function(i){return o.update("fullScreenMode",i.fullScreenMode),i},renderViewer:function(i){var c=i.slot;return c.subSlot&&(c.subSlot.children=T.createElement(T.Fragment,null,r.enableShortcuts&&T.createElement(Nr,{containerRef:i.containerRef,getFullScreenTarget:t,store:o}),T.createElement($r,{store:o,onEnterFullScreen:r.onEnterFullScreen,onExitFullScreen:r.onExitFullScreen}),T.createElement(a,null),c.subSlot.children)),c},EnterFullScreen:n,EnterFullScreenButton:function(){return T.createElement(n,null,function(i){return T.createElement(hn,pt({enableShortcuts:r.enableShortcuts},i))})},EnterFullScreenMenuItem:function(i){return T.createElement(n,null,function(c){return T.createElement(Lr,{onClick:function(){c.onClick(),i.onClick()}})})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */vn.exports=st;var Rr=vn.exports,bn={exports:{}},St={},We=Pe;function zr(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var ge=zr(Se),Dt=function(){return ge.createElement(We.Icon,{size:16},ge.createElement("path",{d:"M5.5,11.5c-.275,0-.341.159-.146.354l6.292,6.293a.5.5,0,0,0,.709,0l6.311-6.275c.2-.193.13-.353-.145-.355L15.5,11.5V1.5a1,1,0,0,0-1-1h-5a1,1,0,0,0-1,1V11a.5.5,0,0,1-.5.5Z"}),ge.createElement("path",{d:"M23.5,18.5v4a1,1,0,0,1-1,1H1.5a1,1,0,0,1-1-1v-4"}))},gt=function(){return gt=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},gt.apply(this,arguments)},jr={left:0,top:8},En=function(e){var t=e.onClick,r=ge.useContext(We.LocalizationContext).l10n,o=r&&r.download?r.download.download:"Download";return ge.createElement(We.Tooltip,{ariaControlsSuffix:"get-file",position:We.Position.BottomCenter,target:ge.createElement(We.MinimalButton,{ariaLabel:o,testId:"get-file__download-button",onClick:t},ge.createElement(Dt,null)),content:function(){return o},offset:jr})},kn=function(e,t){var r=document.createElement("a");r.style.display="none",r.href=e,r.setAttribute("download",t),document.body.appendChild(r),r.click(),document.body.removeChild(r)},qt=function(e,t,r){var o=URL.createObjectURL(new Blob([e],{type:r}));kn(o,t),o&&URL.revokeObjectURL(o)},Fr=function(e,t){e.getData().then(function(r){/iphone|ipod|ipad/i.test(navigator.userAgent)&&!/CriOS/i.test(navigator.userAgent)?qt(r,t,"application/octet-stream"):/iphone|ipod|ipad/i.test(navigator.userAgent)&&/CriOS/i.test(navigator.userAgent)?kn("data:application/pdf;base64,".concat(function(o){return btoa(Array(o.length).fill("").map(function(n,a){return String.fromCharCode(o[a])}).join(""))}(r)),t):qt(r,t,"application/pdf")})},Br=function(e){var t=e.children,r=e.fileNameGenerator,o=e.store,n=ge.useState(o.get("file")),a=n[0],i=n[1],c=ge.useState(o.get("doc")),s=c[0],g=c[1],f=function(m){g(m)},b=function(m){i(m)};return ge.useEffect(function(){return o.subscribe("doc",f),o.subscribe("file",b),function(){o.subscribe("doc",f),o.unsubscribe("file",b)}},[]),(t||function(m){return ge.createElement(En,{onClick:m.onClick})})({onClick:function(){s&&a&&Fr(s,r(a))}})},Vr=function(e){var t=e.onClick,r=ge.useContext(We.LocalizationContext).l10n,o=r&&r.download?r.download.download:"Download";return ge.createElement(We.MenuItem,{icon:ge.createElement(Dt,null),testId:"get-file__download-menu",onClick:t},o)};St.DownloadIcon=Dt,St.getFilePlugin=function(e){var t=ge.useMemo(function(){return We.createStore({})},[]),r=function(n){return n.name?(a=n.name,(i=a.split("/").pop())?i.split("#")[0].split("?")[0]:a):"document.pdf";var a,i},o=function(n){return ge.createElement(Br,gt({},n,{fileNameGenerator:e&&e.fileNameGenerator||r,store:t}))};return{onDocumentLoad:function(n){t.update("doc",n.doc),t.update("file",n.file)},Download:o,DownloadButton:function(){return ge.createElement(o,null,function(n){return ge.createElement(En,gt({},n))})},DownloadMenuItem:function(n){return ge.createElement(o,null,function(a){return ge.createElement(Vr,{onClick:function(){a.onClick(),n.onClick()}})})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */bn.exports=St;var Ar=bn.exports,Cn={exports:{}},Pt={},Ae=Pe;function Hr(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var ne=Hr(Se),$t=function(){return ne.createElement(Ae.Icon,{size:16},ne.createElement("path",{d:"M18.5,7.5c.275,0,.341-.159.146-.354L12.354.854a.5.5,0,0,0-.708,0L5.354,7.147c-.2.195-.129.354.146.354h3v10a1,1,0,0,0,1,1h5a1,1,0,0,0,1-1V7.5Z"}),ne.createElement("path",{d:"M23.5,18.5v4a1,1,0,0,1-1,1H1.5a1,1,0,0,1-1-1v-4"}))},tt=function(){return tt=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},tt.apply(this,arguments)},_n=function(e){var t=ne.useRef(),r=function(){var n=t.current;n&&(n.click(),e.get("triggerOpenFile")&&e.update("triggerOpenFile",!1))},o=function(n){n&&r()};return ne.useEffect(function(){return e.subscribe("triggerOpenFile",o),function(){e.unsubscribe("triggerOpenFile",o)}},[]),{inputRef:t,openFile:r}},Wr={left:0,top:8},Kr=function(e){var t=e.enableShortcuts,r=e.store,o=e.onClick,n=ne.useContext(Ae.LocalizationContext).l10n,a=n&&n.open?n.open.openFile:"Open file",i=_n(r),c=i.inputRef,s=i.openFile,g=t?Ae.isMac()?"Meta+O":"Ctrl+O":"";return ne.createElement(Ae.Tooltip,{ariaControlsSuffix:"open",position:Ae.Position.BottomCenter,target:ne.createElement("div",{className:"rpv-open__input-wrapper"},ne.createElement("input",{accept:".pdf",ref:c,className:"rpv-open__input",multiple:!1,tabIndex:-1,title:"",type:"file",onChange:o}),ne.createElement(Ae.MinimalButton,{ariaKeyShortcuts:g,ariaLabel:a,testId:"open__button",onClick:s},ne.createElement($t,null))),content:function(){return a},offset:Wr})},Zr=function(e){var t=e.children,r=e.enableShortcuts,o=e.store;return(t||function(n){return ne.createElement(Kr,{enableShortcuts:r,store:o,onClick:n.onClick})})({onClick:function(n){var a=n.target.files;if(a&&a.length){var i=o.get("openFile");i&&i(a[0])}}})},Gr=function(e){var t=e.store,r=e.onClick,o=ne.useContext(Ae.LocalizationContext).l10n,n=o&&o.open?o.open.openFile:"Open file",a=_n(t),i=a.inputRef,c=a.openFile;return ne.createElement(Ae.MenuItem,{icon:ne.createElement($t,null),testId:"open__menu",onClick:c},ne.createElement("div",{className:"rpv-open__input-wrapper"},ne.createElement("input",{accept:".pdf",ref:i,className:"rpv-open__input",multiple:!1,tabIndex:-1,title:"",type:"file",onChange:r}),n))},Ur=function(e){var t=e.containerRef,r=e.store,o=function(n){if(!n.shiftKey&&!n.altKey&&n.key==="o"&&(Ae.isMac()?n.metaKey:n.ctrlKey)){var a=t.current;a&&document.activeElement&&a.contains(document.activeElement)&&(n.preventDefault(),r.update("triggerOpenFile",!0))}};return ne.useEffect(function(){if(t.current)return document.addEventListener("keydown",o),function(){document.removeEventListener("keydown",o)}},[t.current]),ne.createElement(ne.Fragment,null)};Pt.OpenFileIcon=$t,Pt.openPlugin=function(e){var t=ne.useMemo(function(){return Object.assign({},{enableShortcuts:!0},e)},[]),r=ne.useMemo(function(){return Ae.createStore({})},[]),o=function(n){return ne.createElement(Zr,tt({enableShortcuts:t.enableShortcuts},n,{store:r}))};return{install:function(n){r.update("openFile",n.openFile)},renderViewer:function(n){var a=n.slot,i={children:ne.createElement(ne.Fragment,null,t.enableShortcuts&&ne.createElement(Ur,{containerRef:n.containerRef,store:r}),a.children)};return tt(tt({},a),i)},Open:o,OpenButton:function(){return ne.createElement(o,null)},OpenMenuItem:function(){return ne.createElement(o,null,function(n){return ne.createElement(Gr,{store:r,onClick:n.onClick})})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */Cn.exports=Pt;var qr=Cn.exports,Sn={exports:{}},qe={},q=Pe;function Jr(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var C=Jr(Se),Nt=function(){return C.createElement(q.Icon,{size:16},C.createElement("path",{d:`M2.32,2.966h19.452c0.552,0.001,1,0.449,0.999,1.001c0,0.182-0.05,0.36-0.144,0.516L12.9,20.552
            c-0.286,0.472-0.901,0.624-1.373,0.338c-0.138-0.084-0.254-0.2-0.338-0.338L1.465,4.483C1.179,4.01,1.331,3.396,1.804,3.11
            C1.96,3.016,2.138,2.966,2.32,2.966z`}))},Rt=function(){return C.createElement(q.Icon,{size:16},C.createElement("path",{d:`M0.541,5.627L11.666,18.2c0.183,0.207,0.499,0.226,0.706,0.043c0.015-0.014,0.03-0.028,0.043-0.043
            L23.541,5.627`}))},Ie=function(){return Ie=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Ie.apply(this,arguments)},Ue=function(e){var t=C.useState(e.get("currentPage")||0),r=t[0],o=t[1],n=function(a){o(a)};return q.useIsomorphicLayoutEffect(function(){return e.subscribe("currentPage",n),function(){e.unsubscribe("currentPage",n)}},[]),{currentPage:r}},it=function(e){var t=C.useState(e.get("numberOfPages")||0),r=t[0],o=t[1],n=function(a){o(a)};return C.useEffect(function(){return e.subscribe("numberOfPages",n),function(){e.unsubscribe("numberOfPages",n)}},[]),{numberOfPages:r}},Xr=function(e){var t=e.store,r=C.useContext(q.LocalizationContext).l10n,o=C.useState("1"),n=o[0],a=o[1],i=Ue(t).currentPage,c=it(t).numberOfPages;C.useEffect(function(){return a("".concat(i+1))},[i]);var s=function(f){var b=t.get("jumpToPage");b&&b(f)},g=r&&r.pageNavigation?r.pageNavigation.enterPageNumber:"Enter a page number";return C.createElement("span",{className:"rpv-page-navigation__current-page-input"},C.createElement(q.TextBox,{ariaLabel:g,testId:"page-navigation__current-page-input",type:"text",value:n,onChange:a,onKeyDown:function(f){switch(f.key){case"ArrowUp":(d=i-1)>=0&&(a("".concat(d+1)),s(d));break;case"ArrowDown":(m=i+1)<c&&(a("".concat(m+1)),s(m));break;case"Enter":b=parseInt(n,10),n===""||b<1||b>c?a("".concat(i+1)):s(b-1)}var b,m,d}}))},Yr=function(e){var t=e.children,r=e.doc,o=q.useIsMounted(),n=C.useState({loading:!0,labels:[]}),a=n[0],i=n[1];return C.useEffect(function(){r.getPageLabels().then(function(c){o.current&&i({loading:!1,labels:c||[]})})},[r.loadingTask.docId]),a.loading?C.createElement(C.Fragment,null):t(a.labels)},Qr=function(e){var t=e.children,r=e.store,o=function(c){var s=C.useState(c.get("doc")),g=s[0],f=s[1],b=function(m){f(m)};return C.useEffect(function(){return c.subscribe("doc",b),function(){c.unsubscribe("doc",b)}},[]),g}(r),n=Ue(r).currentPage,a=it(r).numberOfPages,i=t||function(c){return C.createElement(C.Fragment,null,c.currentPage+1)};return o?C.createElement(Yr,{doc:o},function(c){var s=c.length===a&&a>0?c[n]:"";return i({currentPage:n,numberOfPages:a,pageLabel:s})}):C.createElement(C.Fragment,null)},zt=function(){return C.createElement(q.Icon,{size:16},C.createElement("path",{d:`M21.783,21.034H2.332c-0.552,0-1-0.448-1-1c0-0.182,0.05-0.361,0.144-0.517L11.2,3.448
            c0.286-0.472,0.901-0.624,1.373-0.338c0.138,0.084,0.254,0.2,0.338,0.338l9.726,16.069c0.286,0.473,0.134,1.087-0.339,1.373
            C22.143,20.984,21.965,21.034,21.783,21.034z`}))},eo={left:0,top:8},Pn=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToFirstPage:"First page";return C.createElement(q.Tooltip,{ariaControlsSuffix:"page-navigation-first",position:q.Position.BottomCenter,target:C.createElement(q.MinimalButton,{ariaLabel:n,isDisabled:t,testId:"page-navigation__first-button",onClick:r},C.createElement(zt,null)),content:function(){return n},offset:eo})},to=function(e){var t=e.children,r=e.store;return(t||function(o){return C.createElement(Pn,{isDisabled:o.isDisabled,onClick:o.onClick})})({isDisabled:Ue(r).currentPage===0,onClick:function(){var o=r.get("jumpToPage");o&&o(0)}})},no=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToFirstPage:"First page";return C.createElement(q.MenuItem,{icon:C.createElement(zt,null),isDisabled:t,testId:"page-navigation__first-menu",onClick:r},n)},ro={left:0,top:8},Mn=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToLastPage:"Last page";return C.createElement(q.Tooltip,{ariaControlsSuffix:"page-navigation-last",position:q.Position.BottomCenter,target:C.createElement(q.MinimalButton,{ariaLabel:n,isDisabled:t,testId:"page-navigation__last-button",onClick:r},C.createElement(Nt,null)),content:function(){return n},offset:ro})},oo=function(e){var t=e.children,r=e.store,o=Ue(r).currentPage,n=it(r).numberOfPages;return(t||function(a){return C.createElement(Mn,{isDisabled:a.isDisabled,onClick:a.onClick})})({isDisabled:o+1>=n,onClick:function(){var a=r.get("jumpToPage");a&&a(n-1)}})},ao=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToLastPage:"Last page";return C.createElement(q.MenuItem,{icon:C.createElement(Nt,null),isDisabled:t,testId:"page-navigation__last-menu",onClick:r},n)},io={left:0,top:8},wn=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToNextPage:"Next page";return C.createElement(q.Tooltip,{ariaControlsSuffix:"page-navigation-next",position:q.Position.BottomCenter,target:C.createElement(q.MinimalButton,{ariaLabel:n,isDisabled:t,testId:"page-navigation__next-button",onClick:r},C.createElement(Rt,null)),content:function(){return n},offset:io})},co=function(e){var t=e.children,r=e.store;return(t||function(o){return C.createElement(wn,{onClick:o.onClick,isDisabled:o.isDisabled})})({isDisabled:Ue(r).currentPage+1>=it(r).numberOfPages,onClick:function(){var o=r.get("jumpToNextPage");o&&o()}})},lo=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToNextPage:"Next page";return C.createElement(q.MenuItem,{icon:C.createElement(Rt,null),isDisabled:t,testId:"page-navigation__next-menu",onClick:r},n)},jt=function(){return C.createElement(q.Icon,{size:16},C.createElement("path",{d:`M23.535,18.373L12.409,5.8c-0.183-0.207-0.499-0.226-0.706-0.043C11.688,5.77,11.674,5.785,11.66,5.8
            L0.535,18.373`}))},uo={left:0,top:8},xn=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToPreviousPage:"Previous page";return C.createElement(q.Tooltip,{ariaControlsSuffix:"page-navigation-previous",position:q.Position.BottomCenter,target:C.createElement(q.MinimalButton,{ariaLabel:n,isDisabled:t,testId:"page-navigation__previous-button",onClick:r},C.createElement(jt,null)),content:function(){return n},offset:uo})},so=function(e){var t=e.store;return(e.children||function(r){return C.createElement(xn,{isDisabled:r.isDisabled,onClick:r.onClick})})({isDisabled:Ue(t).currentPage<=0,onClick:function(){var r=t.get("jumpToPreviousPage");r&&r()}})},mo=function(e){var t=e.isDisabled,r=e.onClick,o=C.useContext(q.LocalizationContext).l10n,n=o&&o.pageNavigation?o.pageNavigation.goToPreviousPage:"Previous page";return C.createElement(q.MenuItem,{icon:C.createElement(jt,null),isDisabled:t,testId:"page-navigation__previous-menu",onClick:r},n)},fo=function(e){var t=e.children,r=e.store,o=it(r).numberOfPages;return t?t({numberOfPages:o}):C.createElement(C.Fragment,null,o)},po=function(e){var t=e.containerRef,r=e.numPages,o=e.store,n=Ue(o).currentPage,a=C.useRef(n);a.current=n;var i=C.useRef(!1),c=function(){i.current=!0},s=function(){i.current=!1},g=function(f){var b=t.current,m=i.current||document.activeElement&&b.contains(document.activeElement);if(b&&m){var d,h,l=f.altKey&&f.key==="ArrowDown"||!f.shiftKey&&!f.altKey&&f.key==="PageDown",u=f.altKey&&f.key==="ArrowUp"||!f.shiftKey&&!f.altKey&&f.key==="PageUp";if(l)return f.preventDefault(),d=o.get("jumpToPage"),h=a.current+1,void(d&&h<r&&d(h));if(u)return f.preventDefault(),void function(){var v=o.get("jumpToPage"),p=a.current-1;v&&p>=0&&v(p)}();if(q.isMac()?f.metaKey&&!f.ctrlKey:f.altKey)switch(f.key){case"ArrowLeft":f.preventDefault(),function(){var v=o.get("jumpToPreviousDestination");v&&v()}();break;case"ArrowRight":f.preventDefault(),function(){var v=o.get("jumpToNextDestination");v&&v()}()}}};return C.useEffect(function(){var f=t.current;if(f)return document.addEventListener("keydown",g),f.addEventListener("mouseenter",c),f.addEventListener("mouseleave",s),function(){document.removeEventListener("keydown",g),f.removeEventListener("mouseenter",c),f.removeEventListener("mouseleave",s)}},[t.current]),C.createElement(C.Fragment,null)};qe.DownArrowIcon=Nt,qe.NextIcon=Rt,qe.PreviousIcon=jt,qe.UpArrowIcon=zt,qe.pageNavigationPlugin=function(e){var t=C.useMemo(function(){return Object.assign({},{enableShortcuts:!0},e)},[]),r=C.useMemo(function(){return q.createStore()},[]),o=function(c){return C.createElement(to,Ie({},c,{store:r}))},n=function(c){return C.createElement(oo,Ie({},c,{store:r}))},a=function(c){return C.createElement(co,Ie({},c,{store:r}))},i=function(c){return C.createElement(so,Ie({},c,{store:r}))};return{install:function(c){r.update("jumpToDestination",c.jumpToDestination),r.update("jumpToNextDestination",c.jumpToNextDestination),r.update("jumpToNextPage",c.jumpToNextPage),r.update("jumpToPage",c.jumpToPage),r.update("jumpToPreviousDestination",c.jumpToPreviousDestination),r.update("jumpToPreviousPage",c.jumpToPreviousPage)},renderViewer:function(c){var s=c.slot;if(!t.enableShortcuts)return s;var g={children:C.createElement(C.Fragment,null,C.createElement(po,{containerRef:c.containerRef,numPages:c.doc.numPages,store:r}),s.children)};return Ie(Ie({},s),g)},onDocumentLoad:function(c){r.update("doc",c.doc),r.update("numberOfPages",c.doc.numPages)},onViewerStateChange:function(c){return r.update("currentPage",c.pageIndex),c},jumpToNextPage:function(){var c=r.get("jumpToNextPage");c&&c()},jumpToPage:function(c){var s=r.get("jumpToPage");s&&s(c)},jumpToPreviousPage:function(){var c=r.get("jumpToPreviousPage");c&&c()},CurrentPageInput:function(){return C.createElement(Xr,{store:r})},CurrentPageLabel:function(c){return C.createElement(Qr,Ie({},c,{store:r}))},GoToFirstPage:o,GoToFirstPageButton:function(){return C.createElement(o,null,function(c){return C.createElement(Pn,Ie({},c))})},GoToFirstPageMenuItem:function(c){return C.createElement(o,null,function(s){return C.createElement(no,{isDisabled:s.isDisabled,onClick:function(){s.onClick(),c.onClick()}})})},GoToLastPage:n,GoToLastPageButton:function(){return C.createElement(n,null,function(c){return C.createElement(Mn,Ie({},c))})},GoToLastPageMenuItem:function(c){return C.createElement(n,null,function(s){return C.createElement(ao,{isDisabled:s.isDisabled,onClick:function(){s.onClick(),c.onClick()}})})},GoToNextPage:a,GoToNextPageButton:function(){return C.createElement(a,null,function(c){return C.createElement(wn,Ie({},c))})},GoToNextPageMenuItem:function(c){return C.createElement(a,null,function(s){return C.createElement(lo,{isDisabled:s.isDisabled,onClick:function(){s.onClick(),c.onClick()}})})},GoToPreviousPage:i,GoToPreviousPageButton:function(){return C.createElement(i,null,function(c){return C.createElement(xn,Ie({},c))})},GoToPreviousPageMenuItem:function(c){return C.createElement(i,null,function(s){return C.createElement(mo,{isDisabled:s.isDisabled,onClick:function(){s.onClick(),c.onClick()}})})},NumberOfPages:function(c){return C.createElement(fo,Ie({},c,{store:r}))}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */Sn.exports=qe;var go=Sn.exports,yn={exports:{}},Ge={},me=Pe,vo=Se,ho=dr;function bo(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var Ce,P=bo(vo),Ft=function(){return P.createElement(me.Icon,{size:16},P.createElement("path",{d:`M7.5,19.499h9 M7.5,16.499h9 M5.5,16.5h-3c-1.103-0.003-1.997-0.897-2-2v-6c0.003-1.103,0.897-1.997,2-2h19
            c1.103,0.003,1.997,0.897,2,2v6c-0.003,1.103-0.897,1.997-2,2h-3
            M5.5,4.5v-4h9.586c0.265,0,0.52,0.105,0.707,0.293l2.414,2.414
            C18.395,3.394,18.5,3.649,18.5,3.914V4.5
            M18.5,22.5c0,0.552-0.448,1-1,1h-11c-0.552,0-1-0.448-1-1v-9h13V22.5z
            M3.5,8.499
            c0.552,0,1,0.448,1,1s-0.448,1-1,1s-1-0.448-1-1S2.948,8.499,3.5,8.499z
            M14.5,0.499v4h4`}))},Je=function(){return Je=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Je.apply(this,arguments)},Eo={left:0,top:8},In=function(e){var t=e.enableShortcuts,r=e.onClick,o=P.useContext(me.LocalizationContext).l10n,n=o&&o.print?o.print.print:"Print",a=t?me.isMac()?"Meta+P":"Ctrl+P":"";return P.createElement(me.Tooltip,{ariaControlsSuffix:"print",position:me.Position.BottomCenter,target:P.createElement(me.MinimalButton,{ariaKeyShortcuts:a,ariaLabel:n,testId:"print__button",onClick:r},P.createElement(Ft,null)),content:function(){return n},offset:Eo})};(function(e){e.CheckingPermission="CheckingPermission",e.Inactive="Inactive",e.Preparing="Preparing",e.Cancelled="Cancelled",e.Ready="Ready"})(Ce||(Ce={}));var ko=function(e){var t=e.children,r=e.enableShortcuts,o=e.store;return(t||In)({enableShortcuts:r,onClick:function(){o.update("printStatus",Ce.CheckingPermission)}})},Co=function(e){var t=e.doc,r=e.store,o=P.useContext(me.LocalizationContext).l10n,n=P.useState(!0),a=n[0],i=n[1];return P.useEffect(function(){t.getPermissions().then(function(c){c===null||c.includes(4)||c.includes(2048)?r.update("printStatus",Ce.Preparing):i(!1)})},[]),a?P.createElement(P.Fragment,null):P.createElement(me.Modal,{ariaControlsSuffix:"print-permission",closeOnClickOutside:!1,closeOnEscape:!1,content:function(c){return P.createElement(P.Fragment,null,P.createElement("div",{className:"rpv-print__permission-body"},o&&o.print?o.print.disallowPrint:"The document does not allow to print"),P.createElement("div",{className:"rpv-print__permission-footer"},P.createElement(me.Button,{onClick:function(){c(),r.update("printStatus",Ce.Cancelled)}},o&&o.print?o.print.close:"Close")))},isOpened:!0})},_o=function(e){var t=e.numLoadedPages,r=e.numPages,o=e.onCancel,n=P.useContext(me.LocalizationContext).l10n,a=P.useContext(me.ThemeContext).direction===me.TextDirection.RightToLeft,i=Math.floor(100*t/r);return P.createElement("div",{className:"rpv-print__progress"},P.createElement("div",{className:me.classNames({"rpv-print__progress-body":!0,"rpv-print__progress-body--rtl":a})},P.createElement("div",{className:"rpv-print__progress-message"},n&&n.print?n.print.preparingDocument:"Preparing document ..."),P.createElement("div",{className:"rpv-print__progress-bar"},P.createElement(me.ProgressBar,{progress:i})),P.createElement(me.Button,{onClick:o},n&&n.print?n.print.cancel:"Cancel")))},So=function(e){var t=e.canvas,r=e.page,o=e.pageHeight,n=e.pageIndex,a=e.pageWidth,i=e.rotation,c=e.onLoad,s=me.useIsMounted(),g=P.useRef(),f=P.useState(""),b=f[0],m=f[1],d=P.useMemo(function(){return typeof process<"u"&&{}.JEST_WORKER_ID!==void 0},[]);return P.useEffect(function(){var h=g.current;h&&h.cancel();var l=150/72;t.height=Math.floor(o*l),t.width=Math.floor(a*l);var u=t.getContext("2d");u.save(),u.fillStyle="rgb(255, 255, 255)",u.fillRect(0,0,t.width,t.height),u.restore();var v=r.getViewport({rotation:i,scale:1});g.current=r.render({canvasContext:u,intent:"print",transform:[l,0,0,l,0,0],viewport:v}),g.current.promise.then(function(){"toBlob"in t&&"createObjectURL"in URL?t.toBlob(function(p){s.current&&m(URL.createObjectURL(p)),d&&c()}):(s.current&&m(t.toDataURL()),d&&c())},function(){})},[]),b&&P.createElement("div",{className:"rpv-print__page"},P.createElement("img",{"data-testid":"print__thumbnail-".concat(n),src:b,onLoad:function(){d||c()}}))},Po=function(e){var t=e.canvas,r=e.doc,o=e.pageIndex,n=e.pageRotation,a=e.pageSize,i=e.rotation,c=e.shouldRender,s=e.onLoad,g=me.useIsMounted(),f=P.useState(null),b=f[0],m=f[1],d=Math.abs(i+n)%180==0;P.useEffect(function(){c&&me.getPage(r,o).then(function(l){g.current&&m(l)})},[c]);var h=(a.rotation+i+n)%360;return b&&P.createElement(So,{canvas:t,page:b,pageHeight:d?a.pageHeight:a.pageWidth,pageIndex:o,pageWidth:d?a.pageWidth:a.pageHeight,rotation:h,onLoad:s})},Mo=function(e){var t=e.doc,r=e.numLoadedPages,o=e.pagesRotation,n=e.pageSizes,a=e.printPages,i=e.printStatus,c=e.rotation,s=e.onCancel,g=e.onLoad,f=P.useMemo(function(){return document.createElement("canvas")},[]),b=P.useMemo(function(){var h=document.querySelector(".rpv-print__zone");if(h)return h;var l=document.createElement("div");return l.classList.add("rpv-print__zone"),l.setAttribute("data-testid","print__zone"),document.body.appendChild(l),l},[]);P.useEffect(function(){i===Ce.Ready&&(document.documentElement.classList.add("rpv-print__html-printing"),document.body.classList.add("rpv-print__body-printing"),window.print());var h=function(){if(i===Ce.Ready){document.documentElement.classList.remove("rpv-print__html-printing"),document.body.classList.remove("rpv-print__body-printing");var l=document.querySelectorAll(".rpv-print__zone");l&&l.forEach(function(u){u.parentElement.removeChild(u)}),f.height=0,f.width=0,document.removeEventListener("mousemove",h),s()}};return document.addEventListener("mousemove",h),function(){return document.removeEventListener("mousemove",h)}},[i]);var m=n[0].pageHeight,d=n[0].pageWidth;return ho.createPortal(P.createElement(P.Fragment,null,a.map(function(h,l){return P.createElement(Po,{key:h,canvas:f,doc:t,pageIndex:h,pageRotation:o.has(h)?o.get(h):0,pageSize:n[h],rotation:c,shouldRender:l===r,onLoad:g})}),P.createElement("style",{dangerouslySetInnerHTML:{__html:"@page { size: ".concat(d,"pt ").concat(m,"pt }")}})),b)},wo=function(e){var t=e.doc,r=e.pagesRotation,o=e.pageSizes,n=e.renderProgressBar,a=e.rotation,i=e.setPages,c=e.store,s=P.useState(Ce.Inactive),g=s[0],f=s[1],b=P.useState(0),m=b[0],d=b[1],h=P.useMemo(function(){var p=t.numPages;return i(t).filter(function(E){return E>=0&&E<p})},[t,i]),l=h.length,u=function(){d(0),f(Ce.Inactive)},v=function(p){return f(p)};return P.useEffect(function(){return c.subscribe("printStatus",v),function(){c.unsubscribe("printStatus",v)}},[]),P.createElement(P.Fragment,null,g===Ce.CheckingPermission&&P.createElement(Co,{doc:t,store:c}),g===Ce.Preparing&&(n?n(m,l,u):P.createElement(_o,{numLoadedPages:m,numPages:l,onCancel:u})),(g===Ce.Preparing||g===Ce.Ready)&&m<=l&&P.createElement(Mo,{doc:t,numLoadedPages:m,pagesRotation:r,pageSizes:o,printPages:h,printStatus:g,rotation:a,onCancel:u,onLoad:function(){var p=m+1;p<=l&&(d(p),p===l&&f(Ce.Ready))}}))},xo=function(e){var t=e.onClick,r=P.useContext(me.LocalizationContext).l10n,o=r&&r.print?r.print.print:"Print";return P.createElement(me.MenuItem,{icon:P.createElement(Ft,null),testId:"print__menu",onClick:t},o)},yo=function(e){var t=e.containerRef,r=e.store,o=function(n){if(!n.shiftKey&&!n.altKey&&n.key==="p"&&(me.isMac()?n.metaKey:n.ctrlKey)){var a=t.current;a&&document.activeElement&&a.contains(document.activeElement)&&(n.preventDefault(),r.update("printStatus",Ce.Preparing))}};return P.useEffect(function(){if(t.current)return document.addEventListener("keydown",o),function(){document.removeEventListener("keydown",o)}},[t.current]),P.createElement(P.Fragment,null)};Ge.PrintIcon=Ft,Ge.getAllPagesNumbers=function(e){return Array(e.numPages).fill(0).map(function(t,r){return r})},Ge.getCustomPagesNumbers=function(e){return function(t){var r,o=[];return e.replace(/\s+/g,"").split(",").forEach(function(n){var a,i,c=n.split("-").map(function(s){return parseInt(s,10)}).filter(function(s){return Number.isInteger(s)});c.length===1?o.push(c[0]-1):c.length===2&&o.push.apply(o,(a=c[0]-1,i=c[1]-1,Array(i-a+1).fill(0).map(function(s,g){return a+g})))}),(r=o,r.filter(function(n){return r.indexOf(n)===r.lastIndexOf(n)})).filter(function(n){return n>=0&&n<t.numPages})}},Ge.getEvenPagesNumbers=function(e){return Array(e.numPages).fill(0).map(function(t,r){return r}).filter(function(t){return(t+1)%2==0})},Ge.getOddPagesNumbers=function(e){return Array(e.numPages).fill(0).map(function(t,r){return r}).filter(function(t){return(t+1)%2==1})},Ge.printPlugin=function(e){var t=P.useMemo(function(){return Object.assign({},{enableShortcuts:!0,setPages:function(n){return Array(n.numPages).fill(0).map(function(a,i){return i})}},e)},[]),r=P.useMemo(function(){return me.createStore({printStatus:Ce.Inactive})},[]),o=function(n){return P.createElement(ko,Je({enableShortcuts:t.enableShortcuts},n,{store:r}))};return{print:function(){r.update("printStatus",Ce.CheckingPermission)},renderViewer:function(n){var a=n.slot,i={children:P.createElement(P.Fragment,null,t.enableShortcuts&&P.createElement(yo,{containerRef:n.containerRef,store:r}),P.createElement(wo,{doc:n.doc,pagesRotation:n.pagesRotation,pageSizes:n.pageSizes,renderProgressBar:e==null?void 0:e.renderProgressBar,rotation:n.rotation,setPages:t.setPages,store:r}),a.children)};return Je(Je({},a),i)},Print:o,PrintButton:function(){return P.createElement(o,null,function(n){return P.createElement(In,Je({},n))})},PrintMenuItem:function(n){return P.createElement(o,null,function(a){return P.createElement(xo,{onClick:function(){a.onClick(),n.onClick()}})})},setPages:function(n){t.setPages=n}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */yn.exports=Ge;var Io=yn.exports,Tn={exports:{}},Mt={},_e=Pe;function To(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var $=To(Se),Bt=function(){return $.createElement(_e.Icon,{size:16},$.createElement("path",{d:`M12,1.001c6.075,0,11,4.925,11,11s-4.925,11-11,11s-11-4.925-11-11S5.925,1.001,12,1.001z
            M14.5,17.005H13
            c-0.552,0-1-0.448-1-1v-6.5c0-0.276-0.224-0.5-0.5-0.5H10
            M11.745,6.504L11.745,6.504
            M11.745,6.5c-0.138,0-0.25,0.112-0.25,0.25
            S11.607,7,11.745,7s0.25-0.112,0.25-0.25S11.883,6.5,11.745,6.5`}))},ot=function(){return ot=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},ot.apply(this,arguments)},Lo=function(e){var t=e.doc,r=e.render,o=$.useState(),n=o[0],a=o[1];return $.useEffect(function(){t.getMetadata().then(function(i){return Promise.resolve(i)}).then(function(i){return t.getDownloadInfo().then(function(c){return Promise.resolve({fileName:i.contentDispositionFilename||"",info:i.info,length:c.length})})}).then(function(i){a(i)})},[]),n?r(n):$.createElement("div",{className:"rpv-properties__loader"},$.createElement(_e.Spinner,null))},De=function(e){var t=e.label,r=e.value,o=$.useContext(_e.ThemeContext).direction===_e.TextDirection.RightToLeft;return $.createElement("dl",{className:_e.classNames({"rpv-properties__item":!0,"rpv-properties__item--rtl":o})},$.createElement("dt",{className:"rpv-properties__item-label"},t,":"),$.createElement("dd",{className:"rpv-properties__item-value"},r||"-"))},Oo=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"),Ze=function(e,t,r,o){var n=parseInt(e,10);return n>=t&&n<=r?n:o},Do=function(e){var t=e.doc,r=e.fileName,o=e.onToggle,n=$.useContext(_e.LocalizationContext).l10n,a=function(i){var c=function(s){var g=Oo.exec(s);if(!g)return null;var f=parseInt(g[1],10),b=Ze(g[2],1,12,1)-1,m=Ze(g[3],1,31,1),d=Ze(g[4],0,23,0),h=Ze(g[5],0,59,0),l=Ze(g[6],0,59,0),u=g[7]||"Z",v=Ze(g[8],0,23,0),p=Ze(g[9],0,59,0);switch(u){case"-":d+=v,h+=p;break;case"+":d-=v,h-=p}return new Date(Date.UTC(f,b,m,d,h,l))}(i);return c?"".concat(c.toLocaleDateString(),", ").concat(c.toLocaleTimeString()):""};return $.createElement("div",{className:"rpv-properties__modal"},$.createElement(Lo,{doc:t,render:function(i){return $.createElement($.Fragment,null,$.createElement("div",{className:"rpv-properties__modal-section"},$.createElement(De,{label:n&&n.properties?n.properties.fileName:"File name",value:i.fileName||(g=r,f=g.split("/").pop(),f?f.split("#")[0].split("?")[0]:g)}),$.createElement(De,{label:n&&n.properties?n.properties.fileSize:"File size",value:(c=i.length,s=Math.floor(Math.log(c)/Math.log(1024)),"".concat((c/Math.pow(1024,s)).toFixed(2)," ").concat(["B","kB","MB","GB","TB"][s]))})),$.createElement(_e.Separator,null),$.createElement("div",{className:"rpv-properties__modal-section"},$.createElement(De,{label:n&&n.properties?n.properties.title:"Title",value:i.info.Title}),$.createElement(De,{label:n&&n.properties?n.properties.author:"Author",value:i.info.Author}),$.createElement(De,{label:n&&n.properties?n.properties.subject:"Subject",value:i.info.Subject}),$.createElement(De,{label:n&&n.properties?n.properties.keywords:"Keywords",value:i.info.Keywords}),$.createElement(De,{label:n&&n.properties?n.properties.creator:"Creator",value:i.info.Creator}),$.createElement(De,{label:n&&n.properties?n.properties.creationDate:"Creation date",value:a(i.info.CreationDate)}),$.createElement(De,{label:n&&n.properties?n.properties.modificationDate:"Modification date",value:a(i.info.ModDate)})),$.createElement(_e.Separator,null),$.createElement("div",{className:"rpv-properties__modal-section"},$.createElement(De,{label:n&&n.properties?n.properties.pdfProducer:"PDF producer",value:i.info.Producer}),$.createElement(De,{label:n&&n.properties?n.properties.pdfVersion:"PDF version",value:i.info.PDFFormatVersion}),$.createElement(De,{label:n&&n.properties?n.properties.pageCount:"Page count",value:"".concat(t.numPages)})));var c,s,g,f}}),$.createElement("div",{className:"rpv-properties__modal-footer"},$.createElement(_e.Button,{onClick:o},n&&n.properties?n.properties.close:"Close")))},$o={left:0,top:8},No=function(e){var t=e.onClick,r=$.useContext(_e.LocalizationContext).l10n,o=r&&r.properties?r.properties.showProperties:"Show properties";return $.createElement(_e.Tooltip,{ariaControlsSuffix:"properties",position:_e.Position.BottomCenter,target:$.createElement(_e.MinimalButton,{ariaLabel:o,testId:"properties__button",onClick:t},$.createElement(Bt,null)),content:function(){return o},offset:$o})},Jt=function(e){var t=e.children,r=e.store,o=function(i){var c=$.useState(i.get("doc")),s=c[0],g=c[1],f=function(b){g(b)};return $.useEffect(function(){return i.subscribe("doc",f),function(){i.unsubscribe("doc",f)}},[]),{currentDoc:s}}(r).currentDoc,n=r.get("fileName")||"",a=t||function(i){return $.createElement(No,ot({},i))};return o?$.createElement(_e.Modal,{ariaControlsSuffix:"properties",target:function(i){return a({onClick:i})},content:function(i){return $.createElement(Do,{doc:o,fileName:n,onToggle:i})},closeOnClickOutside:!0,closeOnEscape:!0}):$.createElement($.Fragment,null)},Ro=function(e){var t=e.onClick,r=$.useContext(_e.LocalizationContext).l10n,o=r&&r.properties?r.properties.showProperties:"Show properties";return $.createElement(_e.MenuItem,{icon:$.createElement(Bt,null),testId:"properties__menu",onClick:t},o)};Mt.InfoIcon=Bt,Mt.propertiesPlugin=function(){var e=$.useMemo(function(){return _e.createStore({fileName:""})},[]),t=function(r){return $.createElement(Jt,ot({},r,{store:e}))};return{onDocumentLoad:function(r){e.update("doc",r.doc)},onViewerStateChange:function(r){return e.update("fileName",r.file.name),r},ShowProperties:t,ShowPropertiesButton:function(){return $.createElement(Jt,{store:e})},ShowPropertiesMenuItem:function(r){return $.createElement(t,null,function(o){return $.createElement(Ro,ot({},o))})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */Tn.exports=Mt;var zo=Tn.exports,Ln={exports:{}},mt={},he=Pe;function jo(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var ce=jo(Se),Vt=function(){return ce.createElement(he.Icon,{ignoreDirection:!0,size:16},ce.createElement("path",{d:`M3.434,10.537c0.141-0.438,0.316-0.864,0.523-1.274
            M3.069,14.425C3.023,14.053,3,13.679,3,13.305 c0-0.291,0.014-0.579,0.041-0.863
            M4.389,18.111c-0.341-0.539-0.623-1.112-0.843-1.711
            M7.163,20.9 c-0.543-0.345-1.048-0.747-1.506-1.2
            M10.98,22.248c-0.65-0.074-1.29-0.218-1.909-0.431
            M10,4.25h2 c4.987,0.015,9.017,4.069,9.003,9.055c-0.013,4.581-3.456,8.426-8.008,8.945
            M13.5,1.75L10,4.25l3.5,2.5`}))},At=function(){return ce.createElement(he.Icon,{ignoreDirection:!0,size:16},ce.createElement("path",{d:`M20.566,10.537c-0.141-0.438-0.316-0.864-0.523-1.274
            M20.931,14.425C20.977,14.053,21,13.679,21,13.305 c0-0.291-0.014-0.579-0.041-0.863
            M19.611,18.111c0.341-0.539,0.624-1.114,0.843-1.713
            M16.837,20.9 c0.543-0.345,1.048-0.747,1.506-1.2
            M13.02,22.248c0.65-0.074,1.29-0.218,1.909-0.431
            M14,4.25h-2 c-4.987,0.015-9.017,4.069-9.003,9.055c0.013,4.581,3.456,8.426,8.008,8.945
            M10.5,1.75l3.5,2.5l-3.5,2.5`}))},Xe=function(){return Xe=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Xe.apply(this,arguments)},Fo={left:0,top:8},wt=function(e){var t=e.direction,r=e.onClick,o=ce.useContext(he.LocalizationContext).l10n,n=o&&o.rotate?o.rotate.rotateBackward:"Rotate counterclockwise",a=o&&o.rotate?o.rotate.rotateForward:"Rotate clockwise",i=t===he.RotateDirection.Backward?n:a,c=t===he.RotateDirection.Backward?ce.createElement(Vt,null):ce.createElement(At,null);return ce.createElement(he.Tooltip,{ariaControlsSuffix:"rotate",position:he.Position.BottomCenter,target:ce.createElement(he.MinimalButton,{ariaLabel:i,testId:t===he.RotateDirection.Backward?"rotate__backward-button":"rotate__forward-button",onClick:r},c),content:function(){return i},offset:Fo})},Bo=function(e){var t=e.children,r=e.direction,o=e.store;return(t||function(n){return ce.createElement(wt,{direction:n.direction,onClick:n.onClick})})({direction:r,onClick:function(){var n=o.get("rotate");n&&n(r)}})},Xt=function(e){var t=e.direction,r=e.onClick,o=ce.useContext(he.LocalizationContext).l10n,n=o&&o.rotate?o.rotate.rotateBackward:"Rotate counterclockwise",a=o&&o.rotate?o.rotate.rotateForward:"Rotate clockwise",i=t===he.RotateDirection.Backward?n:a,c=t===he.RotateDirection.Backward?ce.createElement(Vt,null):ce.createElement(At,null);return ce.createElement(he.MenuItem,{icon:c,testId:t===he.RotateDirection.Backward?"rotate__backward-menu":"rotate__forward-menu",onClick:r},i)},Vo=function(e){var t=e.children,r=e.store;return t({onRotatePage:function(o,n){var a=r.get("rotatePage");a&&a(o,n)}})};mt.RotateBackwardIcon=Vt,mt.RotateForwardIcon=At,mt.rotatePlugin=function(){var e=ce.useMemo(function(){return he.createStore()},[]),t=function(r){return ce.createElement(Bo,Xe({},r,{store:e}))};return{install:function(r){e.update("rotate",r.rotate),e.update("rotatePage",r.rotatePage)},Rotate:t,RotateBackwardButton:function(){return ce.createElement(t,{direction:he.RotateDirection.Backward},function(r){return ce.createElement(wt,Xe({},r))})},RotateBackwardMenuItem:function(r){return ce.createElement(t,{direction:he.RotateDirection.Backward},function(o){return ce.createElement(Xt,{direction:o.direction,onClick:function(){o.onClick(),r.onClick()}})})},RotateForwardButton:function(){return ce.createElement(t,{direction:he.RotateDirection.Forward},function(r){return ce.createElement(wt,Xe({},r))})},RotateForwardMenuItem:function(r){return ce.createElement(t,{direction:he.RotateDirection.Forward},function(o){return ce.createElement(Xt,{direction:o.direction,onClick:function(){o.onClick(),r.onClick()}})})},RotatePage:function(r){return ce.createElement(Vo,Xe({},r,{store:e}))}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */Ln.exports=mt;var Ao=Ln.exports,On={exports:{}},He={},L=Pe;function Ho(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var N=Ho(Se),Dn=function(){return N.createElement(L.Icon,{size:16},N.createElement("rect",{x:"0.5",y:"0.497",width:"22",height:"22",rx:"1",ry:"1"}),N.createElement("line",{x1:"0.5",y1:"6.497",x2:"22.5",y2:"6.497"}),N.createElement("line",{x1:"11.5",y1:"6.497",x2:"11.5",y2:"22.497"}))},$n=function(){return N.createElement(L.Icon,{size:16},N.createElement("rect",{x:"0.5",y:"0.497",width:"22",height:"22",rx:"1",ry:"1"}),N.createElement("line",{x1:"11.5",y1:"0.497",x2:"11.5",y2:"22.497"}))},Nn=function(){return N.createElement(L.Icon,{size:16},N.createElement("path",{d:`M6.5,21.5c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z
            M14.5,21.5c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z
            M22.5,21.5 c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z`}))},vt=function(){return N.createElement(L.Icon,{size:16},N.createElement("rect",{x:"0.5",y:"0.497",width:"22",height:"22",rx:"1",ry:"1"}))},ht=function(){return ht=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},ht.apply(this,arguments)},Rn=function(e,t){e.get("switchScrollMode")(t);var r=e.get("viewMode");t!==L.ScrollMode.Horizontal&&t!==L.ScrollMode.Wrapped||r===L.ViewMode.SinglePage||e.get("switchViewMode")(L.ViewMode.SinglePage)},xt=function(){return N.createElement(L.Icon,{size:16},N.createElement("path",{d:`M23.5,5.5c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V5.5z
            M23.5,13.5c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V13.5z
            M23.5,21.5 c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V21.5z`}))},zn=function(){return N.createElement(L.Icon,{size:16},N.createElement("path",{d:`M10.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z
            M23.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z
            M10.5,22.5 c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z
            M23.5,22.5c0,0.552-0.448,1-1,1 h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z`}))},jn=function(e){var t=e.children,r=e.mode,o=e.onClick,n=N.useContext(L.LocalizationContext).l10n,a="",i=N.createElement(xt,null);switch(r){case L.ScrollMode.Horizontal:a=n&&n.scrollMode?n.scrollMode.horizontalScrolling:"Horizontal scrolling",i=N.createElement(Nn,null);break;case L.ScrollMode.Page:a=n&&n.scrollMode?n.scrollMode.pageScrolling:"Page scrolling",i=N.createElement(vt,null);break;case L.ScrollMode.Wrapped:a=n&&n.scrollMode?n.scrollMode.wrappedScrolling:"Wrapped scrolling",i=N.createElement(zn,null);break;case L.ScrollMode.Vertical:default:a=n&&n.scrollMode?n.scrollMode.verticalScrolling:"Vertical scrolling",i=N.createElement(xt,null)}return t({icon:i,label:a,onClick:o})},Wo={left:0,top:8},Fn=function(e){var t=e.isDisabled,r=e.isSelected,o=e.mode,n=e.onClick,a="";switch(o){case L.ScrollMode.Horizontal:a="scroll-mode__horizontal-button";break;case L.ScrollMode.Page:a="scroll-mode__page-button";break;case L.ScrollMode.Wrapped:a="scroll-mode__wrapped-button";break;case L.ScrollMode.Vertical:default:a="scroll-mode__vertical-button"}return N.createElement(jn,{mode:o,onClick:n},function(i){return N.createElement(L.Tooltip,{ariaControlsSuffix:"scroll-mode-switch",position:L.Position.BottomCenter,target:N.createElement(L.MinimalButton,{ariaLabel:i.label,isDisabled:t,isSelected:r,testId:a,onClick:i.onClick},i.icon),content:function(){return i.label},offset:Wo})})},Bn=function(e){var t=N.useState(e.get("scrollMode")||L.ScrollMode.Vertical),r=t[0],o=t[1],n=function(a){o(a)};return N.useEffect(function(){return e.subscribe("scrollMode",n),function(){e.unsubscribe("scrollMode",n)}},[]),{scrollMode:r}},Vn=function(e){var t=N.useState(e.get("viewMode")||L.ViewMode.SinglePage),r=t[0],o=t[1],n=function(a){o(a)};return N.useEffect(function(){return e.subscribe("viewMode",n),function(){e.unsubscribe("viewMode",n)}},[]),{viewMode:r}},Ko=function(e){var t=e.children,r=e.mode,o=e.store,n=Vn(o).viewMode,a=Bn(o).scrollMode===r,i=(r===L.ScrollMode.Horizontal||r===L.ScrollMode.Wrapped)&&n!==L.ViewMode.SinglePage;return(t||function(c){return N.createElement(Fn,{isDisabled:i,isSelected:a,mode:c.mode,onClick:c.onClick})})({isDisabled:i,isSelected:a,mode:r,onClick:function(){Rn(o,r)}})},Zo=function(e){var t=e.isDisabled,r=e.isSelected,o=e.mode,n=e.onClick,a="";switch(o){case L.ScrollMode.Horizontal:a="scroll-mode__horizontal-menu";break;case L.ScrollMode.Page:a="scroll-mode__page-menu";break;case L.ScrollMode.Wrapped:a="scroll-mode__wrapped-menu";break;case L.ScrollMode.Vertical:default:a="scroll-mode__vertical-menu"}return N.createElement(jn,{mode:o,onClick:n},function(i){return N.createElement(L.MenuItem,{checked:r,icon:i.icon,isDisabled:t,testId:a,onClick:i.onClick},i.label)})},An=function(e,t){e.get("switchViewMode")(t);var r=e.get("scrollMode");r!==L.ScrollMode.Horizontal&&r!==L.ScrollMode.Wrapped||t===L.ViewMode.SinglePage||e.get("switchScrollMode")(L.ScrollMode.Vertical)},Hn=function(e){var t=e.children,r=e.mode,o=e.onClick,n=N.useContext(L.LocalizationContext).l10n,a="",i=N.createElement(vt,null);switch(r){case L.ViewMode.DualPage:a=n&&n.scrollMode?n.scrollMode.dualPage:"Dual page",i=N.createElement($n,null);break;case L.ViewMode.DualPageWithCover:a=n&&n.scrollMode?n.scrollMode.dualPageCover:"Dual page with cover",i=N.createElement(Dn,null);break;case L.ViewMode.SinglePage:default:a=n&&n.scrollMode?n.scrollMode.singlePage:"Single page",i=N.createElement(vt,null)}return t({icon:i,label:a,onClick:o})},Go={left:0,top:8},Wn=function(e){var t=e.isDisabled,r=e.isSelected,o=e.mode,n=e.onClick,a="";switch(o){case L.ViewMode.DualPage:a="view-mode__dual-button";break;case L.ViewMode.DualPageWithCover:a="view-mode__dual-cover-button";break;case L.ViewMode.SinglePage:default:a="view-mode__single-button"}return N.createElement(Hn,{mode:o,onClick:n},function(i){return N.createElement(L.Tooltip,{ariaControlsSuffix:"view-mode-switch",position:L.Position.BottomCenter,target:N.createElement(L.MinimalButton,{ariaLabel:i.label,isDisabled:t,isSelected:r,testId:a,onClick:i.onClick},i.icon),content:function(){return i.label},offset:Go})})},Uo=function(e){var t=e.children,r=e.mode,o=e.store,n=Vn(o).viewMode,a=Bn(o).scrollMode,i=n===r,c=(a===L.ScrollMode.Horizontal||a===L.ScrollMode.Wrapped)&&r!==L.ViewMode.SinglePage;return(t||function(s){return N.createElement(Wn,{isDisabled:c,isSelected:i,mode:s.mode,onClick:s.onClick})})({isDisabled:c,isSelected:i,mode:r,onClick:function(){An(o,r)}})},qo=function(e){var t=e.isDisabled,r=e.isSelected,o=e.mode,n=e.onClick,a="";switch(o){case L.ViewMode.DualPage:a="view-mode__dual-menu";break;case L.ViewMode.DualPageWithCover:a="view-mode__dual-cover-menu";break;case L.ViewMode.SinglePage:default:a="view-mode__single-menu"}return N.createElement(Hn,{mode:o,onClick:n},function(i){return N.createElement(L.MenuItem,{checked:r,icon:i.icon,isDisabled:t,testId:a,onClick:i.onClick},i.label)})};He.DualPageCoverViewModeIcon=Dn,He.DualPageViewModeIcon=$n,He.HorizontalScrollingIcon=Nn,He.PageScrollingIcon=vt,He.VerticalScrollingIcon=xt,He.WrappedScrollingIcon=zn,He.scrollModePlugin=function(){var e=N.useMemo(function(){return L.createStore({scrollMode:L.ScrollMode.Vertical,viewMode:L.ViewMode.SinglePage,switchScrollMode:function(){},switchViewMode:function(){}})},[]),t=function(o){return N.createElement(Ko,ht({},o,{store:e}))},r=function(o){return N.createElement(Uo,ht({},o,{store:e}))};return{install:function(o){e.update("switchScrollMode",o.switchScrollMode),e.update("switchViewMode",o.switchViewMode)},onViewerStateChange:function(o){return e.update("scrollMode",o.scrollMode),e.update("viewMode",o.viewMode),o},switchScrollMode:function(o){Rn(e,o)},switchViewMode:function(o){An(e,o)},SwitchScrollMode:t,SwitchScrollModeButton:function(o){return N.createElement(t,{mode:o.mode},function(n){return N.createElement(Fn,{isDisabled:n.isDisabled,isSelected:n.isSelected,mode:n.mode,onClick:function(){n.onClick()}})})},SwitchScrollModeMenuItem:function(o){return N.createElement(t,{mode:o.mode},function(n){return N.createElement(Zo,{isDisabled:n.isDisabled,isSelected:n.isSelected,mode:n.mode,onClick:function(){n.onClick(),o.onClick()}})})},SwitchViewMode:r,SwitchViewModeButton:function(o){return N.createElement(r,{mode:o.mode},function(n){return N.createElement(Wn,{isDisabled:n.isDisabled,isSelected:n.isSelected,mode:n.mode,onClick:function(){n.onClick()}})})},SwitchViewModeMenuItem:function(o){return N.createElement(r,{mode:o.mode},function(n){return N.createElement(qo,{isDisabled:n.isDisabled,isSelected:n.isSelected,mode:n.mode,onClick:function(){n.onClick(),o.onClick()}})})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */On.exports=He;var Jo=On.exports,Kn={exports:{}},Qe={},W=Pe;function Xo(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var _=Xo(Se),Zn=function(){return _.createElement(W.Icon,{size:16},_.createElement("path",{d:`M0.541,5.627L11.666,18.2c0.183,0.207,0.499,0.226,0.706,0.043c0.015-0.014,0.03-0.028,0.043-0.043
            L23.541,5.627`}))},Gn=function(){return _.createElement(W.Icon,{size:16},_.createElement("path",{d:`M23.535,18.373L12.409,5.8c-0.183-0.207-0.499-0.226-0.706-0.043C11.688,5.77,11.674,5.785,11.66,5.8
            L0.535,18.373`}))},Un=function(){return _.createElement(W.Icon,{ignoreDirection:!0,size:16},_.createElement("path",{d:`M10.5,0.5c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.5,10.5,0.5z
            M23.5,23.5
            l-5.929-5.929`}))},Ke=function(){return Ke=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Ke.apply(this,arguments)},at={keyword:"",regExp:new RegExp(" "),wholeWords:!1},qn=function(e){return{left:"".concat(e.left,"%"),top:"".concat(e.top,"%"),height:"".concat(e.height,"%"),width:"".concat(e.width,"%")}},Yo=function(e){var t=e.index,r=e.area,o=e.onHighlightKeyword,n=_.useRef();return W.useIsomorphicLayoutEffect(function(){var a=n.current;o&&a&&o({highlightEle:a,keyword:r.keyword})},[]),_.createElement("div",{className:"rpv-search__highlight","data-index":t,ref:n,style:qn(r),title:r.keywordStr.trim()})},Yt=function(e){var t=e.parentNode;t&&t.removeChild(e)},Qo=function(e){var t=e.parentNode;if(t){var r=document.createRange();r.selectNodeContents(e),function(o,n){Yt(o);var a=n.parentNode;a&&a.insertBefore(o,n),Yt(n)}(r.extractContents(),e),t.normalize()}},ea=function(e,t){return e.top<t.top?-1:e.top>t.top?1:e.left<t.left?-1:e.left>t.left?1:0},ta=function(e){var t=e.numPages,r=e.pageIndex,o=e.renderHighlights,n=e.store,a=e.onHighlightKeyword,i=_.useRef(),c=_.useCallback(function(w){return _.createElement(_.Fragment,null,w.highlightAreas.map(function(D,Z){return _.createElement(Yo,{index:Z,key:Z,area:D,onHighlightKeyword:a})}))},[]),s=o||c,g=_.useState(n.get("matchPosition")),f=g[0],b=g[1],m=_.useState(n.get("keyword")||[at]),d=m[0],h=m[1],l=_.useState({pageIndex:r,scale:1,status:W.LayerRenderStatus.PreRender}),u=l[0],v=l[1],p=_.useRef(null),E=_.useRef([]),S=_.useState([]),j=S[0],A=S[1],H=function(){return!0},J=_.useCallback(function(){return n.get("targetPageFilter")||H},[n.get("targetPageFilter")]),V=function(w){var D=E.current;if(D.length===0)return[];var Z=[],Q=[].slice.call(w.querySelectorAll(".rpv-core__text-layer-text")),oe=D.map(function(Y){return Y.char}).join("");return d.forEach(function(Y){var z=Y.keyword;if(z.trim()){for(var re,le=Y.regExp.flags.indexOf("g")===-1?new RegExp(Y.regExp,"".concat(Y.regExp.flags,"g")):Y.regExp,ee=[];(re=le.exec(oe))!==null;)ee.push({keyword:le,startIndex:re.index,endIndex:le.lastIndex});ee.map(function(K){return{keyword:K.keyword,indexes:D.slice(K.startIndex,K.endIndex)}}).forEach(function(K){var Me=K.indexes.reduce(function(se,ae){return se[ae.spanIndex]=(se[ae.spanIndex]||[]).concat([ae]),se},{});Object.values(Me).forEach(function(se){if(se.length!==1||se[0].char.trim()!==""){var ae=Y.wholeWords?se.slice(1,-1):se,Ee=function(de,Te,Ne,Oe,we){var be=document.createRange(),xe=Oe.firstChild;if(!xe||xe.nodeType!==Node.TEXT_NODE)return null;var Re=xe.textContent.length,Le=we[0].charIndexInSpan,F=we.length===1?Le:we[we.length-1].charIndexInSpan;if(Le>Re||F+1>Re)return null;be.setStart(xe,Le),be.setEnd(xe,F+1);var I=document.createElement("span");be.surroundContents(I);var fe=I.getBoundingClientRect(),ye=Ne.getBoundingClientRect(),G=ye.height,ke=ye.width,Be=100*(fe.left-ye.left)/ke,Ve=100*(fe.top-ye.top)/G,ct=100*fe.height/G,Ct=100*fe.width/ke;return Qo(I),{keyword:Te,keywordStr:de,numPages:t,pageIndex:r,left:Be,top:Ve,height:ct,width:Ct,pageHeight:G,pageWidth:ke}}(z,K.keyword,w,Q[ae[0].spanIndex],ae);Ee&&Z.push(Ee)}})})}}),Z.sort(ea)},x=function(w){w&&w.length>0&&h(w)},M=function(w){return b(w)},O=function(w){if(w.has(r)){var D=w.get(r);D&&v({ele:D.ele,pageIndex:r,scale:D.scale,status:D.status})}},X=function(){return d.length===0||d.length===1&&d[0].keyword.trim()===""};return _.useEffect(function(){if(!X()&&u.status===W.LayerRenderStatus.DidRender&&!E.current.length){var w=u.ele,D=[].slice.call(w.querySelectorAll(".rpv-core__text-layer-text")).map(function(Z){return Z.textContent}).reduce(function(Z,Q,oe){return Z.concat(Q.split("").map(function(Y,z){return{char:Y,charIndexInSpan:z,spanIndex:oe}}))},[{char:"",charIndexInSpan:0,spanIndex:0}]).slice(1);E.current=D}},[d,u.status]),_.useEffect(function(){if(!X()&&u.ele&&u.status===W.LayerRenderStatus.DidRender&&J()({pageIndex:r,numPages:t})){var w=u.ele,D=V(w);A(D)}},[d,f,u.status,E.current]),_.useEffect(function(){X()&&u.ele&&u.status===W.LayerRenderStatus.DidRender&&A([])},[d,u.status]),_.useEffect(function(){if(j.length!==0){var w=i.current;if(f.pageIndex===r&&w&&u.status===W.LayerRenderStatus.DidRender){var D=w.querySelector('.rpv-search__highlight[data-index="'.concat(f.matchIndex,'"]'));if(D){var Z=function(z,re){for(var le=z.offsetTop,ee=z.offsetLeft,K=z.parentElement;K&&K!==re;)le+=K.offsetTop,ee+=K.offsetLeft,K=K.parentElement;return{left:ee,top:le}}(D,w),Q=Z.left,oe=Z.top,Y=n.get("jumpToDestination");Y&&(Y({pageIndex:r,bottomOffset:(w.getBoundingClientRect().height-oe)/u.scale,leftOffset:Q/u.scale,scaleTo:u.scale}),p.current&&p.current.classList.remove("rpv-search__highlight--current"),p.current=D,D.classList.add("rpv-search__highlight--current"))}}}},[j,f]),_.useEffect(function(){return n.subscribe("keyword",x),n.subscribe("matchPosition",M),n.subscribe("renderStatus",O),function(){n.unsubscribe("keyword",x),n.unsubscribe("matchPosition",M),n.unsubscribe("renderStatus",O)}},[]),_.createElement("div",{className:"rpv-search__highlights","data-testid":"search__highlights-".concat(r),ref:i},s({getCssProperties:qn,highlightAreas:j}))},Qt=function(e){var t,r=e.wholeWords?" ".concat(e.keyword," "):e.keyword,o=e.matchCase?"g":"gi";return{keyword:e.keyword,regExp:new RegExp((t=r,t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")),o),wholeWords:e.wholeWords||!1}},bt=function(e,t,r){return e instanceof RegExp?{keyword:e.source,regExp:e,wholeWords:r||!1}:typeof e=="string"?e===""?at:Qt({keyword:e,matchCase:t||!1,wholeWords:r||!1}):(t!==void 0&&(e.matchCase=t),r!==void 0&&(e.wholeWords=r),Qt(e))},Ht=function(e){var t,r=e.get("initialKeyword"),o=_.useMemo(function(){if(r&&r.length===1){var M=bt(r[0]);return{matchCase:M.regExp.flags.indexOf("i")===-1,wholeWords:M.wholeWords}}return{matchCase:!1,wholeWords:!1}},[]),n=function(M){var O=_.useRef(M.get("doc")),X=function(w){O.current=w};return _.useEffect(function(){return M.subscribe("doc",X),function(){M.unsubscribe("doc",X)}},[]),O}(e),a=_.useState(r),i=a[0],c=a[1],s=_.useState([]),g=s[0],f=s[1],b=_.useState(0),m=b[0],d=b[1],h=_.useState(o.matchCase),l=h[0],u=h[1],v=_.useRef([]),p=_.useState(o.wholeWords),E=p[0],S=p[1],j=function(){return!0},A=_.useCallback(function(){return e.get("targetPageFilter")||j},[e.get("targetPageFilter")]),H=function(M){var O=g.length;if(i.length===0||O===0)return null;var X=M===O+1?1:Math.max(1,Math.min(O,M));return d(X),V(g[X-1])},J=function(M){return c(M===""?[]:[M])},V=function(M){var O=e.get("jumpToPage");return O&&O(M.pageIndex),e.update("matchPosition",{matchIndex:M.matchIndex,pageIndex:M.pageIndex}),M},x=function(M,O,X){var w=n.current;if(!w)return Promise.resolve([]);var D=w.numPages,Z=M.map(function(Q){return bt(Q,O,X)});return e.update("keyword",Z),d(0),f([]),new Promise(function(Q,oe){var Y=v.current.length===0?function(){var z=n.current;if(!z)return Promise.resolve([]);var re=Array(z.numPages).fill(0).map(function(le,ee){return W.getPage(z,ee).then(function(K){return K.getTextContent()}).then(function(K){var Me=K.items.map(function(se){return se.str||""}).join("");return Promise.resolve({pageContent:Me,pageIndex:ee})})});return Promise.all(re).then(function(le){return le.sort(function(ee,K){return ee.pageIndex-K.pageIndex}),Promise.resolve(le.map(function(ee){return ee.pageContent}))})}().then(function(z){return v.current=z,Promise.resolve(z)}):Promise.resolve(v.current);Y.then(function(z){var re=[];z.forEach(function(le,ee){A()({pageIndex:ee,numPages:D})&&Z.forEach(function(K){for(var Me,se=0;(Me=K.regExp.exec(le))!==null;)re.push({keyword:K.regExp,matchIndex:se,pageIndex:ee,pageText:le,startIndex:Me.index,endIndex:K.regExp.lastIndex}),se++})}),f(re),re.length>0&&(d(1),V(re[0])),Q(re)})})};return _.useEffect(function(){v.current=[]},[n.current]),{clearKeyword:function(){e.update("keyword",[at]),J(""),d(0),f([]),u(!1),S(!1)},changeMatchCase:function(M){u(M),i.length>0&&x(i,M,E)},changeWholeWords:function(M){S(M),i.length>0&&x(i,l,M)},currentMatch:m,jumpToMatch:H,jumpToNextMatch:function(){return H(m+1)},jumpToPreviousMatch:function(){return H(m-1)},keywords:i,matchCase:l,numberOfMatches:g.length,wholeWords:E,search:function(){return x(i,l,E)},searchFor:x,setKeywords:c,keyword:i.length===0?"":(t=i[0],t instanceof RegExp?t.source:typeof t=="string"?t:t.keyword),setKeyword:J,setTargetPages:function(M){e.update("targetPageFilter",M)}}},na=function(e){var t=e.children,r=e.store,o=Ht(r),n=_.useState(!1),a=n[0],i=n[1],c=function(s){return i(!0)};return _.useEffect(function(){return r.subscribe("doc",c),function(){r.unsubscribe("doc",c)}},[]),t(Ke(Ke({},o),{isDocumentLoaded:a}))},ra=function(e){var t=e.containerRef,r=e.store,o=_.useRef(!1),n=function(){o.current=!0},a=function(){o.current=!1},i=function(c){var s=t.current;s&&(c.shiftKey||c.altKey||c.key!=="f"||(W.isMac()?c.metaKey&&!c.ctrlKey:c.ctrlKey)&&(o.current||document.activeElement&&s.contains(document.activeElement))&&(c.preventDefault(),r.update("areShortcutsPressed",!0)))};return _.useEffect(function(){var c=t.current;if(c)return document.addEventListener("keydown",i),c.addEventListener("mouseenter",n),c.addEventListener("mouseleave",a),function(){document.removeEventListener("keydown",i),c.removeEventListener("mouseenter",n),c.removeEventListener("mouseleave",a)}},[t.current]),_.createElement(_.Fragment,null)},en={left:0,top:8},oa=function(e){var t=e.store,r=e.onToggle,o=_.useContext(W.LocalizationContext).l10n,n=_.useContext(W.ThemeContext).direction,a=_.useState(!1),i=a[0],c=a[1],s=_.useState(!1),g=s[0],f=s[1],b=n===W.TextDirection.RightToLeft,m=Ht(t),d=m.clearKeyword,h=m.changeMatchCase,l=m.changeWholeWords,u=m.currentMatch,v=m.jumpToNextMatch,p=m.jumpToPreviousMatch,E=m.keyword,S=m.matchCase,j=m.numberOfMatches,A=m.wholeWords,H=m.search,J=m.setKeyword,V=function(w){c(!0),H().then(function(D){c(!1),f(!0),w&&w()})};_.useEffect(function(){var w=t.get("initialKeyword");w&&w.length===1&&E&&V(function(){t.update("initialKeyword",[])})},[]);var x=o&&o.search?o.search.enterToSearch:"Enter to search",M=o&&o.search?o.search.previousMatch:"Previous match",O=o&&o.search?o.search.nextMatch:"Next match",X=o&&o.search?o.search.close:"Close";return _.createElement("div",{className:"rpv-search__popover"},_.createElement("div",{className:"rpv-search__popover-input-counter"},_.createElement(W.TextBox,{ariaLabel:x,autoFocus:!0,placeholder:x,type:"text",value:E,onChange:function(w){f(!1),J(w)},onKeyDown:function(w){w.key==="Enter"&&E&&(g?v():V())}}),_.createElement("div",{className:W.classNames({"rpv-search__popover-counter":!0,"rpv-search__popover-counter--ltr":!b,"rpv-search__popover-counter--rtl":b})},i&&_.createElement(W.Spinner,{testId:"search__popover-searching",size:"1rem"}),!i&&_.createElement("span",{"data-testid":"search__popover-num-matches"},u,"/",j))),_.createElement("label",{className:"rpv-search__popover-label"},_.createElement("input",{className:"rpv-search__popover-label-checkbox","data-testid":"search__popover-match-case",checked:S,type:"checkbox",onChange:function(w){f(!1),h(w.target.checked)}})," ",o&&o.search?o.search.matchCase:"Match case"),_.createElement("label",{className:"rpv-search__popover-label"},_.createElement("input",{className:"rpv-search__popover-label-checkbox",checked:A,"data-testid":"search__popover-whole-words",type:"checkbox",onChange:function(w){f(!1),l(w.target.checked)}})," ",o&&o.search?o.search.wholeWords:"Whole words"),_.createElement("div",{className:"rpv-search__popover-footer"},_.createElement("div",{className:"rpv-search__popover-footer-item"},_.createElement(W.Tooltip,{ariaControlsSuffix:"search-previous-match",position:b?W.Position.BottomRight:W.Position.BottomCenter,target:_.createElement(W.MinimalButton,{ariaLabel:M,isDisabled:u<=1,onClick:p},_.createElement(Gn,null)),content:function(){return M},offset:en})),_.createElement("div",{className:"rpv-search__popover-footer-item"},_.createElement(W.Tooltip,{ariaControlsSuffix:"search-next-match",position:W.Position.BottomCenter,target:_.createElement(W.MinimalButton,{ariaLabel:O,isDisabled:u>j-1,onClick:v},_.createElement(Zn,null)),content:function(){return O},offset:en})),_.createElement("div",{className:W.classNames({"rpv-search__popover-footer-button":!0,"rpv-search__popover-footer-button--ltr":!b,"rpv-search__popover-footer-button--rtl":b})},_.createElement(W.Button,{onClick:function(){r(),d()}},X))))},aa=function(e){var t=e.children,r=e.onClick,o=_.useContext(W.LocalizationContext).l10n,n=o&&o.search?o.search.search:"Search";return t({icon:_.createElement(Un,null),label:n,onClick:r})},ia={left:0,top:8},Jn=function(e){var t=e.enableShortcuts,r=e.store,o=e.onClick,n=t?W.isMac()?"Meta+F":"Ctrl+F":"",a=function(i){i&&o()};return _.useEffect(function(){return r.subscribe("areShortcutsPressed",a),function(){r.unsubscribe("areShortcutsPressed",a)}},[]),_.createElement(aa,{onClick:o},function(i){return _.createElement(W.Tooltip,{ariaControlsSuffix:"search-popover",position:W.Position.BottomCenter,target:_.createElement(W.MinimalButton,{ariaKeyShortcuts:n,ariaLabel:i.label,testId:"search__popover-button",onClick:o},i.icon),content:function(){return i.label},offset:ia})})},ca={left:0,top:8},la=function(e){var t=e.children,r=e.enableShortcuts,o=e.store,n=_.useContext(W.ThemeContext).direction===W.TextDirection.RightToLeft?W.Position.BottomRight:W.Position.BottomLeft,a=t||function(i){return _.createElement(Jn,Ke({enableShortcuts:r,store:o},i))};return _.createElement(W.Popover,{ariaControlsSuffix:"search",lockScroll:!1,position:n,target:function(i){return a({onClick:i})},content:function(i){return _.createElement(oa,{store:o,onToggle:i})},offset:ca,closeOnClickOutside:!1,closeOnEscape:!0})},tn=function(e){return Array.isArray(e)?e.map(function(t){return bt(t)}):[bt(e)]};Qe.NextIcon=Zn,Qe.PreviousIcon=Gn,Qe.SearchIcon=Un,Qe.searchPlugin=function(e){var t=_.useMemo(function(){return Object.assign({},{enableShortcuts:!0,onHighlightKeyword:function(){}},e)},[]),r=_.useMemo(function(){return W.createStore({initialKeyword:e&&e.keyword?Array.isArray(e.keyword)?e.keyword:[e.keyword]:[],keyword:e&&e.keyword?tn(e.keyword):[at],matchPosition:{matchIndex:-1,pageIndex:-1},renderStatus:new Map})},[]),o=Ht(r),n=o.clearKeyword,a=o.jumpToMatch,i=o.jumpToNextMatch,c=o.jumpToPreviousMatch,s=o.searchFor,g=o.setKeywords,f=o.setTargetPages,b=function(m){return _.createElement(la,Ke({enableShortcuts:t.enableShortcuts},m,{store:r}))};return{install:function(m){var d=e&&e.keyword?Array.isArray(e.keyword)?e.keyword:[e.keyword]:[],h=e&&e.keyword?tn(e.keyword):[at];r.update("initialKeyword",d),r.update("jumpToDestination",m.jumpToDestination),r.update("jumpToPage",m.jumpToPage),r.update("keyword",h)},renderPageLayer:function(m){return _.createElement(ta,{key:m.pageIndex,numPages:m.doc.numPages,pageIndex:m.pageIndex,renderHighlights:e==null?void 0:e.renderHighlights,store:r,onHighlightKeyword:t.onHighlightKeyword})},renderViewer:function(m){var d=m.slot;return d.subSlot&&(d.subSlot.children=_.createElement(_.Fragment,null,t.enableShortcuts&&_.createElement(ra,{containerRef:m.containerRef,store:r}),d.subSlot.children)),d},uninstall:function(m){var d=r.get("renderStatus");d&&d.clear()},onDocumentLoad:function(m){r.update("doc",m.doc)},onTextLayerRender:function(m){var d=r.get("renderStatus");d&&(d=d.set(m.pageIndex,m),r.update("renderStatus",d))},Search:function(m){return _.createElement(na,Ke({},m,{store:r}))},ShowSearchPopover:b,ShowSearchPopoverButton:function(){return _.createElement(b,null,function(m){return _.createElement(Jn,Ke({enableShortcuts:t.enableShortcuts,store:r},m))})},clearHighlights:function(){n()},highlight:function(m){var d=Array.isArray(m)?m:[m];return g(d),s(d)},jumpToMatch:a,jumpToNextMatch:i,jumpToPreviousMatch:c,setTargetPages:f}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */Kn.exports=Qe;var ua=Kn.exports,Xn={exports:{}},dt={},Fe=Pe;function sa(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var ue=sa(Se),Wt=function(){return ue.createElement(Fe.Icon,{size:16},ue.createElement("path",{d:"M19.5,15.106l2.4-2.4a1,1,0,0,0,0-1.414l-2.4-2.4V5.5a1,1,0,0,0-1-1H15.106l-2.4-2.4a1,1,0,0,0-1.414,0l-2.4,2.4H5.5a1,1,0,0,0-1,1V8.894l-2.4,2.4a1,1,0,0,0,0,1.414l2.4,2.4V18.5a1,1,0,0,0,1,1H8.894l2.4,2.4a1,1,0,0,0,1.414,0l2.4-2.4H18.5a1,1,0,0,0,1-1Z"}),ue.createElement("path",{d:"M10,6.349a6,6,0,0,1,0,11.3,6,6,0,1,0,0-11.3Z"}))},Kt=function(){return ue.createElement(Fe.Icon,{size:16},ue.createElement("path",{d:"M19.491,15.106l2.4-2.4a1,1,0,0,0,0-1.414l-2.4-2.4V5.5a1,1,0,0,0-1-1H15.1L12.7,2.1a1,1,0,0,0-1.414,0l-2.4,2.4H5.491a1,1,0,0,0-1,1V8.894l-2.4,2.4a1,1,0,0,0,0,1.414l2.4,2.4V18.5a1,1,0,0,0,1,1H8.885l2.4,2.4a1,1,0,0,0,1.414,0l2.4-2.4h3.394a1,1,0,0,0,1-1Z"}),ue.createElement("path",{d:"M11.491,6c4,0,6,2.686,6,6s-2,6-6,6Z"}))},Et=function(){return Et=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Et.apply(this,arguments)},ma={left:0,top:8},Yn=function(e){var t=e.onClick,r=ue.useContext(Fe.ThemeContext),o=ue.useContext(Fe.LocalizationContext).l10n,n=r.currentTheme==="dark",a=o&&o.theme?n?o.theme.switchLightTheme:o.theme.switchDarkTheme:n?"Switch to the light theme":"Switch to the dark theme";return ue.createElement(Fe.Tooltip,{ariaControlsSuffix:"theme-switch",position:Fe.Position.BottomCenter,target:ue.createElement(Fe.MinimalButton,{ariaLabel:a,testId:"theme__switch-button",onClick:t},n?ue.createElement(Kt,null):ue.createElement(Wt,null)),content:function(){return a},offset:ma})},da=function(e){var t=e.children,r=ue.useContext(Fe.ThemeContext);return(t||function(o){return ue.createElement(Yn,{onClick:o.onClick})})({onClick:function(){return r.setCurrentTheme(r.currentTheme==="dark"?"light":"dark")}})},fa=function(e){var t=e.onClick,r=ue.useContext(Fe.ThemeContext),o=ue.useContext(Fe.LocalizationContext).l10n,n=r.currentTheme==="dark",a=o&&o.theme?n?o.theme.switchLightTheme:o.theme.switchDarkTheme:n?"Switch to the light theme":"Switch to the dark theme";return ue.createElement(Fe.MenuItem,{icon:n?ue.createElement(Kt,null):ue.createElement(Wt,null),testId:"theme__switch-menu",onClick:t},a)};dt.DarkIcon=Wt,dt.LightIcon=Kt,dt.themePlugin=function(){var e=function(t){return ue.createElement(da,Et({},t))};return{SwitchTheme:e,SwitchThemeButton:function(){return ue.createElement(e,null,function(t){return ue.createElement(Yn,Et({},t))})},SwitchThemeMenuItem:function(t){return ue.createElement(e,null,function(r){return ue.createElement(fa,{onClick:function(){r.onClick(),t.onClick()}})})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */Xn.exports=dt;var pa=Xn.exports,Qn={exports:{}},ft={},U=Pe;function ga(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var y=ga(Se),Zt=function(){return y.createElement(U.Icon,{ignoreDirection:!0,size:16},y.createElement("path",{d:`M10.5,0.499c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.499,10.5,0.499z
            M23.5,23.499
            l-5.929-5.929
            M5.5,10.499h10
            M10.5,5.499v10`}))},Gt=function(){return y.createElement(U.Icon,{ignoreDirection:!0,size:16},y.createElement("path",{d:`M10.5,0.499c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.499,10.5,0.499z
            M23.5,23.499
            l-5.929-5.929
            M5.5,10.499h10`}))},ze=function(){return ze=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},ze.apply(this,arguments)},kt=function(e){var t=y.useState(e.get("scale")||0),r=t[0],o=t[1],n=function(a){o(a)};return y.useEffect(function(){return e.subscribe("scale",n),function(){e.unsubscribe("scale",n)}},[]),{scale:r}},va=function(e){var t=e.children,r=e.store;return(t||function(o){return y.createElement(y.Fragment,null,"".concat(Math.round(100*o.scale),"%"))})({scale:kt(r).scale})},ha={passive:!1},nn=null,ba=function(e){var t=e.pagesContainerRef,r=e.store,o=U.useDebounceCallback(function(a){var i=r.get("zoom");i&&i(a)},40),n=function(a){if(a.ctrlKey){a.preventDefault();var i=a.target.getBoundingClientRect(),c=1-a.deltaY/100,s=a.clientX-i.left,g=a.clientY-i.top,f=r.get("scale"),b=(nn||(nn=document.createElementNS("http://www.w3.org/2000/svg","svg"))).createSVGMatrix().translate(s,g).scale(c).translate(-s,-g).scale(f);o(b.a)}};return U.useIsomorphicLayoutEffect(function(){var a=t.current;if(a)return a.addEventListener("wheel",n,ha),function(){a.removeEventListener("wheel",n)}},[]),y.createElement(y.Fragment,null)},yt=[.1,.2,.3,.4,.5,.6,.7,.8,.9,1,1.1,1.3,1.5,1.7,1.9,2.1,2.4,2.7,3,3.3,3.7,4.1,4.6,5.1,5.7,6.3,7,7.7,8.5,9.4,10],er=function(e){return yt.find(function(t){return t>e})||e},tr=function(e){var t=yt.findIndex(function(r){return r>=e});return t===-1||t===0?e:yt[t-1]},Ea=function(e){var t=e.containerRef,r=e.store,o=function(n){if(!n.shiftKey&&!n.altKey&&(U.isMac()?n.metaKey:n.ctrlKey)){var a=t.current;if(a&&document.activeElement&&a.contains(document.activeElement)){var i=r.get("zoom");if(i){var c=r.get("scale")||1,s=1;switch(n.key){case"-":s=tr(c);break;case"=":s=er(c);break;case"0":s=1;break;default:s=c}s!==c&&(n.preventDefault(),i(s))}}}};return y.useEffect(function(){if(t.current)return document.addEventListener("keydown",o),function(){document.removeEventListener("keydown",o)}},[t.current]),y.createElement(y.Fragment,null)},ka=[.5,.75,1,1.25,1.5,2,3,4],Ca={left:0,top:8},nr=function(e){var t=e.levels,r=t===void 0?ka:t,o=e.scale,n=e.onZoom,a=y.useContext(U.LocalizationContext).l10n,i=y.useContext(U.ThemeContext).direction===U.TextDirection.RightToLeft,c=a&&a.zoom?a.zoom.zoomDocument:"Zoom document";return y.createElement(U.Popover,{ariaControlsSuffix:"zoom",ariaHasPopup:"menu",position:U.Position.BottomCenter,target:function(s){return y.createElement(U.MinimalButton,{ariaLabel:c,testId:"zoom__popover-target",onClick:function(){s()}},y.createElement("span",{className:"rpv-zoom__popover-target"},y.createElement("span",{"data-testid":"zoom__popover-target-scale",className:U.classNames({"rpv-zoom__popover-target-scale":!0,"rpv-zoom__popover-target-scale--ltr":!i,"rpv-zoom__popover-target-scale--rtl":i})},Math.round(100*o),"%"),y.createElement("span",{className:"rpv-zoom__popover-target-arrow"})))},content:function(s){return y.createElement(U.Menu,null,Object.keys(U.SpecialZoomLevel).map(function(g){var f=g;return y.createElement(U.MenuItem,{key:f,onClick:function(){s(),n(f)}},function(b){switch(b){case U.SpecialZoomLevel.ActualSize:return a&&a.zoom?a.zoom.actualSize:"Actual size";case U.SpecialZoomLevel.PageFit:return a&&a.zoom?a.zoom.pageFit:"Page fit";case U.SpecialZoomLevel.PageWidth:return a&&a.zoom?a.zoom.pageWidth:"Page width"}}(f))}),y.createElement(U.MenuDivider,null),r.map(function(g){return y.createElement(U.MenuItem,{key:g,onClick:function(){s(),n(g)}},"".concat(Math.round(100*g),"%"))}))},offset:Ca,closeOnClickOutside:!0,closeOnEscape:!0})},_a=function(e){var t=e.children,r=e.levels,o=e.store;return(t||function(n){return y.createElement(nr,{levels:r,scale:n.scale,onZoom:n.onZoom})})({scale:kt(o).scale,onZoom:function(n){var a=o.get("zoom");a&&a(n)}})},Sa={left:0,top:8},rr=function(e){var t=e.enableShortcuts,r=e.onClick,o=y.useContext(U.LocalizationContext).l10n,n=o&&o.zoom?o.zoom.zoomIn:"Zoom in",a=t?U.isMac()?"Meta+=":"Ctrl+=":"";return y.createElement(U.Tooltip,{ariaControlsSuffix:"zoom-in",position:U.Position.BottomCenter,target:y.createElement(U.MinimalButton,{ariaKeyShortcuts:a,ariaLabel:n,testId:"zoom__in-button",onClick:r},y.createElement(Zt,null)),content:function(){return n},offset:Sa})},Pa=function(e){var t=e.children,r=e.enableShortcuts,o=e.store,n=kt(o).scale;return(t||rr)({enableShortcuts:r,onClick:function(){var a=o.get("zoom");a&&a(er(n))}})},Ma=function(e){var t=e.onClick,r=y.useContext(U.LocalizationContext).l10n,o=r&&r.zoom?r.zoom.zoomIn:"Zoom in";return y.createElement(U.MenuItem,{icon:y.createElement(Zt,null),testId:"zoom__in-menu",onClick:t},o)},wa={left:0,top:8},or=function(e){var t=e.enableShortcuts,r=e.onClick,o=y.useContext(U.LocalizationContext).l10n,n=o&&o.zoom?o.zoom.zoomOut:"Zoom out",a=t?U.isMac()?"Meta+-":"Ctrl+-":"";return y.createElement(U.Tooltip,{ariaControlsSuffix:"zoom-out",position:U.Position.BottomCenter,target:y.createElement(U.MinimalButton,{ariaKeyShortcuts:a,ariaLabel:n,testId:"zoom__out-button",onClick:r},y.createElement(Gt,null)),content:function(){return n},offset:wa})},xa=function(e){var t=e.children,r=e.enableShortcuts,o=e.store,n=kt(o).scale;return(t||or)({enableShortcuts:r,onClick:function(){var a=o.get("zoom");a&&a(tr(n))}})},ya=function(e){var t=e.onClick,r=y.useContext(U.LocalizationContext).l10n,o=r&&r.zoom?r.zoom.zoomOut:"Zoom out";return y.createElement(U.MenuItem,{icon:y.createElement(Gt,null),testId:"zoom__out-menu",onClick:t},o)};ft.ZoomInIcon=Zt,ft.ZoomOutIcon=Gt,ft.zoomPlugin=function(e){var t=y.useMemo(function(){return Object.assign({},{enableShortcuts:!0},e)},[]),r=y.useMemo(function(){return U.createStore({})},[]),o=function(i){return y.createElement(Pa,ze({enableShortcuts:t.enableShortcuts},i,{store:r}))},n=function(i){return y.createElement(xa,ze({enableShortcuts:t.enableShortcuts},i,{store:r}))},a=function(i){return y.createElement(_a,ze({},i,{store:r}))};return{renderViewer:function(i){var c=i.slot;if(!t.enableShortcuts)return c;var s={children:y.createElement(y.Fragment,null,y.createElement(Ea,{containerRef:i.containerRef,store:r}),y.createElement(ba,{pagesContainerRef:i.pagesContainerRef,store:r}),c.children)};return ze(ze({},c),s)},install:function(i){r.update("zoom",i.zoom)},onViewerStateChange:function(i){return r.update("scale",i.scale),i},zoomTo:function(i){var c=r.get("zoom");c&&c(i)},CurrentScale:function(i){return y.createElement(va,ze({},i,{store:r}))},ZoomIn:o,ZoomInButton:function(){return y.createElement(o,null,function(i){return y.createElement(rr,ze({},i))})},ZoomInMenuItem:function(i){return y.createElement(o,null,function(c){return y.createElement(Ma,{onClick:function(){c.onClick(),i.onClick()}})})},ZoomOut:n,ZoomOutButton:function(){return y.createElement(n,null,function(i){return y.createElement(or,ze({},i))})},ZoomOutMenuItem:function(i){return y.createElement(n,null,function(c){return y.createElement(ya,{onClick:function(){c.onClick(),i.onClick()}})})},Zoom:a,ZoomPopover:function(i){return y.createElement(a,null,function(c){return y.createElement(nr,ze({levels:i==null?void 0:i.levels},c))})}}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */Qn.exports=ft;var Ia=Qn.exports,ie=Pe,It=Mr,Ta=Se,La=Rr,Oa=Ar,Da=qr,$a=go,Na=Io,Ra=zo,za=Ao,ja=Jo,Fa=ua,Ba=pa,Va=Ia;function Aa(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var k=Aa(Ta),ar=function(){return k.createElement(ie.Icon,{size:16},k.createElement("path",{d:`M12,0.5c1.381,0,2.5,1.119,2.5,2.5S13.381,5.5,12,5.5S9.5,4.381,9.5,3S10.619,0.5,12,0.5z
            M12,9.5
            c1.381,0,2.5,1.119,2.5,2.5s-1.119,2.5-2.5,2.5S9.5,13.381,9.5,12S10.619,9.5,12,9.5z
            M12,18.5c1.381,0,2.5,1.119,2.5,2.5
            s-1.119,2.5-2.5,2.5S9.5,22.381,9.5,21S10.619,18.5,12,18.5z`}))},rn={left:0,top:8},ir=function(e){var t=e.toolbarSlot,r=k.useContext(ie.LocalizationContext).l10n,o=k.useContext(ie.ThemeContext).direction===ie.TextDirection.RightToLeft?ie.Position.BottomLeft:ie.Position.BottomRight,n=t.DownloadMenuItem,a=t.EnterFullScreenMenuItem,i=t.GoToFirstPageMenuItem,c=t.GoToLastPageMenuItem,s=t.GoToNextPageMenuItem,g=t.GoToPreviousPageMenuItem,f=t.OpenMenuItem,b=t.PrintMenuItem,m=t.RotateBackwardMenuItem,d=t.RotateForwardMenuItem,h=t.ShowPropertiesMenuItem,l=t.SwitchScrollModeMenuItem,u=t.SwitchSelectionModeMenuItem,v=t.SwitchViewModeMenuItem,p=t.SwitchThemeMenuItem;return k.createElement(ie.Popover,{ariaControlsSuffix:"toolbar-more-actions",ariaHasPopup:"menu",position:o,target:function(E,S){var j=r&&r.toolbar?r.toolbar.moreActions:"More actions";return k.createElement(ie.Tooltip,{ariaControlsSuffix:"toolbar-more-actions",position:o,target:k.createElement(ie.MinimalButton,{ariaLabel:j,isSelected:S,testId:"toolbar__more-actions-popover-target",onClick:E},k.createElement(ar,null)),content:function(){return j},offset:rn})},content:function(E){return k.createElement(ie.Menu,null,k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(p,{onClick:E})),k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(a,{onClick:E})),k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(f,null)),k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(b,{onClick:E})),k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(n,{onClick:E})),k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(ie.MenuDivider,null)),k.createElement(i,{onClick:E}),k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(g,{onClick:E})),k.createElement("div",{className:"rpv-core__display--block rpv-core__display--hidden-medium"},k.createElement(s,{onClick:E})),k.createElement(c,{onClick:E}),k.createElement(ie.MenuDivider,null),k.createElement(d,{onClick:E}),k.createElement(m,{onClick:E}),k.createElement(ie.MenuDivider,null),k.createElement(u,{mode:It.SelectionMode.Text,onClick:E}),k.createElement(u,{mode:It.SelectionMode.Hand,onClick:E}),k.createElement(ie.MenuDivider,null),k.createElement(l,{mode:ie.ScrollMode.Page,onClick:E}),k.createElement(l,{mode:ie.ScrollMode.Vertical,onClick:E}),k.createElement(l,{mode:ie.ScrollMode.Horizontal,onClick:E}),k.createElement(l,{mode:ie.ScrollMode.Wrapped,onClick:E}),k.createElement(ie.MenuDivider,null),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-small"},k.createElement(v,{mode:ie.ViewMode.SinglePage,onClick:E})),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-small"},k.createElement(v,{mode:ie.ViewMode.DualPage,onClick:E})),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-small"},k.createElement(v,{mode:ie.ViewMode.DualPageWithCover,onClick:E})),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-small"},k.createElement(ie.MenuDivider,null)),k.createElement(h,{onClick:E}))},offset:rn,closeOnClickOutside:!0,closeOnEscape:!0})},nt=function(){return nt=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},nt.apply(this,arguments)},cr=function(e){return function(t){var r=k.useMemo(function(){return e(t)},[]),o=k.useContext(ie.ThemeContext).direction===ie.TextDirection.RightToLeft,n=r.CurrentPageInput,a=r.Download,i=r.EnterFullScreen,c=r.GoToNextPage,s=r.GoToPreviousPage,g=r.NumberOfPages,f=r.Open,b=r.Print,m=r.ShowSearchPopover,d=r.SwitchTheme,h=r.Zoom,l=r.ZoomIn,u=r.ZoomOut;return k.createElement("div",{"data-testid":"toolbar",className:ie.classNames({"rpv-toolbar":!0,"rpv-toolbar--rtl":o}),role:"toolbar","aria-orientation":"horizontal"},k.createElement("div",{className:"rpv-toolbar__left"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(m,null)),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-small"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(s,null))),k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(n,null),k.createElement("span",{className:"rpv-toolbar__label"},k.createElement(g,null))),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-small"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(c,null)))),k.createElement("div",{className:"rpv-toolbar__center"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(u,null)),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-small"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(h,null))),k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(l,null))),k.createElement("div",{className:"rpv-toolbar__right"},k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-medium"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(d,null))),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-medium"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(i,null))),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-medium"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(f,null))),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-medium"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(a,null))),k.createElement("div",{className:"rpv-core__display--hidden rpv-core__display--block-medium"},k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(b,null))),k.createElement("div",{className:"rpv-toolbar__item"},k.createElement(ir,{toolbarSlot:r}))))}},Ha=function(e){var t=e.NumberOfPages;return Object.assign({},e,{NumberOfPages:function(){return k.createElement(k.Fragment,null,"/ ",k.createElement(t,null))}})},Wa=function(e){return cr(Ha)(e)},Ka=function(e){var t=e.children,r=e.slot;return(t||Wa)(r)};ut.MoreActionsPopover=ir,ut.MoreIcon=ar,ut.toolbarPlugin=function(e){var t=La.fullScreenPlugin(e?e.fullScreenPlugin:{}),r=Oa.getFilePlugin(e?e.getFilePlugin:{}),o=Da.openPlugin(e?e.openPlugin:{}),n=$a.pageNavigationPlugin(e?e.pageNavigationPlugin:{}),a=Na.printPlugin(e?e.printPlugin:{}),i=Ra.propertiesPlugin(),c=za.rotatePlugin(),s=ja.scrollModePlugin(),g=Fa.searchPlugin(e?e.searchPlugin:{}),f=It.selectionModePlugin(e?e.selectionModePlugin:{}),b=Ba.themePlugin(),m=Va.zoomPlugin(e?e.zoomPlugin:{}),d=[t,r,o,n,a,i,c,s,g,f,b,m],h=k.useCallback(function(l){var u=t.EnterFullScreen,v=t.EnterFullScreenMenuItem,p=r.Download,E=r.DownloadMenuItem,S=o.Open,j=o.OpenMenuItem,A=n.CurrentPageInput,H=n.CurrentPageLabel,J=n.GoToFirstPage,V=n.GoToFirstPageMenuItem,x=n.GoToLastPage,M=n.GoToLastPageMenuItem,O=n.GoToNextPage,X=n.GoToNextPageMenuItem,w=n.GoToPreviousPage,D=n.GoToPreviousPageMenuItem,Z=n.NumberOfPages,Q=a.Print,oe=a.PrintMenuItem,Y=i.ShowProperties,z=i.ShowPropertiesMenuItem,re=c.Rotate,le=c.RotateBackwardMenuItem,ee=c.RotateForwardMenuItem,K=s.SwitchScrollMode,Me=s.SwitchScrollModeMenuItem,se=s.SwitchViewMode,ae=s.SwitchViewModeMenuItem,Ee=g.Search,de=g.ShowSearchPopover,Te=f.SwitchSelectionMode,Ne=f.SwitchSelectionModeMenuItem,Oe=b.SwitchTheme,we=b.SwitchThemeMenuItem,be=m.CurrentScale,xe=m.Zoom,Re=m.ZoomIn,Le=m.ZoomInMenuItem,F=m.ZoomOut,I=m.ZoomOutMenuItem;return k.createElement(Ka,nt({},l,{slot:{CurrentPageInput:A,CurrentPageLabel:H,CurrentScale:be,Download:p,DownloadMenuItem:E,EnterFullScreen:u,EnterFullScreenMenuItem:v,GoToFirstPage:J,GoToFirstPageMenuItem:V,GoToLastPage:x,GoToLastPageMenuItem:M,GoToNextPage:O,GoToNextPageMenuItem:X,GoToPreviousPage:w,GoToPreviousPageMenuItem:D,NumberOfPages:Z,Open:S,OpenMenuItem:j,Print:Q,PrintMenuItem:oe,Rotate:re,RotateBackwardMenuItem:le,RotateForwardMenuItem:ee,Search:Ee,ShowProperties:Y,ShowPropertiesMenuItem:z,ShowSearchPopover:de,SwitchScrollMode:K,SwitchScrollModeMenuItem:Me,SwitchSelectionMode:Te,SwitchSelectionModeMenuItem:Ne,SwitchViewMode:se,SwitchViewModeMenuItem:ae,SwitchTheme:Oe,SwitchThemeMenuItem:we,Zoom:xe,ZoomIn:Re,ZoomInMenuItem:Le,ZoomOut:F,ZoomOutMenuItem:I}}))},[]);return{fullScreenPluginInstance:t,getFilePluginInstance:r,openPluginInstance:o,pageNavigationPluginInstance:n,printPluginInstance:a,propertiesPluginInstance:i,rotatePluginInstance:c,scrollModePluginInstance:s,searchPluginInstance:g,selectionModePluginInstance:f,themePluginInstance:b,zoomPluginInstance:m,install:function(l){d.forEach(function(u){u.install&&u.install(l)})},renderPageLayer:function(l){return k.createElement(k.Fragment,null,d.map(function(u,v){return u.renderPageLayer?k.createElement(k.Fragment,{key:v},u.renderPageLayer(l)):k.createElement(k.Fragment,{key:v})}))},renderViewer:function(l){var u=l.slot;return d.forEach(function(v){v.renderViewer&&(u=v.renderViewer(nt(nt({},l),{slot:u})))}),u},uninstall:function(l){d.forEach(function(u){u.uninstall&&u.uninstall(l)})},onDocumentLoad:function(l){d.forEach(function(u){u.onDocumentLoad&&u.onDocumentLoad(l)})},onAnnotationLayerRender:function(l){d.forEach(function(u){u.onAnnotationLayerRender&&u.onAnnotationLayerRender(l)})},onTextLayerRender:function(l){d.forEach(function(u){u.onTextLayerRender&&u.onTextLayerRender(l)})},onViewerStateChange:function(l){var u=l;return d.forEach(function(v){v.onViewerStateChange&&(u=v.onViewerStateChange(u))}),u},renderDefaultToolbar:cr,Toolbar:h}};/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */fn.exports=ut;var Za=fn.exports,ve=Pe,Ga=Se,Ua=hr,qa=Sr,Ja=Pr,Xa=Za;function Ya(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if(r!=="default"){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var R=Ya(Ga),lr=function(){return R.createElement(ve.Icon,{size:16},R.createElement("path",{d:`M11.5,1.5h11c0.552,0,1,0.448,1,1v20c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h3
            M11.5,10.5c0,0.55-0.3,0.661-0.659,0.248L8,7.5l-2.844,3.246c-0.363,0.414-0.659,0.3-0.659-0.247v-9c0-0.552,0.448-1,1-1h5
            c0.552,0,1,0.448,1,1L11.5,10.5z
            M14.5,6.499h6
            M14.5,10.499h6
            M3.5,14.499h17
            M3.5,18.499h16.497`}))},rt=function(){return rt=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},rt.apply(this,arguments)},ur=function(){return R.createElement(ve.Icon,{size:16},R.createElement("path",{d:`M7.618,15.345l8.666-8.666c0.78-0.812,2.071-0.838,2.883-0.058s0.838,2.071,0.058,2.883
            c-0.019,0.02-0.038,0.039-0.058,0.058L7.461,21.305c-1.593,1.593-4.175,1.592-5.767,0s-1.592-4.175,0-5.767c0,0,0,0,0,0
            L13.928,3.305c2.189-2.19,5.739-2.19,7.929-0.001s2.19,5.739,0,7.929l0,0L13.192,19.9`}))},sr=function(){return R.createElement(ve.Icon,{size:16},R.createElement("path",{d:`M10.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z
            M23.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z
            M10.5,22.5 c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z
            M23.5,22.5c0,0.552-0.448,1-1,1 h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z`}))},Qa={left:8,top:0},ei={left:-8,top:0},ti=function(e){var t=e.attachmentTabContent,r=e.bookmarkTabContent,o=e.store,n=e.thumbnailTabContent,a=e.tabs,i=R.useRef(),c=R.useContext(ve.LocalizationContext).l10n,s=R.useState(o.get("isCurrentTabOpened")||!1),g=s[0],f=s[1],b=R.useState(Math.max(o.get("currentTab")||0,0)),m=b[0],d=b[1],h=R.useContext(ve.ThemeContext).direction===ve.TextDirection.RightToLeft,l=[{content:n,icon:R.createElement(sr,null),title:c&&c.defaultLayout?c.defaultLayout.thumbnail:"Thumbnail"},{content:r,icon:R.createElement(lr,null),title:c&&c.defaultLayout?c.defaultLayout.bookmark:"Bookmark"},{content:t,icon:R.createElement(ur,null),title:c&&c.defaultLayout?c.defaultLayout.attachment:"Attachment"}],u=a?a(l):l,v=function(E){E>=0&&E<=u.length-1&&(o.update("isCurrentTabOpened",!0),d(E))},p=function(E){f(E)};return R.useEffect(function(){return o.subscribe("currentTab",v),o.subscribe("isCurrentTabOpened",p),function(){o.unsubscribe("currentTab",v),o.unsubscribe("isCurrentTabOpened",p)}},[]),u.length===0?R.createElement(R.Fragment,null):R.createElement(R.Fragment,null,R.createElement("div",{"data-testid":"default-layout__sidebar",className:ve.classNames({"rpv-default-layout__sidebar":!0,"rpv-default-layout__sidebar--opened":g,"rpv-default-layout__sidebar--ltr":!h,"rpv-default-layout__sidebar--rtl":h}),ref:i},R.createElement("div",{className:"rpv-default-layout__sidebar-tabs"},R.createElement("div",{className:"rpv-default-layout__sidebar-headers",role:"tablist","aria-orientation":"vertical"},u.map(function(E,S){return R.createElement("div",{"aria-controls":"rpv-default-layout__sidebar-content","aria-selected":m===S,key:S,className:"rpv-default-layout__sidebar-header",id:"rpv-default-layout__sidebar-tab-".concat(S),role:"tab"},R.createElement(ve.Tooltip,{ariaControlsSuffix:"default-layout-sidebar-tab-".concat(S),position:h?ve.Position.LeftCenter:ve.Position.RightCenter,target:R.createElement(ve.MinimalButton,{ariaLabel:E.title,isSelected:m===S,onClick:function(){return function(j){if(m===j){o.update("isCurrentTabOpened",!o.get("isCurrentTabOpened"));var A=i.current;A&&A.style.width&&A.style.removeProperty("width")}else o.update("currentTab",j)}(S)}},E.icon),content:function(){return E.title},offset:h?ei:Qa}))})),R.createElement("div",{"aria-labelledby":"rpv-default-layout__sidebar-tab-".concat(m),id:"rpv-default-layout__sidebar-content",className:ve.classNames({"rpv-default-layout__sidebar-content":!0,"rpv-default-layout__sidebar-content--opened":g,"rpv-default-layout__sidebar-content--ltr":!h,"rpv-default-layout__sidebar-content--rtl":h}),role:"tabpanel",tabIndex:-1},u[m].content))),g&&R.createElement(ve.Splitter,{constrain:function(E){return E.firstHalfPercentage>=20&&E.firstHalfPercentage<=80}}))};Ye.BookmarkIcon=lr,Ye.FileIcon=ur,Ye.ThumbnailIcon=sr,Ye.defaultLayoutPlugin=function(e){var t=R.useMemo(function(){return ve.createStore({isCurrentTabOpened:!1,currentTab:0})},[]),r=Ua.attachmentPlugin(),o=qa.bookmarkPlugin(),n=Ja.thumbnailPlugin(e?e.thumbnailPlugin:{}),a=Xa.toolbarPlugin(e?e.toolbarPlugin:{}),i=r.Attachments,c=o.Bookmarks,s=n.Thumbnails,g=a.Toolbar,f=e?e.sidebarTabs:function(m){return m},b=[r,o,n,a];return{attachmentPluginInstance:r,bookmarkPluginInstance:o,thumbnailPluginInstance:n,toolbarPluginInstance:a,activateTab:function(m){t.update("currentTab",m)},toggleTab:function(m){var d=t.get("currentTab");t.update("isCurrentTabOpened",!t.get("isCurrentTabOpened")),d!==m&&t.update("currentTab",m)},install:function(m){b.forEach(function(d){d.install&&d.install(m)})},renderPageLayer:function(m){return R.createElement(R.Fragment,null,b.map(function(d,h){return d.renderPageLayer?R.createElement(R.Fragment,{key:h},d.renderPageLayer(m)):R.createElement(R.Fragment,{key:h},R.createElement(R.Fragment,null))}))},renderViewer:function(m){var d=m.slot;b.forEach(function(l){l.renderViewer&&(d=l.renderViewer(rt(rt({},m),{slot:d})))});var h=d.subSlot&&d.subSlot.attrs?{className:d.subSlot.attrs.className,"data-testid":d.subSlot.attrs["data-testid"],ref:d.subSlot.attrs.ref,style:d.subSlot.attrs.style}:{};return d.children=R.createElement("div",{className:"rpv-default-layout__container"},R.createElement("div",{"data-testid":"default-layout__main",className:ve.classNames({"rpv-default-layout__main":!0,"rpv-default-layout__main--rtl":m.themeContext.direction===ve.TextDirection.RightToLeft})},R.createElement(ti,{attachmentTabContent:R.createElement(i,null),bookmarkTabContent:R.createElement(c,null),store:t,thumbnailTabContent:R.createElement(s,null),tabs:f}),R.createElement("div",{className:"rpv-default-layout__body","data-testid":"default-layout__body"},R.createElement("div",{className:"rpv-default-layout__toolbar"},e&&e.renderToolbar?e.renderToolbar(g):R.createElement(g,null)),R.createElement("div",rt({},h),d.subSlot.children))),d.children),d.subSlot.attrs={},d.subSlot.children=R.createElement(R.Fragment,null),d},uninstall:function(m){b.forEach(function(d){d.uninstall&&d.uninstall(m)})},onDocumentLoad:function(m){b.forEach(function(d){d.onDocumentLoad&&d.onDocumentLoad(m)}),e&&e.setInitialTab&&e.setInitialTab(m.doc).then(function(d){t.update("currentTab",d),t.update("isCurrentTabOpened",!0)})},onAnnotationLayerRender:function(m){b.forEach(function(d){d.onAnnotationLayerRender&&d.onAnnotationLayerRender(m)})},onTextLayerRender:function(m){b.forEach(function(d){d.onTextLayerRender&&d.onTextLayerRender(m)})},onViewerStateChange:function(m){var d=m;return b.forEach(function(h){h.onViewerStateChange&&(d=h.onViewerStateChange(d))}),d}}},Ye.setInitialTabFromPageMode=function(e){return new Promise(function(t,r){e.getPageMode().then(function(o){if(o)switch(o){case ve.PageMode.Attachments:t(2);break;case ve.PageMode.Bookmarks:t(1);break;case ve.PageMode.Thumbnails:t(0);break;default:t(-1)}else t(-1)})})};
