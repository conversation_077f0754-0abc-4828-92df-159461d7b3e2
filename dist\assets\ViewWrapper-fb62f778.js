import{j as r}from"./@chakra-ui/react-e5fbf24f.js";import{a as s}from"./vendor-df163860.js";import{T as f}from"./index-99294b93.js";import{L as e}from"./index-ca2a1632.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./pdf-lib-d770586c.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./yup-b43a9d72.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@tanstack/react-query-eaef12f9.js";const G=({children:i,view:o,views:t,setView:p,viewsMap:l})=>{const a=s.Children.toArray(i);return r.jsxs("div",{className:"grid !h-full max-h-full min-h-full w-full min-w-full max-w-full grid-rows-[auto_1fr]",children:[r.jsx("div",{className:"mb-5 w-full min-w-full max-w-full overflow-auto",children:r.jsx(e,{children:r.jsx(f,{tabs:t,view:o,setView:p,viewsMap:l})})}),a.map(m=>m.props.view===o?m:null)]})};export{G as default};
