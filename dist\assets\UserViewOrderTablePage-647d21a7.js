import{j as s}from"./@chakra-ui/react-e5fbf24f.js";import{a as l,u as t}from"./vendor-df163860.js";import"./yup-b43a9d72.js";import{e as h,G as m,t as N,c as j}from"./index-ca2a1632.js";import"./pdf-lib-d770586c.js";import"./react-toggle-6e1dcb63.js";import"./index-f1c3f18b.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./@tanstack/react-query-eaef12f9.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@uppy/dashboard-12912511.js";import"./@uppy/core-59463b77.js";import"./@uppy/aws-s3-67e39052.js";import"./@uppy/compressor-abfad62a.js";let d=new h;const U=()=>{l.useContext(m);const{dispatch:x}=l.useContext(m),[e,r]=l.useState({}),[n,c]=l.useState(!0),i=t();return l.useEffect(function(){(async function(){try{c(!0),d.setTable("order");const a=await d.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");a.error||(r(a.model),c(!1))}catch(a){c(!1),console.log("error",a),N(x,a.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:n?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Order"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Customer"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.customer})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Division"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.division})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Order Number"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.order_number})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Config Number"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.config_number})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Ref Number"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.ref_number})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Campaign"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.campaign})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Quantity"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.quantity})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Location"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.location})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"City"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.city})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"State"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.state})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Services"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.services})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Package Type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.package_type})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Package Weight"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.package_weight})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Zone"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.zone})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Tracking Number"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.tracking_number})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Address"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.address})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Second Address"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.second_address})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Zip"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.zip})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Country"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.country})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Theater Id Tds"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.theater_id_tds})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Theater Id Rtk"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.theater_id_rtk})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Theater Id Maccs"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.theater_id_maccs})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Sku"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.sku})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Options"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.options})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Length"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.length})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Width"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.width})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Height"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.height})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Weight"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.weight})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Notes"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.notes})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Warehouse"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.warehouse})]})})]})})};export{U as default};
