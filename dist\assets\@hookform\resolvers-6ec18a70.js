import{g as a,s as o}from"../react-hook-form-c0d39979.js";var u=function(r,i,e){if(r&&"reportValidity"in r){var t=a(e,i);r.setCustomValidity(t&&t.message||""),r.reportValidity()}},c=function(r,i){var e=function(n){var f=i.fields[n];f&&f.ref&&"reportValidity"in f.ref?u(f.ref,n,r):f.refs&&f.refs.forEach(function(s){return u(s,n,r)})};for(var t in i.fields)e(t)},d=function(r,i){i.shouldUseNativeValidation&&c(r,i);var e={};for(var t in r){var n=a(i.fields,t),f=Object.assign(r[t]||{},{ref:n&&n.ref});if(l(i.names||Object.keys(r),t)){var s=Object.assign({},a(e,t));o(s,"root",f),o(e,t,s)}else o(e,t,f)}return e},l=function(r,i){return r.some(function(e){return e.startsWith(i+".")})};export{c as i,d as n};
