import{j as p}from"./@chakra-ui/react-e5fbf24f.js";import{a as m,r as i}from"./vendor-df163860.js";import{G as x,A as W}from"./index-ca2a1632.js";import"./pdf-lib-d770586c.js";import"./react-toggle-6e1dcb63.js";import"./index-f1c3f18b.js";import{S as D}from"./index-ceeca4cd.js";import{u as S}from"./useWarehouses-472eda20.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./yup-b43a9d72.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./@tanstack/react-query-eaef12f9.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@uppy/dashboard-12912511.js";import"./@uppy/core-59463b77.js";import"./@uppy/aws-s3-67e39052.js";import"./@uppy/compressor-abfad62a.js";const et=({errors:s,register:b,warehouse:n=0,className:u="w-[23rem]",disabled:l=!1,setDefault:f=!1,setValue:r=null,required:d=!1,name:h="warehouse",label:c="Warehouse"})=>{m.useContext(x),m.useContext(W),i.useState({}),i.useState([]);const{getWarehouses:w,loading:a,warehouse:{all:t}}=S({filter:["status,eq,1"]});return i.useEffect(()=>{if(t!=null&&t.length){const e=f?t.find(o=>o==null?void 0:o.default_warehouse):null;e&&r&&r(e)}},[t==null?void 0:t.length]),p.jsx("div",{className:"relative",children:p.jsx(D,{errors:s,useExternalData:!0,value:n,uniqueKey:"id",name:h,label:c,required:d,className:u,disabled:l,displaySeparator:"|",placeholder:"Warehouse",display:["warehouse"],externalDataOptions:t,externalDataLoading:a==null?void 0:a.all,onSelect:(e,o)=>r&&r(e,o)})})};export{et as default};
