var ie=(e,t,s)=>{if(!t.has(e))throw TypeError("Cannot "+s)};var i=(e,t,s)=>(ie(e,t,"read from private field"),s?s.call(e):t.get(e)),h=(e,t,s)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,s)},o=(e,t,s,r)=>(ie(e,t,"write to private field"),r?r.call(e,s):t.set(e,s),s);var Xt=(e,t,s,r)=>({set _(n){o(e,t,n,s)},get _(){return i(e,t,r)}}),f=(e,t,s)=>(ie(e,t,"access private method"),s);import{r as Q}from"../vendor-df163860.js";import{j as ss}from"../@chakra-ui/react-e5fbf24f.js";var Ut=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},mt=typeof window>"u"||"Deno"in globalThis;function L(){}function is(e,t){return typeof e=="function"?e(t):e}function ne(e){return typeof e=="number"&&e>=0&&e!==1/0}function _e(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Ot(e,t){return typeof e=="function"?e(t):e}function G(e,t){return typeof e=="function"?e(t):e}function Re(e,t){const{type:s="all",exact:r,fetchStatus:n,predicate:a,queryKey:u,stale:l}=e;if(u){if(r){if(t.queryHash!==ge(u,t.options))return!1}else if(!jt(t.queryKey,u))return!1}if(s!=="all"){const d=t.isActive();if(s==="active"&&!d||s==="inactive"&&d)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||n&&n!==t.state.fetchStatus||a&&!a(t))}function Se(e,t){const{exact:s,status:r,predicate:n,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(vt(t.options.mutationKey)!==vt(a))return!1}else if(!jt(t.options.mutationKey,a))return!1}return!(r&&t.state.status!==r||n&&!n(t))}function ge(e,t){return((t==null?void 0:t.queryKeyHashFn)||vt)(e)}function vt(e){return JSON.stringify(e,(t,s)=>ae(s)?Object.keys(s).sort().reduce((r,n)=>(r[n]=s[n],r),{}):s)}function jt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(s=>!jt(e[s],t[s])):!1}function Ge(e,t){if(e===t)return e;const s=Pe(e)&&Pe(t);if(s||ae(e)&&ae(t)){const r=s?e:Object.keys(e),n=r.length,a=s?t:Object.keys(t),u=a.length,l=s?[]:{};let d=0;for(let b=0;b<u;b++){const y=s?b:a[b];(!s&&r.includes(y)||s)&&e[y]===void 0&&t[y]===void 0?(l[y]=void 0,d++):(l[y]=Ge(e[y],t[y]),l[y]===e[y]&&e[y]!==void 0&&d++)}return n===u&&d===n?e:l}return t}function te(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function Pe(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function ae(e){if(!Fe(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!Fe(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Fe(e){return Object.prototype.toString.call(e)==="[object Object]"}function rs(e){return new Promise(t=>{setTimeout(t,e)})}function ue(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?Ge(e,t):t}function ns(e,t,s=0){const r=[...e,t];return s&&r.length>s?r.slice(1):r}function as(e,t,s=0){const r=[t,...e];return s&&r.length>s?r.slice(0,-1):r}var Oe=Symbol();function Ne(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===Oe?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var ut,Y,wt,Ae,us=(Ae=class extends Ut{constructor(){super();h(this,ut,void 0);h(this,Y,void 0);h(this,wt,void 0);o(this,wt,t=>{if(!mt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){i(this,Y)||this.setEventListener(i(this,wt))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,Y))==null||t.call(this),o(this,Y,void 0))}setEventListener(t){var s;o(this,wt,t),(s=i(this,Y))==null||s.call(this),o(this,Y,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){i(this,ut)!==t&&(o(this,ut,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof i(this,ut)=="boolean"?i(this,ut):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},ut=new WeakMap,Y=new WeakMap,wt=new WeakMap,Ae),we=new us,Ct,Z,Rt,Te,os=(Te=class extends Ut{constructor(){super();h(this,Ct,!0);h(this,Z,void 0);h(this,Rt,void 0);o(this,Rt,t=>{if(!mt&&window.addEventListener){const s=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",r)}}})}onSubscribe(){i(this,Z)||this.setEventListener(i(this,Rt))}onUnsubscribe(){var t;this.hasListeners()||((t=i(this,Z))==null||t.call(this),o(this,Z,void 0))}setEventListener(t){var s;o(this,Rt,t),(s=i(this,Z))==null||s.call(this),o(this,Z,t(this.setOnline.bind(this)))}setOnline(t){i(this,Ct)!==t&&(o(this,Ct,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return i(this,Ct)}},Ct=new WeakMap,Z=new WeakMap,Rt=new WeakMap,Te),ee=new os;function oe(){let e,t;const s=new Promise((n,a)=>{e=n,t=a});s.status="pending",s.catch(()=>{});function r(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{r({status:"fulfilled",value:n}),e(n)},s.reject=n=>{r({status:"rejected",reason:n}),t(n)},s}function hs(e){return Math.min(1e3*2**e,3e4)}function Be(e){return(e??"online")==="online"?ee.isOnline():!0}var ze=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function re(e){return e instanceof ze}function Ve(e){let t=!1,s=0,r=!1,n;const a=oe(),u=p=>{var m;r||(O(new ze(p)),(m=e.abort)==null||m.call(e))},l=()=>{t=!0},d=()=>{t=!1},b=()=>we.isFocused()&&(e.networkMode==="always"||ee.isOnline())&&e.canRun(),y=()=>Be(e.networkMode)&&e.canRun(),c=p=>{var m;r||(r=!0,(m=e.onSuccess)==null||m.call(e,p),n==null||n(),a.resolve(p))},O=p=>{var m;r||(r=!0,(m=e.onError)==null||m.call(e,p),n==null||n(),a.reject(p))},g=()=>new Promise(p=>{var m;n=F=>{(r||b())&&p(F)},(m=e.onPause)==null||m.call(e)}).then(()=>{var p;n=void 0,r||(p=e.onContinue)==null||p.call(e)}),S=()=>{if(r)return;let p;const m=s===0?e.initialPromise:void 0;try{p=m??e.fn()}catch(F){p=Promise.reject(F)}Promise.resolve(p).then(c).catch(F=>{var K;if(r)return;const x=e.retry??(mt?0:3),C=e.retryDelay??hs,M=typeof C=="function"?C(s,F):C,j=x===!0||typeof x=="number"&&s<x||typeof x=="function"&&x(s,F);if(t||!j){O(F);return}s++,(K=e.onFail)==null||K.call(e,s,F),rs(M).then(()=>b()?void 0:g()).then(()=>{t?O(F):S()})})};return{promise:a,cancel:u,continue:()=>(n==null||n(),a),cancelRetry:l,continueRetry:d,canStart:y,start:()=>(y()?S():g().then(S),a)}}function cs(){let e=[],t=0,s=l=>{l()},r=l=>{l()},n=l=>setTimeout(l,0);const a=l=>{t?e.push(l):n(()=>{s(l)})},u=()=>{const l=e;e=[],l.length&&n(()=>{r(()=>{l.forEach(d=>{s(d)})})})};return{batch:l=>{let d;t++;try{d=l()}finally{t--,t||u()}return d},batchCalls:l=>(...d)=>{a(()=>{l(...d)})},schedule:a,setNotifyFunction:l=>{s=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{n=l}}}var E=cs(),ot,Ie,$e=(Ie=class{constructor(){h(this,ot,void 0)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),ne(this.gcTime)&&o(this,ot,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(mt?1/0:5*60*1e3))}clearGcTimeout(){i(this,ot)&&(clearTimeout(i(this,ot)),o(this,ot,void 0))}},ot=new WeakMap,Ie),St,Pt,k,ht,D,kt,ct,H,$,Ue,ls=(Ue=class extends $e{constructor(t){super();h(this,H);h(this,St,void 0);h(this,Pt,void 0);h(this,k,void 0);h(this,ht,void 0);h(this,D,void 0);h(this,kt,void 0);h(this,ct,void 0);o(this,ct,!1),o(this,kt,t.defaultOptions),this.setOptions(t.options),this.observers=[],o(this,ht,t.client),o(this,k,i(this,ht).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,o(this,St,ds(this.options)),this.state=t.state??i(this,St),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=i(this,D))==null?void 0:t.promise}setOptions(t){this.options={...i(this,kt),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&i(this,k).remove(this)}setData(t,s){const r=ue(this.state.data,t,this.options);return f(this,H,$).call(this,{data:r,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),r}setState(t,s){f(this,H,$).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var r,n;const s=(r=i(this,D))==null?void 0:r.promise;return(n=i(this,D))==null||n.cancel(t),s?s.then(L).catch(L):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(i(this,St))}isActive(){return this.observers.some(t=>G(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Oe||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!_e(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,D))==null||s.continue()}onOnline(){var s;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=i(this,D))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),i(this,k).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(i(this,D)&&(i(this,ct)?i(this,D).cancel({revert:!0}):i(this,D).cancelRetry()),this.scheduleGc()),i(this,k).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||f(this,H,$).call(this,{type:"invalidate"})}fetch(t,s){var d,b,y;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(i(this,D))return i(this,D).continueRetry(),i(this,D).promise}if(t&&this.setOptions(t),!this.options.queryFn){const c=this.observers.find(O=>O.options.queryFn);c&&this.setOptions(c.options)}const r=new AbortController,n=c=>{Object.defineProperty(c,"signal",{enumerable:!0,get:()=>(o(this,ct,!0),r.signal)})},a=()=>{const c=Ne(this.options,s),O={client:i(this,ht),queryKey:this.queryKey,meta:this.meta};return n(O),o(this,ct,!1),this.options.persister?this.options.persister(c,O,this):c(O)},u={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:i(this,ht),state:this.state,fetchFn:a};n(u),(d=this.options.behavior)==null||d.onFetch(u,this),o(this,Pt,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((b=u.fetchOptions)==null?void 0:b.meta))&&f(this,H,$).call(this,{type:"fetch",meta:(y=u.fetchOptions)==null?void 0:y.meta});const l=c=>{var O,g,S,p;re(c)&&c.silent||f(this,H,$).call(this,{type:"error",error:c}),re(c)||((g=(O=i(this,k).config).onError)==null||g.call(O,c,this),(p=(S=i(this,k).config).onSettled)==null||p.call(S,this.state.data,c,this)),this.scheduleGc()};return o(this,D,Ve({initialPromise:s==null?void 0:s.initialPromise,fn:u.fetchFn,abort:r.abort.bind(r),onSuccess:c=>{var O,g,S,p;if(c===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(c)}catch(m){l(m);return}(g=(O=i(this,k).config).onSuccess)==null||g.call(O,c,this),(p=(S=i(this,k).config).onSettled)==null||p.call(S,c,this.state.error,this),this.scheduleGc()},onError:l,onFail:(c,O)=>{f(this,H,$).call(this,{type:"failed",failureCount:c,error:O})},onPause:()=>{f(this,H,$).call(this,{type:"pause"})},onContinue:()=>{f(this,H,$).call(this,{type:"continue"})},retry:u.options.retry,retryDelay:u.options.retryDelay,networkMode:u.options.networkMode,canRun:()=>!0})),i(this,D).start()}},St=new WeakMap,Pt=new WeakMap,k=new WeakMap,ht=new WeakMap,D=new WeakMap,kt=new WeakMap,ct=new WeakMap,H=new WeakSet,$=function(t){const s=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...We(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return re(n)&&n.revert&&i(this,Pt)?{...i(this,Pt),fetchStatus:"idle"}:{...r,error:n,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=s(this.state),E.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),i(this,k).notify({query:this,type:"updated",action:t})})},Ue);function We(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Be(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ds(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,r=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var N,qe,fs=(qe=class extends Ut{constructor(t={}){super();h(this,N,void 0);this.config=t,o(this,N,new Map)}build(t,s,r){const n=s.queryKey,a=s.queryHash??ge(n,s);let u=this.get(a);return u||(u=new ls({client:t,queryKey:n,queryHash:a,options:t.defaultQueryOptions(s),state:r,defaultOptions:t.getQueryDefaults(n)}),this.add(u)),u}add(t){i(this,N).has(t.queryHash)||(i(this,N).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=i(this,N).get(t.queryHash);s&&(t.destroy(),s===t&&i(this,N).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){E.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return i(this,N).get(t)}getAll(){return[...i(this,N).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(r=>Re(s,r))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(r=>Re(t,r)):s}notify(t){E.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){E.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){E.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},N=new WeakMap,qe),B,T,lt,z,X,je,ys=(je=class extends $e{constructor(t){super();h(this,z);h(this,B,void 0);h(this,T,void 0);h(this,lt,void 0);this.mutationId=t.mutationId,o(this,T,t.mutationCache),o(this,B,[]),this.state=t.state||Je(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){i(this,B).includes(t)||(i(this,B).push(t),this.clearGcTimeout(),i(this,T).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){o(this,B,i(this,B).filter(s=>s!==t)),this.scheduleGc(),i(this,T).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){i(this,B).length||(this.state.status==="pending"?this.scheduleGc():i(this,T).remove(this))}continue(){var t;return((t=i(this,lt))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var n,a,u,l,d,b,y,c,O,g,S,p,m,F,x,C,M,j,K,A;o(this,lt,Ve({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(P,w)=>{f(this,z,X).call(this,{type:"failed",failureCount:P,error:w})},onPause:()=>{f(this,z,X).call(this,{type:"pause"})},onContinue:()=>{f(this,z,X).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>i(this,T).canRun(this)}));const s=this.state.status==="pending",r=!i(this,lt).canStart();try{if(!s){f(this,z,X).call(this,{type:"pending",variables:t,isPaused:r}),await((a=(n=i(this,T).config).onMutate)==null?void 0:a.call(n,t,this));const w=await((l=(u=this.options).onMutate)==null?void 0:l.call(u,t));w!==this.state.context&&f(this,z,X).call(this,{type:"pending",context:w,variables:t,isPaused:r})}const P=await i(this,lt).start();return await((b=(d=i(this,T).config).onSuccess)==null?void 0:b.call(d,P,t,this.state.context,this)),await((c=(y=this.options).onSuccess)==null?void 0:c.call(y,P,t,this.state.context)),await((g=(O=i(this,T).config).onSettled)==null?void 0:g.call(O,P,null,this.state.variables,this.state.context,this)),await((p=(S=this.options).onSettled)==null?void 0:p.call(S,P,null,t,this.state.context)),f(this,z,X).call(this,{type:"success",data:P}),P}catch(P){try{throw await((F=(m=i(this,T).config).onError)==null?void 0:F.call(m,P,t,this.state.context,this)),await((C=(x=this.options).onError)==null?void 0:C.call(x,P,t,this.state.context)),await((j=(M=i(this,T).config).onSettled)==null?void 0:j.call(M,void 0,P,this.state.variables,this.state.context,this)),await((A=(K=this.options).onSettled)==null?void 0:A.call(K,void 0,P,t,this.state.context)),P}finally{f(this,z,X).call(this,{type:"error",error:P})}}finally{i(this,T).runNext(this)}}},B=new WeakMap,T=new WeakMap,lt=new WeakMap,z=new WeakSet,X=function(t){const s=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),E.batch(()=>{i(this,B).forEach(r=>{r.onMutationUpdate(t)}),i(this,T).notify({mutation:this,type:"updated",action:t})})},je);function Je(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var W,_,Lt,ke,ps=(ke=class extends Ut{constructor(t={}){super();h(this,W,void 0);h(this,_,void 0);h(this,Lt,void 0);this.config=t,o(this,W,new Set),o(this,_,new Map),o(this,Lt,0)}build(t,s,r){const n=new ys({mutationCache:this,mutationId:++Xt(this,Lt)._,options:t.defaultMutationOptions(s),state:r});return this.add(n),n}add(t){i(this,W).add(t);const s=Yt(t);if(typeof s=="string"){const r=i(this,_).get(s);r?r.push(t):i(this,_).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(i(this,W).delete(t)){const s=Yt(t);if(typeof s=="string"){const r=i(this,_).get(s);if(r)if(r.length>1){const n=r.indexOf(t);n!==-1&&r.splice(n,1)}else r[0]===t&&i(this,_).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=Yt(t);if(typeof s=="string"){const r=i(this,_).get(s),n=r==null?void 0:r.find(a=>a.state.status==="pending");return!n||n===t}else return!0}runNext(t){var r;const s=Yt(t);if(typeof s=="string"){const n=(r=i(this,_).get(s))==null?void 0:r.find(a=>a!==t&&a.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){E.batch(()=>{i(this,W).forEach(t=>{this.notify({type:"removed",mutation:t})}),i(this,W).clear(),i(this,_).clear()})}getAll(){return Array.from(i(this,W))}find(t){const s={exact:!0,...t};return this.getAll().find(r=>Se(s,r))}findAll(t={}){return this.getAll().filter(s=>Se(t,s))}notify(t){E.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return E.batch(()=>Promise.all(t.map(s=>s.continue().catch(L))))}},W=new WeakMap,_=new WeakMap,Lt=new WeakMap,ke);function Yt(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Ee(e){return{onFetch:(t,s)=>{var y,c,O,g,S;const r=t.options,n=(O=(c=(y=t.fetchOptions)==null?void 0:y.meta)==null?void 0:c.fetchMore)==null?void 0:O.direction,a=((g=t.state.data)==null?void 0:g.pages)||[],u=((S=t.state.data)==null?void 0:S.pageParams)||[];let l={pages:[],pageParams:[]},d=0;const b=async()=>{let p=!1;const m=C=>{Object.defineProperty(C,"signal",{enumerable:!0,get:()=>(t.signal.aborted?p=!0:t.signal.addEventListener("abort",()=>{p=!0}),t.signal)})},F=Ne(t.options,t.fetchOptions),x=async(C,M,j)=>{if(p)return Promise.reject();if(M==null&&C.pages.length)return Promise.resolve(C);const K={client:t.client,queryKey:t.queryKey,pageParam:M,direction:j?"backward":"forward",meta:t.options.meta};m(K);const A=await F(K),{maxPages:P}=t.options,w=j?as:ns;return{pages:w(C.pages,A,P),pageParams:w(C.pageParams,M,P)}};if(n&&a.length){const C=n==="backward",M=C?ms:Qe,j={pages:a,pageParams:u},K=M(r,j);l=await x(j,K,C)}else{const C=e??a.length;do{const M=d===0?u[0]??r.initialPageParam:Qe(r,l);if(d>0&&M==null)break;l=await x(l,M),d++}while(d<C)}return l};t.options.persister?t.fetchFn=()=>{var p,m;return(m=(p=t.options).persister)==null?void 0:m.call(p,b,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=b}}}function Qe(e,{pages:t,pageParams:s}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}function ms(e,{pages:t,pageParams:s}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,s[0],s):void 0}var R,tt,et,Ft,Et,st,Qt,Mt,Le,Us=(Le=class{constructor(e={}){h(this,R,void 0);h(this,tt,void 0);h(this,et,void 0);h(this,Ft,void 0);h(this,Et,void 0);h(this,st,void 0);h(this,Qt,void 0);h(this,Mt,void 0);o(this,R,e.queryCache||new fs),o(this,tt,e.mutationCache||new ps),o(this,et,e.defaultOptions||{}),o(this,Ft,new Map),o(this,Et,new Map),o(this,st,0)}mount(){Xt(this,st)._++,i(this,st)===1&&(o(this,Qt,we.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,R).onFocus())})),o(this,Mt,ee.subscribe(async e=>{e&&(await this.resumePausedMutations(),i(this,R).onOnline())})))}unmount(){var e,t;Xt(this,st)._--,i(this,st)===0&&((e=i(this,Qt))==null||e.call(this),o(this,Qt,void 0),(t=i(this,Mt))==null||t.call(this),o(this,Mt,void 0))}isFetching(e){return i(this,R).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return i(this,tt).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,R).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=i(this,R).build(this,t),r=s.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(Ot(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return i(this,R).findAll(e).map(({queryKey:t,state:s})=>{const r=s.data;return[t,r]})}setQueryData(e,t,s){const r=this.defaultQueryOptions({queryKey:e}),n=i(this,R).get(r.queryHash),a=n==null?void 0:n.state.data,u=is(t,a);if(u!==void 0)return i(this,R).build(this,r).setData(u,{...s,manual:!0})}setQueriesData(e,t,s){return E.batch(()=>i(this,R).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=i(this,R).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=i(this,R);E.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=i(this,R);return E.batch(()=>(s.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const s={revert:!0,...t},r=E.batch(()=>i(this,R).findAll(e).map(n=>n.cancel(s)));return Promise.all(r).then(L).catch(L)}invalidateQueries(e,t={}){return E.batch(()=>(i(this,R).findAll(e).forEach(s=>{s.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},r=E.batch(()=>i(this,R).findAll(e).filter(n=>!n.isDisabled()).map(n=>{let a=n.fetch(void 0,s);return s.throwOnError||(a=a.catch(L)),n.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(r).then(L)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=i(this,R).build(this,t);return s.isStaleByTime(Ot(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(L).catch(L)}fetchInfiniteQuery(e){return e.behavior=Ee(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(L).catch(L)}ensureInfiniteQueryData(e){return e.behavior=Ee(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return ee.isOnline()?i(this,tt).resumePausedMutations():Promise.resolve()}getQueryCache(){return i(this,R)}getMutationCache(){return i(this,tt)}getDefaultOptions(){return i(this,et)}setDefaultOptions(e){o(this,et,e)}setQueryDefaults(e,t){i(this,Ft).set(vt(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...i(this,Ft).values()],s={};return t.forEach(r=>{jt(e,r.queryKey)&&Object.assign(s,r.defaultOptions)}),s}setMutationDefaults(e,t){i(this,Et).set(vt(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...i(this,Et).values()],s={};return t.forEach(r=>{jt(e,r.mutationKey)&&Object.assign(s,r.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...i(this,et).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=ge(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===Oe&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...i(this,et).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){i(this,R).clear(),i(this,tt).clear()}},R=new WeakMap,tt=new WeakMap,et=new WeakMap,Ft=new WeakMap,Et=new WeakMap,st=new WeakMap,Qt=new WeakMap,Mt=new WeakMap,Le),U,v,Kt,I,dt,Dt,it,V,Ht,xt,At,ft,yt,rt,Tt,pt,qt,_t,he,Gt,ce,Nt,le,Bt,de,zt,fe,Vt,ye,$t,pe,se,Xe,Ke,vs=(Ke=class extends Ut{constructor(t,s){super();h(this,pt);h(this,_t);h(this,Gt);h(this,Nt);h(this,Bt);h(this,zt);h(this,Vt);h(this,$t);h(this,se);h(this,U,void 0);h(this,v,void 0);h(this,Kt,void 0);h(this,I,void 0);h(this,dt,void 0);h(this,Dt,void 0);h(this,it,void 0);h(this,V,void 0);h(this,Ht,void 0);h(this,xt,void 0);h(this,At,void 0);h(this,ft,void 0);h(this,yt,void 0);h(this,rt,void 0);h(this,Tt,new Set);this.options=s,o(this,U,t),o(this,V,null),o(this,it,oe()),this.options.experimental_prefetchInRender||i(this,it).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(i(this,v).addObserver(this),Me(i(this,v),this.options)?f(this,pt,qt).call(this):this.updateResult(),f(this,Bt,de).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return me(i(this,v),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return me(i(this,v),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,f(this,zt,fe).call(this),f(this,Vt,ye).call(this),i(this,v).removeObserver(this)}setOptions(t,s){const r=this.options,n=i(this,v);if(this.options=i(this,U).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof G(this.options.enabled,i(this,v))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");f(this,$t,pe).call(this),i(this,v).setOptions(this.options),r._defaulted&&!te(this.options,r)&&i(this,U).getQueryCache().notify({type:"observerOptionsUpdated",query:i(this,v),observer:this});const a=this.hasListeners();a&&De(i(this,v),n,this.options,r)&&f(this,pt,qt).call(this),this.updateResult(s),a&&(i(this,v)!==n||G(this.options.enabled,i(this,v))!==G(r.enabled,i(this,v))||Ot(this.options.staleTime,i(this,v))!==Ot(r.staleTime,i(this,v)))&&f(this,_t,he).call(this);const u=f(this,Gt,ce).call(this);a&&(i(this,v)!==n||G(this.options.enabled,i(this,v))!==G(r.enabled,i(this,v))||u!==i(this,rt))&&f(this,Nt,le).call(this,u)}getOptimisticResult(t){const s=i(this,U).getQueryCache().build(i(this,U),t),r=this.createResult(s,t);return gs(this,r)&&(o(this,I,r),o(this,Dt,this.options),o(this,dt,i(this,v).state)),r}getCurrentResult(){return i(this,I)}trackResult(t,s){const r={};return Object.keys(t).forEach(n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),s==null||s(n),t[n])})}),r}trackProp(t){i(this,Tt).add(t)}getCurrentQuery(){return i(this,v)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=i(this,U).defaultQueryOptions(t),r=i(this,U).getQueryCache().build(i(this,U),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return f(this,pt,qt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),i(this,I)))}createResult(t,s){var P;const r=i(this,v),n=this.options,a=i(this,I),u=i(this,dt),l=i(this,Dt),b=t!==r?t.state:i(this,Kt),{state:y}=t;let c={...y},O=!1,g;if(s._optimisticResults){const w=this.hasListeners(),bt=!w&&Me(t,s),gt=w&&De(t,r,s,n);(bt||gt)&&(c={...c,...We(y.data,t.options)}),s._optimisticResults==="isRestoring"&&(c.fetchStatus="idle")}let{error:S,errorUpdatedAt:p,status:m}=c;if(s.select&&c.data!==void 0)if(a&&c.data===(u==null?void 0:u.data)&&s.select===i(this,Ht))g=i(this,xt);else try{o(this,Ht,s.select),g=s.select(c.data),g=ue(a==null?void 0:a.data,g,s),o(this,xt,g),o(this,V,null)}catch(w){o(this,V,w)}else g=c.data;if(s.placeholderData!==void 0&&g===void 0&&m==="pending"){let w;if(a!=null&&a.isPlaceholderData&&s.placeholderData===(l==null?void 0:l.placeholderData))w=a.data;else if(w=typeof s.placeholderData=="function"?s.placeholderData((P=i(this,At))==null?void 0:P.state.data,i(this,At)):s.placeholderData,s.select&&w!==void 0)try{w=s.select(w),o(this,V,null)}catch(bt){o(this,V,bt)}w!==void 0&&(m="success",g=ue(a==null?void 0:a.data,w,s),O=!0)}i(this,V)&&(S=i(this,V),g=i(this,xt),p=Date.now(),m="error");const F=c.fetchStatus==="fetching",x=m==="pending",C=m==="error",M=x&&F,j=g!==void 0,A={status:m,fetchStatus:c.fetchStatus,isPending:x,isSuccess:m==="success",isError:C,isInitialLoading:M,isLoading:M,data:g,dataUpdatedAt:c.dataUpdatedAt,error:S,errorUpdatedAt:p,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>b.dataUpdateCount||c.errorUpdateCount>b.errorUpdateCount,isFetching:F,isRefetching:F&&!x,isLoadingError:C&&!j,isPaused:c.fetchStatus==="paused",isPlaceholderData:O,isRefetchError:C&&j,isStale:Ce(t,s),refetch:this.refetch,promise:i(this,it)};if(this.options.experimental_prefetchInRender){const w=Jt=>{A.status==="error"?Jt.reject(A.error):A.data!==void 0&&Jt.resolve(A.data)},bt=()=>{const Jt=o(this,it,A.promise=oe());w(Jt)},gt=i(this,it);switch(gt.status){case"pending":t.queryHash===r.queryHash&&w(gt);break;case"fulfilled":(A.status==="error"||A.data!==gt.value)&&bt();break;case"rejected":(A.status!=="error"||A.error!==gt.reason)&&bt();break}}return A}updateResult(t){const s=i(this,I),r=this.createResult(i(this,v),this.options);if(o(this,dt,i(this,v).state),o(this,Dt,this.options),i(this,dt).data!==void 0&&o(this,At,i(this,v)),te(r,s))return;o(this,I,r);const n={},a=()=>{if(!s)return!0;const{notifyOnChangeProps:u}=this.options,l=typeof u=="function"?u():u;if(l==="all"||!l&&!i(this,Tt).size)return!0;const d=new Set(l??i(this,Tt));return this.options.throwOnError&&d.add("error"),Object.keys(i(this,I)).some(b=>{const y=b;return i(this,I)[y]!==s[y]&&d.has(y)})};(t==null?void 0:t.listeners)!==!1&&a()&&(n.listeners=!0),f(this,se,Xe).call(this,{...n,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&f(this,Bt,de).call(this)}},U=new WeakMap,v=new WeakMap,Kt=new WeakMap,I=new WeakMap,dt=new WeakMap,Dt=new WeakMap,it=new WeakMap,V=new WeakMap,Ht=new WeakMap,xt=new WeakMap,At=new WeakMap,ft=new WeakMap,yt=new WeakMap,rt=new WeakMap,Tt=new WeakMap,pt=new WeakSet,qt=function(t){f(this,$t,pe).call(this);let s=i(this,v).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(L)),s},_t=new WeakSet,he=function(){f(this,zt,fe).call(this);const t=Ot(this.options.staleTime,i(this,v));if(mt||i(this,I).isStale||!ne(t))return;const r=_e(i(this,I).dataUpdatedAt,t)+1;o(this,ft,setTimeout(()=>{i(this,I).isStale||this.updateResult()},r))},Gt=new WeakSet,ce=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(i(this,v)):this.options.refetchInterval)??!1},Nt=new WeakSet,le=function(t){f(this,Vt,ye).call(this),o(this,rt,t),!(mt||G(this.options.enabled,i(this,v))===!1||!ne(i(this,rt))||i(this,rt)===0)&&o(this,yt,setInterval(()=>{(this.options.refetchIntervalInBackground||we.isFocused())&&f(this,pt,qt).call(this)},i(this,rt)))},Bt=new WeakSet,de=function(){f(this,_t,he).call(this),f(this,Nt,le).call(this,f(this,Gt,ce).call(this))},zt=new WeakSet,fe=function(){i(this,ft)&&(clearTimeout(i(this,ft)),o(this,ft,void 0))},Vt=new WeakSet,ye=function(){i(this,yt)&&(clearInterval(i(this,yt)),o(this,yt,void 0))},$t=new WeakSet,pe=function(){const t=i(this,U).getQueryCache().build(i(this,U),this.options);if(t===i(this,v))return;const s=i(this,v);o(this,v,t),o(this,Kt,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},se=new WeakSet,Xe=function(t){E.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(i(this,I))}),i(this,U).getQueryCache().notify({query:i(this,v),type:"observerResultsUpdated"})})},Ke);function bs(e,t){return G(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Me(e,t){return bs(e,t)||e.state.data!==void 0&&me(e,t,t.refetchOnMount)}function me(e,t,s){if(G(t.enabled,e)!==!1){const r=typeof s=="function"?s(e):s;return r==="always"||r!==!1&&Ce(e,t)}return!1}function De(e,t,s,r){return(e!==t||G(r.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&Ce(e,s)}function Ce(e,t){return G(t.enabled,e)!==!1&&e.isStaleByTime(Ot(t.staleTime,e))}function gs(e,t){return!te(e.getCurrentResult(),t)}var nt,at,q,J,It,Zt,Wt,ve,He,Os=(He=class extends Ut{constructor(t,s){super();h(this,It);h(this,Wt);h(this,nt,void 0);h(this,at,void 0);h(this,q,void 0);h(this,J,void 0);o(this,nt,t),this.setOptions(s),this.bindMethods(),f(this,It,Zt).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var r;const s=this.options;this.options=i(this,nt).defaultMutationOptions(t),te(this.options,s)||i(this,nt).getMutationCache().notify({type:"observerOptionsUpdated",mutation:i(this,q),observer:this}),s!=null&&s.mutationKey&&this.options.mutationKey&&vt(s.mutationKey)!==vt(this.options.mutationKey)?this.reset():((r=i(this,q))==null?void 0:r.state.status)==="pending"&&i(this,q).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(t=i(this,q))==null||t.removeObserver(this)}onMutationUpdate(t){f(this,It,Zt).call(this),f(this,Wt,ve).call(this,t)}getCurrentResult(){return i(this,at)}reset(){var t;(t=i(this,q))==null||t.removeObserver(this),o(this,q,void 0),f(this,It,Zt).call(this),f(this,Wt,ve).call(this)}mutate(t,s){var r;return o(this,J,s),(r=i(this,q))==null||r.removeObserver(this),o(this,q,i(this,nt).getMutationCache().build(i(this,nt),this.options)),i(this,q).addObserver(this),i(this,q).execute(t)}},nt=new WeakMap,at=new WeakMap,q=new WeakMap,J=new WeakMap,It=new WeakSet,Zt=function(){var s;const t=((s=i(this,q))==null?void 0:s.state)??Je();o(this,at,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},Wt=new WeakSet,ve=function(t){E.batch(()=>{var s,r,n,a,u,l,d,b;if(i(this,J)&&this.hasListeners()){const y=i(this,at).variables,c=i(this,at).context;(t==null?void 0:t.type)==="success"?((r=(s=i(this,J)).onSuccess)==null||r.call(s,t.data,y,c),(a=(n=i(this,J)).onSettled)==null||a.call(n,t.data,null,y,c)):(t==null?void 0:t.type)==="error"&&((l=(u=i(this,J)).onError)==null||l.call(u,t.error,y,c),(b=(d=i(this,J)).onSettled)==null||b.call(d,void 0,t.error,y,c))}this.listeners.forEach(y=>{y(i(this,at))})})},He),Ye=Q.createContext(void 0),Ze=e=>{const t=Q.useContext(Ye);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},qs=({client:e,children:t})=>(Q.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),ss.jsx(Ye.Provider,{value:e,children:t})),ts=Q.createContext(!1),ws=()=>Q.useContext(ts);ts.Provider;function Cs(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var Rs=Q.createContext(Cs()),Ss=()=>Q.useContext(Rs);function es(e,t){return typeof e=="function"?e(...t):!!e}function be(){}var Ps=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},Fs=e=>{Q.useEffect(()=>{e.clearReset()},[e])},Es=({result:e,errorResetBoundary:t,throwOnError:s,query:r,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(n&&e.data===void 0||es(s,[e.error,r])),Qs=e=>{const t=e.staleTime;e.suspense&&(e.staleTime=typeof t=="function"?(...s)=>Math.max(t(...s),1e3):Math.max(t??1e3,1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},Ms=(e,t)=>e.isLoading&&e.isFetching&&!t,Ds=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,xe=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function xs(e,t,s){var c,O,g,S,p;const r=Ze(s),n=ws(),a=Ss(),u=r.defaultQueryOptions(e);(O=(c=r.getDefaultOptions().queries)==null?void 0:c._experimental_beforeQuery)==null||O.call(c,u),u._optimisticResults=n?"isRestoring":"optimistic",Qs(u),Ps(u,a),Fs(a);const l=!r.getQueryCache().get(u.queryHash),[d]=Q.useState(()=>new t(r,u)),b=d.getOptimisticResult(u),y=!n&&e.subscribed!==!1;if(Q.useSyncExternalStore(Q.useCallback(m=>{const F=y?d.subscribe(E.batchCalls(m)):be;return d.updateResult(),F},[d,y]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),Q.useEffect(()=>{d.setOptions(u,{listeners:!1})},[u,d]),Ds(u,b))throw xe(u,d,a);if(Es({result:b,errorResetBoundary:a,throwOnError:u.throwOnError,query:r.getQueryCache().get(u.queryHash),suspense:u.suspense}))throw b.error;if((S=(g=r.getDefaultOptions().queries)==null?void 0:g._experimental_afterQuery)==null||S.call(g,u,b),u.experimental_prefetchInRender&&!mt&&Ms(b,n)){const m=l?xe(u,d,a):(p=r.getQueryCache().get(u.queryHash))==null?void 0:p.promise;m==null||m.catch(be).finally(()=>{d.updateResult()})}return u.notifyOnChangeProps?b:d.trackResult(b)}function js(e,t){return xs(e,vs,t)}function ks(e,t){const s=Ze(t),[r]=Q.useState(()=>new Os(s,e));Q.useEffect(()=>{r.setOptions(e)},[r,e]);const n=Q.useSyncExternalStore(Q.useCallback(u=>r.subscribe(E.batchCalls(u)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),a=Q.useCallback((u,l)=>{r.mutate(u,l).catch(be)},[r]);if(n.error&&es(r.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:a,mutateAsync:n.mutate}}export{Us as Q,js as a,ks as b,qs as c,Ze as u};
