var Ys=Object.defineProperty;var Ks=(f,v,vt)=>v in f?Ys(f,v,{enumerable:!0,configurable:!0,writable:!0,value:vt}):f[v]=vt;var Je=(f,v,vt)=>(Ks(f,typeof v!="symbol"?v+"":v,vt),vt),Li=(f,v,vt)=>{if(!v.has(f))throw TypeError("Cannot "+vt)};var t=(f,v,vt)=>(Li(f,v,"read from private field"),vt?vt.call(f):v.get(f)),U=(f,v,vt)=>{if(v.has(f))throw TypeError("Cannot add the same private member more than once");v instanceof WeakSet?v.add(f):v.set(f,vt)},ft=(f,v,vt,d)=>(Li(f,v,"write to private field"),d?d.call(f,vt):v.set(f,vt),vt);var qn=(f,v,vt,d)=>({set _(P){ft(f,v,P,vt)},get _(){return t(f,v,d)}}),Q=(f,v,vt)=>(Li(f,v,"access private method"),vt);import{g as getAugmentedNamespace,d as getDefaultExportFromCjs,r as reactExports}from"../vendor-df163860.js";function _mergeNamespaces(f,v){for(var vt=0;vt<v.length;vt++){const d=v[vt];if(typeof d!="string"&&!Array.isArray(d)){for(const P in d)if(P!=="default"&&!(P in f)){const e=Object.getOwnPropertyDescriptor(d,P);e&&Object.defineProperty(f,P,e.get?e:{enumerable:!0,get:()=>d[P]})}}}return Object.freeze(Object.defineProperty(f,Symbol.toStringTag,{value:"Module"}))}function commonjsRequire(f){throw new Error('Could not dynamically require "'+f+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var pdf$1={exports:{}};const __viteBrowserExternal={},__viteBrowserExternal$1=Object.freeze(Object.defineProperty({__proto__:null,default:__viteBrowserExternal},Symbol.toStringTag,{value:"Module"})),require$$0=getAugmentedNamespace(__viteBrowserExternal$1);(function(module,exports){(function(v,vt){module.exports=v.pdfjsLib=vt()})(globalThis,()=>(()=>{var __webpack_modules__=[,(f,v)=>{var we;Object.defineProperty(v,"__esModule",{value:!0}),v.VerbosityLevel=v.Util=v.UnknownErrorException=v.UnexpectedResponseException=v.TextRenderingMode=v.RenderingIntentFlag=v.PromiseCapability=v.PermissionFlag=v.PasswordResponses=v.PasswordException=v.PageActionEventType=v.OPS=v.MissingPDFException=v.MAX_IMAGE_SIZE_TO_CACHE=v.LINE_FACTOR=v.LINE_DESCENT_FACTOR=v.InvalidPDFException=v.ImageKind=v.IDENTITY_MATRIX=v.FormatError=v.FeatureTest=v.FONT_IDENTITY_MATRIX=v.DocumentActionEventType=v.CMapCompressionType=v.BaseException=v.BASELINE_FACTOR=v.AnnotationType=v.AnnotationReplyType=v.AnnotationPrefix=v.AnnotationMode=v.AnnotationFlag=v.AnnotationFieldFlag=v.AnnotationEditorType=v.AnnotationEditorPrefix=v.AnnotationEditorParamsType=v.AnnotationBorderStyleType=v.AnnotationActionEventType=v.AbortException=void 0,v.assert=yt,v.bytesToString=z,v.createValidAbsoluteUrl=Bt,v.getModificationDate=Ht,v.getUuid=_e,v.getVerbosityLevel=nt,v.info=pt,v.isArrayBuffer=St,v.isArrayEqual=jt,v.isNodeJS=void 0,v.normalizeUnicode=ge,v.objectFromMap=Et,v.objectSize=mt,v.setVerbosityLevel=R,v.shadow=Gt,v.string32=ct,v.stringToBytes=dt,v.stringToPDFString=Ct,v.stringToUTF8String=ht,v.unreachable=Y,v.utf8StringToString=at,v.warn=bt;const vt=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser");v.isNodeJS=vt;const d=[1,0,0,1,0,0];v.IDENTITY_MATRIX=d;const P=[.001,0,0,.001,0,0];v.FONT_IDENTITY_MATRIX=P;const e=1e7;v.MAX_IMAGE_SIZE_TO_CACHE=e;const tt=1.35;v.LINE_FACTOR=tt;const Tt=.35;v.LINE_DESCENT_FACTOR=Tt;const G=Tt/tt;v.BASELINE_FACTOR=G;const D={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};v.RenderingIntentFlag=D;const g={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};v.AnnotationMode=g;const j="pdfjs_internal_editor_";v.AnnotationEditorPrefix=j;const H={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};v.AnnotationEditorType=H;const k={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};v.AnnotationEditorParamsType=k;const y={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};v.PermissionFlag=y;const w={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};v.TextRenderingMode=w;const m={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};v.ImageKind=m;const E={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};v.AnnotationType=E;const x={GROUP:"Group",REPLY:"R"};v.AnnotationReplyType=x;const M={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};v.AnnotationFlag=M;const C={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};v.AnnotationFieldFlag=C;const o={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};v.AnnotationBorderStyleType=o;const u={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};v.AnnotationActionEventType=u;const L={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};v.DocumentActionEventType=L;const _={O:"PageOpen",C:"PageClose"};v.PageActionEventType=_;const l={ERRORS:0,WARNINGS:1,INFOS:5};v.VerbosityLevel=l;const F={NONE:0,BINARY:1};v.CMapCompressionType=F;const A={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};v.OPS=A;const q={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};v.PasswordResponses=q;let Z=l.WARNINGS;function R(_t){Number.isInteger(_t)&&(Z=_t)}function nt(){return Z}function pt(_t){Z>=l.INFOS&&console.log(`Info: ${_t}`)}function bt(_t){Z>=l.WARNINGS&&console.log(`Warning: ${_t}`)}function Y(_t){throw new Error(_t)}function yt(_t,et){_t||Y(et)}function kt(_t){switch(_t==null?void 0:_t.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function Bt(_t,et=null,$=null){if(!_t)return null;try{if($&&typeof _t=="string"){if($.addDefaultProtocol&&_t.startsWith("www.")){const At=_t.match(/\./g);(At==null?void 0:At.length)>=2&&(_t=`http://${_t}`)}if($.tryConvertEncoding)try{_t=ht(_t)}catch{}}const Ft=et?new URL(_t,et):new URL(_t);if(kt(Ft))return Ft}catch{}return null}function Gt(_t,et,$,Ft=!1){return Object.defineProperty(_t,et,{value:$,enumerable:!Ft,configurable:!0,writable:!1}),$}const T=function(){function et($,Ft){this.constructor===et&&Y("Cannot initialize BaseException."),this.message=$,this.name=Ft}return et.prototype=new Error,et.constructor=et,et}();v.BaseException=T;class i extends T{constructor(et,$){super(et,"PasswordException"),this.code=$}}v.PasswordException=i;class a extends T{constructor(et,$){super(et,"UnknownErrorException"),this.details=$}}v.UnknownErrorException=a;class s extends T{constructor(et){super(et,"InvalidPDFException")}}v.InvalidPDFException=s;class c extends T{constructor(et){super(et,"MissingPDFException")}}v.MissingPDFException=c;class h extends T{constructor(et,$){super(et,"UnexpectedResponseException"),this.status=$}}v.UnexpectedResponseException=h;class S extends T{constructor(et){super(et,"FormatError")}}v.FormatError=S;class I extends T{constructor(et){super(et,"AbortException")}}v.AbortException=I;function z(_t){(typeof _t!="object"||(_t==null?void 0:_t.length)===void 0)&&Y("Invalid argument for bytesToString");const et=_t.length,$=8192;if(et<$)return String.fromCharCode.apply(null,_t);const Ft=[];for(let At=0;At<et;At+=$){const xe=Math.min(At+$,et),je=_t.subarray(At,xe);Ft.push(String.fromCharCode.apply(null,je))}return Ft.join("")}function dt(_t){typeof _t!="string"&&Y("Invalid argument for stringToBytes");const et=_t.length,$=new Uint8Array(et);for(let Ft=0;Ft<et;++Ft)$[Ft]=_t.charCodeAt(Ft)&255;return $}function ct(_t){return String.fromCharCode(_t>>24&255,_t>>16&255,_t>>8&255,_t&255)}function mt(_t){return Object.keys(_t).length}function Et(_t){const et=Object.create(null);for(const[$,Ft]of _t)et[$]=Ft;return et}function Dt(){const _t=new Uint8Array(4);return _t[0]=1,new Uint32Array(_t.buffer,0,1)[0]===1}function wt(){try{return new Function(""),!0}catch{return!1}}class $t{static get isLittleEndian(){return Gt(this,"isLittleEndian",Dt())}static get isEvalSupported(){return Gt(this,"isEvalSupported",wt())}static get isOffscreenCanvasSupported(){return Gt(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get platform(){return typeof navigator>"u"?Gt(this,"platform",{isWin:!1,isMac:!1}):Gt(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){var et,$;return Gt(this,"isCSSRoundSupported",($=(et=globalThis.CSS)==null?void 0:et.supports)==null?void 0:$.call(et,"width: round(1.5px, 1px)"))}}v.FeatureTest=$t;const ie=[...Array(256).keys()].map(_t=>_t.toString(16).padStart(2,"0"));class It{static makeHexColor(et,$,Ft){return`#${ie[et]}${ie[$]}${ie[Ft]}`}static scaleMinMax(et,$){let Ft;et[0]?(et[0]<0&&(Ft=$[0],$[0]=$[1],$[1]=Ft),$[0]*=et[0],$[1]*=et[0],et[3]<0&&(Ft=$[2],$[2]=$[3],$[3]=Ft),$[2]*=et[3],$[3]*=et[3]):(Ft=$[0],$[0]=$[2],$[2]=Ft,Ft=$[1],$[1]=$[3],$[3]=Ft,et[1]<0&&(Ft=$[2],$[2]=$[3],$[3]=Ft),$[2]*=et[1],$[3]*=et[1],et[2]<0&&(Ft=$[0],$[0]=$[1],$[1]=Ft),$[0]*=et[2],$[1]*=et[2]),$[0]+=et[4],$[1]+=et[4],$[2]+=et[5],$[3]+=et[5]}static transform(et,$){return[et[0]*$[0]+et[2]*$[1],et[1]*$[0]+et[3]*$[1],et[0]*$[2]+et[2]*$[3],et[1]*$[2]+et[3]*$[3],et[0]*$[4]+et[2]*$[5]+et[4],et[1]*$[4]+et[3]*$[5]+et[5]]}static applyTransform(et,$){const Ft=et[0]*$[0]+et[1]*$[2]+$[4],At=et[0]*$[1]+et[1]*$[3]+$[5];return[Ft,At]}static applyInverseTransform(et,$){const Ft=$[0]*$[3]-$[1]*$[2],At=(et[0]*$[3]-et[1]*$[2]+$[2]*$[5]-$[4]*$[3])/Ft,xe=(-et[0]*$[1]+et[1]*$[0]+$[4]*$[1]-$[5]*$[0])/Ft;return[At,xe]}static getAxialAlignedBoundingBox(et,$){const Ft=this.applyTransform(et,$),At=this.applyTransform(et.slice(2,4),$),xe=this.applyTransform([et[0],et[3]],$),je=this.applyTransform([et[2],et[1]],$);return[Math.min(Ft[0],At[0],xe[0],je[0]),Math.min(Ft[1],At[1],xe[1],je[1]),Math.max(Ft[0],At[0],xe[0],je[0]),Math.max(Ft[1],At[1],xe[1],je[1])]}static inverseTransform(et){const $=et[0]*et[3]-et[1]*et[2];return[et[3]/$,-et[1]/$,-et[2]/$,et[0]/$,(et[2]*et[5]-et[4]*et[3])/$,(et[4]*et[1]-et[5]*et[0])/$]}static singularValueDecompose2dScale(et){const $=[et[0],et[2],et[1],et[3]],Ft=et[0]*$[0]+et[1]*$[2],At=et[0]*$[1]+et[1]*$[3],xe=et[2]*$[0]+et[3]*$[2],je=et[2]*$[1]+et[3]*$[3],st=(Ft+je)/2,Ot=Math.sqrt((Ft+je)**2-4*(Ft*je-xe*At))/2,Qt=st+Ot||1,Wt=st-Ot||1;return[Math.sqrt(Qt),Math.sqrt(Wt)]}static normalizeRect(et){const $=et.slice(0);return et[0]>et[2]&&($[0]=et[2],$[2]=et[0]),et[1]>et[3]&&($[1]=et[3],$[3]=et[1]),$}static intersect(et,$){const Ft=Math.max(Math.min(et[0],et[2]),Math.min($[0],$[2])),At=Math.min(Math.max(et[0],et[2]),Math.max($[0],$[2]));if(Ft>At)return null;const xe=Math.max(Math.min(et[1],et[3]),Math.min($[1],$[3])),je=Math.min(Math.max(et[1],et[3]),Math.max($[1],$[3]));return xe>je?null:[Ft,xe,At,je]}static bezierBoundingBox(et,$,Ft,At,xe,je,st,Ot){const Qt=[],Wt=[[],[]];let te,ne,Ie,Lt,ke,Zt,N,n;for(let it=0;it<2;++it){if(it===0?(ne=6*et-12*Ft+6*xe,te=-3*et+9*Ft-9*xe+3*st,Ie=3*Ft-3*et):(ne=6*$-12*At+6*je,te=-3*$+9*At-9*je+3*Ot,Ie=3*At-3*$),Math.abs(te)<1e-12){if(Math.abs(ne)<1e-12)continue;Lt=-Ie/ne,0<Lt&&Lt<1&&Qt.push(Lt);continue}N=ne*ne-4*Ie*te,n=Math.sqrt(N),!(N<0)&&(ke=(-ne+n)/(2*te),0<ke&&ke<1&&Qt.push(ke),Zt=(-ne-n)/(2*te),0<Zt&&Zt<1&&Qt.push(Zt))}let b=Qt.length,B;const K=b;for(;b--;)Lt=Qt[b],B=1-Lt,Wt[0][b]=B*B*B*et+3*B*B*Lt*Ft+3*B*Lt*Lt*xe+Lt*Lt*Lt*st,Wt[1][b]=B*B*B*$+3*B*B*Lt*At+3*B*Lt*Lt*je+Lt*Lt*Lt*Ot;return Wt[0][K]=et,Wt[1][K]=$,Wt[0][K+1]=st,Wt[1][K+1]=Ot,Wt[0].length=Wt[1].length=K+2,[Math.min(...Wt[0]),Math.min(...Wt[1]),Math.max(...Wt[0]),Math.max(...Wt[1])]}}v.Util=It;const Jt=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function Ct(_t){if(_t[0]>="ï"){let $;if(_t[0]==="þ"&&_t[1]==="ÿ"?$="utf-16be":_t[0]==="ÿ"&&_t[1]==="þ"?$="utf-16le":_t[0]==="ï"&&_t[1]==="»"&&_t[2]==="¿"&&($="utf-8"),$)try{const Ft=new TextDecoder($,{fatal:!0}),At=dt(_t);return Ft.decode(At)}catch(Ft){bt(`stringToPDFString: "${Ft}".`)}}const et=[];for(let $=0,Ft=_t.length;$<Ft;$++){const At=Jt[_t.charCodeAt($)];et.push(At?String.fromCharCode(At):_t.charAt($))}return et.join("")}function ht(_t){return decodeURIComponent(escape(_t))}function at(_t){return unescape(encodeURIComponent(_t))}function St(_t){return typeof _t=="object"&&(_t==null?void 0:_t.byteLength)!==void 0}function jt(_t,et){if(_t.length!==et.length)return!1;for(let $=0,Ft=_t.length;$<Ft;$++)if(_t[$]!==et[$])return!1;return!0}function Ht(_t=new Date){return[_t.getUTCFullYear().toString(),(_t.getUTCMonth()+1).toString().padStart(2,"0"),_t.getUTCDate().toString().padStart(2,"0"),_t.getUTCHours().toString().padStart(2,"0"),_t.getUTCMinutes().toString().padStart(2,"0"),_t.getUTCSeconds().toString().padStart(2,"0")].join("")}class le{constructor(){U(this,we,!1);this.promise=new Promise((et,$)=>{this.resolve=Ft=>{ft(this,we,!0),et(Ft)},this.reject=Ft=>{ft(this,we,!0),$(Ft)}})}get settled(){return t(this,we)}}we=new WeakMap,v.PromiseCapability=le;let fe=null,he=null;function ge(_t){return fe||(fe=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,he=new Map([["ﬅ","ſt"]])),_t.replaceAll(fe,(et,$,Ft)=>$?$.normalize("NFKC"):he.get(Ft))}function _e(){if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.randomUUID)=="function")return crypto.randomUUID();const _t=new Uint8Array(32);if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.getRandomValues)=="function")crypto.getRandomValues(_t);else for(let et=0;et<32;et++)_t[et]=Math.floor(Math.random()*255);return z(_t)}const re="pdfjs_internal_id_";v.AnnotationPrefix=re},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{var f,vt,d,P,Hn,tt,oi,G,D,g,j,H,k,y,w,m,li,x,M,Di,o,u;Object.defineProperty(exports,"__esModule",{value:!0}),exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultFilterFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0,Object.defineProperty(exports,"SVGGraphics",{enumerable:!0,get:function(){return _displaySvg.SVGGraphics}}),exports.build=void 0,exports.getDocument=getDocument,exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(3),_display_utils=__w_pdfjs_require__(6),_font_loader=__w_pdfjs_require__(9),_displayNode_utils=__w_pdfjs_require__(10),_canvas=__w_pdfjs_require__(11),_worker_options=__w_pdfjs_require__(14),_message_handler=__w_pdfjs_require__(15),_metadata=__w_pdfjs_require__(16),_optional_content_config=__w_pdfjs_require__(17),_transport_stream=__w_pdfjs_require__(18),_displayFetch_stream=__w_pdfjs_require__(19),_displayNetwork=__w_pdfjs_require__(22),_displayNode_stream=__w_pdfjs_require__(23),_displaySvg=__w_pdfjs_require__(24),_xfa_text=__w_pdfjs_require__(25);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DELAYED_CLEANUP_TIMEOUT=5e3,DefaultCanvasFactory=_util.isNodeJS?_displayNode_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;const DefaultCMapReaderFactory=_util.isNodeJS?_displayNode_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;const DefaultFilterFactory=_util.isNodeJS?_displayNode_utils.NodeFilterFactory:_display_utils.DOMFilterFactory;exports.DefaultFilterFactory=DefaultFilterFactory;const DefaultStandardFontDataFactory=_util.isNodeJS?_displayNode_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;function getDocument(_){if(typeof _=="string"||_ instanceof URL?_={url:_}:(0,_util.isArrayBuffer)(_)&&(_={data:_}),typeof _!="object")throw new Error("Invalid parameter in getDocument, need parameter object.");if(!_.url&&!_.data&&!_.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const l=new PDFDocumentLoadingTask,{docId:F}=l,A=_.url?getUrlProp(_.url):null,q=_.data?getDataProp(_.data):null,Z=_.httpHeaders||null,R=_.withCredentials===!0,nt=_.password??null,pt=_.range instanceof PDFDataRangeTransport?_.range:null,bt=Number.isInteger(_.rangeChunkSize)&&_.rangeChunkSize>0?_.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE;let Y=_.worker instanceof PDFWorker?_.worker:null;const yt=_.verbosity,kt=typeof _.docBaseUrl=="string"&&!(0,_display_utils.isDataScheme)(_.docBaseUrl)?_.docBaseUrl:null,Bt=typeof _.cMapUrl=="string"?_.cMapUrl:null,Gt=_.cMapPacked!==!1,T=_.CMapReaderFactory||DefaultCMapReaderFactory,i=typeof _.standardFontDataUrl=="string"?_.standardFontDataUrl:null,a=_.StandardFontDataFactory||DefaultStandardFontDataFactory,s=_.stopAtErrors!==!0,c=Number.isInteger(_.maxImageSize)&&_.maxImageSize>-1?_.maxImageSize:-1,h=_.isEvalSupported!==!1,S=typeof _.isOffscreenCanvasSupported=="boolean"?_.isOffscreenCanvasSupported:!_util.isNodeJS,I=Number.isInteger(_.canvasMaxAreaInBytes)?_.canvasMaxAreaInBytes:-1,z=typeof _.disableFontFace=="boolean"?_.disableFontFace:_util.isNodeJS,dt=_.fontExtraProperties===!0,ct=_.enableXfa===!0,mt=_.ownerDocument||globalThis.document,Et=_.disableRange===!0,Dt=_.disableStream===!0,wt=_.disableAutoFetch===!0,$t=_.pdfBug===!0,ie=pt?pt.length:_.length??NaN,It=typeof _.useSystemFonts=="boolean"?_.useSystemFonts:!_util.isNodeJS&&!z,Jt=typeof _.useWorkerFetch=="boolean"?_.useWorkerFetch:T===_display_utils.DOMCMapReaderFactory&&a===_display_utils.DOMStandardFontDataFactory&&Bt&&i&&(0,_display_utils.isValidFetchUrl)(Bt,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(i,document.baseURI),Ct=_.canvasFactory||new DefaultCanvasFactory({ownerDocument:mt}),ht=_.filterFactory||new DefaultFilterFactory({docId:F,ownerDocument:mt}),at=null;(0,_util.setVerbosityLevel)(yt);const St={canvasFactory:Ct,filterFactory:ht};if(Jt||(St.cMapReaderFactory=new T({baseUrl:Bt,isCompressed:Gt}),St.standardFontDataFactory=new a({baseUrl:i})),!Y){const le={verbosity:yt,port:_worker_options.GlobalWorkerOptions.workerPort};Y=le.port?PDFWorker.fromPort(le):new PDFWorker(le),l._worker=Y}const jt={docId:F,apiVersion:"3.11.174",data:q,password:nt,disableAutoFetch:wt,rangeChunkSize:bt,length:ie,docBaseUrl:kt,enableXfa:ct,evaluatorOptions:{maxImageSize:c,disableFontFace:z,ignoreErrors:s,isEvalSupported:h,isOffscreenCanvasSupported:S,canvasMaxAreaInBytes:I,fontExtraProperties:dt,useSystemFonts:It,cMapUrl:Jt?Bt:null,standardFontDataUrl:Jt?i:null}},Ht={ignoreErrors:s,isEvalSupported:h,disableFontFace:z,fontExtraProperties:dt,enableXfa:ct,ownerDocument:mt,disableAutoFetch:wt,pdfBug:$t,styleElement:at};return Y.promise.then(function(){if(l.destroyed)throw new Error("Loading aborted");const le=_fetchDocument(Y,jt),fe=new Promise(function(he){let ge;pt?ge=new _transport_stream.PDFDataTransportStream({length:ie,initialData:pt.initialData,progressiveDone:pt.progressiveDone,contentDispositionFilename:pt.contentDispositionFilename,disableRange:Et,disableStream:Dt},pt):q||(ge=(re=>_util.isNodeJS?new _displayNode_stream.PDFNodeStream(re):(0,_display_utils.isValidFetchUrl)(re.url)?new _displayFetch_stream.PDFFetchStream(re):new _displayNetwork.PDFNetworkStream(re))({url:A,length:ie,httpHeaders:Z,withCredentials:R,rangeChunkSize:bt,disableRange:Et,disableStream:Dt})),he(ge)});return Promise.all([le,fe]).then(function([he,ge]){if(l.destroyed)throw new Error("Loading aborted");const _e=new _message_handler.MessageHandler(F,he,Y.port),re=new WorkerTransport(_e,l,ge,Ht,St);l._transport=re,_e.send("Ready",null)})}).catch(l._capability.reject),l}async function _fetchDocument(_,l){if(_.destroyed)throw new Error("Worker was destroyed");const F=await _.messageHandler.sendWithPromise("GetDocRequest",l,l.data?[l.data.buffer]:null);if(_.destroyed)throw new Error("Worker was destroyed");return F}function getUrlProp(_){if(_ instanceof URL)return _.href;try{return new URL(_,window.location).href}catch{if(_util.isNodeJS&&typeof _=="string")return _}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(_){if(_util.isNodeJS&&typeof Buffer<"u"&&_ instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(_ instanceof Uint8Array&&_.byteLength===_.buffer.byteLength)return _;if(typeof _=="string")return(0,_util.stringToBytes)(_);if(typeof _=="object"&&!isNaN(_==null?void 0:_.length)||(0,_util.isArrayBuffer)(_))return new Uint8Array(_);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}const v=class v{constructor(){this._capability=new _util.PromiseCapability,this._transport=null,this._worker=null,this.docId=`d${qn(v,f)._++}`,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){var l,F,A;this.destroyed=!0;try{(l=this._worker)!=null&&l.port&&(this._worker._pendingDestroy=!0),await((F=this._transport)==null?void 0:F.destroy())}catch(q){throw(A=this._worker)!=null&&A.port&&delete this._worker._pendingDestroy,q}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}};f=new WeakMap,U(v,f,0);let PDFDocumentLoadingTask=v;exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(l,F,A=!1,q=null){this.length=l,this.initialData=F,this.progressiveDone=A,this.contentDispositionFilename=q,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=new _util.PromiseCapability}addRangeListener(l){this._rangeListeners.push(l)}addProgressListener(l){this._progressListeners.push(l)}addProgressiveReadListener(l){this._progressiveReadListeners.push(l)}addProgressiveDoneListener(l){this._progressiveDoneListeners.push(l)}onDataRange(l,F){for(const A of this._rangeListeners)A(l,F)}onDataProgress(l,F){this._readyCapability.promise.then(()=>{for(const A of this._progressListeners)A(l,F)})}onDataProgressiveRead(l){this._readyCapability.promise.then(()=>{for(const F of this._progressiveReadListeners)F(l)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const l of this._progressiveDoneListeners)l()})}transportReady(){this._readyCapability.resolve()}requestDataRange(l,F){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(l,F){this._pdfInfo=l,this._transport=F,Object.defineProperty(this,"getJavaScript",{value:()=>((0,_display_utils.deprecated)("`PDFDocumentProxy.getJavaScript`, please use `PDFDocumentProxy.getJSActions` instead."),this.getJSActions().then(A=>{if(!A)return A;const q=[];for(const Z in A)q.push(...A[Z]);return q}))})}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(l){return this._transport.getPage(l)}getPageIndex(l){return this._transport.getPageIndex(l)}getDestinations(){return this._transport.getDestinations()}getDestination(l){return this._transport.getDestination(l)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(l=!1){return this._transport.startCleanup(l||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(l,F,A,q=!1){U(this,P);U(this,tt);U(this,vt,null);U(this,d,!1);this._pageIndex=l,this._pageInfo=F,this._transport=A,this._stats=q?new _display_utils.StatTimer:null,this._pdfBug=q,this.commonObjs=A.commonObjs,this.objs=new PDFObjects,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:l,rotation:F=this.rotate,offsetX:A=0,offsetY:q=0,dontFlip:Z=!1}={}){return new _display_utils.PageViewport({viewBox:this.view,scale:l,rotation:F,offsetX:A,offsetY:q,dontFlip:Z})}getAnnotations({intent:l="display"}={}){const F=this._transport.getRenderingIntent(l);return this._transport.getAnnotations(this._pageIndex,F.renderingIntent)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var l;return((l=this._transport._htmlForXfa)==null?void 0:l.children[this._pageIndex])||null}render({canvasContext:l,viewport:F,intent:A="display",annotationMode:q=_util.AnnotationMode.ENABLE,transform:Z=null,background:R=null,optionalContentConfigPromise:nt=null,annotationCanvasMap:pt=null,pageColors:bt=null,printAnnotationStorage:Y=null}){var a,s;(a=this._stats)==null||a.time("Overall");const yt=this._transport.getRenderingIntent(A,q,Y);ft(this,d,!1),Q(this,tt,oi).call(this),nt||(nt=this._transport.getOptionalContentConfig());let kt=this._intentStates.get(yt.cacheKey);kt||(kt=Object.create(null),this._intentStates.set(yt.cacheKey,kt)),kt.streamReaderCancelTimeout&&(clearTimeout(kt.streamReaderCancelTimeout),kt.streamReaderCancelTimeout=null);const Bt=!!(yt.renderingIntent&_util.RenderingIntentFlag.PRINT);kt.displayReadyCapability||(kt.displayReadyCapability=new _util.PromiseCapability,kt.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(s=this._stats)==null||s.time("Page Request"),this._pumpOperatorList(yt));const Gt=c=>{var h,S;kt.renderTasks.delete(T),(this._maybeCleanupAfterRender||Bt)&&ft(this,d,!0),Q(this,P,Hn).call(this,!Bt),c?(T.capability.reject(c),this._abortOperatorList({intentState:kt,reason:c instanceof Error?c:new Error(c)})):T.capability.resolve(),(h=this._stats)==null||h.timeEnd("Rendering"),(S=this._stats)==null||S.timeEnd("Overall")},T=new InternalRenderTask({callback:Gt,params:{canvasContext:l,viewport:F,transform:Z,background:R},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:pt,operatorList:kt.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!Bt,pdfBug:this._pdfBug,pageColors:bt});(kt.renderTasks||(kt.renderTasks=new Set)).add(T);const i=T.task;return Promise.all([kt.displayReadyCapability.promise,nt]).then(([c,h])=>{var S;if(this.destroyed){Gt();return}(S=this._stats)==null||S.time("Rendering"),T.initializeGraphics({transparency:c,optionalContentConfig:h}),T.operatorListChanged()}).catch(Gt),i}getOperatorList({intent:l="display",annotationMode:F=_util.AnnotationMode.ENABLE,printAnnotationStorage:A=null}={}){var pt;function q(){R.operatorList.lastChunk&&(R.opListReadCapability.resolve(R.operatorList),R.renderTasks.delete(nt))}const Z=this._transport.getRenderingIntent(l,F,A,!0);let R=this._intentStates.get(Z.cacheKey);R||(R=Object.create(null),this._intentStates.set(Z.cacheKey,R));let nt;return R.opListReadCapability||(nt=Object.create(null),nt.operatorListChanged=q,R.opListReadCapability=new _util.PromiseCapability,(R.renderTasks||(R.renderTasks=new Set)).add(nt),R.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(pt=this._stats)==null||pt.time("Page Request"),this._pumpOperatorList(Z)),R.opListReadCapability.promise}streamTextContent({includeMarkedContent:l=!1,disableNormalization:F=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:l===!0,disableNormalization:F===!0},{highWaterMark:100,size(q){return q.items.length}})}getTextContent(l={}){if(this._transport._htmlForXfa)return this.getXfa().then(A=>_xfa_text.XfaText.textContent(A));const F=this.streamTextContent(l);return new Promise(function(A,q){function Z(){R.read().then(function({value:pt,done:bt}){if(bt){A(nt);return}Object.assign(nt.styles,pt.styles),nt.items.push(...pt.items),Z()},q)}const R=F.getReader(),nt={items:[],styles:Object.create(null)};Z()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const l=[];for(const F of this._intentStates.values())if(this._abortOperatorList({intentState:F,reason:new Error("Page was destroyed."),force:!0}),!F.opListReadCapability)for(const A of F.renderTasks)l.push(A.completed),A.cancel();return this.objs.clear(),ft(this,d,!1),Q(this,tt,oi).call(this),Promise.all(l)}cleanup(l=!1){ft(this,d,!0);const F=Q(this,P,Hn).call(this,!1);return l&&F&&this._stats&&(this._stats=new _display_utils.StatTimer),F}_startRenderPage(l,F){var q,Z;const A=this._intentStates.get(F);A&&((q=this._stats)==null||q.timeEnd("Page Request"),(Z=A.displayReadyCapability)==null||Z.resolve(l))}_renderPageChunk(l,F){for(let A=0,q=l.length;A<q;A++)F.operatorList.fnArray.push(l.fnArray[A]),F.operatorList.argsArray.push(l.argsArray[A]);F.operatorList.lastChunk=l.lastChunk,F.operatorList.separateAnnots=l.separateAnnots;for(const A of F.renderTasks)A.operatorListChanged();l.lastChunk&&Q(this,P,Hn).call(this,!0)}_pumpOperatorList({renderingIntent:l,cacheKey:F,annotationStorageSerializable:A}){const{map:q,transfers:Z}=A,nt=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:l,cacheKey:F,annotationStorage:q},Z).getReader(),pt=this._intentStates.get(F);pt.streamReader=nt;const bt=()=>{nt.read().then(({value:Y,done:yt})=>{if(yt){pt.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(Y,pt),bt())},Y=>{if(pt.streamReader=null,!this._transport.destroyed){if(pt.operatorList){pt.operatorList.lastChunk=!0;for(const yt of pt.renderTasks)yt.operatorListChanged();Q(this,P,Hn).call(this,!0)}if(pt.displayReadyCapability)pt.displayReadyCapability.reject(Y);else if(pt.opListReadCapability)pt.opListReadCapability.reject(Y);else throw Y}})};bt()}_abortOperatorList({intentState:l,reason:F,force:A=!1}){if(l.streamReader){if(l.streamReaderCancelTimeout&&(clearTimeout(l.streamReaderCancelTimeout),l.streamReaderCancelTimeout=null),!A){if(l.renderTasks.size>0)return;if(F instanceof _display_utils.RenderingCancelledException){let q=RENDERING_CANCELLED_TIMEOUT;F.extraDelay>0&&F.extraDelay<1e3&&(q+=F.extraDelay),l.streamReaderCancelTimeout=setTimeout(()=>{l.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:l,reason:F,force:!0})},q);return}}if(l.streamReader.cancel(new _util.AbortException(F.message)).catch(()=>{}),l.streamReader=null,!this._transport.destroyed){for(const[q,Z]of this._intentStates)if(Z===l){this._intentStates.delete(q);break}this.cleanup()}}}get stats(){return this._stats}}vt=new WeakMap,d=new WeakMap,P=new WeakSet,Hn=function(l=!1){if(Q(this,tt,oi).call(this),!t(this,d)||this.destroyed)return!1;if(l)return ft(this,vt,setTimeout(()=>{ft(this,vt,null),Q(this,P,Hn).call(this,!1)},DELAYED_CLEANUP_TIMEOUT)),!1;for(const{renderTasks:F,operatorList:A}of this._intentStates.values())if(F.size>0||!A.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),ft(this,d,!1),!0},tt=new WeakSet,oi=function(){t(this,vt)&&(clearTimeout(t(this,vt)),ft(this,vt,null))},exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{constructor(){U(this,G,new Set);U(this,D,Promise.resolve())}postMessage(l,F){const A={data:structuredClone(l,F?{transfer:F}:null)};t(this,D).then(()=>{for(const q of t(this,G))q.call(this,A)})}addEventListener(l,F){t(this,G).add(F)}removeEventListener(l,F){t(this,G).delete(F)}terminate(){t(this,G).clear()}}G=new WeakMap,D=new WeakMap,exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};exports.PDFWorkerUtil=PDFWorkerUtil;{if(_util.isNodeJS&&typeof commonjsRequire=="function")PDFWorkerUtil.isWorkerDisabled=!0,PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js";else if(typeof document=="object"){const _=(g=document==null?void 0:document.currentScript)==null?void 0:g.src;_&&(PDFWorkerUtil.fallbackWorkerSrc=_.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(_,l){let F;try{if(F=new URL(_),!F.origin||F.origin==="null")return!1}catch{return!1}const A=new URL(l,F);return F.origin===A.origin},PDFWorkerUtil.createCDNWrapper=function(_){const l=`importScripts("${_}");`;return URL.createObjectURL(new Blob([l]))}}const _PDFWorker=class _PDFWorker{constructor({name:_=null,port:l=null,verbosity:F=(0,_util.getVerbosityLevel)()}={}){var A;if(this.name=_,this.destroyed=!1,this.verbosity=F,this._readyCapability=new _util.PromiseCapability,this._port=null,this._webWorker=null,this._messageHandler=null,l){if((A=t(_PDFWorker,j))!=null&&A.has(l))throw new Error("Cannot use more than one PDFWorker per port.");(t(_PDFWorker,j)||ft(_PDFWorker,j,new WeakMap)).set(l,this),this._initializeFromPort(l);return}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(_){this._port=_,this._messageHandler=new _message_handler.MessageHandler("main","worker",_),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!_PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:_}=_PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,_)||(_=PDFWorkerUtil.createCDNWrapper(new URL(_,window.location).href));const l=new Worker(_),F=new _message_handler.MessageHandler("main","worker",l),A=()=>{l.removeEventListener("error",q),F.destroy(),l.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},q=()=>{this._webWorker||A()};l.addEventListener("error",q),F.on("test",R=>{if(l.removeEventListener("error",q),this.destroyed){A();return}R?(this._messageHandler=F,this._port=l,this._webWorker=l,this._readyCapability.resolve(),F.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),F.destroy(),l.terminate())}),F.on("ready",R=>{if(l.removeEventListener("error",q),this.destroyed){A();return}try{Z()}catch{this._setupFakeWorker()}});const Z=()=>{const R=new Uint8Array;F.send("test",R,[R.buffer])};Z();return}catch{(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){PDFWorkerUtil.isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),PDFWorkerUtil.isWorkerDisabled=!0),_PDFWorker._setupFakeWorkerGlobal.then(_=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const l=new LoopbackPort;this._port=l;const F=`fake${PDFWorkerUtil.fakeWorkerId++}`,A=new _message_handler.MessageHandler(F+"_worker",F,l);_.setup(A,l);const q=new _message_handler.MessageHandler(F,F+"_worker",l);this._messageHandler=q,this._readyCapability.resolve(),q.send("configure",{verbosity:this.verbosity})}).catch(_=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${_.message}".`))})}destroy(){var _;this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),(_=t(_PDFWorker,j))==null||_.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(_){var F;if(!(_!=null&&_.port))throw new Error("PDFWorker.fromPort - invalid method signature.");const l=(F=t(this,j))==null?void 0:F.get(_.port);if(l){if(l._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return l}return new _PDFWorker(_)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(PDFWorkerUtil.fallbackWorkerSrc!==null)return _util.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),PDFWorkerUtil.fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){var _;try{return((_=globalThis.pdfjsWorker)==null?void 0:_.WorkerMessageHandler)||null}catch{return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_util.isNodeJS&&typeof commonjsRequire=="function"){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}return await(0,_display_utils.loadScript)(this.workerSrc),window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}};j=new WeakMap,U(_PDFWorker,j,void 0);let PDFWorker=_PDFWorker;exports.PDFWorker=PDFWorker;class WorkerTransport{constructor(l,F,A,q,Z){U(this,m);U(this,H,new Map);U(this,k,new Map);U(this,y,new Map);U(this,w,null);this.messageHandler=l,this.loadingTask=F,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({ownerDocument:q.ownerDocument,styleElement:q.styleElement}),this._params=q,this.canvasFactory=Z.canvasFactory,this.filterFactory=Z.filterFactory,this.cMapReaderFactory=Z.cMapReaderFactory,this.standardFontDataFactory=Z.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=A,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=new _util.PromiseCapability,this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}getRenderingIntent(l,F=_util.AnnotationMode.ENABLE,A=null,q=!1){let Z=_util.RenderingIntentFlag.DISPLAY,R=_annotation_storage.SerializableEmpty;switch(l){case"any":Z=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":Z=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${l}`)}switch(F){case _util.AnnotationMode.DISABLE:Z+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:Z+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:Z+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE,R=(Z&_util.RenderingIntentFlag.PRINT&&A instanceof _annotation_storage.PrintAnnotationStorage?A:this.annotationStorage).serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${F}`)}return q&&(Z+=_util.RenderingIntentFlag.OPLIST),{renderingIntent:Z,cacheKey:`${Z}_${R.hash}`,annotationStorageSerializable:R}}destroy(){var A;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=new _util.PromiseCapability,(A=t(this,w))==null||A.reject(new Error("Worker was destroyed during onPassword callback"));const l=[];for(const q of t(this,k).values())l.push(q._destroy());t(this,k).clear(),t(this,y).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const F=this.messageHandler.sendWithPromise("Terminate",null);return l.push(F),Promise.all(l).then(()=>{var q;this.commonObjs.clear(),this.fontLoader.clear(),t(this,H).clear(),this.filterFactory.destroy(),(q=this._networkStream)==null||q.cancelAllRequests(new _util.AbortException("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:l,loadingTask:F}=this;l.on("GetReader",(A,q)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=Z=>{this._lastProgress={loaded:Z.loaded,total:Z.total}},q.onPull=()=>{this._fullReader.read().then(function({value:Z,done:R}){if(R){q.close();return}(0,_util.assert)(Z instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),q.enqueue(new Uint8Array(Z),1,[Z])}).catch(Z=>{q.error(Z)})},q.onCancel=Z=>{this._fullReader.cancel(Z),q.ready.catch(R=>{if(!this.destroyed)throw R})}}),l.on("ReaderHeadersReady",A=>{const q=new _util.PromiseCapability,Z=this._fullReader;return Z.headersReady.then(()=>{var R;(!Z.isStreamingSupported||!Z.isRangeSupported)&&(this._lastProgress&&((R=F.onProgress)==null||R.call(F,this._lastProgress)),Z.onProgress=nt=>{var pt;(pt=F.onProgress)==null||pt.call(F,{loaded:nt.loaded,total:nt.total})}),q.resolve({isStreamingSupported:Z.isStreamingSupported,isRangeSupported:Z.isRangeSupported,contentLength:Z.contentLength})},q.reject),q.promise}),l.on("GetRangeReader",(A,q)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const Z=this._networkStream.getRangeReader(A.begin,A.end);if(!Z){q.close();return}q.onPull=()=>{Z.read().then(function({value:R,done:nt}){if(nt){q.close();return}(0,_util.assert)(R instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),q.enqueue(new Uint8Array(R),1,[R])}).catch(R=>{q.error(R)})},q.onCancel=R=>{Z.cancel(R),q.ready.catch(nt=>{if(!this.destroyed)throw nt})}}),l.on("GetDoc",({pdfInfo:A})=>{this._numPages=A.numPages,this._htmlForXfa=A.htmlForXfa,delete A.htmlForXfa,F._capability.resolve(new PDFDocumentProxy(A,this))}),l.on("DocException",function(A){let q;switch(A.name){case"PasswordException":q=new _util.PasswordException(A.message,A.code);break;case"InvalidPDFException":q=new _util.InvalidPDFException(A.message);break;case"MissingPDFException":q=new _util.MissingPDFException(A.message);break;case"UnexpectedResponseException":q=new _util.UnexpectedResponseException(A.message,A.status);break;case"UnknownErrorException":q=new _util.UnknownErrorException(A.message,A.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}F._capability.reject(q)}),l.on("PasswordRequest",A=>{if(ft(this,w,new _util.PromiseCapability),F.onPassword){const q=Z=>{Z instanceof Error?t(this,w).reject(Z):t(this,w).resolve({password:Z})};try{F.onPassword(q,A.code)}catch(Z){t(this,w).reject(Z)}}else t(this,w).reject(new _util.PasswordException(A.message,A.code));return t(this,w).promise}),l.on("DataLoaded",A=>{var q;(q=F.onProgress)==null||q.call(F,{loaded:A.length,total:A.length}),this.downloadInfoCapability.resolve(A)}),l.on("StartRenderPage",A=>{if(this.destroyed)return;t(this,k).get(A.pageIndex)._startRenderPage(A.transparency,A.cacheKey)}),l.on("commonobj",([A,q,Z])=>{var R;if(!this.destroyed&&!this.commonObjs.has(A))switch(q){case"Font":const nt=this._params;if("error"in Z){const Y=Z.error;(0,_util.warn)(`Error during font loading: ${Y}`),this.commonObjs.resolve(A,Y);break}const pt=nt.pdfBug&&((R=globalThis.FontInspector)!=null&&R.enabled)?(Y,yt)=>globalThis.FontInspector.fontAdded(Y,yt):null,bt=new _font_loader.FontFaceObject(Z,{isEvalSupported:nt.isEvalSupported,disableFontFace:nt.disableFontFace,ignoreErrors:nt.ignoreErrors,inspectFont:pt});this.fontLoader.bind(bt).catch(Y=>l.sendWithPromise("FontFallback",{id:A})).finally(()=>{!nt.fontExtraProperties&&bt.data&&(bt.data=null),this.commonObjs.resolve(A,bt)});break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(A,Z);break;default:throw new Error(`Got unknown common object type ${q}`)}}),l.on("obj",([A,q,Z,R])=>{var pt;if(this.destroyed)return;const nt=t(this,k).get(q);if(!nt.objs.has(A))switch(Z){case"Image":if(nt.objs.resolve(A,R),R){let bt;if(R.bitmap){const{width:Y,height:yt}=R;bt=Y*yt*4}else bt=((pt=R.data)==null?void 0:pt.length)||0;bt>_util.MAX_IMAGE_SIZE_TO_CACHE&&(nt._maybeCleanupAfterRender=!0)}break;case"Pattern":nt.objs.resolve(A,R);break;default:throw new Error(`Got unknown object type ${Z}`)}}),l.on("DocProgress",A=>{var q;this.destroyed||(q=F.onProgress)==null||q.call(F,{loaded:A.loaded,total:A.total})}),l.on("FetchBuiltInCMap",A=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(A):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))),l.on("FetchStandardFontData",A=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(A):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.")))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var A;this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:l,transfers:F}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:l,filename:((A=this._fullReader)==null?void 0:A.filename)??null},F).finally(()=>{this.annotationStorage.resetModified()})}getPage(l){if(!Number.isInteger(l)||l<=0||l>this._numPages)return Promise.reject(new Error("Invalid page request."));const F=l-1,A=t(this,y).get(F);if(A)return A;const q=this.messageHandler.sendWithPromise("GetPage",{pageIndex:F}).then(Z=>{if(this.destroyed)throw new Error("Transport destroyed");const R=new PDFPageProxy(F,Z,this,this._params.pdfBug);return t(this,k).set(F,R),R});return t(this,y).set(F,q),q}getPageIndex(l){return typeof l!="object"||l===null||!Number.isInteger(l.num)||l.num<0||!Number.isInteger(l.gen)||l.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:l.num,gen:l.gen})}getAnnotations(l,F){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:l,intent:F})}getFieldObjects(){return Q(this,m,li).call(this,"GetFieldObjects")}hasJSActions(){return Q(this,m,li).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(l){return typeof l!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:l})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return Q(this,m,li).call(this,"GetDocJSActions")}getPageJSActions(l){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:l})}getStructTree(l){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:l})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then(l=>new _optional_content_config.OptionalContentConfig(l))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const l="GetMetadata",F=t(this,H).get(l);if(F)return F;const A=this.messageHandler.sendWithPromise(l,null).then(q=>{var Z,R;return{info:q[0],metadata:q[1]?new _metadata.Metadata(q[1]):null,contentDispositionFilename:((Z=this._fullReader)==null?void 0:Z.filename)??null,contentLength:((R=this._fullReader)==null?void 0:R.contentLength)??null}});return t(this,H).set(l,A),A}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(l=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const F of t(this,k).values())if(!F.cleanup())throw new Error(`startCleanup: Page ${F.pageNumber} is currently rendering.`);this.commonObjs.clear(),l||this.fontLoader.clear(),t(this,H).clear(),this.filterFactory.destroy(!0)}}get loadingParams(){const{disableAutoFetch:l,enableXfa:F}=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:l,enableXfa:F})}}H=new WeakMap,k=new WeakMap,y=new WeakMap,w=new WeakMap,m=new WeakSet,li=function(l,F=null){const A=t(this,H).get(l);if(A)return A;const q=this.messageHandler.sendWithPromise(l,F);return t(this,H).set(l,q),q};class PDFObjects{constructor(){U(this,M);U(this,x,Object.create(null))}get(l,F=null){if(F){const q=Q(this,M,Di).call(this,l);return q.capability.promise.then(()=>F(q.data)),null}const A=t(this,x)[l];if(!(A!=null&&A.capability.settled))throw new Error(`Requesting object that isn't resolved yet ${l}.`);return A.data}has(l){const F=t(this,x)[l];return(F==null?void 0:F.capability.settled)||!1}resolve(l,F=null){const A=Q(this,M,Di).call(this,l);A.data=F,A.capability.resolve()}clear(){var l;for(const F in t(this,x)){const{data:A}=t(this,x)[F];(l=A==null?void 0:A.bitmap)==null||l.close()}ft(this,x,Object.create(null))}}x=new WeakMap,M=new WeakSet,Di=function(l){var F;return(F=t(this,x))[l]||(F[l]={capability:new _util.PromiseCapability,data:null})};class RenderTask{constructor(l){U(this,o,null);ft(this,o,l),this.onContinue=null}get promise(){return t(this,o).capability.promise}cancel(l=0){t(this,o).cancel(null,l)}get separateAnnots(){const{separateAnnots:l}=t(this,o).operatorList;if(!l)return!1;const{annotationCanvasMap:F}=t(this,o);return l.form||l.canvas&&(F==null?void 0:F.size)>0}}o=new WeakMap,exports.RenderTask=RenderTask;const L=class L{constructor({callback:l,params:F,objs:A,commonObjs:q,annotationCanvasMap:Z,operatorList:R,pageIndex:nt,canvasFactory:pt,filterFactory:bt,useRequestAnimationFrame:Y=!1,pdfBug:yt=!1,pageColors:kt=null}){this.callback=l,this.params=F,this.objs=A,this.commonObjs=q,this.annotationCanvasMap=Z,this.operatorListIdx=null,this.operatorList=R,this._pageIndex=nt,this.canvasFactory=pt,this.filterFactory=bt,this._pdfBug=yt,this.pageColors=kt,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=Y===!0&&typeof window<"u",this.cancelled=!1,this.capability=new _util.PromiseCapability,this.task=new RenderTask(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=F.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:l=!1,optionalContentConfig:F}){var nt,pt;if(this.cancelled)return;if(this._canvas){if(t(L,u).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");t(L,u).add(this._canvas)}this._pdfBug&&((nt=globalThis.StepperManager)!=null&&nt.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:A,viewport:q,transform:Z,background:R}=this.params;this.gfx=new _canvas.CanvasGraphics(A,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:F},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:Z,viewport:q,transparency:l,background:R}),this.operatorListIdx=0,this.graphicsReady=!0,(pt=this.graphicsReadyCallback)==null||pt.call(this)}cancel(l=null,F=0){var A;this.running=!1,this.cancelled=!0,(A=this.gfx)==null||A.endDrawing(),t(L,u).delete(this._canvas),this.callback(l||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,F))}operatorListChanged(){var l;if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(l=this.stepper)==null||l.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame(()=>{this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),t(L,u).delete(this._canvas),this.callback())))}};u=new WeakMap,U(L,u,new WeakSet);let InternalRenderTask=L;const version="3.11.174";exports.version=version;const build="ce8716743";exports.build=build},(f,v,vt)=>{var D,g,j,Fr,k;Object.defineProperty(v,"__esModule",{value:!0}),v.SerializableEmpty=v.PrintAnnotationStorage=v.AnnotationStorage=void 0;var d=vt(1),P=vt(4),e=vt(8);const tt=Object.freeze({map:null,hash:"",transfers:void 0});v.SerializableEmpty=tt;class Tt{constructor(){U(this,j);U(this,D,!1);U(this,g,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(w,m){const E=t(this,g).get(w);return E===void 0?m:Object.assign(m,E)}getRawValue(w){return t(this,g).get(w)}remove(w){if(t(this,g).delete(w),t(this,g).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const m of t(this,g).values())if(m instanceof P.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(w,m){const E=t(this,g).get(w);let x=!1;if(E!==void 0)for(const[M,C]of Object.entries(m))E[M]!==C&&(x=!0,E[M]=C);else x=!0,t(this,g).set(w,m);x&&Q(this,j,Fr).call(this),m instanceof P.AnnotationEditor&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(m.constructor._type)}has(w){return t(this,g).has(w)}getAll(){return t(this,g).size>0?(0,d.objectFromMap)(t(this,g)):null}setAll(w){for(const[m,E]of Object.entries(w))this.setValue(m,E)}get size(){return t(this,g).size}resetModified(){t(this,D)&&(ft(this,D,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new G(this)}get serializable(){if(t(this,g).size===0)return tt;const w=new Map,m=new e.MurmurHash3_64,E=[],x=Object.create(null);let M=!1;for(const[C,o]of t(this,g)){const u=o instanceof P.AnnotationEditor?o.serialize(!1,x):o;u&&(w.set(C,u),m.update(`${C}:${JSON.stringify(u)}`),M||(M=!!u.bitmap))}if(M)for(const C of w.values())C.bitmap&&E.push(C.bitmap);return w.size>0?{map:w,hash:m.hexdigest(),transfers:E}:tt}}D=new WeakMap,g=new WeakMap,j=new WeakSet,Fr=function(){t(this,D)||(ft(this,D,!0),typeof this.onSetModified=="function"&&this.onSetModified())},v.AnnotationStorage=Tt;class G extends Tt{constructor(m){super();U(this,k,void 0);const{map:E,hash:x,transfers:M}=m.serializable,C=structuredClone(E,M?{transfer:M}:null);ft(this,k,{map:C,hash:x,transfers:M})}get print(){(0,d.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return t(this,k)}}k=new WeakMap,v.PrintAnnotationStorage=G},(f,v,vt)=>{var G,D,g,j,H,k,y,w,m,E,x,M,C,o,u,Ii,_,Oi,F,Ni,q,Bi,R,Lr,pt,Dr,Y,Ir,kt,Hi,Gt,Or;Object.defineProperty(v,"__esModule",{value:!0}),v.AnnotationEditor=void 0;var d=vt(5),P=vt(1),e=vt(6);const i=class i{constructor(s){U(this,u);U(this,_);U(this,q);U(this,R);U(this,pt);U(this,Y);U(this,kt);U(this,Gt);U(this,G,"");U(this,D,!1);U(this,g,null);U(this,j,null);U(this,H,null);U(this,k,!1);U(this,y,null);U(this,w,this.focusin.bind(this));U(this,m,this.focusout.bind(this));U(this,E,!1);U(this,x,!1);U(this,M,!1);Je(this,"_initialOptions",Object.create(null));Je(this,"_uiManager",null);Je(this,"_focusEventsAllowed",!0);Je(this,"_l10nPromise",null);U(this,C,!1);U(this,o,i._zIndex++);this.constructor===i&&(0,P.unreachable)("Cannot initialize AnnotationEditor."),this.parent=s.parent,this.id=s.id,this.width=this.height=null,this.pageIndex=s.parent.pageIndex,this.name=s.name,this.div=null,this._uiManager=s.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=s.isCentered,this._structTreeParentId=null;const{rotation:c,rawDims:{pageWidth:h,pageHeight:S,pageX:I,pageY:z}}=this.parent.viewport;this.rotation=c,this.pageRotation=(360+c-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[h,S],this.pageTranslation=[I,z];const[dt,ct]=this.parentDimensions;this.x=s.x/dt,this.y=s.y/ct,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return(0,P.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(s){const c=new Tt({id:s.parent.getNextId(),parent:s.parent,uiManager:s._uiManager});c.annotationElementId=s.annotationElementId,c.deleted=!0,c._uiManager.addToAnnotationStorage(c)}static initialize(s,c=null){if(i._l10nPromise||(i._l10nPromise=new Map(["editor_alt_text_button_label","editor_alt_text_edit_button_label","editor_alt_text_decorative_tooltip"].map(S=>[S,s.get(S)]))),c!=null&&c.strings)for(const S of c.strings)i._l10nPromise.set(S,s.get(S));if(i._borderLineWidth!==-1)return;const h=getComputedStyle(document.documentElement);i._borderLineWidth=parseFloat(h.getPropertyValue("--outline-width"))||0}static updateDefaultParams(s,c){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(s){return!1}static paste(s,c){(0,P.unreachable)("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return t(this,C)}set _isDraggable(s){var c;ft(this,C,s),(c=this.div)==null||c.classList.toggle("draggable",s)}center(){const[s,c]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*c/(s*2),this.y+=this.width*s/(c*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*c/(s*2),this.y-=this.width*s/(c*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(s){this._uiManager.addCommands(s)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=t(this,o)}setParent(s){s!==null&&(this.pageIndex=s.pageIndex,this.pageDimensions=s.pageDimensions),this.parent=s}focusin(s){this._focusEventsAllowed&&(t(this,E)?ft(this,E,!1):this.parent.setSelected(this))}focusout(s){var h;if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;const c=s.relatedTarget;c!=null&&c.closest(`#${this.id}`)||(s.preventDefault(),(h=this.parent)!=null&&h.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(s,c,h,S){const[I,z]=this.parentDimensions;[h,S]=this.screenToPageTranslation(h,S),this.x=(s+h)/I,this.y=(c+S)/z,this.fixAndSetPosition()}translate(s,c){Q(this,u,Ii).call(this,this.parentDimensions,s,c)}translateInPage(s,c){Q(this,u,Ii).call(this,this.pageDimensions,s,c),this.div.scrollIntoView({block:"nearest"})}drag(s,c){const[h,S]=this.parentDimensions;if(this.x+=s/h,this.y+=c/S,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:mt,y:Et}=this.div.getBoundingClientRect();this.parent.findNewParent(this,mt,Et)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:I,y:z}=this;const[dt,ct]=Q(this,_,Oi).call(this);I+=dt,z+=ct,this.div.style.left=`${(100*I).toFixed(2)}%`,this.div.style.top=`${(100*z).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}fixAndSetPosition(){const[s,c]=this.pageDimensions;let{x:h,y:S,width:I,height:z}=this;switch(I*=s,z*=c,h*=s,S*=c,this.rotation){case 0:h=Math.max(0,Math.min(s-I,h)),S=Math.max(0,Math.min(c-z,S));break;case 90:h=Math.max(0,Math.min(s-z,h)),S=Math.min(c,Math.max(I,S));break;case 180:h=Math.min(s,Math.max(I,h)),S=Math.min(c,Math.max(z,S));break;case 270:h=Math.min(s,Math.max(z,h)),S=Math.max(0,Math.min(c-I,S));break}this.x=h/=s,this.y=S/=c;const[dt,ct]=Q(this,_,Oi).call(this);h+=dt,S+=ct;const{style:mt}=this.div;mt.left=`${(100*h).toFixed(2)}%`,mt.top=`${(100*S).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(s,c){var h;return Q(h=i,F,Ni).call(h,s,c,this.parentRotation)}pageTranslationToScreen(s,c){var h;return Q(h=i,F,Ni).call(h,s,c,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:s,pageDimensions:[c,h]}=this,S=c*s,I=h*s;return P.FeatureTest.isCSSRoundSupported?[Math.round(S),Math.round(I)]:[S,I]}setDims(s,c){var I;const[h,S]=this.parentDimensions;this.div.style.width=`${(100*s/h).toFixed(2)}%`,t(this,k)||(this.div.style.height=`${(100*c/S).toFixed(2)}%`),(I=t(this,g))==null||I.classList.toggle("small",s<i.SMALL_EDITOR_SIZE||c<i.SMALL_EDITOR_SIZE)}fixDims(){const{style:s}=this.div,{height:c,width:h}=s,S=h.endsWith("%"),I=!t(this,k)&&c.endsWith("%");if(S&&I)return;const[z,dt]=this.parentDimensions;S||(s.width=`${(100*parseFloat(h)/z).toFixed(2)}%`),!t(this,k)&&!I&&(s.height=`${(100*parseFloat(c)/dt).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}async addAltTextButton(){if(t(this,g))return;const s=ft(this,g,document.createElement("button"));s.className="altText";const c=await i._l10nPromise.get("editor_alt_text_button_label");s.textContent=c,s.setAttribute("aria-label",c),s.tabIndex="0",s.addEventListener("contextmenu",e.noContextMenu),s.addEventListener("pointerdown",h=>h.stopPropagation()),s.addEventListener("click",h=>{h.preventDefault(),this._uiManager.editAltText(this)},{capture:!0}),s.addEventListener("keydown",h=>{h.target===s&&h.key==="Enter"&&(h.preventDefault(),this._uiManager.editAltText(this))}),Q(this,kt,Hi).call(this),this.div.append(s),i.SMALL_EDITOR_SIZE||(i.SMALL_EDITOR_SIZE=Math.min(128,Math.round(s.getBoundingClientRect().width*1.4)))}getClientDimensions(){return this.div.getBoundingClientRect()}get altTextData(){return{altText:t(this,G),decorative:t(this,D)}}set altTextData({altText:s,decorative:c}){t(this,G)===s&&t(this,D)===c||(ft(this,G,s),ft(this,D,c),Q(this,kt,Hi).call(this))}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.setAttribute("tabIndex",0),this.setInForeground(),this.div.addEventListener("focusin",t(this,w)),this.div.addEventListener("focusout",t(this,m));const[s,c]=this.parentDimensions;this.parentRotation%180!==0&&(this.div.style.maxWidth=`${(100*c/s).toFixed(2)}%`,this.div.style.maxHeight=`${(100*s/c).toFixed(2)}%`);const[h,S]=this.getInitialTranslation();return this.translate(h,S),(0,d.bindEvents)(this,this.div,["pointerdown"]),this.div}pointerdown(s){const{isMac:c}=P.FeatureTest.platform;if(s.button!==0||s.ctrlKey&&c){s.preventDefault();return}ft(this,E,!0),Q(this,Gt,Or).call(this,s)}moveInDOM(){var s;(s=this.parent)==null||s.moveEditorInDOM(this)}_setParentAndPosition(s,c,h){s.changeParent(this),this.x=c,this.y=h,this.fixAndSetPosition()}getRect(s,c){const h=this.parentScale,[S,I]=this.pageDimensions,[z,dt]=this.pageTranslation,ct=s/h,mt=c/h,Et=this.x*S,Dt=this.y*I,wt=this.width*S,$t=this.height*I;switch(this.rotation){case 0:return[Et+ct+z,I-Dt-mt-$t+dt,Et+ct+wt+z,I-Dt-mt+dt];case 90:return[Et+mt+z,I-Dt+ct+dt,Et+mt+$t+z,I-Dt+ct+wt+dt];case 180:return[Et-ct-wt+z,I-Dt+mt+dt,Et-ct+z,I-Dt+mt+$t+dt];case 270:return[Et-mt-$t+z,I-Dt-ct-wt+dt,Et-mt+z,I-Dt-ct+dt];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(s,c){const[h,S,I,z]=s,dt=I-h,ct=z-S;switch(this.rotation){case 0:return[h,c-z,dt,ct];case 90:return[h,c-S,ct,dt];case 180:return[I,c-S,dt,ct];case 270:return[I,c-z,ct,dt];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){ft(this,M,!0)}disableEditMode(){ft(this,M,!1)}isInEditMode(){return t(this,M)}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){var s,c;(s=this.div)==null||s.addEventListener("focusin",t(this,w)),(c=this.div)==null||c.addEventListener("focusout",t(this,m))}serialize(s=!1,c=null){(0,P.unreachable)("An editor must be serializable")}static deserialize(s,c,h){const S=new this.prototype.constructor({parent:c,id:c.getNextId(),uiManager:h});S.rotation=s.rotation;const[I,z]=S.pageDimensions,[dt,ct,mt,Et]=S.getRectInCurrentCoords(s.rect,z);return S.x=dt/I,S.y=ct/z,S.width=mt/I,S.height=Et/z,S}remove(){var s;this.div.removeEventListener("focusin",t(this,w)),this.div.removeEventListener("focusout",t(this,m)),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),(s=t(this,g))==null||s.remove(),ft(this,g,null),ft(this,j,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(Q(this,R,Lr).call(this),t(this,y).classList.remove("hidden"))}select(){var s;this.makeResizable(),(s=this.div)==null||s.classList.add("selectedEditor")}unselect(){var s,c,h;(s=t(this,y))==null||s.classList.add("hidden"),(c=this.div)==null||c.classList.remove("selectedEditor"),(h=this.div)!=null&&h.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus()}updateParams(s,c){}disableEditing(){t(this,g)&&(t(this,g).hidden=!0)}enableEditing(){t(this,g)&&(t(this,g).hidden=!1)}enterInEditMode(){}get contentDiv(){return this.div}get isEditing(){return t(this,x)}set isEditing(s){ft(this,x,s),this.parent&&(s?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(s,c){ft(this,k,!0);const h=s/c,{style:S}=this.div;S.aspectRatio=h,S.height="auto"}static get MIN_SIZE(){return 16}};G=new WeakMap,D=new WeakMap,g=new WeakMap,j=new WeakMap,H=new WeakMap,k=new WeakMap,y=new WeakMap,w=new WeakMap,m=new WeakMap,E=new WeakMap,x=new WeakMap,M=new WeakMap,C=new WeakMap,o=new WeakMap,u=new WeakSet,Ii=function([s,c],h,S){[h,S]=this.screenToPageTranslation(h,S),this.x+=h/s,this.y+=S/c,this.fixAndSetPosition()},_=new WeakSet,Oi=function(){const[s,c]=this.parentDimensions,{_borderLineWidth:h}=i,S=h/s,I=h/c;switch(this.rotation){case 90:return[-S,I];case 180:return[S,I];case 270:return[S,-I];default:return[-S,-I]}},F=new WeakSet,Ni=function(s,c,h){switch(h){case 90:return[c,-s];case 180:return[-s,-c];case 270:return[-c,s];default:return[s,c]}},q=new WeakSet,Bi=function(s){switch(s){case 90:{const[c,h]=this.pageDimensions;return[0,-c/h,h/c,0]}case 180:return[-1,0,0,-1];case 270:{const[c,h]=this.pageDimensions;return[0,c/h,-h/c,0]}default:return[1,0,0,1]}},R=new WeakSet,Lr=function(){if(t(this,y))return;ft(this,y,document.createElement("div")),t(this,y).classList.add("resizers");const s=["topLeft","topRight","bottomRight","bottomLeft"];this._willKeepAspectRatio||s.push("topMiddle","middleRight","bottomMiddle","middleLeft");for(const c of s){const h=document.createElement("div");t(this,y).append(h),h.classList.add("resizer",c),h.addEventListener("pointerdown",Q(this,pt,Dr).bind(this,c)),h.addEventListener("contextmenu",e.noContextMenu)}this.div.prepend(t(this,y))},pt=new WeakSet,Dr=function(s,c){c.preventDefault();const{isMac:h}=P.FeatureTest.platform;if(c.button!==0||c.ctrlKey&&h)return;const S=Q(this,Y,Ir).bind(this,s),I=this._isDraggable;this._isDraggable=!1;const z={passive:!0,capture:!0};window.addEventListener("pointermove",S,z);const dt=this.x,ct=this.y,mt=this.width,Et=this.height,Dt=this.parent.div.style.cursor,wt=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(c.target).cursor;const $t=()=>{this._isDraggable=I,window.removeEventListener("pointerup",$t),window.removeEventListener("blur",$t),window.removeEventListener("pointermove",S,z),this.parent.div.style.cursor=Dt,this.div.style.cursor=wt;const ie=this.x,It=this.y,Jt=this.width,Ct=this.height;ie===dt&&It===ct&&Jt===mt&&Ct===Et||this.addCommands({cmd:()=>{this.width=Jt,this.height=Ct,this.x=ie,this.y=It;const[ht,at]=this.parentDimensions;this.setDims(ht*Jt,at*Ct),this.fixAndSetPosition()},undo:()=>{this.width=mt,this.height=Et,this.x=dt,this.y=ct;const[ht,at]=this.parentDimensions;this.setDims(ht*mt,at*Et),this.fixAndSetPosition()},mustExec:!0})};window.addEventListener("pointerup",$t),window.addEventListener("blur",$t)},Y=new WeakSet,Ir=function(s,c){const[h,S]=this.parentDimensions,I=this.x,z=this.y,dt=this.width,ct=this.height,mt=i.MIN_SIZE/h,Et=i.MIN_SIZE/S,Dt=Ft=>Math.round(Ft*1e4)/1e4,wt=Q(this,q,Bi).call(this,this.rotation),$t=(Ft,At)=>[wt[0]*Ft+wt[2]*At,wt[1]*Ft+wt[3]*At],ie=Q(this,q,Bi).call(this,360-this.rotation),It=(Ft,At)=>[ie[0]*Ft+ie[2]*At,ie[1]*Ft+ie[3]*At];let Jt,Ct,ht=!1,at=!1;switch(s){case"topLeft":ht=!0,Jt=(Ft,At)=>[0,0],Ct=(Ft,At)=>[Ft,At];break;case"topMiddle":Jt=(Ft,At)=>[Ft/2,0],Ct=(Ft,At)=>[Ft/2,At];break;case"topRight":ht=!0,Jt=(Ft,At)=>[Ft,0],Ct=(Ft,At)=>[0,At];break;case"middleRight":at=!0,Jt=(Ft,At)=>[Ft,At/2],Ct=(Ft,At)=>[0,At/2];break;case"bottomRight":ht=!0,Jt=(Ft,At)=>[Ft,At],Ct=(Ft,At)=>[0,0];break;case"bottomMiddle":Jt=(Ft,At)=>[Ft/2,At],Ct=(Ft,At)=>[Ft/2,0];break;case"bottomLeft":ht=!0,Jt=(Ft,At)=>[0,At],Ct=(Ft,At)=>[Ft,0];break;case"middleLeft":at=!0,Jt=(Ft,At)=>[0,At/2],Ct=(Ft,At)=>[Ft,At/2];break}const St=Jt(dt,ct),jt=Ct(dt,ct);let Ht=$t(...jt);const le=Dt(I+Ht[0]),fe=Dt(z+Ht[1]);let he=1,ge=1,[_e,re]=this.screenToPageTranslation(c.movementX,c.movementY);if([_e,re]=It(_e/h,re/S),ht){const Ft=Math.hypot(dt,ct);he=ge=Math.max(Math.min(Math.hypot(jt[0]-St[0]-_e,jt[1]-St[1]-re)/Ft,1/dt,1/ct),mt/dt,Et/ct)}else at?he=Math.max(mt,Math.min(1,Math.abs(jt[0]-St[0]-_e)))/dt:ge=Math.max(Et,Math.min(1,Math.abs(jt[1]-St[1]-re)))/ct;const we=Dt(dt*he),_t=Dt(ct*ge);Ht=$t(...Ct(we,_t));const et=le-Ht[0],$=fe-Ht[1];this.width=we,this.height=_t,this.x=et,this.y=$,this.setDims(h*we,S*_t),this.fixAndSetPosition()},kt=new WeakSet,Hi=async function(){var h;const s=t(this,g);if(!s)return;if(!t(this,G)&&!t(this,D)){s.classList.remove("done"),(h=t(this,j))==null||h.remove();return}i._l10nPromise.get("editor_alt_text_edit_button_label").then(S=>{s.setAttribute("aria-label",S)});let c=t(this,j);if(!c){ft(this,j,c=document.createElement("span")),c.className="tooltip",c.setAttribute("role","tooltip");const S=c.id=`alt-text-tooltip-${this.id}`;s.setAttribute("aria-describedby",S);const I=100;s.addEventListener("mouseenter",()=>{ft(this,H,setTimeout(()=>{ft(this,H,null),t(this,j).classList.add("show"),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"alt_text_tooltip"}}})},I))}),s.addEventListener("mouseleave",()=>{var z;clearTimeout(t(this,H)),ft(this,H,null),(z=t(this,j))==null||z.classList.remove("show")})}s.classList.add("done"),c.innerText=t(this,D)?await i._l10nPromise.get("editor_alt_text_decorative_tooltip"):t(this,G),c.parentNode||s.append(c)},Gt=new WeakSet,Or=function(s){if(!this._isDraggable)return;const c=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let h,S;c&&(h={passive:!0,capture:!0},S=z=>{const[dt,ct]=this.screenToPageTranslation(z.movementX,z.movementY);this._uiManager.dragSelectedEditors(dt,ct)},window.addEventListener("pointermove",S,h));const I=()=>{if(window.removeEventListener("pointerup",I),window.removeEventListener("blur",I),c&&window.removeEventListener("pointermove",S,h),ft(this,E,!1),!this._uiManager.endDragSession()){const{isMac:z}=P.FeatureTest.platform;s.ctrlKey&&!z||s.shiftKey||s.metaKey&&z?this.parent.toggleSelected(this):this.parent.setSelected(this)}};window.addEventListener("pointerup",I),window.addEventListener("blur",I)},U(i,F),Je(i,"_borderLineWidth",-1),Je(i,"_colorManager",new d.ColorManager),Je(i,"_zIndex",1),Je(i,"SMALL_EDITOR_SIZE",0);let tt=i;v.AnnotationEditor=tt;class Tt extends tt{constructor(s){super(s),this.annotationElementId=s.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}},(f,v,vt)=>{var k,y,w,m,E,Ui,C,o,u,L,_,Nr,A,q,Z,R,nt,pt,bt,Y,yt,kt,Bt,Gt,T,i,a,s,c,h,S,I,z,dt,ct,mt,Et,Dt,wt,$t,ie,It,Jt,Ct,ht,at,St,Br,Ht,ji,fe,Wi,ge,ci,re,zi,_t,Vi,$,Sn,At,Xn,je,Hr,Ot,Ur,Wt,Gi,ne,$n,Lt,qi;Object.defineProperty(v,"__esModule",{value:!0}),v.KeyboardManager=v.CommandManager=v.ColorManager=v.AnnotationEditorUIManager=void 0,v.bindEvents=e,v.opacityToHex=tt;var d=vt(1),P=vt(6);function e(N,n,b){for(const B of b)n.addEventListener(B,N[B].bind(N))}function tt(N){return Math.round(Math.min(255,Math.max(1,255*N))).toString(16).padStart(2,"0")}class Tt{constructor(){U(this,k,0)}getId(){return`${d.AnnotationEditorPrefix}${qn(this,k)._++}`}}k=new WeakMap;const M=class M{constructor(){U(this,E);U(this,y,(0,d.getUuid)());U(this,w,0);U(this,m,null)}static get _isSVGFittingCanvas(){const n='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',B=new OffscreenCanvas(1,3).getContext("2d"),K=new Image;K.src=n;const it=K.decode().then(()=>(B.drawImage(K,0,0,1,1,0,0,1,3),new Uint32Array(B.getImageData(0,0,1,1).data.buffer)[0]===0));return(0,d.shadow)(this,"_isSVGFittingCanvas",it)}async getFromFile(n){const{lastModified:b,name:B,size:K,type:it}=n;return Q(this,E,Ui).call(this,`${b}_${B}_${K}_${it}`,n)}async getFromUrl(n){return Q(this,E,Ui).call(this,n,n)}async getFromId(n){t(this,m)||ft(this,m,new Map);const b=t(this,m).get(n);return b?b.bitmap?(b.refCounter+=1,b):b.file?this.getFromFile(b.file):this.getFromUrl(b.url):null}getSvgUrl(n){const b=t(this,m).get(n);return b!=null&&b.isSvg?b.svgUrl:null}deleteId(n){t(this,m)||ft(this,m,new Map);const b=t(this,m).get(n);b&&(b.refCounter-=1,b.refCounter===0&&(b.bitmap=null))}isValidId(n){return n.startsWith(`image_${t(this,y)}_`)}};y=new WeakMap,w=new WeakMap,m=new WeakMap,E=new WeakSet,Ui=async function(n,b){t(this,m)||ft(this,m,new Map);let B=t(this,m).get(n);if(B===null)return null;if(B!=null&&B.bitmap)return B.refCounter+=1,B;try{B||(B={bitmap:null,id:`image_${t(this,y)}_${qn(this,w)._++}`,refCounter:0,isSvg:!1});let K;if(typeof b=="string"){B.url=b;const it=await fetch(b);if(!it.ok)throw new Error(it.statusText);K=await it.blob()}else K=B.file=b;if(K.type==="image/svg+xml"){const it=M._isSVGFittingCanvas,gt=new FileReader,Mt=new Image,qt=new Promise((zt,ve)=>{Mt.onload=()=>{B.bitmap=Mt,B.isSvg=!0,zt()},gt.onload=async()=>{const Yt=B.svgUrl=gt.result;Mt.src=await it?`${Yt}#svgView(preserveAspectRatio(none))`:Yt},Mt.onerror=gt.onerror=ve});gt.readAsDataURL(K),await qt}else B.bitmap=await createImageBitmap(K);B.refCounter=1}catch(K){console.error(K),B=null}return t(this,m).set(n,B),B&&t(this,m).set(B.id,B),B};let G=M;class D{constructor(n=128){U(this,C,[]);U(this,o,!1);U(this,u,void 0);U(this,L,-1);ft(this,u,n)}add({cmd:n,undo:b,mustExec:B,type:K=NaN,overwriteIfSameType:it=!1,keepUndo:gt=!1}){if(B&&n(),t(this,o))return;const Mt={cmd:n,undo:b,type:K};if(t(this,L)===-1){t(this,C).length>0&&(t(this,C).length=0),ft(this,L,0),t(this,C).push(Mt);return}if(it&&t(this,C)[t(this,L)].type===K){gt&&(Mt.undo=t(this,C)[t(this,L)].undo),t(this,C)[t(this,L)]=Mt;return}const qt=t(this,L)+1;qt===t(this,u)?t(this,C).splice(0,1):(ft(this,L,qt),qt<t(this,C).length&&t(this,C).splice(qt)),t(this,C).push(Mt)}undo(){t(this,L)!==-1&&(ft(this,o,!0),t(this,C)[t(this,L)].undo(),ft(this,o,!1),ft(this,L,t(this,L)-1))}redo(){t(this,L)<t(this,C).length-1&&(ft(this,L,t(this,L)+1),ft(this,o,!0),t(this,C)[t(this,L)].cmd(),ft(this,o,!1))}hasSomethingToUndo(){return t(this,L)!==-1}hasSomethingToRedo(){return t(this,L)<t(this,C).length-1}destroy(){ft(this,C,null)}}C=new WeakMap,o=new WeakMap,u=new WeakMap,L=new WeakMap,v.CommandManager=D;class g{constructor(n){U(this,_);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:b}=d.FeatureTest.platform;for(const[B,K,it={}]of n)for(const gt of B){const Mt=gt.startsWith("mac+");b&&Mt?(this.callbacks.set(gt.slice(4),{callback:K,options:it}),this.allKeys.add(gt.split("+").at(-1))):!b&&!Mt&&(this.callbacks.set(gt,{callback:K,options:it}),this.allKeys.add(gt.split("+").at(-1)))}}exec(n,b){if(!this.allKeys.has(b.key))return;const B=this.callbacks.get(Q(this,_,Nr).call(this,b));if(!B)return;const{callback:K,options:{bubbles:it=!1,args:gt=[],checker:Mt=null}}=B;Mt&&!Mt(n,b)||(K.bind(n,...gt)(),it||(b.stopPropagation(),b.preventDefault()))}}_=new WeakSet,Nr=function(n){n.altKey&&this.buffer.push("alt"),n.ctrlKey&&this.buffer.push("ctrl"),n.metaKey&&this.buffer.push("meta"),n.shiftKey&&this.buffer.push("shift"),this.buffer.push(n.key);const b=this.buffer.join("+");return this.buffer.length=0,b},v.KeyboardManager=g;const F=class F{get _colors(){const n=new Map([["CanvasText",null],["Canvas",null]]);return(0,P.getColorValues)(n),(0,d.shadow)(this,"_colors",n)}convert(n){const b=(0,P.getRGB)(n);if(!window.matchMedia("(forced-colors: active)").matches)return b;for(const[B,K]of this._colors)if(K.every((it,gt)=>it===b[gt]))return F._colorsMapping.get(B);return b}getHexCode(n){const b=this._colors.get(n);return b?d.Util.makeHexColor(...b):n}};Je(F,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let j=F;v.ColorManager=j;const Zt=class Zt{constructor(n,b,B,K,it,gt){U(this,St);U(this,Ht);U(this,fe);U(this,ge);U(this,re);U(this,_t);U(this,$);U(this,At);U(this,je);U(this,Ot);U(this,Wt);U(this,ne);U(this,Lt);U(this,A,null);U(this,q,new Map);U(this,Z,new Map);U(this,R,null);U(this,nt,null);U(this,pt,new D);U(this,bt,0);U(this,Y,new Set);U(this,yt,null);U(this,kt,null);U(this,Bt,new Set);U(this,Gt,null);U(this,T,new Tt);U(this,i,!1);U(this,a,!1);U(this,s,null);U(this,c,d.AnnotationEditorType.NONE);U(this,h,new Set);U(this,S,null);U(this,I,this.blur.bind(this));U(this,z,this.focus.bind(this));U(this,dt,this.copy.bind(this));U(this,ct,this.cut.bind(this));U(this,mt,this.paste.bind(this));U(this,Et,this.keydown.bind(this));U(this,Dt,this.onEditingAction.bind(this));U(this,wt,this.onPageChanging.bind(this));U(this,$t,this.onScaleChanging.bind(this));U(this,ie,this.onRotationChanging.bind(this));U(this,It,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1});U(this,Jt,[0,0]);U(this,Ct,null);U(this,ht,null);U(this,at,null);ft(this,ht,n),ft(this,at,b),ft(this,R,B),this._eventBus=K,this._eventBus._on("editingaction",t(this,Dt)),this._eventBus._on("pagechanging",t(this,wt)),this._eventBus._on("scalechanging",t(this,$t)),this._eventBus._on("rotationchanging",t(this,ie)),ft(this,nt,it.annotationStorage),ft(this,Gt,it.filterFactory),ft(this,S,gt),this.viewParameters={realScale:P.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}static get _keyboardManager(){const n=Zt.prototype,b=it=>{const{activeElement:gt}=document;return gt&&t(it,ht).contains(gt)&&it.hasSomethingToControl()},B=this.TRANSLATE_SMALL,K=this.TRANSLATE_BIG;return(0,d.shadow)(this,"_keyboardManager",new g([[["ctrl+a","mac+meta+a"],n.selectAll],[["ctrl+z","mac+meta+z"],n.undo],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],n.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],n.delete],[["Escape","mac+Escape"],n.unselectAll],[["ArrowLeft","mac+ArrowLeft"],n.translateSelectedEditors,{args:[-B,0],checker:b}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],n.translateSelectedEditors,{args:[-K,0],checker:b}],[["ArrowRight","mac+ArrowRight"],n.translateSelectedEditors,{args:[B,0],checker:b}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],n.translateSelectedEditors,{args:[K,0],checker:b}],[["ArrowUp","mac+ArrowUp"],n.translateSelectedEditors,{args:[0,-B],checker:b}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],n.translateSelectedEditors,{args:[0,-K],checker:b}],[["ArrowDown","mac+ArrowDown"],n.translateSelectedEditors,{args:[0,B],checker:b}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],n.translateSelectedEditors,{args:[0,K],checker:b}]]))}destroy(){Q(this,ge,ci).call(this),Q(this,Ht,ji).call(this),this._eventBus._off("editingaction",t(this,Dt)),this._eventBus._off("pagechanging",t(this,wt)),this._eventBus._off("scalechanging",t(this,$t)),this._eventBus._off("rotationchanging",t(this,ie));for(const n of t(this,Z).values())n.destroy();t(this,Z).clear(),t(this,q).clear(),t(this,Bt).clear(),ft(this,A,null),t(this,h).clear(),t(this,pt).destroy(),t(this,R).destroy()}get hcmFilter(){return(0,d.shadow)(this,"hcmFilter",t(this,S)?t(this,Gt).addHCMFilter(t(this,S).foreground,t(this,S).background):"none")}get direction(){return(0,d.shadow)(this,"direction",getComputedStyle(t(this,ht)).direction)}editAltText(n){var b;(b=t(this,R))==null||b.editAltText(this,n)}onPageChanging({pageNumber:n}){ft(this,bt,n-1)}focusMainContainer(){t(this,ht).focus()}findParent(n,b){for(const B of t(this,Z).values()){const{x:K,y:it,width:gt,height:Mt}=B.div.getBoundingClientRect();if(n>=K&&n<=K+gt&&b>=it&&b<=it+Mt)return B}return null}disableUserSelect(n=!1){t(this,at).classList.toggle("noUserSelect",n)}addShouldRescale(n){t(this,Bt).add(n)}removeShouldRescale(n){t(this,Bt).delete(n)}onScaleChanging({scale:n}){this.commitOrRemove(),this.viewParameters.realScale=n*P.PixelsPerInch.PDF_TO_CSS_UNITS;for(const b of t(this,Bt))b.onScaleChanging()}onRotationChanging({pagesRotation:n}){this.commitOrRemove(),this.viewParameters.rotation=n}addToAnnotationStorage(n){!n.isEmpty()&&t(this,nt)&&!t(this,nt).has(n.id)&&t(this,nt).setValue(n.id,n)}blur(){if(!this.hasSelection)return;const{activeElement:n}=document;for(const b of t(this,h))if(b.div.contains(n)){ft(this,s,[b,n]),b._focusEventsAllowed=!1;break}}focus(){if(!t(this,s))return;const[n,b]=t(this,s);ft(this,s,null),b.addEventListener("focusin",()=>{n._focusEventsAllowed=!0},{once:!0}),b.focus()}addEditListeners(){Q(this,fe,Wi).call(this),Q(this,re,zi).call(this)}removeEditListeners(){Q(this,ge,ci).call(this),Q(this,_t,Vi).call(this)}copy(n){var B;if(n.preventDefault(),(B=t(this,A))==null||B.commitOrRemove(),!this.hasSelection)return;const b=[];for(const K of t(this,h)){const it=K.serialize(!0);it&&b.push(it)}b.length!==0&&n.clipboardData.setData("application/pdfjs",JSON.stringify(b))}cut(n){this.copy(n),this.delete()}paste(n){n.preventDefault();const{clipboardData:b}=n;for(const it of b.items)for(const gt of t(this,kt))if(gt.isHandlingMimeForPasting(it.type)){gt.paste(it,this.currentLayer);return}let B=b.getData("application/pdfjs");if(!B)return;try{B=JSON.parse(B)}catch(it){(0,d.warn)(`paste: "${it.message}".`);return}if(!Array.isArray(B))return;this.unselectAll();const K=this.currentLayer;try{const it=[];for(const qt of B){const zt=K.deserialize(qt);if(!zt)return;it.push(zt)}const gt=()=>{for(const qt of it)Q(this,Wt,Gi).call(this,qt);Q(this,Lt,qi).call(this,it)},Mt=()=>{for(const qt of it)qt.remove()};this.addCommands({cmd:gt,undo:Mt,mustExec:!0})}catch(it){(0,d.warn)(`paste: "${it.message}".`)}}keydown(n){var b;(b=this.getActive())!=null&&b.shouldGetKeyboardEvents()||Zt._keyboardManager.exec(this,n)}onEditingAction(n){["undo","redo","delete","selectAll"].includes(n.name)&&this[n.name]()}setEditingState(n){n?(Q(this,St,Br).call(this),Q(this,fe,Wi).call(this),Q(this,re,zi).call(this),Q(this,$,Sn).call(this,{isEditing:t(this,c)!==d.AnnotationEditorType.NONE,isEmpty:Q(this,ne,$n).call(this),hasSomethingToUndo:t(this,pt).hasSomethingToUndo(),hasSomethingToRedo:t(this,pt).hasSomethingToRedo(),hasSelectedEditor:!1})):(Q(this,Ht,ji).call(this),Q(this,ge,ci).call(this),Q(this,_t,Vi).call(this),Q(this,$,Sn).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(n){if(!t(this,kt)){ft(this,kt,n);for(const b of t(this,kt))Q(this,At,Xn).call(this,b.defaultPropertiesToUpdate)}}getId(){return t(this,T).getId()}get currentLayer(){return t(this,Z).get(t(this,bt))}getLayer(n){return t(this,Z).get(n)}get currentPageIndex(){return t(this,bt)}addLayer(n){t(this,Z).set(n.pageIndex,n),t(this,i)?n.enable():n.disable()}removeLayer(n){t(this,Z).delete(n.pageIndex)}updateMode(n,b=null){if(t(this,c)!==n){if(ft(this,c,n),n===d.AnnotationEditorType.NONE){this.setEditingState(!1),Q(this,Ot,Ur).call(this);return}this.setEditingState(!0),Q(this,je,Hr).call(this),this.unselectAll();for(const B of t(this,Z).values())B.updateMode(n);if(b){for(const B of t(this,q).values())if(B.annotationElementId===b){this.setSelected(B),B.enterInEditMode();break}}}}updateToolbar(n){n!==t(this,c)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:n})}updateParams(n,b){if(t(this,kt)){if(n===d.AnnotationEditorParamsType.CREATE){this.currentLayer.addNewEditor(n);return}for(const B of t(this,h))B.updateParams(n,b);for(const B of t(this,kt))B.updateDefaultParams(n,b)}}enableWaiting(n=!1){if(t(this,a)!==n){ft(this,a,n);for(const b of t(this,Z).values())n?b.disableClick():b.enableClick(),b.div.classList.toggle("waiting",n)}}getEditors(n){const b=[];for(const B of t(this,q).values())B.pageIndex===n&&b.push(B);return b}getEditor(n){return t(this,q).get(n)}addEditor(n){t(this,q).set(n.id,n)}removeEditor(n){var b;t(this,q).delete(n.id),this.unselect(n),(!n.annotationElementId||!t(this,Y).has(n.annotationElementId))&&((b=t(this,nt))==null||b.remove(n.id))}addDeletedAnnotationElement(n){t(this,Y).add(n.annotationElementId),n.deleted=!0}isDeletedAnnotationElement(n){return t(this,Y).has(n)}removeDeletedAnnotationElement(n){t(this,Y).delete(n.annotationElementId),n.deleted=!1}setActiveEditor(n){t(this,A)!==n&&(ft(this,A,n),n&&Q(this,At,Xn).call(this,n.propertiesToUpdate))}toggleSelected(n){if(t(this,h).has(n)){t(this,h).delete(n),n.unselect(),Q(this,$,Sn).call(this,{hasSelectedEditor:this.hasSelection});return}t(this,h).add(n),n.select(),Q(this,At,Xn).call(this,n.propertiesToUpdate),Q(this,$,Sn).call(this,{hasSelectedEditor:!0})}setSelected(n){for(const b of t(this,h))b!==n&&b.unselect();t(this,h).clear(),t(this,h).add(n),n.select(),Q(this,At,Xn).call(this,n.propertiesToUpdate),Q(this,$,Sn).call(this,{hasSelectedEditor:!0})}isSelected(n){return t(this,h).has(n)}unselect(n){n.unselect(),t(this,h).delete(n),Q(this,$,Sn).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return t(this,h).size!==0}undo(){t(this,pt).undo(),Q(this,$,Sn).call(this,{hasSomethingToUndo:t(this,pt).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:Q(this,ne,$n).call(this)})}redo(){t(this,pt).redo(),Q(this,$,Sn).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:t(this,pt).hasSomethingToRedo(),isEmpty:Q(this,ne,$n).call(this)})}addCommands(n){t(this,pt).add(n),Q(this,$,Sn).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:Q(this,ne,$n).call(this)})}delete(){if(this.commitOrRemove(),!this.hasSelection)return;const n=[...t(this,h)],b=()=>{for(const K of n)K.remove()},B=()=>{for(const K of n)Q(this,Wt,Gi).call(this,K)};this.addCommands({cmd:b,undo:B,mustExec:!0})}commitOrRemove(){var n;(n=t(this,A))==null||n.commitOrRemove()}hasSomethingToControl(){return t(this,A)||this.hasSelection}selectAll(){for(const n of t(this,h))n.commit();Q(this,Lt,qi).call(this,t(this,q).values())}unselectAll(){if(t(this,A)){t(this,A).commitOrRemove();return}if(this.hasSelection){for(const n of t(this,h))n.unselect();t(this,h).clear(),Q(this,$,Sn).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(n,b,B=!1){if(B||this.commitOrRemove(),!this.hasSelection)return;t(this,Jt)[0]+=n,t(this,Jt)[1]+=b;const[K,it]=t(this,Jt),gt=[...t(this,h)],Mt=1e3;t(this,Ct)&&clearTimeout(t(this,Ct)),ft(this,Ct,setTimeout(()=>{ft(this,Ct,null),t(this,Jt)[0]=t(this,Jt)[1]=0,this.addCommands({cmd:()=>{for(const qt of gt)t(this,q).has(qt.id)&&qt.translateInPage(K,it)},undo:()=>{for(const qt of gt)t(this,q).has(qt.id)&&qt.translateInPage(-K,-it)},mustExec:!1})},Mt));for(const qt of gt)qt.translateInPage(n,b)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),ft(this,yt,new Map);for(const n of t(this,h))t(this,yt).set(n,{savedX:n.x,savedY:n.y,savedPageIndex:n.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!t(this,yt))return!1;this.disableUserSelect(!1);const n=t(this,yt);ft(this,yt,null);let b=!1;for(const[{x:K,y:it,pageIndex:gt},Mt]of n)Mt.newX=K,Mt.newY=it,Mt.newPageIndex=gt,b||(b=K!==Mt.savedX||it!==Mt.savedY||gt!==Mt.savedPageIndex);if(!b)return!1;const B=(K,it,gt,Mt)=>{if(t(this,q).has(K.id)){const qt=t(this,Z).get(Mt);qt?K._setParentAndPosition(qt,it,gt):(K.pageIndex=Mt,K.x=it,K.y=gt)}};return this.addCommands({cmd:()=>{for(const[K,{newX:it,newY:gt,newPageIndex:Mt}]of n)B(K,it,gt,Mt)},undo:()=>{for(const[K,{savedX:it,savedY:gt,savedPageIndex:Mt}]of n)B(K,it,gt,Mt)},mustExec:!0}),!0}dragSelectedEditors(n,b){if(t(this,yt))for(const B of t(this,yt).keys())B.drag(n,b)}rebuild(n){if(n.parent===null){const b=this.getLayer(n.pageIndex);b?(b.changeParent(n),b.addOrRebuild(n)):(this.addEditor(n),this.addToAnnotationStorage(n),n.rebuild())}else n.parent.addOrRebuild(n)}isActive(n){return t(this,A)===n}getActive(){return t(this,A)}getMode(){return t(this,c)}get imageManager(){return(0,d.shadow)(this,"imageManager",new G)}};A=new WeakMap,q=new WeakMap,Z=new WeakMap,R=new WeakMap,nt=new WeakMap,pt=new WeakMap,bt=new WeakMap,Y=new WeakMap,yt=new WeakMap,kt=new WeakMap,Bt=new WeakMap,Gt=new WeakMap,T=new WeakMap,i=new WeakMap,a=new WeakMap,s=new WeakMap,c=new WeakMap,h=new WeakMap,S=new WeakMap,I=new WeakMap,z=new WeakMap,dt=new WeakMap,ct=new WeakMap,mt=new WeakMap,Et=new WeakMap,Dt=new WeakMap,wt=new WeakMap,$t=new WeakMap,ie=new WeakMap,It=new WeakMap,Jt=new WeakMap,Ct=new WeakMap,ht=new WeakMap,at=new WeakMap,St=new WeakSet,Br=function(){window.addEventListener("focus",t(this,z)),window.addEventListener("blur",t(this,I))},Ht=new WeakSet,ji=function(){window.removeEventListener("focus",t(this,z)),window.removeEventListener("blur",t(this,I))},fe=new WeakSet,Wi=function(){window.addEventListener("keydown",t(this,Et),{capture:!0})},ge=new WeakSet,ci=function(){window.removeEventListener("keydown",t(this,Et),{capture:!0})},re=new WeakSet,zi=function(){document.addEventListener("copy",t(this,dt)),document.addEventListener("cut",t(this,ct)),document.addEventListener("paste",t(this,mt))},_t=new WeakSet,Vi=function(){document.removeEventListener("copy",t(this,dt)),document.removeEventListener("cut",t(this,ct)),document.removeEventListener("paste",t(this,mt))},$=new WeakSet,Sn=function(n){Object.entries(n).some(([B,K])=>t(this,It)[B]!==K)&&this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(t(this,It),n)})},At=new WeakSet,Xn=function(n){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:n})},je=new WeakSet,Hr=function(){if(!t(this,i)){ft(this,i,!0);for(const n of t(this,Z).values())n.enable()}},Ot=new WeakSet,Ur=function(){if(this.unselectAll(),t(this,i)){ft(this,i,!1);for(const n of t(this,Z).values())n.disable()}},Wt=new WeakSet,Gi=function(n){const b=t(this,Z).get(n.pageIndex);b?b.addOrRebuild(n):this.addEditor(n)},ne=new WeakSet,$n=function(){if(t(this,q).size===0)return!0;if(t(this,q).size===1)for(const n of t(this,q).values())return n.isEmpty();return!1},Lt=new WeakSet,qi=function(n){t(this,h).clear();for(const b of n)b.isEmpty()||(t(this,h).add(b),b.select());Q(this,$,Sn).call(this,{hasSelectedEditor:!0})},Je(Zt,"TRANSLATE_SMALL",1),Je(Zt,"TRANSLATE_BIG",10);let H=Zt;v.AnnotationEditorUIManager=H},(f,v,vt)=>{var bt,Y,yt,kt,Bt,Gt,T,i,a,s,c,h,Un,I,jn,dt,Xi,mt,hi,Dt,ui,$t,Yn,It,Kn;Object.defineProperty(v,"__esModule",{value:!0}),v.StatTimer=v.RenderingCancelledException=v.PixelsPerInch=v.PageViewport=v.PDFDateString=v.DOMStandardFontDataFactory=v.DOMSVGFactory=v.DOMFilterFactory=v.DOMCanvasFactory=v.DOMCMapReaderFactory=void 0,v.deprecated=L,v.getColorValues=q,v.getCurrentTransform=Z,v.getCurrentTransformInverse=R,v.getFilenameFromUrl=E,v.getPdfFilenameFromUrl=x,v.getRGB=A,v.getXfaPageViewport=F,v.isDataScheme=w,v.isPdfFile=m,v.isValidFetchUrl=C,v.loadScript=u,v.noContextMenu=o,v.setLayerDimensions=nt;var d=vt(7),P=vt(1);const e="http://www.w3.org/2000/svg",pt=class pt{};Je(pt,"CSS",96),Je(pt,"PDF",72),Je(pt,"PDF_TO_CSS_UNITS",pt.CSS/pt.PDF);let tt=pt;v.PixelsPerInch=tt;class Tt extends d.BaseFilterFactory{constructor({docId:at,ownerDocument:St=globalThis.document}={}){super();U(this,h);U(this,I);U(this,dt);U(this,mt);U(this,Dt);U(this,$t);U(this,It);U(this,bt,void 0);U(this,Y,void 0);U(this,yt,void 0);U(this,kt,void 0);U(this,Bt,void 0);U(this,Gt,void 0);U(this,T,void 0);U(this,i,void 0);U(this,a,void 0);U(this,s,void 0);U(this,c,0);ft(this,yt,at),ft(this,kt,St)}addFilter(at){if(!at)return"none";let St=t(this,h,Un).get(at);if(St)return St;let jt,Ht,le,fe;if(at.length===1){const re=at[0],we=new Array(256);for(let _t=0;_t<256;_t++)we[_t]=re[_t]/255;fe=jt=Ht=le=we.join(",")}else{const[re,we,_t]=at,et=new Array(256),$=new Array(256),Ft=new Array(256);for(let At=0;At<256;At++)et[At]=re[At]/255,$[At]=we[At]/255,Ft[At]=_t[At]/255;jt=et.join(","),Ht=$.join(","),le=Ft.join(","),fe=`${jt}${Ht}${le}`}if(St=t(this,h,Un).get(fe),St)return t(this,h,Un).set(at,St),St;const he=`g_${t(this,yt)}_transfer_map_${qn(this,c)._++}`,ge=`url(#${he})`;t(this,h,Un).set(at,ge),t(this,h,Un).set(fe,ge);const _e=Q(this,mt,hi).call(this,he);return Q(this,$t,Yn).call(this,jt,Ht,le,_e),ge}addHCMFilter(at,St){var we;const jt=`${at}-${St}`;if(t(this,Gt)===jt)return t(this,T);if(ft(this,Gt,jt),ft(this,T,"none"),(we=t(this,Bt))==null||we.remove(),!at||!St)return t(this,T);const Ht=Q(this,It,Kn).call(this,at);at=P.Util.makeHexColor(...Ht);const le=Q(this,It,Kn).call(this,St);if(St=P.Util.makeHexColor(...le),t(this,I,jn).style.color="",at==="#000000"&&St==="#ffffff"||at===St)return t(this,T);const fe=new Array(256);for(let _t=0;_t<=255;_t++){const et=_t/255;fe[_t]=et<=.03928?et/12.92:((et+.055)/1.055)**2.4}const he=fe.join(","),ge=`g_${t(this,yt)}_hcm_filter`,_e=ft(this,i,Q(this,mt,hi).call(this,ge));Q(this,$t,Yn).call(this,he,he,he,_e),Q(this,dt,Xi).call(this,_e);const re=(_t,et)=>{const $=Ht[_t]/255,Ft=le[_t]/255,At=new Array(et+1);for(let xe=0;xe<=et;xe++)At[xe]=$+xe/et*(Ft-$);return At.join(",")};return Q(this,$t,Yn).call(this,re(0,5),re(1,5),re(2,5),_e),ft(this,T,`url(#${ge})`),t(this,T)}addHighlightHCMFilter(at,St,jt,Ht){var Ft;const le=`${at}-${St}-${jt}-${Ht}`;if(t(this,a)===le)return t(this,s);if(ft(this,a,le),ft(this,s,"none"),(Ft=t(this,i))==null||Ft.remove(),!at||!St)return t(this,s);const[fe,he]=[at,St].map(Q(this,It,Kn).bind(this));let ge=Math.round(.2126*fe[0]+.7152*fe[1]+.0722*fe[2]),_e=Math.round(.2126*he[0]+.7152*he[1]+.0722*he[2]),[re,we]=[jt,Ht].map(Q(this,It,Kn).bind(this));_e<ge&&([ge,_e,re,we]=[_e,ge,we,re]),t(this,I,jn).style.color="";const _t=(At,xe,je)=>{const st=new Array(256),Ot=(_e-ge)/je,Qt=At/255,Wt=(xe-At)/(255*je);let te=0;for(let ne=0;ne<=je;ne++){const Ie=Math.round(ge+ne*Ot),Lt=Qt+ne*Wt;for(let ke=te;ke<=Ie;ke++)st[ke]=Lt;te=Ie+1}for(let ne=te;ne<256;ne++)st[ne]=st[te-1];return st.join(",")},et=`g_${t(this,yt)}_hcm_highlight_filter`,$=ft(this,i,Q(this,mt,hi).call(this,et));return Q(this,dt,Xi).call(this,$),Q(this,$t,Yn).call(this,_t(re[0],we[0],5),_t(re[1],we[1],5),_t(re[2],we[2],5),$),ft(this,s,`url(#${et})`),t(this,s)}destroy(at=!1){at&&(t(this,T)||t(this,s))||(t(this,Y)&&(t(this,Y).parentNode.parentNode.remove(),ft(this,Y,null)),t(this,bt)&&(t(this,bt).clear(),ft(this,bt,null)),ft(this,c,0))}}bt=new WeakMap,Y=new WeakMap,yt=new WeakMap,kt=new WeakMap,Bt=new WeakMap,Gt=new WeakMap,T=new WeakMap,i=new WeakMap,a=new WeakMap,s=new WeakMap,c=new WeakMap,h=new WeakSet,Un=function(){return t(this,bt)||ft(this,bt,new Map)},I=new WeakSet,jn=function(){if(!t(this,Y)){const at=t(this,kt).createElement("div"),{style:St}=at;St.visibility="hidden",St.contain="strict",St.width=St.height=0,St.position="absolute",St.top=St.left=0,St.zIndex=-1;const jt=t(this,kt).createElementNS(e,"svg");jt.setAttribute("width",0),jt.setAttribute("height",0),ft(this,Y,t(this,kt).createElementNS(e,"defs")),at.append(jt),jt.append(t(this,Y)),t(this,kt).body.append(at)}return t(this,Y)},dt=new WeakSet,Xi=function(at){const St=t(this,kt).createElementNS(e,"feColorMatrix");St.setAttribute("type","matrix"),St.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),at.append(St)},mt=new WeakSet,hi=function(at){const St=t(this,kt).createElementNS(e,"filter");return St.setAttribute("color-interpolation-filters","sRGB"),St.setAttribute("id",at),t(this,I,jn).append(St),St},Dt=new WeakSet,ui=function(at,St,jt){const Ht=t(this,kt).createElementNS(e,St);Ht.setAttribute("type","discrete"),Ht.setAttribute("tableValues",jt),at.append(Ht)},$t=new WeakSet,Yn=function(at,St,jt,Ht){const le=t(this,kt).createElementNS(e,"feComponentTransfer");Ht.append(le),Q(this,Dt,ui).call(this,le,"feFuncR",at),Q(this,Dt,ui).call(this,le,"feFuncG",St),Q(this,Dt,ui).call(this,le,"feFuncB",jt)},It=new WeakSet,Kn=function(at){return t(this,I,jn).style.color=at,A(getComputedStyle(t(this,I,jn)).getPropertyValue("color"))},v.DOMFilterFactory=Tt;class G extends d.BaseCanvasFactory{constructor({ownerDocument:ht=globalThis.document}={}){super(),this._document=ht}_createCanvas(ht,at){const St=this._document.createElement("canvas");return St.width=ht,St.height=at,St}}v.DOMCanvasFactory=G;async function D(Ct,ht=!1){if(C(Ct,document.baseURI)){const at=await fetch(Ct);if(!at.ok)throw new Error(at.statusText);return ht?new Uint8Array(await at.arrayBuffer()):(0,P.stringToBytes)(await at.text())}return new Promise((at,St)=>{const jt=new XMLHttpRequest;jt.open("GET",Ct,!0),ht&&(jt.responseType="arraybuffer"),jt.onreadystatechange=()=>{if(jt.readyState===XMLHttpRequest.DONE){if(jt.status===200||jt.status===0){let Ht;if(ht&&jt.response?Ht=new Uint8Array(jt.response):!ht&&jt.responseText&&(Ht=(0,P.stringToBytes)(jt.responseText)),Ht){at(Ht);return}}St(new Error(jt.statusText))}},jt.send(null)})}class g extends d.BaseCMapReaderFactory{_fetchData(ht,at){return D(ht,this.isCompressed).then(St=>({cMapData:St,compressionType:at}))}}v.DOMCMapReaderFactory=g;class j extends d.BaseStandardFontDataFactory{_fetchData(ht){return D(ht,!0)}}v.DOMStandardFontDataFactory=j;class H extends d.BaseSVGFactory{_createSVG(ht){return document.createElementNS(e,ht)}}v.DOMSVGFactory=H;class k{constructor({viewBox:ht,scale:at,rotation:St,offsetX:jt=0,offsetY:Ht=0,dontFlip:le=!1}){this.viewBox=ht,this.scale=at,this.rotation=St,this.offsetX=jt,this.offsetY=Ht;const fe=(ht[2]+ht[0])/2,he=(ht[3]+ht[1])/2;let ge,_e,re,we;switch(St%=360,St<0&&(St+=360),St){case 180:ge=-1,_e=0,re=0,we=1;break;case 90:ge=0,_e=1,re=1,we=0;break;case 270:ge=0,_e=-1,re=-1,we=0;break;case 0:ge=1,_e=0,re=0,we=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}le&&(re=-re,we=-we);let _t,et,$,Ft;ge===0?(_t=Math.abs(he-ht[1])*at+jt,et=Math.abs(fe-ht[0])*at+Ht,$=(ht[3]-ht[1])*at,Ft=(ht[2]-ht[0])*at):(_t=Math.abs(fe-ht[0])*at+jt,et=Math.abs(he-ht[1])*at+Ht,$=(ht[2]-ht[0])*at,Ft=(ht[3]-ht[1])*at),this.transform=[ge*at,_e*at,re*at,we*at,_t-ge*at*fe-re*at*he,et-_e*at*fe-we*at*he],this.width=$,this.height=Ft}get rawDims(){const{viewBox:ht}=this;return(0,P.shadow)(this,"rawDims",{pageWidth:ht[2]-ht[0],pageHeight:ht[3]-ht[1],pageX:ht[0],pageY:ht[1]})}clone({scale:ht=this.scale,rotation:at=this.rotation,offsetX:St=this.offsetX,offsetY:jt=this.offsetY,dontFlip:Ht=!1}={}){return new k({viewBox:this.viewBox.slice(),scale:ht,rotation:at,offsetX:St,offsetY:jt,dontFlip:Ht})}convertToViewportPoint(ht,at){return P.Util.applyTransform([ht,at],this.transform)}convertToViewportRectangle(ht){const at=P.Util.applyTransform([ht[0],ht[1]],this.transform),St=P.Util.applyTransform([ht[2],ht[3]],this.transform);return[at[0],at[1],St[0],St[1]]}convertToPdfPoint(ht,at){return P.Util.applyInverseTransform([ht,at],this.transform)}}v.PageViewport=k;class y extends P.BaseException{constructor(ht,at=0){super(ht,"RenderingCancelledException"),this.extraDelay=at}}v.RenderingCancelledException=y;function w(Ct){const ht=Ct.length;let at=0;for(;at<ht&&Ct[at].trim()==="";)at++;return Ct.substring(at,at+5).toLowerCase()==="data:"}function m(Ct){return typeof Ct=="string"&&/\.pdf$/i.test(Ct)}function E(Ct,ht=!1){return ht||([Ct]=Ct.split(/[#?]/,1)),Ct.substring(Ct.lastIndexOf("/")+1)}function x(Ct,ht="document.pdf"){if(typeof Ct!="string")return ht;if(w(Ct))return(0,P.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),ht;const at=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,St=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,jt=at.exec(Ct);let Ht=St.exec(jt[1])||St.exec(jt[2])||St.exec(jt[3]);if(Ht&&(Ht=Ht[0],Ht.includes("%")))try{Ht=St.exec(decodeURIComponent(Ht))[0]}catch{}return Ht||ht}class M{constructor(){Je(this,"started",Object.create(null));Je(this,"times",[])}time(ht){ht in this.started&&(0,P.warn)(`Timer is already running for ${ht}`),this.started[ht]=Date.now()}timeEnd(ht){ht in this.started||(0,P.warn)(`Timer has not been started for ${ht}`),this.times.push({name:ht,start:this.started[ht],end:Date.now()}),delete this.started[ht]}toString(){const ht=[];let at=0;for(const{name:St}of this.times)at=Math.max(St.length,at);for(const{name:St,start:jt,end:Ht}of this.times)ht.push(`${St.padEnd(at)} ${Ht-jt}ms
`);return ht.join("")}}v.StatTimer=M;function C(Ct,ht){try{const{protocol:at}=ht?new URL(Ct,ht):new URL(Ct);return at==="http:"||at==="https:"}catch{return!1}}function o(Ct){Ct.preventDefault()}function u(Ct,ht=!1){return new Promise((at,St)=>{const jt=document.createElement("script");jt.src=Ct,jt.onload=function(Ht){ht&&jt.remove(),at(Ht)},jt.onerror=function(){St(new Error(`Cannot load script at: ${jt.src}`))},(document.head||document.documentElement).append(jt)})}function L(Ct){console.log("Deprecated API usage: "+Ct)}let _;class l{static toDateObject(ht){if(!ht||typeof ht!="string")return null;_||(_=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const at=_.exec(ht);if(!at)return null;const St=parseInt(at[1],10);let jt=parseInt(at[2],10);jt=jt>=1&&jt<=12?jt-1:0;let Ht=parseInt(at[3],10);Ht=Ht>=1&&Ht<=31?Ht:1;let le=parseInt(at[4],10);le=le>=0&&le<=23?le:0;let fe=parseInt(at[5],10);fe=fe>=0&&fe<=59?fe:0;let he=parseInt(at[6],10);he=he>=0&&he<=59?he:0;const ge=at[7]||"Z";let _e=parseInt(at[8],10);_e=_e>=0&&_e<=23?_e:0;let re=parseInt(at[9],10)||0;return re=re>=0&&re<=59?re:0,ge==="-"?(le+=_e,fe+=re):ge==="+"&&(le-=_e,fe-=re),new Date(Date.UTC(St,jt,Ht,le,fe,he))}}v.PDFDateString=l;function F(Ct,{scale:ht=1,rotation:at=0}){const{width:St,height:jt}=Ct.attributes.style,Ht=[0,0,parseInt(St),parseInt(jt)];return new k({viewBox:Ht,scale:ht,rotation:at})}function A(Ct){if(Ct.startsWith("#")){const ht=parseInt(Ct.slice(1),16);return[(ht&16711680)>>16,(ht&65280)>>8,ht&255]}return Ct.startsWith("rgb(")?Ct.slice(4,-1).split(",").map(ht=>parseInt(ht)):Ct.startsWith("rgba(")?Ct.slice(5,-1).split(",").map(ht=>parseInt(ht)).slice(0,3):((0,P.warn)(`Not a valid color format: "${Ct}"`),[0,0,0])}function q(Ct){const ht=document.createElement("span");ht.style.visibility="hidden",document.body.append(ht);for(const at of Ct.keys()){ht.style.color=at;const St=window.getComputedStyle(ht).color;Ct.set(at,A(St))}ht.remove()}function Z(Ct){const{a:ht,b:at,c:St,d:jt,e:Ht,f:le}=Ct.getTransform();return[ht,at,St,jt,Ht,le]}function R(Ct){const{a:ht,b:at,c:St,d:jt,e:Ht,f:le}=Ct.getTransform().invertSelf();return[ht,at,St,jt,Ht,le]}function nt(Ct,ht,at=!1,St=!0){if(ht instanceof k){const{pageWidth:jt,pageHeight:Ht}=ht.rawDims,{style:le}=Ct,fe=P.FeatureTest.isCSSRoundSupported,he=`var(--scale-factor) * ${jt}px`,ge=`var(--scale-factor) * ${Ht}px`,_e=fe?`round(${he}, 1px)`:`calc(${he})`,re=fe?`round(${ge}, 1px)`:`calc(${ge})`;!at||ht.rotation%180===0?(le.width=_e,le.height=re):(le.width=re,le.height=_e)}St&&Ct.setAttribute("data-main-rotation",ht.rotation)}},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.BaseStandardFontDataFactory=v.BaseSVGFactory=v.BaseFilterFactory=v.BaseCanvasFactory=v.BaseCMapReaderFactory=void 0;var d=vt(1);class P{constructor(){this.constructor===P&&(0,d.unreachable)("Cannot initialize BaseFilterFactory.")}addFilter(g){return"none"}addHCMFilter(g,j){return"none"}addHighlightHCMFilter(g,j,H,k){return"none"}destroy(g=!1){}}v.BaseFilterFactory=P;class e{constructor(){this.constructor===e&&(0,d.unreachable)("Cannot initialize BaseCanvasFactory.")}create(g,j){if(g<=0||j<=0)throw new Error("Invalid canvas size");const H=this._createCanvas(g,j);return{canvas:H,context:H.getContext("2d")}}reset(g,j,H){if(!g.canvas)throw new Error("Canvas is not specified");if(j<=0||H<=0)throw new Error("Invalid canvas size");g.canvas.width=j,g.canvas.height=H}destroy(g){if(!g.canvas)throw new Error("Canvas is not specified");g.canvas.width=0,g.canvas.height=0,g.canvas=null,g.context=null}_createCanvas(g,j){(0,d.unreachable)("Abstract method `_createCanvas` called.")}}v.BaseCanvasFactory=e;class tt{constructor({baseUrl:g=null,isCompressed:j=!0}){this.constructor===tt&&(0,d.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=g,this.isCompressed=j}async fetch({name:g}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!g)throw new Error("CMap name must be specified.");const j=this.baseUrl+g+(this.isCompressed?".bcmap":""),H=this.isCompressed?d.CMapCompressionType.BINARY:d.CMapCompressionType.NONE;return this._fetchData(j,H).catch(k=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${j}`)})}_fetchData(g,j){(0,d.unreachable)("Abstract method `_fetchData` called.")}}v.BaseCMapReaderFactory=tt;class Tt{constructor({baseUrl:g=null}){this.constructor===Tt&&(0,d.unreachable)("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=g}async fetch({filename:g}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!g)throw new Error("Font filename must be specified.");const j=`${this.baseUrl}${g}`;return this._fetchData(j).catch(H=>{throw new Error(`Unable to load font data at: ${j}`)})}_fetchData(g){(0,d.unreachable)("Abstract method `_fetchData` called.")}}v.BaseStandardFontDataFactory=Tt;class G{constructor(){this.constructor===G&&(0,d.unreachable)("Cannot initialize BaseSVGFactory.")}create(g,j,H=!1){if(g<=0||j<=0)throw new Error("Invalid SVG dimensions");const k=this._createSVG("svg:svg");return k.setAttribute("version","1.1"),H||(k.setAttribute("width",`${g}px`),k.setAttribute("height",`${j}px`)),k.setAttribute("preserveAspectRatio","none"),k.setAttribute("viewBox",`0 0 ${g} ${j}`),k}createElement(g){if(typeof g!="string")throw new Error("Invalid SVG element type");return this._createSVG(g)}_createSVG(g){(0,d.unreachable)("Abstract method `_createSVG` called.")}}v.BaseSVGFactory=G},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.MurmurHash3_64=void 0;var d=vt(1);const P=3285377520,e=4294901760,tt=65535;class Tt{constructor(D){this.h1=D?D&4294967295:P,this.h2=D?D&4294967295:P}update(D){let g,j;if(typeof D=="string"){g=new Uint8Array(D.length*2),j=0;for(let L=0,_=D.length;L<_;L++){const l=D.charCodeAt(L);l<=255?g[j++]=l:(g[j++]=l>>>8,g[j++]=l&255)}}else if((0,d.isArrayBuffer)(D))g=D.slice(),j=g.byteLength;else throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");const H=j>>2,k=j-H*4,y=new Uint32Array(g.buffer,0,H);let w=0,m=0,E=this.h1,x=this.h2;const M=3432918353,C=461845907,o=M&tt,u=C&tt;for(let L=0;L<H;L++)L&1?(w=y[L],w=w*M&e|w*o&tt,w=w<<15|w>>>17,w=w*C&e|w*u&tt,E^=w,E=E<<13|E>>>19,E=E*5+3864292196):(m=y[L],m=m*M&e|m*o&tt,m=m<<15|m>>>17,m=m*C&e|m*u&tt,x^=m,x=x<<13|x>>>19,x=x*5+3864292196);switch(w=0,k){case 3:w^=g[H*4+2]<<16;case 2:w^=g[H*4+1]<<8;case 1:w^=g[H*4],w=w*M&e|w*o&tt,w=w<<15|w>>>17,w=w*C&e|w*u&tt,H&1?E^=w:x^=w}this.h1=E,this.h2=x}hexdigest(){let D=this.h1,g=this.h2;return D^=g>>>1,D=D*3981806797&e|D*36045&tt,g=g*4283543511&e|((g<<16|D>>>16)*2950163797&e)>>>16,D^=g>>>1,D=D*444984403&e|D*60499&tt,g=g*3301882366&e|((g<<16|D>>>16)*3120437893&e)>>>16,D^=g>>>1,(D>>>0).toString(16).padStart(8,"0")+(g>>>0).toString(16).padStart(8,"0")}}v.MurmurHash3_64=Tt},(f,v,vt)=>{var tt;Object.defineProperty(v,"__esModule",{value:!0}),v.FontLoader=v.FontFaceObject=void 0;var d=vt(1);class P{constructor({ownerDocument:G=globalThis.document,styleElement:D=null}){U(this,tt,new Set);this._document=G,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(G){this.nativeFontFaces.add(G),this._document.fonts.add(G)}removeNativeFontFace(G){this.nativeFontFaces.delete(G),this._document.fonts.delete(G)}insertRule(G){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const D=this.styleElement.sheet;D.insertRule(G,D.cssRules.length)}clear(){for(const G of this.nativeFontFaces)this._document.fonts.delete(G);this.nativeFontFaces.clear(),t(this,tt).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont(G){if(!(!G||t(this,tt).has(G.loadedName))){if((0,d.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:D,src:g,style:j}=G,H=new FontFace(D,g,j);this.addNativeFontFace(H);try{await H.load(),t(this,tt).add(D)}catch{(0,d.warn)(`Cannot load system font: ${G.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(H)}return}(0,d.unreachable)("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(G){if(G.attached||G.missingFile&&!G.systemFontInfo)return;if(G.attached=!0,G.systemFontInfo){await this.loadSystemFont(G.systemFontInfo);return}if(this.isFontLoadingAPISupported){const g=G.createNativeFontFace();if(g){this.addNativeFontFace(g);try{await g.loaded}catch(j){throw(0,d.warn)(`Failed to load font '${g.family}': '${j}'.`),G.disableFontFace=!0,j}}return}const D=G.createFontFaceRule();if(D){if(this.insertRule(D),this.isSyncFontLoadingSupported)return;await new Promise(g=>{const j=this._queueLoadingCallback(g);this._prepareFontLoadEvent(G,j)})}}get isFontLoadingAPISupported(){var D;const G=!!((D=this._document)!=null&&D.fonts);return(0,d.shadow)(this,"isFontLoadingAPISupported",G)}get isSyncFontLoadingSupported(){let G=!1;return(d.isNodeJS||typeof navigator<"u"&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(G=!0),(0,d.shadow)(this,"isSyncFontLoadingSupported",G)}_queueLoadingCallback(G){function D(){for((0,d.assert)(!j.done,"completeRequest() cannot be called twice."),j.done=!0;g.length>0&&g[0].done;){const H=g.shift();setTimeout(H.callback,0)}}const{loadingRequests:g}=this,j={done:!1,complete:D,callback:G};return g.push(j),j}get _loadTestFont(){const G=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,d.shadow)(this,"_loadTestFont",G)}_prepareFontLoadEvent(G,D){function g(A,q){return A.charCodeAt(q)<<24|A.charCodeAt(q+1)<<16|A.charCodeAt(q+2)<<8|A.charCodeAt(q+3)&255}function j(A,q,Z,R){const nt=A.substring(0,q),pt=A.substring(q+Z);return nt+R+pt}let H,k;const y=this._document.createElement("canvas");y.width=1,y.height=1;const w=y.getContext("2d");let m=0;function E(A,q){if(++m>30){(0,d.warn)("Load test font never loaded."),q();return}if(w.font="30px "+A,w.fillText(".",0,20),w.getImageData(0,0,1,1).data[3]>0){q();return}setTimeout(E.bind(null,A,q))}const x=`lt${Date.now()}${this.loadTestFontId++}`;let M=this._loadTestFont;M=j(M,976,x.length,x);const o=16,u=1482184792;let L=g(M,o);for(H=0,k=x.length-3;H<k;H+=4)L=L-u+g(x,H)|0;H<x.length&&(L=L-u+g(x+"XXX",H)|0),M=j(M,o,4,(0,d.string32)(L));const _=`url(data:font/opentype;base64,${btoa(M)});`,l=`@font-face {font-family:"${x}";src:${_}}`;this.insertRule(l);const F=this._document.createElement("div");F.style.visibility="hidden",F.style.width=F.style.height="10px",F.style.position="absolute",F.style.top=F.style.left="0px";for(const A of[G.loadedName,x]){const q=this._document.createElement("span");q.textContent="Hi",q.style.fontFamily=A,F.append(q)}this._document.body.append(F),E(x,()=>{F.remove(),D.complete()})}}tt=new WeakMap,v.FontLoader=P;class e{constructor(G,{isEvalSupported:D=!0,disableFontFace:g=!1,ignoreErrors:j=!1,inspectFont:H=null}){this.compiledGlyphs=Object.create(null);for(const k in G)this[k]=G[k];this.isEvalSupported=D!==!1,this.disableFontFace=g===!0,this.ignoreErrors=j===!0,this._inspectFont=H}createNativeFontFace(){var D;if(!this.data||this.disableFontFace)return null;let G;if(!this.cssFontInfo)G=new FontFace(this.loadedName,this.data,{});else{const g={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(g.style=`oblique ${this.cssFontInfo.italicAngle}deg`),G=new FontFace(this.cssFontInfo.fontFamily,this.data,g)}return(D=this._inspectFont)==null||D.call(this,this),G}createFontFaceRule(){var j;if(!this.data||this.disableFontFace)return null;const G=(0,d.bytesToString)(this.data),D=`url(data:${this.mimetype};base64,${btoa(G)});`;let g;if(!this.cssFontInfo)g=`@font-face {font-family:"${this.loadedName}";src:${D}}`;else{let H=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(H+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),g=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${H}src:${D}}`}return(j=this._inspectFont)==null||j.call(this,this,D),g}getPathGenerator(G,D){if(this.compiledGlyphs[D]!==void 0)return this.compiledGlyphs[D];let g;try{g=G.get(this.loadedName+"_path_"+D)}catch(j){if(!this.ignoreErrors)throw j;return(0,d.warn)(`getPathGenerator - ignoring character: "${j}".`),this.compiledGlyphs[D]=function(H,k){}}if(this.isEvalSupported&&d.FeatureTest.isEvalSupported){const j=[];for(const H of g){const k=H.args!==void 0?H.args.join(","):"";j.push("c.",H.cmd,"(",k,`);
`)}return this.compiledGlyphs[D]=new Function("c","size",j.join(""))}return this.compiledGlyphs[D]=function(j,H){for(const k of g)k.cmd==="scale"&&(k.args=[H,-H]),j[k.cmd].apply(j,k.args)}}}v.FontFaceObject=e},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.NodeStandardFontDataFactory=v.NodeFilterFactory=v.NodeCanvasFactory=v.NodeCMapReaderFactory=void 0;var d=vt(7);vt(1);const P=function(D){return new Promise((g,j)=>{require$$0.readFile(D,(k,y)=>{if(k||!y){j(new Error(k));return}g(new Uint8Array(y))})})};class e extends d.BaseFilterFactory{}v.NodeFilterFactory=e;class tt extends d.BaseCanvasFactory{_createCanvas(g,j){return require$$0.createCanvas(g,j)}}v.NodeCanvasFactory=tt;class Tt extends d.BaseCMapReaderFactory{_fetchData(g,j){return P(g).then(H=>({cMapData:H,compressionType:j}))}}v.NodeCMapReaderFactory=Tt;class G extends d.BaseStandardFontDataFactory{_fetchData(g){return P(g)}}v.NodeStandardFontDataFactory=G},(f,v,vt)=>{var Y,$i,kt,Yi;Object.defineProperty(v,"__esModule",{value:!0}),v.CanvasGraphics=void 0;var d=vt(1),P=vt(6),e=vt(12),tt=vt(13);const Tt=16,G=100,D=4096,g=15,j=10,H=1e3,k=16;function y(T,i){if(T._removeMirroring)throw new Error("Context is already forwarding operations.");T.__originalSave=T.save,T.__originalRestore=T.restore,T.__originalRotate=T.rotate,T.__originalScale=T.scale,T.__originalTranslate=T.translate,T.__originalTransform=T.transform,T.__originalSetTransform=T.setTransform,T.__originalResetTransform=T.resetTransform,T.__originalClip=T.clip,T.__originalMoveTo=T.moveTo,T.__originalLineTo=T.lineTo,T.__originalBezierCurveTo=T.bezierCurveTo,T.__originalRect=T.rect,T.__originalClosePath=T.closePath,T.__originalBeginPath=T.beginPath,T._removeMirroring=()=>{T.save=T.__originalSave,T.restore=T.__originalRestore,T.rotate=T.__originalRotate,T.scale=T.__originalScale,T.translate=T.__originalTranslate,T.transform=T.__originalTransform,T.setTransform=T.__originalSetTransform,T.resetTransform=T.__originalResetTransform,T.clip=T.__originalClip,T.moveTo=T.__originalMoveTo,T.lineTo=T.__originalLineTo,T.bezierCurveTo=T.__originalBezierCurveTo,T.rect=T.__originalRect,T.closePath=T.__originalClosePath,T.beginPath=T.__originalBeginPath,delete T._removeMirroring},T.save=function(){i.save(),this.__originalSave()},T.restore=function(){i.restore(),this.__originalRestore()},T.translate=function(s,c){i.translate(s,c),this.__originalTranslate(s,c)},T.scale=function(s,c){i.scale(s,c),this.__originalScale(s,c)},T.transform=function(s,c,h,S,I,z){i.transform(s,c,h,S,I,z),this.__originalTransform(s,c,h,S,I,z)},T.setTransform=function(s,c,h,S,I,z){i.setTransform(s,c,h,S,I,z),this.__originalSetTransform(s,c,h,S,I,z)},T.resetTransform=function(){i.resetTransform(),this.__originalResetTransform()},T.rotate=function(s){i.rotate(s),this.__originalRotate(s)},T.clip=function(s){i.clip(s),this.__originalClip(s)},T.moveTo=function(a,s){i.moveTo(a,s),this.__originalMoveTo(a,s)},T.lineTo=function(a,s){i.lineTo(a,s),this.__originalLineTo(a,s)},T.bezierCurveTo=function(a,s,c,h,S,I){i.bezierCurveTo(a,s,c,h,S,I),this.__originalBezierCurveTo(a,s,c,h,S,I)},T.rect=function(a,s,c,h){i.rect(a,s,c,h),this.__originalRect(a,s,c,h)},T.closePath=function(){i.closePath(),this.__originalClosePath()},T.beginPath=function(){i.beginPath(),this.__originalBeginPath()}}class w{constructor(i){this.canvasFactory=i,this.cache=Object.create(null)}getCanvas(i,a,s){let c;return this.cache[i]!==void 0?(c=this.cache[i],this.canvasFactory.reset(c,a,s)):(c=this.canvasFactory.create(a,s),this.cache[i]=c),c}delete(i){delete this.cache[i]}clear(){for(const i in this.cache){const a=this.cache[i];this.canvasFactory.destroy(a),delete this.cache[i]}}}function m(T,i,a,s,c,h,S,I,z,dt){const[ct,mt,Et,Dt,wt,$t]=(0,P.getCurrentTransform)(T);if(mt===0&&Et===0){const Jt=S*ct+wt,Ct=Math.round(Jt),ht=I*Dt+$t,at=Math.round(ht),St=(S+z)*ct+wt,jt=Math.abs(Math.round(St)-Ct)||1,Ht=(I+dt)*Dt+$t,le=Math.abs(Math.round(Ht)-at)||1;return T.setTransform(Math.sign(ct),0,0,Math.sign(Dt),Ct,at),T.drawImage(i,a,s,c,h,0,0,jt,le),T.setTransform(ct,mt,Et,Dt,wt,$t),[jt,le]}if(ct===0&&Dt===0){const Jt=I*Et+wt,Ct=Math.round(Jt),ht=S*mt+$t,at=Math.round(ht),St=(I+dt)*Et+wt,jt=Math.abs(Math.round(St)-Ct)||1,Ht=(S+z)*mt+$t,le=Math.abs(Math.round(Ht)-at)||1;return T.setTransform(0,Math.sign(mt),Math.sign(Et),0,Ct,at),T.drawImage(i,a,s,c,h,0,0,le,jt),T.setTransform(ct,mt,Et,Dt,wt,$t),[le,jt]}T.drawImage(i,a,s,c,h,S,I,z,dt);const ie=Math.hypot(ct,mt),It=Math.hypot(Et,Dt);return[ie*z,It*dt]}function E(T){const{width:i,height:a}=T;if(i>H||a>H)return null;const s=1e3,c=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),h=i+1;let S=new Uint8Array(h*(a+1)),I,z,dt;const ct=i+7&-8;let mt=new Uint8Array(ct*a),Et=0;for(const It of T.data){let Jt=128;for(;Jt>0;)mt[Et++]=It&Jt?0:255,Jt>>=1}let Dt=0;for(Et=0,mt[Et]!==0&&(S[0]=1,++Dt),z=1;z<i;z++)mt[Et]!==mt[Et+1]&&(S[z]=mt[Et]?2:1,++Dt),Et++;for(mt[Et]!==0&&(S[z]=2,++Dt),I=1;I<a;I++){Et=I*ct,dt=I*h,mt[Et-ct]!==mt[Et]&&(S[dt]=mt[Et]?1:8,++Dt);let It=(mt[Et]?4:0)+(mt[Et-ct]?8:0);for(z=1;z<i;z++)It=(It>>2)+(mt[Et+1]?4:0)+(mt[Et-ct+1]?8:0),c[It]&&(S[dt+z]=c[It],++Dt),Et++;if(mt[Et-ct]!==mt[Et]&&(S[dt+z]=mt[Et]?2:4,++Dt),Dt>s)return null}for(Et=ct*(a-1),dt=I*h,mt[Et]!==0&&(S[dt]=8,++Dt),z=1;z<i;z++)mt[Et]!==mt[Et+1]&&(S[dt+z]=mt[Et]?4:8,++Dt),Et++;if(mt[Et]!==0&&(S[dt+z]=4,++Dt),Dt>s)return null;const wt=new Int32Array([0,h,-1,0,-h,0,0,0,1]),$t=new Path2D;for(I=0;Dt&&I<=a;I++){let It=I*h;const Jt=It+i;for(;It<Jt&&!S[It];)It++;if(It===Jt)continue;$t.moveTo(It%h,I);const Ct=It;let ht=S[It];do{const at=wt[ht];do It+=at;while(!S[It]);const St=S[It];St!==5&&St!==10?(ht=St,S[It]=0):(ht=St&51*ht>>4,S[It]&=ht>>2|ht<<2),$t.lineTo(It%h,It/h|0),S[It]||--Dt}while(Ct!==It);--I}return mt=null,S=null,function(It){It.save(),It.scale(1/i,-1/a),It.translate(0,-a),It.fill($t),It.beginPath(),It.restore()}}class x{constructor(i,a){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=d.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=d.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=d.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,i,a])}clone(){const i=Object.create(this);return i.clipBox=this.clipBox.slice(),i}setCurrentPoint(i,a){this.x=i,this.y=a}updatePathMinMax(i,a,s){[a,s]=d.Util.applyTransform([a,s],i),this.minX=Math.min(this.minX,a),this.minY=Math.min(this.minY,s),this.maxX=Math.max(this.maxX,a),this.maxY=Math.max(this.maxY,s)}updateRectMinMax(i,a){const s=d.Util.applyTransform(a,i),c=d.Util.applyTransform(a.slice(2),i);this.minX=Math.min(this.minX,s[0],c[0]),this.minY=Math.min(this.minY,s[1],c[1]),this.maxX=Math.max(this.maxX,s[0],c[0]),this.maxY=Math.max(this.maxY,s[1],c[1])}updateScalingPathMinMax(i,a){d.Util.scaleMinMax(i,a),this.minX=Math.min(this.minX,a[0]),this.maxX=Math.max(this.maxX,a[1]),this.minY=Math.min(this.minY,a[2]),this.maxY=Math.max(this.maxY,a[3])}updateCurvePathMinMax(i,a,s,c,h,S,I,z,dt,ct){const mt=d.Util.bezierBoundingBox(a,s,c,h,S,I,z,dt);if(ct){ct[0]=Math.min(ct[0],mt[0],mt[2]),ct[1]=Math.max(ct[1],mt[0],mt[2]),ct[2]=Math.min(ct[2],mt[1],mt[3]),ct[3]=Math.max(ct[3],mt[1],mt[3]);return}this.updateRectMinMax(i,mt)}getPathBoundingBox(i=e.PathType.FILL,a=null){const s=[this.minX,this.minY,this.maxX,this.maxY];if(i===e.PathType.STROKE){a||(0,d.unreachable)("Stroke bounding box must include transform.");const c=d.Util.singularValueDecompose2dScale(a),h=c[0]*this.lineWidth/2,S=c[1]*this.lineWidth/2;s[0]-=h,s[1]-=S,s[2]+=h,s[3]+=S}return s}updateClipFromPath(){const i=d.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(i||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(i){this.clipBox=i,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(i=e.PathType.FILL,a=null){return d.Util.intersect(this.clipBox,this.getPathBoundingBox(i,a))}}function M(T,i){if(typeof ImageData<"u"&&i instanceof ImageData){T.putImageData(i,0,0);return}const a=i.height,s=i.width,c=a%k,h=(a-c)/k,S=c===0?h:h+1,I=T.createImageData(s,k);let z=0,dt;const ct=i.data,mt=I.data;let Et,Dt,wt,$t;if(i.kind===d.ImageKind.GRAYSCALE_1BPP){const ie=ct.byteLength,It=new Uint32Array(mt.buffer,0,mt.byteLength>>2),Jt=It.length,Ct=s+7>>3,ht=4294967295,at=d.FeatureTest.isLittleEndian?4278190080:255;for(Et=0;Et<S;Et++){for(wt=Et<h?k:c,dt=0,Dt=0;Dt<wt;Dt++){const St=ie-z;let jt=0;const Ht=St>Ct?s:St*8-7,le=Ht&-8;let fe=0,he=0;for(;jt<le;jt+=8)he=ct[z++],It[dt++]=he&128?ht:at,It[dt++]=he&64?ht:at,It[dt++]=he&32?ht:at,It[dt++]=he&16?ht:at,It[dt++]=he&8?ht:at,It[dt++]=he&4?ht:at,It[dt++]=he&2?ht:at,It[dt++]=he&1?ht:at;for(;jt<Ht;jt++)fe===0&&(he=ct[z++],fe=128),It[dt++]=he&fe?ht:at,fe>>=1}for(;dt<Jt;)It[dt++]=0;T.putImageData(I,0,Et*k)}}else if(i.kind===d.ImageKind.RGBA_32BPP){for(Dt=0,$t=s*k*4,Et=0;Et<h;Et++)mt.set(ct.subarray(z,z+$t)),z+=$t,T.putImageData(I,0,Dt),Dt+=k;Et<S&&($t=s*c*4,mt.set(ct.subarray(z,z+$t)),T.putImageData(I,0,Dt))}else if(i.kind===d.ImageKind.RGB_24BPP)for(wt=k,$t=s*wt,Et=0;Et<S;Et++){for(Et>=h&&(wt=c,$t=s*wt),dt=0,Dt=$t;Dt--;)mt[dt++]=ct[z++],mt[dt++]=ct[z++],mt[dt++]=ct[z++],mt[dt++]=255;T.putImageData(I,0,Et*k)}else throw new Error(`bad image kind: ${i.kind}`)}function C(T,i){if(i.bitmap){T.drawImage(i.bitmap,0,0);return}const a=i.height,s=i.width,c=a%k,h=(a-c)/k,S=c===0?h:h+1,I=T.createImageData(s,k);let z=0;const dt=i.data,ct=I.data;for(let mt=0;mt<S;mt++){const Et=mt<h?k:c;({srcPos:z}=(0,tt.convertBlackAndWhiteToRGBA)({src:dt,srcPos:z,dest:ct,width:s,height:Et,nonBlackColor:0})),T.putImageData(I,0,mt*k)}}function o(T,i){const a=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of a)T[s]!==void 0&&(i[s]=T[s]);T.setLineDash!==void 0&&(i.setLineDash(T.getLineDash()),i.lineDashOffset=T.lineDashOffset)}function u(T){if(T.strokeStyle=T.fillStyle="#000000",T.fillRule="nonzero",T.globalAlpha=1,T.lineWidth=1,T.lineCap="butt",T.lineJoin="miter",T.miterLimit=10,T.globalCompositeOperation="source-over",T.font="10px sans-serif",T.setLineDash!==void 0&&(T.setLineDash([]),T.lineDashOffset=0),!d.isNodeJS){const{filter:i}=T;i!=="none"&&i!==""&&(T.filter="none")}}function L(T,i,a,s){const c=T.length;for(let h=3;h<c;h+=4){const S=T[h];if(S===0)T[h-3]=i,T[h-2]=a,T[h-1]=s;else if(S<255){const I=255-S;T[h-3]=T[h-3]*S+i*I>>8,T[h-2]=T[h-2]*S+a*I>>8,T[h-1]=T[h-1]*S+s*I>>8}}}function _(T,i,a){const s=T.length,c=1/255;for(let h=3;h<s;h+=4){const S=a?a[T[h]]:T[h];i[h]=i[h]*S*c|0}}function l(T,i,a){const s=T.length;for(let c=3;c<s;c+=4){const h=T[c-3]*77+T[c-2]*152+T[c-1]*28;i[c]=a?i[c]*a[h>>8]>>8:i[c]*h>>16}}function F(T,i,a,s,c,h,S,I,z,dt,ct){const mt=!!h,Et=mt?h[0]:0,Dt=mt?h[1]:0,wt=mt?h[2]:0,$t=c==="Luminosity"?l:_,It=Math.min(s,Math.ceil(1048576/a));for(let Jt=0;Jt<s;Jt+=It){const Ct=Math.min(It,s-Jt),ht=T.getImageData(I-dt,Jt+(z-ct),a,Ct),at=i.getImageData(I,Jt+z,a,Ct);mt&&L(ht.data,Et,Dt,wt),$t(ht.data,at.data,S),i.putImageData(at,I,Jt+z)}}function A(T,i,a,s){const c=s[0],h=s[1],S=s[2]-c,I=s[3]-h;S===0||I===0||(F(i.context,a,S,I,i.subtype,i.backdrop,i.transferMap,c,h,i.offsetX,i.offsetY),T.save(),T.globalAlpha=1,T.globalCompositeOperation="source-over",T.setTransform(1,0,0,1,0,0),T.drawImage(a.canvas,0,0),T.restore())}function q(T,i){const a=d.Util.singularValueDecompose2dScale(T);a[0]=Math.fround(a[0]),a[1]=Math.fround(a[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*P.PixelsPerInch.PDF_TO_CSS_UNITS);return i!==void 0?i:a[0]<=s||a[1]<=s}const Z=["butt","round","square"],R=["miter","round","bevel"],nt={},pt={},Gt=class Gt{constructor(i,a,s,c,h,{optionalContentConfig:S,markedContentStack:I=null},z,dt){U(this,Y);U(this,kt);this.ctx=i,this.current=new x(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=a,this.objs=s,this.canvasFactory=c,this.filterFactory=h,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=I||[],this.optionalContentConfig=S,this.cachedCanvases=new w(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=z,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=dt,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(i,a=null){return typeof i=="string"?i.startsWith("g_")?this.commonObjs.get(i):this.objs.get(i):a}beginDrawing({transform:i,viewport:a,transparency:s=!1,background:c=null}){const h=this.ctx.canvas.width,S=this.ctx.canvas.height,I=this.ctx.fillStyle;if(this.ctx.fillStyle=c||"#ffffff",this.ctx.fillRect(0,0,h,S),this.ctx.fillStyle=I,s){const z=this.cachedCanvases.getCanvas("transparent",h,S);this.compositeCtx=this.ctx,this.transparentCanvas=z.canvas,this.ctx=z.context,this.ctx.save(),this.ctx.transform(...(0,P.getCurrentTransform)(this.compositeCtx))}this.ctx.save(),u(this.ctx),i&&(this.ctx.transform(...i),this.outputScaleX=i[0],this.outputScaleY=i[0]),this.ctx.transform(...a.transform),this.viewportScale=a.scale,this.baseTransform=(0,P.getCurrentTransform)(this.ctx)}executeOperatorList(i,a,s,c){const h=i.argsArray,S=i.fnArray;let I=a||0;const z=h.length;if(z===I)return I;const dt=z-I>j&&typeof s=="function",ct=dt?Date.now()+g:0;let mt=0;const Et=this.commonObjs,Dt=this.objs;let wt;for(;;){if(c!==void 0&&I===c.nextBreakPoint)return c.breakIt(I,s),I;if(wt=S[I],wt!==d.OPS.dependency)this[wt].apply(this,h[I]);else for(const $t of h[I]){const ie=$t.startsWith("g_")?Et:Dt;if(!ie.has($t))return ie.get($t,s),I}if(I++,I===z)return I;if(dt&&++mt>j){if(Date.now()>ct)return s(),I;mt=0}}}endDrawing(){Q(this,Y,$i).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const i of this._cachedBitmapsMap.values()){for(const a of i.values())typeof HTMLCanvasElement<"u"&&a instanceof HTMLCanvasElement&&(a.width=a.height=0);i.clear()}this._cachedBitmapsMap.clear(),Q(this,kt,Yi).call(this)}_scaleImage(i,a){const s=i.width,c=i.height;let h=Math.max(Math.hypot(a[0],a[1]),1),S=Math.max(Math.hypot(a[2],a[3]),1),I=s,z=c,dt="prescale1",ct,mt;for(;h>2&&I>1||S>2&&z>1;){let Et=I,Dt=z;h>2&&I>1&&(Et=I>=16384?Math.floor(I/2)-1||1:Math.ceil(I/2),h/=I/Et),S>2&&z>1&&(Dt=z>=16384?Math.floor(z/2)-1||1:Math.ceil(z)/2,S/=z/Dt),ct=this.cachedCanvases.getCanvas(dt,Et,Dt),mt=ct.context,mt.clearRect(0,0,Et,Dt),mt.drawImage(i,0,0,I,z,0,0,Et,Dt),i=ct.canvas,I=Et,z=Dt,dt=dt==="prescale1"?"prescale2":"prescale1"}return{img:i,paintWidth:I,paintHeight:z}}_createMaskCanvas(i){const a=this.ctx,{width:s,height:c}=i,h=this.current.fillColor,S=this.current.patternFill,I=(0,P.getCurrentTransform)(a);let z,dt,ct,mt;if((i.bitmap||i.data)&&i.count>1){const jt=i.bitmap||i.data.buffer;dt=JSON.stringify(S?I:[I.slice(0,4),h]),z=this._cachedBitmapsMap.get(jt),z||(z=new Map,this._cachedBitmapsMap.set(jt,z));const Ht=z.get(dt);if(Ht&&!S){const le=Math.round(Math.min(I[0],I[2])+I[4]),fe=Math.round(Math.min(I[1],I[3])+I[5]);return{canvas:Ht,offsetX:le,offsetY:fe}}ct=Ht}ct||(mt=this.cachedCanvases.getCanvas("maskCanvas",s,c),C(mt.context,i));let Et=d.Util.transform(I,[1/s,0,0,-1/c,0,0]);Et=d.Util.transform(Et,[1,0,0,1,0,-c]);const Dt=d.Util.applyTransform([0,0],Et),wt=d.Util.applyTransform([s,c],Et),$t=d.Util.normalizeRect([Dt[0],Dt[1],wt[0],wt[1]]),ie=Math.round($t[2]-$t[0])||1,It=Math.round($t[3]-$t[1])||1,Jt=this.cachedCanvases.getCanvas("fillCanvas",ie,It),Ct=Jt.context,ht=Math.min(Dt[0],wt[0]),at=Math.min(Dt[1],wt[1]);Ct.translate(-ht,-at),Ct.transform(...Et),ct||(ct=this._scaleImage(mt.canvas,(0,P.getCurrentTransformInverse)(Ct)),ct=ct.img,z&&S&&z.set(dt,ct)),Ct.imageSmoothingEnabled=q((0,P.getCurrentTransform)(Ct),i.interpolate),m(Ct,ct,0,0,ct.width,ct.height,0,0,s,c),Ct.globalCompositeOperation="source-in";const St=d.Util.transform((0,P.getCurrentTransformInverse)(Ct),[1,0,0,1,-ht,-at]);return Ct.fillStyle=S?h.getPattern(a,this,St,e.PathType.FILL):h,Ct.fillRect(0,0,s,c),z&&!S&&(this.cachedCanvases.delete("fillCanvas"),z.set(dt,Jt.canvas)),{canvas:Jt.canvas,offsetX:Math.round(ht),offsetY:Math.round(at)}}setLineWidth(i){i!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=i,this.ctx.lineWidth=i}setLineCap(i){this.ctx.lineCap=Z[i]}setLineJoin(i){this.ctx.lineJoin=R[i]}setMiterLimit(i){this.ctx.miterLimit=i}setDash(i,a){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(i),s.lineDashOffset=a)}setRenderingIntent(i){}setFlatness(i){}setGState(i){for(const[a,s]of i)switch(a){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.current.fillAlpha=s,this.ctx.globalAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const i=this.inSMaskMode;this.current.activeSMask&&!i?this.beginSMaskMode():!this.current.activeSMask&&i&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const i=this.ctx.canvas.width,a=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,c=this.cachedCanvases.getCanvas(s,i,a);this.suspendedCtx=this.ctx,this.ctx=c.context;const h=this.ctx;h.setTransform(...(0,P.getCurrentTransform)(this.suspendedCtx)),o(this.suspendedCtx,h),y(h,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),o(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(i){if(!this.current.activeSMask)return;i?(i[0]=Math.floor(i[0]),i[1]=Math.floor(i[1]),i[2]=Math.ceil(i[2]),i[3]=Math.ceil(i[3])):i=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const a=this.current.activeSMask,s=this.suspendedCtx;A(s,a,this.ctx,i),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}save(){this.inSMaskMode?(o(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const i=this.current;this.stateStack.push(i),this.current=i.clone()}restore(){this.stateStack.length===0&&this.inSMaskMode&&this.endSMaskMode(),this.stateStack.length!==0&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),o(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(i,a,s,c,h,S){this.ctx.transform(i,a,s,c,h,S),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(i,a,s){const c=this.ctx,h=this.current;let S=h.x,I=h.y,z,dt;const ct=(0,P.getCurrentTransform)(c),mt=ct[0]===0&&ct[3]===0||ct[1]===0&&ct[2]===0,Et=mt?s.slice(0):null;for(let Dt=0,wt=0,$t=i.length;Dt<$t;Dt++)switch(i[Dt]|0){case d.OPS.rectangle:S=a[wt++],I=a[wt++];const ie=a[wt++],It=a[wt++],Jt=S+ie,Ct=I+It;c.moveTo(S,I),ie===0||It===0?c.lineTo(Jt,Ct):(c.lineTo(Jt,I),c.lineTo(Jt,Ct),c.lineTo(S,Ct)),mt||h.updateRectMinMax(ct,[S,I,Jt,Ct]),c.closePath();break;case d.OPS.moveTo:S=a[wt++],I=a[wt++],c.moveTo(S,I),mt||h.updatePathMinMax(ct,S,I);break;case d.OPS.lineTo:S=a[wt++],I=a[wt++],c.lineTo(S,I),mt||h.updatePathMinMax(ct,S,I);break;case d.OPS.curveTo:z=S,dt=I,S=a[wt+4],I=a[wt+5],c.bezierCurveTo(a[wt],a[wt+1],a[wt+2],a[wt+3],S,I),h.updateCurvePathMinMax(ct,z,dt,a[wt],a[wt+1],a[wt+2],a[wt+3],S,I,Et),wt+=6;break;case d.OPS.curveTo2:z=S,dt=I,c.bezierCurveTo(S,I,a[wt],a[wt+1],a[wt+2],a[wt+3]),h.updateCurvePathMinMax(ct,z,dt,S,I,a[wt],a[wt+1],a[wt+2],a[wt+3],Et),S=a[wt+2],I=a[wt+3],wt+=4;break;case d.OPS.curveTo3:z=S,dt=I,S=a[wt+2],I=a[wt+3],c.bezierCurveTo(a[wt],a[wt+1],S,I,S,I),h.updateCurvePathMinMax(ct,z,dt,a[wt],a[wt+1],S,I,S,I,Et),wt+=4;break;case d.OPS.closePath:c.closePath();break}mt&&h.updateScalingPathMinMax(ct,Et),h.setCurrentPoint(S,I)}closePath(){this.ctx.closePath()}stroke(i=!0){const a=this.ctx,s=this.current.strokeColor;a.globalAlpha=this.current.strokeAlpha,this.contentVisible&&(typeof s=="object"&&(s!=null&&s.getPattern)?(a.save(),a.strokeStyle=s.getPattern(a,this,(0,P.getCurrentTransformInverse)(a),e.PathType.STROKE),this.rescaleAndStroke(!1),a.restore()):this.rescaleAndStroke(!0)),i&&this.consumePath(this.current.getClippedPathBoundingBox()),a.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(i=!0){const a=this.ctx,s=this.current.fillColor,c=this.current.patternFill;let h=!1;c&&(a.save(),a.fillStyle=s.getPattern(a,this,(0,P.getCurrentTransformInverse)(a),e.PathType.FILL),h=!0);const S=this.current.getClippedPathBoundingBox();this.contentVisible&&S!==null&&(this.pendingEOFill?(a.fill("evenodd"),this.pendingEOFill=!1):a.fill()),h&&a.restore(),i&&this.consumePath(S)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=nt}eoClip(){this.pendingClip=pt}beginText(){this.current.textMatrix=d.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const i=this.pendingTextPaths,a=this.ctx;if(i===void 0){a.beginPath();return}a.save(),a.beginPath();for(const s of i)a.setTransform(...s.transform),a.translate(s.x,s.y),s.addToPath(a,s.fontSize);a.restore(),a.clip(),a.beginPath(),delete this.pendingTextPaths}setCharSpacing(i){this.current.charSpacing=i}setWordSpacing(i){this.current.wordSpacing=i}setHScale(i){this.current.textHScale=i/100}setLeading(i){this.current.leading=-i}setFont(i,a){var ct;const s=this.commonObjs.get(i),c=this.current;if(!s)throw new Error(`Can't find font for ${i}`);if(c.fontMatrix=s.fontMatrix||d.FONT_IDENTITY_MATRIX,(c.fontMatrix[0]===0||c.fontMatrix[3]===0)&&(0,d.warn)("Invalid font matrix for font "+i),a<0?(a=-a,c.fontDirection=-1):c.fontDirection=1,this.current.font=s,this.current.fontSize=a,s.isType3Font)return;const h=s.loadedName||"sans-serif",S=((ct=s.systemFontInfo)==null?void 0:ct.css)||`"${h}", ${s.fallbackName}`;let I="normal";s.black?I="900":s.bold&&(I="bold");const z=s.italic?"italic":"normal";let dt=a;a<Tt?dt=Tt:a>G&&(dt=G),this.current.fontSizeScale=a/dt,this.ctx.font=`${z} ${I} ${dt}px ${S}`}setTextRenderingMode(i){this.current.textRenderingMode=i}setTextRise(i){this.current.textRise=i}moveText(i,a){this.current.x=this.current.lineX+=i,this.current.y=this.current.lineY+=a}setLeadingMoveText(i,a){this.setLeading(-a),this.moveText(i,a)}setTextMatrix(i,a,s,c,h,S){this.current.textMatrix=[i,a,s,c,h,S],this.current.textMatrixScale=Math.hypot(i,a),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(i,a,s,c){const h=this.ctx,S=this.current,I=S.font,z=S.textRenderingMode,dt=S.fontSize/S.fontSizeScale,ct=z&d.TextRenderingMode.FILL_STROKE_MASK,mt=!!(z&d.TextRenderingMode.ADD_TO_PATH_FLAG),Et=S.patternFill&&!I.missingFile;let Dt;(I.disableFontFace||mt||Et)&&(Dt=I.getPathGenerator(this.commonObjs,i)),I.disableFontFace||Et?(h.save(),h.translate(a,s),h.beginPath(),Dt(h,dt),c&&h.setTransform(...c),(ct===d.TextRenderingMode.FILL||ct===d.TextRenderingMode.FILL_STROKE)&&h.fill(),(ct===d.TextRenderingMode.STROKE||ct===d.TextRenderingMode.FILL_STROKE)&&h.stroke(),h.restore()):((ct===d.TextRenderingMode.FILL||ct===d.TextRenderingMode.FILL_STROKE)&&h.fillText(i,a,s),(ct===d.TextRenderingMode.STROKE||ct===d.TextRenderingMode.FILL_STROKE)&&h.strokeText(i,a,s)),mt&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:(0,P.getCurrentTransform)(h),x:a,y:s,fontSize:dt,addToPath:Dt})}get isFontSubpixelAAEnabled(){const{context:i}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);i.scale(1.5,1),i.fillText("I",0,10);const a=i.getImageData(0,0,10,10).data;let s=!1;for(let c=3;c<a.length;c+=4)if(a[c]>0&&a[c]<255){s=!0;break}return(0,d.shadow)(this,"isFontSubpixelAAEnabled",s)}showText(i){const a=this.current,s=a.font;if(s.isType3Font)return this.showType3Text(i);const c=a.fontSize;if(c===0)return;const h=this.ctx,S=a.fontSizeScale,I=a.charSpacing,z=a.wordSpacing,dt=a.fontDirection,ct=a.textHScale*dt,mt=i.length,Et=s.vertical,Dt=Et?1:-1,wt=s.defaultVMetrics,$t=c*a.fontMatrix[0],ie=a.textRenderingMode===d.TextRenderingMode.FILL&&!s.disableFontFace&&!a.patternFill;h.save(),h.transform(...a.textMatrix),h.translate(a.x,a.y+a.textRise),dt>0?h.scale(ct,-1):h.scale(ct,1);let It;if(a.patternFill){h.save();const St=a.fillColor.getPattern(h,this,(0,P.getCurrentTransformInverse)(h),e.PathType.FILL);It=(0,P.getCurrentTransform)(h),h.restore(),h.fillStyle=St}let Jt=a.lineWidth;const Ct=a.textMatrixScale;if(Ct===0||Jt===0){const St=a.textRenderingMode&d.TextRenderingMode.FILL_STROKE_MASK;(St===d.TextRenderingMode.STROKE||St===d.TextRenderingMode.FILL_STROKE)&&(Jt=this.getSinglePixelWidth())}else Jt/=Ct;if(S!==1&&(h.scale(S,S),Jt/=S),h.lineWidth=Jt,s.isInvalidPDFjsFont){const St=[];let jt=0;for(const Ht of i)St.push(Ht.unicode),jt+=Ht.width;h.fillText(St.join(""),0,0),a.x+=jt*$t*ct,h.restore(),this.compose();return}let ht=0,at;for(at=0;at<mt;++at){const St=i[at];if(typeof St=="number"){ht+=Dt*St*c/1e3;continue}let jt=!1;const Ht=(St.isSpace?z:0)+I,le=St.fontChar,fe=St.accent;let he,ge,_e=St.width;if(Et){const we=St.vmetric||wt,_t=-(St.vmetric?we[1]:_e*.5)*$t,et=we[2]*$t;_e=we?-we[0]:_e,he=_t/S,ge=(ht+et)/S}else he=ht/S,ge=0;if(s.remeasure&&_e>0){const we=h.measureText(le).width*1e3/c*S;if(_e<we&&this.isFontSubpixelAAEnabled){const _t=_e/we;jt=!0,h.save(),h.scale(_t,1),he/=_t}else _e!==we&&(he+=(_e-we)/2e3*c/S)}if(this.contentVisible&&(St.isInFont||s.missingFile)){if(ie&&!fe)h.fillText(le,he,ge);else if(this.paintChar(le,he,ge,It),fe){const we=he+c*fe.offset.x/S,_t=ge-c*fe.offset.y/S;this.paintChar(fe.fontChar,we,_t,It)}}const re=Et?_e*$t-Ht*dt:_e*$t+Ht*dt;ht+=re,jt&&h.restore()}Et?a.y-=ht:a.x+=ht*ct,h.restore(),this.compose()}showType3Text(i){const a=this.ctx,s=this.current,c=s.font,h=s.fontSize,S=s.fontDirection,I=c.vertical?1:-1,z=s.charSpacing,dt=s.wordSpacing,ct=s.textHScale*S,mt=s.fontMatrix||d.FONT_IDENTITY_MATRIX,Et=i.length,Dt=s.textRenderingMode===d.TextRenderingMode.INVISIBLE;let wt,$t,ie,It;if(!(Dt||h===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,a.save(),a.transform(...s.textMatrix),a.translate(s.x,s.y),a.scale(ct,S),wt=0;wt<Et;++wt){if($t=i[wt],typeof $t=="number"){It=I*$t*h/1e3,this.ctx.translate(It,0),s.x+=It*ct;continue}const Jt=($t.isSpace?dt:0)+z,Ct=c.charProcOperatorList[$t.operatorListId];if(!Ct){(0,d.warn)(`Type3 character "${$t.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=$t,this.save(),a.scale(h,h),a.transform(...mt),this.executeOperatorList(Ct),this.restore()),ie=d.Util.applyTransform([$t.width,0],mt)[0]*h+Jt,a.translate(ie,0),s.x+=ie*ct}a.restore(),this.processingType3=null}}setCharWidth(i,a){}setCharWidthAndBounds(i,a,s,c,h,S){this.ctx.rect(s,c,h-s,S-c),this.ctx.clip(),this.endPath()}getColorN_Pattern(i){let a;if(i[0]==="TilingPattern"){const s=i[1],c=this.baseTransform||(0,P.getCurrentTransform)(this.ctx),h={createCanvasGraphics:S=>new Gt(S,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};a=new e.TilingPattern(i,s,this.ctx,h,c)}else a=this._getPattern(i[1],i[2]);return a}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(i,a,s){const c=d.Util.makeHexColor(i,a,s);this.ctx.strokeStyle=c,this.current.strokeColor=c}setFillRGBColor(i,a,s){const c=d.Util.makeHexColor(i,a,s);this.ctx.fillStyle=c,this.current.fillColor=c,this.current.patternFill=!1}_getPattern(i,a=null){let s;return this.cachedPatterns.has(i)?s=this.cachedPatterns.get(i):(s=(0,e.getShadingPattern)(this.getObject(i)),this.cachedPatterns.set(i,s)),a&&(s.matrix=a),s}shadingFill(i){if(!this.contentVisible)return;const a=this.ctx;this.save();const s=this._getPattern(i);a.fillStyle=s.getPattern(a,this,(0,P.getCurrentTransformInverse)(a),e.PathType.SHADING);const c=(0,P.getCurrentTransformInverse)(a);if(c){const{width:h,height:S}=a.canvas,[I,z,dt,ct]=d.Util.getAxialAlignedBoundingBox([0,0,h,S],c);this.ctx.fillRect(I,z,dt-I,ct-z)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){(0,d.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,d.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(i,a){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(i)&&i.length===6&&this.transform(...i),this.baseTransform=(0,P.getCurrentTransform)(this.ctx),a)){const s=a[2]-a[0],c=a[3]-a[1];this.ctx.rect(a[0],a[1],s,c),this.current.updateRectMinMax((0,P.getCurrentTransform)(this.ctx),a),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(i){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const a=this.ctx;i.isolated||(0,d.info)("TODO: Support non-isolated groups."),i.knockout&&(0,d.warn)("Knockout groups not supported.");const s=(0,P.getCurrentTransform)(a);if(i.matrix&&a.transform(...i.matrix),!i.bbox)throw new Error("Bounding box is required.");let c=d.Util.getAxialAlignedBoundingBox(i.bbox,(0,P.getCurrentTransform)(a));const h=[0,0,a.canvas.width,a.canvas.height];c=d.Util.intersect(c,h)||[0,0,0,0];const S=Math.floor(c[0]),I=Math.floor(c[1]);let z=Math.max(Math.ceil(c[2])-S,1),dt=Math.max(Math.ceil(c[3])-I,1),ct=1,mt=1;z>D&&(ct=z/D,z=D),dt>D&&(mt=dt/D,dt=D),this.current.startNewPathAndClipBox([0,0,z,dt]);let Et="groupAt"+this.groupLevel;i.smask&&(Et+="_smask_"+this.smaskCounter++%2);const Dt=this.cachedCanvases.getCanvas(Et,z,dt),wt=Dt.context;wt.scale(1/ct,1/mt),wt.translate(-S,-I),wt.transform(...s),i.smask?this.smaskStack.push({canvas:Dt.canvas,context:wt,offsetX:S,offsetY:I,scaleX:ct,scaleY:mt,subtype:i.smask.subtype,backdrop:i.smask.backdrop,transferMap:i.smask.transferMap||null,startTransformInverse:null}):(a.setTransform(1,0,0,1,0,0),a.translate(S,I),a.scale(ct,mt),a.save()),o(a,wt),this.ctx=wt,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(a),this.groupLevel++}endGroup(i){if(!this.contentVisible)return;this.groupLevel--;const a=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,i.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const c=(0,P.getCurrentTransform)(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...c);const h=d.Util.getAxialAlignedBoundingBox([0,0,a.canvas.width,a.canvas.height],c);this.ctx.drawImage(a.canvas,0,0),this.ctx.restore(),this.compose(h)}}beginAnnotation(i,a,s,c,h){if(Q(this,Y,$i).call(this),u(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),Array.isArray(a)&&a.length===4){const S=a[2]-a[0],I=a[3]-a[1];if(h&&this.annotationCanvasMap){s=s.slice(),s[4]-=a[0],s[5]-=a[1],a=a.slice(),a[0]=a[1]=0,a[2]=S,a[3]=I;const[z,dt]=d.Util.singularValueDecompose2dScale((0,P.getCurrentTransform)(this.ctx)),{viewportScale:ct}=this,mt=Math.ceil(S*this.outputScaleX*ct),Et=Math.ceil(I*this.outputScaleY*ct);this.annotationCanvas=this.canvasFactory.create(mt,Et);const{canvas:Dt,context:wt}=this.annotationCanvas;this.annotationCanvasMap.set(i,Dt),this.annotationCanvas.savedCtx=this.ctx,this.ctx=wt,this.ctx.save(),this.ctx.setTransform(z,0,0,-dt,0,I*dt),u(this.ctx)}else u(this.ctx),this.ctx.rect(a[0],a[1],S,I),this.ctx.clip(),this.endPath()}this.current=new x(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...c)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),Q(this,kt,Yi).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(i){if(!this.contentVisible)return;const a=i.count;i=this.getObject(i.data,i),i.count=a;const s=this.ctx,c=this.processingType3;if(c&&(c.compiled===void 0&&(c.compiled=E(i)),c.compiled)){c.compiled(s);return}const h=this._createMaskCanvas(i),S=h.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(S,h.offsetX,h.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(i,a,s=0,c=0,h,S){if(!this.contentVisible)return;i=this.getObject(i.data,i);const I=this.ctx;I.save();const z=(0,P.getCurrentTransform)(I);I.transform(a,s,c,h,0,0);const dt=this._createMaskCanvas(i);I.setTransform(1,0,0,1,dt.offsetX-z[4],dt.offsetY-z[5]);for(let ct=0,mt=S.length;ct<mt;ct+=2){const Et=d.Util.transform(z,[a,s,c,h,S[ct],S[ct+1]]),[Dt,wt]=d.Util.applyTransform([0,0],Et);I.drawImage(dt.canvas,Dt,wt)}I.restore(),this.compose()}paintImageMaskXObjectGroup(i){if(!this.contentVisible)return;const a=this.ctx,s=this.current.fillColor,c=this.current.patternFill;for(const h of i){const{data:S,width:I,height:z,transform:dt}=h,ct=this.cachedCanvases.getCanvas("maskCanvas",I,z),mt=ct.context;mt.save();const Et=this.getObject(S,h);C(mt,Et),mt.globalCompositeOperation="source-in",mt.fillStyle=c?s.getPattern(mt,this,(0,P.getCurrentTransformInverse)(a),e.PathType.FILL):s,mt.fillRect(0,0,I,z),mt.restore(),a.save(),a.transform(...dt),a.scale(1,-1),m(a,ct.canvas,0,0,I,z,0,-1,1,1),a.restore()}this.compose()}paintImageXObject(i){if(!this.contentVisible)return;const a=this.getObject(i);if(!a){(0,d.warn)("Dependent image isn't ready yet");return}this.paintInlineImageXObject(a)}paintImageXObjectRepeat(i,a,s,c){if(!this.contentVisible)return;const h=this.getObject(i);if(!h){(0,d.warn)("Dependent image isn't ready yet");return}const S=h.width,I=h.height,z=[];for(let dt=0,ct=c.length;dt<ct;dt+=2)z.push({transform:[a,0,0,s,c[dt],c[dt+1]],x:0,y:0,w:S,h:I});this.paintInlineImageXObjectGroup(h,z)}applyTransferMapsToCanvas(i){return this.current.transferMaps!=="none"&&(i.filter=this.current.transferMaps,i.drawImage(i.canvas,0,0),i.filter="none"),i.canvas}applyTransferMapsToBitmap(i){if(this.current.transferMaps==="none")return i.bitmap;const{bitmap:a,width:s,height:c}=i,h=this.cachedCanvases.getCanvas("inlineImage",s,c),S=h.context;return S.filter=this.current.transferMaps,S.drawImage(a,0,0),S.filter="none",h.canvas}paintInlineImageXObject(i){if(!this.contentVisible)return;const a=i.width,s=i.height,c=this.ctx;if(this.save(),!d.isNodeJS){const{filter:I}=c;I!=="none"&&I!==""&&(c.filter="none")}c.scale(1/a,-1/s);let h;if(i.bitmap)h=this.applyTransferMapsToBitmap(i);else if(typeof HTMLElement=="function"&&i instanceof HTMLElement||!i.data)h=i;else{const z=this.cachedCanvases.getCanvas("inlineImage",a,s).context;M(z,i),h=this.applyTransferMapsToCanvas(z)}const S=this._scaleImage(h,(0,P.getCurrentTransformInverse)(c));c.imageSmoothingEnabled=q((0,P.getCurrentTransform)(c),i.interpolate),m(c,S.img,0,0,S.paintWidth,S.paintHeight,0,-s,a,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(i,a){if(!this.contentVisible)return;const s=this.ctx;let c;if(i.bitmap)c=i.bitmap;else{const h=i.width,S=i.height,z=this.cachedCanvases.getCanvas("inlineImage",h,S).context;M(z,i),c=this.applyTransferMapsToCanvas(z)}for(const h of a)s.save(),s.transform(...h.transform),s.scale(1,-1),m(s,c,h.x,h.y,h.w,h.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(i){}markPointProps(i,a){}beginMarkedContent(i){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(i,a){i==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(a)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(i){const a=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(i);const s=this.ctx;this.pendingClip&&(a||(this.pendingClip===pt?s.clip("evenodd"):s.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),s.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const i=(0,P.getCurrentTransform)(this.ctx);if(i[1]===0&&i[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(i[0]),Math.abs(i[3]));else{const a=Math.abs(i[0]*i[3]-i[2]*i[1]),s=Math.hypot(i[0],i[2]),c=Math.hypot(i[1],i[3]);this._cachedGetSinglePixelWidth=Math.max(s,c)/a}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:i}=this.current,{a,b:s,c,d:h}=this.ctx.getTransform();let S,I;if(s===0&&c===0){const z=Math.abs(a),dt=Math.abs(h);if(z===dt)if(i===0)S=I=1/z;else{const ct=z*i;S=I=ct<1?1/ct:1}else if(i===0)S=1/z,I=1/dt;else{const ct=z*i,mt=dt*i;S=ct<1?1/ct:1,I=mt<1?1/mt:1}}else{const z=Math.abs(a*h-s*c),dt=Math.hypot(a,s),ct=Math.hypot(c,h);if(i===0)S=ct/z,I=dt/z;else{const mt=i*z;S=ct>mt?ct/mt:1,I=dt>mt?dt/mt:1}}this._cachedScaleForStroking[0]=S,this._cachedScaleForStroking[1]=I}return this._cachedScaleForStroking}rescaleAndStroke(i){const{ctx:a}=this,{lineWidth:s}=this.current,[c,h]=this.getScaleForStroking();if(a.lineWidth=s||1,c===1&&h===1){a.stroke();return}const S=a.getLineDash();if(i&&a.save(),a.scale(c,h),S.length>0){const I=Math.max(c,h);a.setLineDash(S.map(z=>z/I)),a.lineDashOffset/=I}a.stroke(),i&&a.restore()}isContentVisible(){for(let i=this.markedContentStack.length-1;i>=0;i--)if(!this.markedContentStack[i].visible)return!1;return!0}};Y=new WeakSet,$i=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},kt=new WeakSet,Yi=function(){if(this.pageColors){const i=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(i!=="none"){const a=this.ctx.filter;this.ctx.filter=i,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=a}}};let bt=Gt;v.CanvasGraphics=bt;for(const T in d.OPS)bt.prototype[T]!==void 0&&(bt.prototype[d.OPS[T]]=bt.prototype[T])},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.TilingPattern=v.PathType=void 0,v.getShadingPattern=k;var d=vt(1),P=vt(6);const e={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};v.PathType=e;function tt(E,x){if(!x)return;const M=x[2]-x[0],C=x[3]-x[1],o=new Path2D;o.rect(x[0],x[1],M,C),E.clip(o)}class Tt{constructor(){this.constructor===Tt&&(0,d.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,d.unreachable)("Abstract method `getPattern` called.")}}class G extends Tt{constructor(x){super(),this._type=x[1],this._bbox=x[2],this._colorStops=x[3],this._p0=x[4],this._p1=x[5],this._r0=x[6],this._r1=x[7],this.matrix=null}_createGradient(x){let M;this._type==="axial"?M=x.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(M=x.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const C of this._colorStops)M.addColorStop(C[0],C[1]);return M}getPattern(x,M,C,o){let u;if(o===e.STROKE||o===e.FILL){const L=M.current.getClippedPathBoundingBox(o,(0,P.getCurrentTransform)(x))||[0,0,0,0],_=Math.ceil(L[2]-L[0])||1,l=Math.ceil(L[3]-L[1])||1,F=M.cachedCanvases.getCanvas("pattern",_,l,!0),A=F.context;A.clearRect(0,0,A.canvas.width,A.canvas.height),A.beginPath(),A.rect(0,0,A.canvas.width,A.canvas.height),A.translate(-L[0],-L[1]),C=d.Util.transform(C,[1,0,0,1,L[0],L[1]]),A.transform(...M.baseTransform),this.matrix&&A.transform(...this.matrix),tt(A,this._bbox),A.fillStyle=this._createGradient(A),A.fill(),u=x.createPattern(F.canvas,"no-repeat");const q=new DOMMatrix(C);u.setTransform(q)}else tt(x,this._bbox),u=this._createGradient(x);return u}}function D(E,x,M,C,o,u,L,_){const l=x.coords,F=x.colors,A=E.data,q=E.width*4;let Z;l[M+1]>l[C+1]&&(Z=M,M=C,C=Z,Z=u,u=L,L=Z),l[C+1]>l[o+1]&&(Z=C,C=o,o=Z,Z=L,L=_,_=Z),l[M+1]>l[C+1]&&(Z=M,M=C,C=Z,Z=u,u=L,L=Z);const R=(l[M]+x.offsetX)*x.scaleX,nt=(l[M+1]+x.offsetY)*x.scaleY,pt=(l[C]+x.offsetX)*x.scaleX,bt=(l[C+1]+x.offsetY)*x.scaleY,Y=(l[o]+x.offsetX)*x.scaleX,yt=(l[o+1]+x.offsetY)*x.scaleY;if(nt>=yt)return;const kt=F[u],Bt=F[u+1],Gt=F[u+2],T=F[L],i=F[L+1],a=F[L+2],s=F[_],c=F[_+1],h=F[_+2],S=Math.round(nt),I=Math.round(yt);let z,dt,ct,mt,Et,Dt,wt,$t;for(let ie=S;ie<=I;ie++){if(ie<bt){const at=ie<nt?0:(nt-ie)/(nt-bt);z=R-(R-pt)*at,dt=kt-(kt-T)*at,ct=Bt-(Bt-i)*at,mt=Gt-(Gt-a)*at}else{let at;ie>yt?at=1:bt===yt?at=0:at=(bt-ie)/(bt-yt),z=pt-(pt-Y)*at,dt=T-(T-s)*at,ct=i-(i-c)*at,mt=a-(a-h)*at}let It;ie<nt?It=0:ie>yt?It=1:It=(nt-ie)/(nt-yt),Et=R-(R-Y)*It,Dt=kt-(kt-s)*It,wt=Bt-(Bt-c)*It,$t=Gt-(Gt-h)*It;const Jt=Math.round(Math.min(z,Et)),Ct=Math.round(Math.max(z,Et));let ht=q*ie+Jt*4;for(let at=Jt;at<=Ct;at++)It=(z-at)/(z-Et),It<0?It=0:It>1&&(It=1),A[ht++]=dt-(dt-Dt)*It|0,A[ht++]=ct-(ct-wt)*It|0,A[ht++]=mt-(mt-$t)*It|0,A[ht++]=255}}function g(E,x,M){const C=x.coords,o=x.colors;let u,L;switch(x.type){case"lattice":const _=x.verticesPerRow,l=Math.floor(C.length/_)-1,F=_-1;for(u=0;u<l;u++){let A=u*_;for(let q=0;q<F;q++,A++)D(E,M,C[A],C[A+1],C[A+_],o[A],o[A+1],o[A+_]),D(E,M,C[A+_+1],C[A+1],C[A+_],o[A+_+1],o[A+1],o[A+_])}break;case"triangles":for(u=0,L=C.length;u<L;u+=3)D(E,M,C[u],C[u+1],C[u+2],o[u],o[u+1],o[u+2]);break;default:throw new Error("illegal figure")}}class j extends Tt{constructor(x){super(),this._coords=x[2],this._colors=x[3],this._figures=x[4],this._bounds=x[5],this._bbox=x[7],this._background=x[8],this.matrix=null}_createMeshCanvas(x,M,C){const _=Math.floor(this._bounds[0]),l=Math.floor(this._bounds[1]),F=Math.ceil(this._bounds[2])-_,A=Math.ceil(this._bounds[3])-l,q=Math.min(Math.ceil(Math.abs(F*x[0]*1.1)),3e3),Z=Math.min(Math.ceil(Math.abs(A*x[1]*1.1)),3e3),R=F/q,nt=A/Z,pt={coords:this._coords,colors:this._colors,offsetX:-_,offsetY:-l,scaleX:1/R,scaleY:1/nt},bt=q+2*2,Y=Z+2*2,yt=C.getCanvas("mesh",bt,Y,!1),kt=yt.context,Bt=kt.createImageData(q,Z);if(M){const T=Bt.data;for(let i=0,a=T.length;i<a;i+=4)T[i]=M[0],T[i+1]=M[1],T[i+2]=M[2],T[i+3]=255}for(const T of this._figures)g(Bt,T,pt);return kt.putImageData(Bt,2,2),{canvas:yt.canvas,offsetX:_-2*R,offsetY:l-2*nt,scaleX:R,scaleY:nt}}getPattern(x,M,C,o){tt(x,this._bbox);let u;if(o===e.SHADING)u=d.Util.singularValueDecompose2dScale((0,P.getCurrentTransform)(x));else if(u=d.Util.singularValueDecompose2dScale(M.baseTransform),this.matrix){const _=d.Util.singularValueDecompose2dScale(this.matrix);u=[u[0]*_[0],u[1]*_[1]]}const L=this._createMeshCanvas(u,o===e.SHADING?null:this._background,M.cachedCanvases);return o!==e.SHADING&&(x.setTransform(...M.baseTransform),this.matrix&&x.transform(...this.matrix)),x.translate(L.offsetX,L.offsetY),x.scale(L.scaleX,L.scaleY),x.createPattern(L.canvas,"no-repeat")}}class H extends Tt{getPattern(){return"hotpink"}}function k(E){switch(E[0]){case"RadialAxial":return new G(E);case"Mesh":return new j(E);case"Dummy":return new H}throw new Error(`Unknown IR type: ${E[0]}`)}const y={COLORED:1,UNCOLORED:2},m=class m{constructor(x,M,C,o,u){this.operatorList=x[2],this.matrix=x[3]||[1,0,0,1,0,0],this.bbox=x[4],this.xstep=x[5],this.ystep=x[6],this.paintType=x[7],this.tilingType=x[8],this.color=M,this.ctx=C,this.canvasGraphicsFactory=o,this.baseTransform=u}createPatternCanvas(x){const M=this.operatorList,C=this.bbox,o=this.xstep,u=this.ystep,L=this.paintType,_=this.tilingType,l=this.color,F=this.canvasGraphicsFactory;(0,d.info)("TilingType: "+_);const A=C[0],q=C[1],Z=C[2],R=C[3],nt=d.Util.singularValueDecompose2dScale(this.matrix),pt=d.Util.singularValueDecompose2dScale(this.baseTransform),bt=[nt[0]*pt[0],nt[1]*pt[1]],Y=this.getSizeAndScale(o,this.ctx.canvas.width,bt[0]),yt=this.getSizeAndScale(u,this.ctx.canvas.height,bt[1]),kt=x.cachedCanvases.getCanvas("pattern",Y.size,yt.size,!0),Bt=kt.context,Gt=F.createCanvasGraphics(Bt);Gt.groupLevel=x.groupLevel,this.setFillAndStrokeStyleToContext(Gt,L,l);let T=A,i=q,a=Z,s=R;return A<0&&(T=0,a+=Math.abs(A)),q<0&&(i=0,s+=Math.abs(q)),Bt.translate(-(Y.scale*T),-(yt.scale*i)),Gt.transform(Y.scale,0,0,yt.scale,0,0),Bt.save(),this.clipBbox(Gt,T,i,a,s),Gt.baseTransform=(0,P.getCurrentTransform)(Gt.ctx),Gt.executeOperatorList(M),Gt.endDrawing(),{canvas:kt.canvas,scaleX:Y.scale,scaleY:yt.scale,offsetX:T,offsetY:i}}getSizeAndScale(x,M,C){x=Math.abs(x);const o=Math.max(m.MAX_PATTERN_SIZE,M);let u=Math.ceil(x*C);return u>=o?u=o:C=u/x,{scale:C,size:u}}clipBbox(x,M,C,o,u){const L=o-M,_=u-C;x.ctx.rect(M,C,L,_),x.current.updateRectMinMax((0,P.getCurrentTransform)(x.ctx),[M,C,o,u]),x.clip(),x.endPath()}setFillAndStrokeStyleToContext(x,M,C){const o=x.ctx,u=x.current;switch(M){case y.COLORED:const L=this.ctx;o.fillStyle=L.fillStyle,o.strokeStyle=L.strokeStyle,u.fillColor=L.fillStyle,u.strokeColor=L.strokeStyle;break;case y.UNCOLORED:const _=d.Util.makeHexColor(C[0],C[1],C[2]);o.fillStyle=_,o.strokeStyle=_,u.fillColor=_,u.strokeColor=_;break;default:throw new d.FormatError(`Unsupported paint type: ${M}`)}}getPattern(x,M,C,o){let u=C;o!==e.SHADING&&(u=d.Util.transform(u,M.baseTransform),this.matrix&&(u=d.Util.transform(u,this.matrix)));const L=this.createPatternCanvas(M);let _=new DOMMatrix(u);_=_.translate(L.offsetX,L.offsetY),_=_.scale(1/L.scaleX,1/L.scaleY);const l=x.createPattern(L.canvas,"repeat");return l.setTransform(_),l}};Je(m,"MAX_PATTERN_SIZE",3e3);let w=m;v.TilingPattern=w},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.convertBlackAndWhiteToRGBA=e,v.convertToRGBA=P,v.grayToRGBA=Tt;var d=vt(1);function P(G){switch(G.kind){case d.ImageKind.GRAYSCALE_1BPP:return e(G);case d.ImageKind.RGB_24BPP:return tt(G)}return null}function e({src:G,srcPos:D=0,dest:g,width:j,height:H,nonBlackColor:k=4294967295,inverseDecode:y=!1}){const w=d.FeatureTest.isLittleEndian?4278190080:255,[m,E]=y?[k,w]:[w,k],x=j>>3,M=j&7,C=G.length;g=new Uint32Array(g.buffer);let o=0;for(let u=0;u<H;u++){for(const _=D+x;D<_;D++){const l=D<C?G[D]:255;g[o++]=l&128?E:m,g[o++]=l&64?E:m,g[o++]=l&32?E:m,g[o++]=l&16?E:m,g[o++]=l&8?E:m,g[o++]=l&4?E:m,g[o++]=l&2?E:m,g[o++]=l&1?E:m}if(M===0)continue;const L=D<C?G[D++]:255;for(let _=0;_<M;_++)g[o++]=L&1<<7-_?E:m}return{srcPos:D,destPos:o}}function tt({src:G,srcPos:D=0,dest:g,destPos:j=0,width:H,height:k}){let y=0;const w=G.length>>2,m=new Uint32Array(G.buffer,D,w);if(d.FeatureTest.isLittleEndian){for(;y<w-2;y+=3,j+=4){const E=m[y],x=m[y+1],M=m[y+2];g[j]=E|4278190080,g[j+1]=E>>>24|x<<8|4278190080,g[j+2]=x>>>16|M<<16|4278190080,g[j+3]=M>>>8|4278190080}for(let E=y*4,x=G.length;E<x;E+=3)g[j++]=G[E]|G[E+1]<<8|G[E+2]<<16|4278190080}else{for(;y<w-2;y+=3,j+=4){const E=m[y],x=m[y+1],M=m[y+2];g[j]=E|255,g[j+1]=E<<24|x>>>8|255,g[j+2]=x<<16|M>>>16|255,g[j+3]=M<<8|255}for(let E=y*4,x=G.length;E<x;E+=3)g[j++]=G[E]<<24|G[E+1]<<16|G[E+2]<<8|255}return{srcPos:D,destPos:j}}function Tt(G,D){if(d.FeatureTest.isLittleEndian)for(let g=0,j=G.length;g<j;g++)D[g]=G[g]*65793|4278190080;else for(let g=0,j=G.length;g<j;g++)D[g]=G[g]*16843008|255}},(f,v)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.GlobalWorkerOptions=void 0;const vt=Object.create(null);v.GlobalWorkerOptions=vt,vt.workerPort=null,vt.workerSrc=""},(f,v,vt)=>{var G,jr,g,Wr,H,di;Object.defineProperty(v,"__esModule",{value:!0}),v.MessageHandler=void 0;var d=vt(1);const P={UNKNOWN:0,DATA:1,ERROR:2},e={UNKNOWN:0,CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function tt(y){switch(y instanceof Error||typeof y=="object"&&y!==null||(0,d.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),y.name){case"AbortException":return new d.AbortException(y.message);case"MissingPDFException":return new d.MissingPDFException(y.message);case"PasswordException":return new d.PasswordException(y.message,y.code);case"UnexpectedResponseException":return new d.UnexpectedResponseException(y.message,y.status);case"UnknownErrorException":return new d.UnknownErrorException(y.message,y.details);default:return new d.UnknownErrorException(y.message,y.toString())}}class Tt{constructor(w,m,E){U(this,G);U(this,g);U(this,H);this.sourceName=w,this.targetName=m,this.comObj=E,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=x=>{const M=x.data;if(M.targetName!==this.sourceName)return;if(M.stream){Q(this,g,Wr).call(this,M);return}if(M.callback){const o=M.callbackId,u=this.callbackCapabilities[o];if(!u)throw new Error(`Cannot resolve callback ${o}`);if(delete this.callbackCapabilities[o],M.callback===P.DATA)u.resolve(M.data);else if(M.callback===P.ERROR)u.reject(tt(M.reason));else throw new Error("Unexpected callback case");return}const C=this.actionHandler[M.action];if(!C)throw new Error(`Unknown action from worker: ${M.action}`);if(M.callbackId){const o=this.sourceName,u=M.sourceName;new Promise(function(L){L(C(M.data))}).then(function(L){E.postMessage({sourceName:o,targetName:u,callback:P.DATA,callbackId:M.callbackId,data:L})},function(L){E.postMessage({sourceName:o,targetName:u,callback:P.ERROR,callbackId:M.callbackId,reason:tt(L)})});return}if(M.streamId){Q(this,G,jr).call(this,M);return}C(M.data)},E.addEventListener("message",this._onComObjOnMessage)}on(w,m){const E=this.actionHandler;if(E[w])throw new Error(`There is already an actionName called "${w}"`);E[w]=m}send(w,m,E){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:w,data:m},E)}sendWithPromise(w,m,E){const x=this.callbackId++,M=new d.PromiseCapability;this.callbackCapabilities[x]=M;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:w,callbackId:x,data:m},E)}catch(C){M.reject(C)}return M.promise}sendWithStream(w,m,E,x){const M=this.streamId++,C=this.sourceName,o=this.targetName,u=this.comObj;return new ReadableStream({start:L=>{const _=new d.PromiseCapability;return this.streamControllers[M]={controller:L,startCall:_,pullCall:null,cancelCall:null,isClosed:!1},u.postMessage({sourceName:C,targetName:o,action:w,streamId:M,data:m,desiredSize:L.desiredSize},x),_.promise},pull:L=>{const _=new d.PromiseCapability;return this.streamControllers[M].pullCall=_,u.postMessage({sourceName:C,targetName:o,stream:e.PULL,streamId:M,desiredSize:L.desiredSize}),_.promise},cancel:L=>{(0,d.assert)(L instanceof Error,"cancel must have a valid reason");const _=new d.PromiseCapability;return this.streamControllers[M].cancelCall=_,this.streamControllers[M].isClosed=!0,u.postMessage({sourceName:C,targetName:o,stream:e.CANCEL,streamId:M,reason:tt(L)}),_.promise}},E)}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}G=new WeakSet,jr=function(w){const m=w.streamId,E=this.sourceName,x=w.sourceName,M=this.comObj,C=this,o=this.actionHandler[w.action],u={enqueue(L,_=1,l){if(this.isCancelled)return;const F=this.desiredSize;this.desiredSize-=_,F>0&&this.desiredSize<=0&&(this.sinkCapability=new d.PromiseCapability,this.ready=this.sinkCapability.promise),M.postMessage({sourceName:E,targetName:x,stream:e.ENQUEUE,streamId:m,chunk:L},l)},close(){this.isCancelled||(this.isCancelled=!0,M.postMessage({sourceName:E,targetName:x,stream:e.CLOSE,streamId:m}),delete C.streamSinks[m])},error(L){(0,d.assert)(L instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,M.postMessage({sourceName:E,targetName:x,stream:e.ERROR,streamId:m,reason:tt(L)}))},sinkCapability:new d.PromiseCapability,onPull:null,onCancel:null,isCancelled:!1,desiredSize:w.desiredSize,ready:null};u.sinkCapability.resolve(),u.ready=u.sinkCapability.promise,this.streamSinks[m]=u,new Promise(function(L){L(o(w.data,u))}).then(function(){M.postMessage({sourceName:E,targetName:x,stream:e.START_COMPLETE,streamId:m,success:!0})},function(L){M.postMessage({sourceName:E,targetName:x,stream:e.START_COMPLETE,streamId:m,reason:tt(L)})})},g=new WeakSet,Wr=function(w){const m=w.streamId,E=this.sourceName,x=w.sourceName,M=this.comObj,C=this.streamControllers[m],o=this.streamSinks[m];switch(w.stream){case e.START_COMPLETE:w.success?C.startCall.resolve():C.startCall.reject(tt(w.reason));break;case e.PULL_COMPLETE:w.success?C.pullCall.resolve():C.pullCall.reject(tt(w.reason));break;case e.PULL:if(!o){M.postMessage({sourceName:E,targetName:x,stream:e.PULL_COMPLETE,streamId:m,success:!0});break}o.desiredSize<=0&&w.desiredSize>0&&o.sinkCapability.resolve(),o.desiredSize=w.desiredSize,new Promise(function(u){var L;u((L=o.onPull)==null?void 0:L.call(o))}).then(function(){M.postMessage({sourceName:E,targetName:x,stream:e.PULL_COMPLETE,streamId:m,success:!0})},function(u){M.postMessage({sourceName:E,targetName:x,stream:e.PULL_COMPLETE,streamId:m,reason:tt(u)})});break;case e.ENQUEUE:if((0,d.assert)(C,"enqueue should have stream controller"),C.isClosed)break;C.controller.enqueue(w.chunk);break;case e.CLOSE:if((0,d.assert)(C,"close should have stream controller"),C.isClosed)break;C.isClosed=!0,C.controller.close(),Q(this,H,di).call(this,C,m);break;case e.ERROR:(0,d.assert)(C,"error should have stream controller"),C.controller.error(tt(w.reason)),Q(this,H,di).call(this,C,m);break;case e.CANCEL_COMPLETE:w.success?C.cancelCall.resolve():C.cancelCall.reject(tt(w.reason)),Q(this,H,di).call(this,C,m);break;case e.CANCEL:if(!o)break;new Promise(function(u){var L;u((L=o.onCancel)==null?void 0:L.call(o,tt(w.reason)))}).then(function(){M.postMessage({sourceName:E,targetName:x,stream:e.CANCEL_COMPLETE,streamId:m,success:!0})},function(u){M.postMessage({sourceName:E,targetName:x,stream:e.CANCEL_COMPLETE,streamId:m,reason:tt(u)})}),o.sinkCapability.reject(tt(w.reason)),o.isCancelled=!0,delete this.streamSinks[m];break;default:throw new Error("Unexpected stream case")}},H=new WeakSet,di=async function(w,m){var E,x,M;await Promise.allSettled([(E=w.startCall)==null?void 0:E.promise,(x=w.pullCall)==null?void 0:x.promise,(M=w.cancelCall)==null?void 0:M.promise]),delete this.streamControllers[m]},v.MessageHandler=Tt},(f,v,vt)=>{var e,tt;Object.defineProperty(v,"__esModule",{value:!0}),v.Metadata=void 0;var d=vt(1);class P{constructor({parsedData:G,rawData:D}){U(this,e,void 0);U(this,tt,void 0);ft(this,e,G),ft(this,tt,D)}getRaw(){return t(this,tt)}get(G){return t(this,e).get(G)??null}getAll(){return(0,d.objectFromMap)(t(this,e))}has(G){return t(this,e).has(G)}}e=new WeakMap,tt=new WeakMap,v.Metadata=P},(f,v,vt)=>{var G,D,g,j,H,k,Ki;Object.defineProperty(v,"__esModule",{value:!0}),v.OptionalContentConfig=void 0;var d=vt(1),P=vt(8);const e=Symbol("INTERNAL");class tt{constructor(m,E){U(this,G,!0);this.name=m,this.intent=E}get visible(){return t(this,G)}_setVisible(m,E){m!==e&&(0,d.unreachable)("Internal method `_setVisible` called."),ft(this,G,E)}}G=new WeakMap;class Tt{constructor(m){U(this,k);U(this,D,null);U(this,g,new Map);U(this,j,null);U(this,H,null);if(this.name=null,this.creator=null,m!==null){this.name=m.name,this.creator=m.creator,ft(this,H,m.order);for(const E of m.groups)t(this,g).set(E.id,new tt(E.name,E.intent));if(m.baseState==="OFF")for(const E of t(this,g).values())E._setVisible(e,!1);for(const E of m.on)t(this,g).get(E)._setVisible(e,!0);for(const E of m.off)t(this,g).get(E)._setVisible(e,!1);ft(this,j,this.getHash())}}isVisible(m){if(t(this,g).size===0)return!0;if(!m)return(0,d.warn)("Optional content group not defined."),!0;if(m.type==="OCG")return t(this,g).has(m.id)?t(this,g).get(m.id).visible:((0,d.warn)(`Optional content group not found: ${m.id}`),!0);if(m.type==="OCMD"){if(m.expression)return Q(this,k,Ki).call(this,m.expression);if(!m.policy||m.policy==="AnyOn"){for(const E of m.ids){if(!t(this,g).has(E))return(0,d.warn)(`Optional content group not found: ${E}`),!0;if(t(this,g).get(E).visible)return!0}return!1}else if(m.policy==="AllOn"){for(const E of m.ids){if(!t(this,g).has(E))return(0,d.warn)(`Optional content group not found: ${E}`),!0;if(!t(this,g).get(E).visible)return!1}return!0}else if(m.policy==="AnyOff"){for(const E of m.ids){if(!t(this,g).has(E))return(0,d.warn)(`Optional content group not found: ${E}`),!0;if(!t(this,g).get(E).visible)return!0}return!1}else if(m.policy==="AllOff"){for(const E of m.ids){if(!t(this,g).has(E))return(0,d.warn)(`Optional content group not found: ${E}`),!0;if(t(this,g).get(E).visible)return!1}return!0}return(0,d.warn)(`Unknown optional content policy ${m.policy}.`),!0}return(0,d.warn)(`Unknown group type ${m.type}.`),!0}setVisibility(m,E=!0){if(!t(this,g).has(m)){(0,d.warn)(`Optional content group not found: ${m}`);return}t(this,g).get(m)._setVisible(e,!!E),ft(this,D,null)}get hasInitialVisibility(){return t(this,j)===null||this.getHash()===t(this,j)}getOrder(){return t(this,g).size?t(this,H)?t(this,H).slice():[...t(this,g).keys()]:null}getGroups(){return t(this,g).size>0?(0,d.objectFromMap)(t(this,g)):null}getGroup(m){return t(this,g).get(m)||null}getHash(){if(t(this,D)!==null)return t(this,D);const m=new P.MurmurHash3_64;for(const[E,x]of t(this,g))m.update(`${E}:${x.visible}`);return ft(this,D,m.hexdigest())}}D=new WeakMap,g=new WeakMap,j=new WeakMap,H=new WeakMap,k=new WeakSet,Ki=function(m){const E=m.length;if(E<2)return!0;const x=m[0];for(let M=1;M<E;M++){const C=m[M];let o;if(Array.isArray(C))o=Q(this,k,Ki).call(this,C);else if(t(this,g).has(C))o=t(this,g).get(C).visible;else return(0,d.warn)(`Optional content group not found: ${C}`),!0;switch(x){case"And":if(!o)return!1;break;case"Or":if(o)return!0;break;case"Not":return!o;default:return!0}}return x==="And"},v.OptionalContentConfig=Tt},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.PDFDataTransportStream=void 0;var d=vt(1),P=vt(6);class e{constructor({length:D,initialData:g,progressiveDone:j=!1,contentDispositionFilename:H=null,disableRange:k=!1,disableStream:y=!1},w){if((0,d.assert)(w,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=j,this._contentDispositionFilename=H,(g==null?void 0:g.length)>0){const m=g instanceof Uint8Array&&g.byteLength===g.buffer.byteLength?g.buffer:new Uint8Array(g).buffer;this._queuedChunks.push(m)}this._pdfDataRangeTransport=w,this._isStreamingSupported=!y,this._isRangeSupported=!k,this._contentLength=D,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((m,E)=>{this._onReceiveData({begin:m,chunk:E})}),this._pdfDataRangeTransport.addProgressListener((m,E)=>{this._onProgress({loaded:m,total:E})}),this._pdfDataRangeTransport.addProgressiveReadListener(m=>{this._onReceiveData({chunk:m})}),this._pdfDataRangeTransport.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),this._pdfDataRangeTransport.transportReady()}_onReceiveData({begin:D,chunk:g}){const j=g instanceof Uint8Array&&g.byteLength===g.buffer.byteLength?g.buffer:new Uint8Array(g).buffer;if(D===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(j):this._queuedChunks.push(j);else{const H=this._rangeReaders.some(function(k){return k._begin!==D?!1:(k._enqueue(j),!0)});(0,d.assert)(H,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var D;return((D=this._fullRequestReader)==null?void 0:D._loaded)??0}_onProgress(D){var g,j,H,k;D.total===void 0?(j=(g=this._rangeReaders[0])==null?void 0:g.onProgress)==null||j.call(g,{loaded:D.loaded}):(k=(H=this._fullRequestReader)==null?void 0:H.onProgress)==null||k.call(H,{loaded:D.loaded,total:D.total})}_onProgressiveDone(){var D;(D=this._fullRequestReader)==null||D.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(D){const g=this._rangeReaders.indexOf(D);g>=0&&this._rangeReaders.splice(g,1)}getFullReader(){(0,d.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const D=this._queuedChunks;return this._queuedChunks=null,new tt(this,D,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(D,g){if(g<=this._progressiveDataLength)return null;const j=new Tt(this,D,g);return this._pdfDataRangeTransport.requestDataRange(D,g),this._rangeReaders.push(j),j}cancelAllRequests(D){var g;(g=this._fullRequestReader)==null||g.cancel(D);for(const j of this._rangeReaders.slice(0))j.cancel(D);this._pdfDataRangeTransport.abort()}}v.PDFDataTransportStream=e;class tt{constructor(D,g,j=!1,H=null){this._stream=D,this._done=j||!1,this._filename=(0,P.isPdfFile)(H)?H:null,this._queuedChunks=g||[],this._loaded=0;for(const k of this._queuedChunks)this._loaded+=k.byteLength;this._requests=[],this._headersReady=Promise.resolve(),D._fullRequestReader=this,this.onProgress=null}_enqueue(D){this._done||(this._requests.length>0?this._requests.shift().resolve({value:D,done:!1}):this._queuedChunks.push(D),this._loaded+=D.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const D=new d.PromiseCapability;return this._requests.push(D),D.promise}cancel(D){this._done=!0;for(const g of this._requests)g.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class Tt{constructor(D,g,j){this._stream=D,this._begin=g,this._end=j,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(D){if(!this._done){if(this._requests.length===0)this._queuedChunk=D;else{this._requests.shift().resolve({value:D,done:!1});for(const j of this._requests)j.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const g=this._queuedChunk;return this._queuedChunk=null,{value:g,done:!1}}if(this._done)return{value:void 0,done:!0};const D=new d.PromiseCapability;return this._requests.push(D),D.promise}cancel(D){this._done=!0;for(const g of this._requests)g.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.PDFFetchStream=void 0;var d=vt(1),P=vt(20);function e(j,H,k){return{method:"GET",headers:j,signal:k.signal,mode:"cors",credentials:H?"include":"same-origin",redirect:"follow"}}function tt(j){const H=new Headers;for(const k in j){const y=j[k];y!==void 0&&H.append(k,y)}return H}function Tt(j){return j instanceof Uint8Array?j.buffer:j instanceof ArrayBuffer?j:((0,d.warn)(`getArrayBuffer - unexpected data format: ${j}`),new Uint8Array(j).buffer)}class G{constructor(H){this.source=H,this.isHttp=/^https?:/i.test(H.url),this.httpHeaders=this.isHttp&&H.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var H;return((H=this._fullRequestReader)==null?void 0:H._loaded)??0}getFullReader(){return(0,d.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new D(this),this._fullRequestReader}getRangeReader(H,k){if(k<=this._progressiveDataLength)return null;const y=new g(this,H,k);return this._rangeRequestReaders.push(y),y}cancelAllRequests(H){var k;(k=this._fullRequestReader)==null||k.cancel(H);for(const y of this._rangeRequestReaders.slice(0))y.cancel(H)}}v.PDFFetchStream=G;class D{constructor(H){this._stream=H,this._reader=null,this._loaded=0,this._filename=null;const k=H.source;this._withCredentials=k.withCredentials||!1,this._contentLength=k.length,this._headersCapability=new d.PromiseCapability,this._disableRange=k.disableRange||!1,this._rangeChunkSize=k.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!k.disableStream,this._isRangeSupported=!k.disableRange,this._headers=tt(this._stream.httpHeaders);const y=k.url;fetch(y,e(this._headers,this._withCredentials,this._abortController)).then(w=>{if(!(0,P.validateResponseStatus)(w.status))throw(0,P.createResponseStatusError)(w.status,y);this._reader=w.body.getReader(),this._headersCapability.resolve();const m=M=>w.headers.get(M),{allowRangeRequests:E,suggestedLength:x}=(0,P.validateRangeRequestCapabilities)({getResponseHeader:m,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=E,this._contentLength=x||this._contentLength,this._filename=(0,P.extractFilenameFromHeader)(m),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new d.AbortException("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var y;await this._headersCapability.promise;const{value:H,done:k}=await this._reader.read();return k?{value:H,done:k}:(this._loaded+=H.byteLength,(y=this.onProgress)==null||y.call(this,{loaded:this._loaded,total:this._contentLength}),{value:Tt(H),done:!1})}cancel(H){var k;(k=this._reader)==null||k.cancel(H),this._abortController.abort()}}class g{constructor(H,k,y){this._stream=H,this._reader=null,this._loaded=0;const w=H.source;this._withCredentials=w.withCredentials||!1,this._readCapability=new d.PromiseCapability,this._isStreamingSupported=!w.disableStream,this._abortController=new AbortController,this._headers=tt(this._stream.httpHeaders),this._headers.append("Range",`bytes=${k}-${y-1}`);const m=w.url;fetch(m,e(this._headers,this._withCredentials,this._abortController)).then(E=>{if(!(0,P.validateResponseStatus)(E.status))throw(0,P.createResponseStatusError)(E.status,m);this._readCapability.resolve(),this._reader=E.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var y;await this._readCapability.promise;const{value:H,done:k}=await this._reader.read();return k?{value:H,done:k}:(this._loaded+=H.byteLength,(y=this.onProgress)==null||y.call(this,{loaded:this._loaded}),{value:Tt(H),done:!1})}cancel(H){var k;(k=this._reader)==null||k.cancel(H),this._abortController.abort()}}},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.createResponseStatusError=G,v.extractFilenameFromHeader=Tt,v.validateRangeRequestCapabilities=tt,v.validateResponseStatus=D;var d=vt(1),P=vt(21),e=vt(6);function tt({getResponseHeader:g,isHttp:j,rangeChunkSize:H,disableRange:k}){const y={allowRangeRequests:!1,suggestedLength:void 0},w=parseInt(g("Content-Length"),10);return!Number.isInteger(w)||(y.suggestedLength=w,w<=2*H)||k||!j||g("Accept-Ranges")!=="bytes"||(g("Content-Encoding")||"identity")!=="identity"||(y.allowRangeRequests=!0),y}function Tt(g){const j=g("Content-Disposition");if(j){let H=(0,P.getFilenameFromContentDispositionHeader)(j);if(H.includes("%"))try{H=decodeURIComponent(H)}catch{}if((0,e.isPdfFile)(H))return H}return null}function G(g,j){return g===404||g===0&&j.startsWith("file:")?new d.MissingPDFException('Missing PDF "'+j+'".'):new d.UnexpectedResponseException(`Unexpected server response (${g}) while retrieving PDF "${j}".`,g)}function D(g){return g===200||g===206}},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.getFilenameFromContentDispositionHeader=P;var d=vt(1);function P(e){let tt=!0,Tt=G("filename\\*","i").exec(e);if(Tt){Tt=Tt[1];let w=H(Tt);return w=unescape(w),w=k(w),w=y(w),g(w)}if(Tt=j(e),Tt){const w=y(Tt);return g(w)}if(Tt=G("filename","i").exec(e),Tt){Tt=Tt[1];let w=H(Tt);return w=y(w),g(w)}function G(w,m){return new RegExp("(?:^|;)\\s*"+w+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',m)}function D(w,m){if(w){if(!/^[\x00-\xFF]+$/.test(m))return m;try{const E=new TextDecoder(w,{fatal:!0}),x=(0,d.stringToBytes)(m);m=E.decode(x),tt=!1}catch{}}return m}function g(w){return tt&&/[\x80-\xff]/.test(w)&&(w=D("utf-8",w),tt&&(w=D("iso-8859-1",w))),w}function j(w){const m=[];let E;const x=G("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(E=x.exec(w))!==null;){let[,C,o,u]=E;if(C=parseInt(C,10),C in m){if(C===0)break;continue}m[C]=[o,u]}const M=[];for(let C=0;C<m.length&&C in m;++C){let[o,u]=m[C];u=H(u),o&&(u=unescape(u),C===0&&(u=k(u))),M.push(u)}return M.join("")}function H(w){if(w.startsWith('"')){const m=w.slice(1).split('\\"');for(let E=0;E<m.length;++E){const x=m[E].indexOf('"');x!==-1&&(m[E]=m[E].slice(0,x),m.length=E+1),m[E]=m[E].replaceAll(/\\(.)/g,"$1")}w=m.join('"')}return w}function k(w){const m=w.indexOf("'");if(m===-1)return w;const E=w.slice(0,m),M=w.slice(m+1).replace(/^[^']*'/,"");return D(E,M)}function y(w){return!w.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(w)?w:w.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(m,E,x,M){if(x==="q"||x==="Q")return M=M.replaceAll("_"," "),M=M.replaceAll(/=([0-9a-fA-F]{2})/g,function(C,o){return String.fromCharCode(parseInt(o,16))}),D(E,M);try{M=atob(M)}catch{}return D(E,M)})}return""}},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.PDFNetworkStream=void 0;var d=vt(1),P=vt(20);const e=200,tt=206;function Tt(H){const k=H.response;return typeof k!="string"?k:(0,d.stringToBytes)(k).buffer}class G{constructor(k,y={}){this.url=k,this.isHttp=/^https?:/i.test(k),this.httpHeaders=this.isHttp&&y.httpHeaders||Object.create(null),this.withCredentials=y.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(k,y,w){const m={begin:k,end:y};for(const E in w)m[E]=w[E];return this.request(m)}requestFull(k){return this.request(k)}request(k){const y=new XMLHttpRequest,w=this.currXhrId++,m=this.pendingRequests[w]={xhr:y};y.open("GET",this.url),y.withCredentials=this.withCredentials;for(const E in this.httpHeaders){const x=this.httpHeaders[E];x!==void 0&&y.setRequestHeader(E,x)}return this.isHttp&&"begin"in k&&"end"in k?(y.setRequestHeader("Range",`bytes=${k.begin}-${k.end-1}`),m.expectedStatus=tt):m.expectedStatus=e,y.responseType="arraybuffer",k.onError&&(y.onerror=function(E){k.onError(y.status)}),y.onreadystatechange=this.onStateChange.bind(this,w),y.onprogress=this.onProgress.bind(this,w),m.onHeadersReceived=k.onHeadersReceived,m.onDone=k.onDone,m.onError=k.onError,m.onProgress=k.onProgress,y.send(null),w}onProgress(k,y){var m;const w=this.pendingRequests[k];w&&((m=w.onProgress)==null||m.call(w,y))}onStateChange(k,y){var C,o,u;const w=this.pendingRequests[k];if(!w)return;const m=w.xhr;if(m.readyState>=2&&w.onHeadersReceived&&(w.onHeadersReceived(),delete w.onHeadersReceived),m.readyState!==4||!(k in this.pendingRequests))return;if(delete this.pendingRequests[k],m.status===0&&this.isHttp){(C=w.onError)==null||C.call(w,m.status);return}const E=m.status||e;if(!(E===e&&w.expectedStatus===tt)&&E!==w.expectedStatus){(o=w.onError)==null||o.call(w,m.status);return}const M=Tt(m);if(E===tt){const L=m.getResponseHeader("Content-Range"),_=/bytes (\d+)-(\d+)\/(\d+)/.exec(L);w.onDone({begin:parseInt(_[1],10),chunk:M})}else M?w.onDone({begin:0,chunk:M}):(u=w.onError)==null||u.call(w,m.status)}getRequestXhr(k){return this.pendingRequests[k].xhr}isPendingRequest(k){return k in this.pendingRequests}abortRequest(k){const y=this.pendingRequests[k].xhr;delete this.pendingRequests[k],y.abort()}}class D{constructor(k){this._source=k,this._manager=new G(k.url,{httpHeaders:k.httpHeaders,withCredentials:k.withCredentials}),this._rangeChunkSize=k.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(k){const y=this._rangeRequestReaders.indexOf(k);y>=0&&this._rangeRequestReaders.splice(y,1)}getFullReader(){return(0,d.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new g(this._manager,this._source),this._fullRequestReader}getRangeReader(k,y){const w=new j(this._manager,k,y);return w.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(w),w}cancelAllRequests(k){var y;(y=this._fullRequestReader)==null||y.cancel(k);for(const w of this._rangeRequestReaders.slice(0))w.cancel(k)}}v.PDFNetworkStream=D;class g{constructor(k,y){this._manager=k;const w={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=y.url,this._fullRequestId=k.requestFull(w),this._headersReceivedCapability=new d.PromiseCapability,this._disableRange=y.disableRange||!1,this._contentLength=y.length,this._rangeChunkSize=y.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const k=this._fullRequestId,y=this._manager.getRequestXhr(k),w=x=>y.getResponseHeader(x),{allowRangeRequests:m,suggestedLength:E}=(0,P.validateRangeRequestCapabilities)({getResponseHeader:w,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});m&&(this._isRangeSupported=!0),this._contentLength=E||this._contentLength,this._filename=(0,P.extractFilenameFromHeader)(w),this._isRangeSupported&&this._manager.abortRequest(k),this._headersReceivedCapability.resolve()}_onDone(k){if(k&&(this._requests.length>0?this._requests.shift().resolve({value:k.chunk,done:!1}):this._cachedChunks.push(k.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const y of this._requests)y.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(k){this._storedError=(0,P.createResponseStatusError)(k,this._url),this._headersReceivedCapability.reject(this._storedError);for(const y of this._requests)y.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(k){var y;(y=this.onProgress)==null||y.call(this,{loaded:k.loaded,total:k.lengthComputable?k.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const k=new d.PromiseCapability;return this._requests.push(k),k.promise}cancel(k){this._done=!0,this._headersReceivedCapability.reject(k);for(const y of this._requests)y.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class j{constructor(k,y,w){this._manager=k;const m={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=k.url,this._requestId=k.requestRange(y,w,m),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){var k;(k=this.onClosed)==null||k.call(this,this)}_onDone(k){const y=k.chunk;this._requests.length>0?this._requests.shift().resolve({value:y,done:!1}):this._queuedChunk=y,this._done=!0;for(const w of this._requests)w.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(k){this._storedError=(0,P.createResponseStatusError)(k,this._url);for(const y of this._requests)y.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(k){var y;this.isStreamingSupported||(y=this.onProgress)==null||y.call(this,{loaded:k.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const y=this._queuedChunk;return this._queuedChunk=null,{value:y,done:!1}}if(this._done)return{value:void 0,done:!0};const k=new d.PromiseCapability;return this._requests.push(k),k.promise}cancel(k){this._done=!0;for(const y of this._requests)y.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.PDFNodeStream=void 0;var d=vt(1),P=vt(20);const e=/^file:\/\/\/[a-zA-Z]:\//;function tt(w){const m=require$$0,E=m.parse(w);return E.protocol==="file:"||E.host?E:/^[a-z]:[/\\]/i.test(w)?m.parse(`file:///${w}`):(E.host||(E.protocol="file:"),E)}class Tt{constructor(m){this.source=m,this.url=tt(m.url),this.isHttp=this.url.protocol==="http:"||this.url.protocol==="https:",this.isFsUrl=this.url.protocol==="file:",this.httpHeaders=this.isHttp&&m.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var m;return((m=this._fullRequestReader)==null?void 0:m._loaded)??0}getFullReader(){return(0,d.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new k(this):new j(this),this._fullRequestReader}getRangeReader(m,E){if(E<=this._progressiveDataLength)return null;const x=this.isFsUrl?new y(this,m,E):new H(this,m,E);return this._rangeRequestReaders.push(x),x}cancelAllRequests(m){var E;(E=this._fullRequestReader)==null||E.cancel(m);for(const x of this._rangeRequestReaders.slice(0))x.cancel(m)}}v.PDFNodeStream=Tt;class G{constructor(m){this._url=m.url,this._done=!1,this._storedError=null,this.onProgress=null;const E=m.source;this._contentLength=E.length,this._loaded=0,this._filename=null,this._disableRange=E.disableRange||!1,this._rangeChunkSize=E.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!E.disableStream,this._isRangeSupported=!E.disableRange,this._readableStream=null,this._readCapability=new d.PromiseCapability,this._headersCapability=new d.PromiseCapability}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var x;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const m=this._readableStream.read();return m===null?(this._readCapability=new d.PromiseCapability,this.read()):(this._loaded+=m.length,(x=this.onProgress)==null||x.call(this,{loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(m).buffer,done:!1})}cancel(m){if(!this._readableStream){this._error(m);return}this._readableStream.destroy(m)}_error(m){this._storedError=m,this._readCapability.resolve()}_setReadableStream(m){this._readableStream=m,m.on("readable",()=>{this._readCapability.resolve()}),m.on("end",()=>{m.destroy(),this._done=!0,this._readCapability.resolve()}),m.on("error",E=>{this._error(E)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new d.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class D{constructor(m){this._url=m.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=new d.PromiseCapability;const E=m.source;this._isStreamingSupported=!E.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){var x;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const m=this._readableStream.read();return m===null?(this._readCapability=new d.PromiseCapability,this.read()):(this._loaded+=m.length,(x=this.onProgress)==null||x.call(this,{loaded:this._loaded}),{value:new Uint8Array(m).buffer,done:!1})}cancel(m){if(!this._readableStream){this._error(m);return}this._readableStream.destroy(m)}_error(m){this._storedError=m,this._readCapability.resolve()}_setReadableStream(m){this._readableStream=m,m.on("readable",()=>{this._readCapability.resolve()}),m.on("end",()=>{m.destroy(),this._done=!0,this._readCapability.resolve()}),m.on("error",E=>{this._error(E)}),this._storedError&&this._readableStream.destroy(this._storedError)}}function g(w,m){return{protocol:w.protocol,auth:w.auth,host:w.hostname,port:w.port,path:w.path,method:"GET",headers:m}}class j extends G{constructor(m){super(m);const E=x=>{if(x.statusCode===404){const u=new d.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=u,this._headersCapability.reject(u);return}this._headersCapability.resolve(),this._setReadableStream(x);const M=u=>this._readableStream.headers[u.toLowerCase()],{allowRangeRequests:C,suggestedLength:o}=(0,P.validateRangeRequestCapabilities)({getResponseHeader:M,isHttp:m.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=C,this._contentLength=o||this._contentLength,this._filename=(0,P.extractFilenameFromHeader)(M)};if(this._request=null,this._url.protocol==="http:"){const x=require$$0;this._request=x.request(g(this._url,m.httpHeaders),E)}else{const x=require$$0;this._request=x.request(g(this._url,m.httpHeaders),E)}this._request.on("error",x=>{this._storedError=x,this._headersCapability.reject(x)}),this._request.end()}}class H extends D{constructor(m,E,x){super(m),this._httpHeaders={};for(const C in m.httpHeaders){const o=m.httpHeaders[C];o!==void 0&&(this._httpHeaders[C]=o)}this._httpHeaders.Range=`bytes=${E}-${x-1}`;const M=C=>{if(C.statusCode===404){const o=new d.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=o;return}this._setReadableStream(C)};if(this._request=null,this._url.protocol==="http:"){const C=require$$0;this._request=C.request(g(this._url,this._httpHeaders),M)}else{const C=require$$0;this._request=C.request(g(this._url,this._httpHeaders),M)}this._request.on("error",C=>{this._storedError=C}),this._request.end()}}class k extends G{constructor(m){super(m);let E=decodeURIComponent(this._url.path);e.test(this._url.href)&&(E=E.replace(/^\//,""));const x=require$$0;x.lstat(E,(M,C)=>{if(M){M.code==="ENOENT"&&(M=new d.MissingPDFException(`Missing PDF "${E}".`)),this._storedError=M,this._headersCapability.reject(M);return}this._contentLength=C.size,this._setReadableStream(x.createReadStream(E)),this._headersCapability.resolve()})}}class y extends D{constructor(m,E,x){super(m);let M=decodeURIComponent(this._url.path);e.test(this._url.href)&&(M=M.replace(/^\//,""));const C=require$$0;this._setReadableStream(C.createReadStream(M,{start:E,end:x-1}))}}},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.SVGGraphics=void 0;var d=vt(6),P=vt(1);const e={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},tt="http://www.w3.org/XML/1998/namespace",Tt="http://www.w3.org/1999/xlink",G=["butt","round","square"],D=["miter","round","bevel"],g=function(C,o="",u=!1){if(URL.createObjectURL&&typeof Blob<"u"&&!u)return URL.createObjectURL(new Blob([C],{type:o}));const L="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let _=`data:${o};base64,`;for(let l=0,F=C.length;l<F;l+=3){const A=C[l]&255,q=C[l+1]&255,Z=C[l+2]&255,R=A>>2,nt=(A&3)<<4|q>>4,pt=l+1<F?(q&15)<<2|Z>>6:64,bt=l+2<F?Z&63:64;_+=L[R]+L[nt]+L[pt]+L[bt]}return _},j=function(){const C=new Uint8Array([137,80,78,71,13,10,26,10]),o=12,u=new Int32Array(256);for(let Z=0;Z<256;Z++){let R=Z;for(let nt=0;nt<8;nt++)R=R&1?3988292384^R>>1&2147483647:R>>1&2147483647;u[Z]=R}function L(Z,R,nt){let pt=-1;for(let bt=R;bt<nt;bt++){const Y=(pt^Z[bt])&255,yt=u[Y];pt=pt>>>8^yt}return pt^-1}function _(Z,R,nt,pt){let bt=pt;const Y=R.length;nt[bt]=Y>>24&255,nt[bt+1]=Y>>16&255,nt[bt+2]=Y>>8&255,nt[bt+3]=Y&255,bt+=4,nt[bt]=Z.charCodeAt(0)&255,nt[bt+1]=Z.charCodeAt(1)&255,nt[bt+2]=Z.charCodeAt(2)&255,nt[bt+3]=Z.charCodeAt(3)&255,bt+=4,nt.set(R,bt),bt+=R.length;const yt=L(nt,pt+4,bt);nt[bt]=yt>>24&255,nt[bt+1]=yt>>16&255,nt[bt+2]=yt>>8&255,nt[bt+3]=yt&255}function l(Z,R,nt){let pt=1,bt=0;for(let Y=R;Y<nt;++Y)pt=(pt+(Z[Y]&255))%65521,bt=(bt+pt)%65521;return bt<<16|pt}function F(Z){if(!P.isNodeJS)return A(Z);try{const R=parseInt(process.versions.node)>=8?Z:Buffer.from(Z),nt=require$$0.deflateSync(R,{level:9});return nt instanceof Uint8Array?nt:new Uint8Array(nt)}catch(R){(0,P.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+R)}return A(Z)}function A(Z){let R=Z.length;const nt=65535,pt=Math.ceil(R/nt),bt=new Uint8Array(2+R+pt*5+4);let Y=0;bt[Y++]=120,bt[Y++]=156;let yt=0;for(;R>nt;)bt[Y++]=0,bt[Y++]=255,bt[Y++]=255,bt[Y++]=0,bt[Y++]=0,bt.set(Z.subarray(yt,yt+nt),Y),Y+=nt,yt+=nt,R-=nt;bt[Y++]=1,bt[Y++]=R&255,bt[Y++]=R>>8&255,bt[Y++]=~R&65535&255,bt[Y++]=(~R&65535)>>8&255,bt.set(Z.subarray(yt),Y),Y+=Z.length-yt;const kt=l(Z,0,Z.length);return bt[Y++]=kt>>24&255,bt[Y++]=kt>>16&255,bt[Y++]=kt>>8&255,bt[Y++]=kt&255,bt}function q(Z,R,nt,pt){const bt=Z.width,Y=Z.height;let yt,kt,Bt;const Gt=Z.data;switch(R){case P.ImageKind.GRAYSCALE_1BPP:kt=0,yt=1,Bt=bt+7>>3;break;case P.ImageKind.RGB_24BPP:kt=2,yt=8,Bt=bt*3;break;case P.ImageKind.RGBA_32BPP:kt=6,yt=8,Bt=bt*4;break;default:throw new Error("invalid format")}const T=new Uint8Array((1+Bt)*Y);let i=0,a=0;for(let z=0;z<Y;++z)T[i++]=0,T.set(Gt.subarray(a,a+Bt),i),a+=Bt,i+=Bt;if(R===P.ImageKind.GRAYSCALE_1BPP&&pt){i=0;for(let z=0;z<Y;z++){i++;for(let dt=0;dt<Bt;dt++)T[i++]^=255}}const s=new Uint8Array([bt>>24&255,bt>>16&255,bt>>8&255,bt&255,Y>>24&255,Y>>16&255,Y>>8&255,Y&255,yt,kt,0,0,0]),c=F(T),h=C.length+o*3+s.length+c.length,S=new Uint8Array(h);let I=0;return S.set(C,I),I+=C.length,_("IHDR",s,S,I),I+=o+s.length,_("IDATA",c,S,I),I+=o+c.length,_("IEND",new Uint8Array(0),S,I),g(S,"image/png",nt)}return function(R,nt,pt){const bt=R.kind===void 0?P.ImageKind.GRAYSCALE_1BPP:R.kind;return q(R,bt,nt,pt)}}();class H{constructor(){this.fontSizeScale=1,this.fontWeight=e.fontWeight,this.fontSize=0,this.textMatrix=P.IDENTITY_MATRIX,this.fontMatrix=P.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=P.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=e.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(o,u){this.x=o,this.y=u}}function k(C){let o=[];const u=[];for(const L of C){if(L.fn==="save"){o.push({fnId:92,fn:"group",items:[]}),u.push(o),o=o.at(-1).items;continue}L.fn==="restore"?o=u.pop():o.push(L)}return o}function y(C){if(Number.isInteger(C))return C.toString();const o=C.toFixed(10);let u=o.length-1;if(o[u]!=="0")return o;do u--;while(o[u]==="0");return o.substring(0,o[u]==="."?u:u+1)}function w(C){if(C[4]===0&&C[5]===0){if(C[1]===0&&C[2]===0)return C[0]===1&&C[3]===1?"":`scale(${y(C[0])} ${y(C[3])})`;if(C[0]===C[3]&&C[1]===-C[2]){const o=Math.acos(C[0])*180/Math.PI;return`rotate(${y(o)})`}}else if(C[0]===1&&C[1]===0&&C[2]===0&&C[3]===1)return`translate(${y(C[4])} ${y(C[5])})`;return`matrix(${y(C[0])} ${y(C[1])} ${y(C[2])} ${y(C[3])} ${y(C[4])} ${y(C[5])})`}let m=0,E=0,x=0;class M{constructor(o,u,L=!1){(0,d.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future."),this.svgFactory=new d.DOMSVGFactory,this.current=new H,this.transformMatrix=P.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=o,this.objs=u,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!L,this._operatorIdMapping=[];for(const _ in P.OPS)this._operatorIdMapping[P.OPS[_]]=_}getObject(o,u=null){return typeof o=="string"?o.startsWith("g_")?this.commonObjs.get(o):this.objs.get(o):u}save(){this.transformStack.push(this.transformMatrix);const o=this.current;this.extraStack.push(o),this.current=o.clone()}restore(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}group(o){this.save(),this.executeOpTree(o),this.restore()}loadDependencies(o){const u=o.fnArray,L=o.argsArray;for(let _=0,l=u.length;_<l;_++)if(u[_]===P.OPS.dependency)for(const F of L[_]){const A=F.startsWith("g_")?this.commonObjs:this.objs,q=new Promise(Z=>{A.get(F,Z)});this.current.dependencies.push(q)}return Promise.all(this.current.dependencies)}transform(o,u,L,_,l,F){const A=[o,u,L,_,l,F];this.transformMatrix=P.Util.transform(this.transformMatrix,A),this.tgrp=null}getSVG(o,u){this.viewport=u;const L=this._initialize(u);return this.loadDependencies(o).then(()=>(this.transformMatrix=P.IDENTITY_MATRIX,this.executeOpTree(this.convertOpList(o)),L))}convertOpList(o){const u=this._operatorIdMapping,L=o.argsArray,_=o.fnArray,l=[];for(let F=0,A=_.length;F<A;F++){const q=_[F];l.push({fnId:q,fn:u[q],args:L[F]})}return k(l)}executeOpTree(o){for(const u of o){const L=u.fn,_=u.fnId,l=u.args;switch(_|0){case P.OPS.beginText:this.beginText();break;case P.OPS.dependency:break;case P.OPS.setLeading:this.setLeading(l);break;case P.OPS.setLeadingMoveText:this.setLeadingMoveText(l[0],l[1]);break;case P.OPS.setFont:this.setFont(l);break;case P.OPS.showText:this.showText(l[0]);break;case P.OPS.showSpacedText:this.showText(l[0]);break;case P.OPS.endText:this.endText();break;case P.OPS.moveText:this.moveText(l[0],l[1]);break;case P.OPS.setCharSpacing:this.setCharSpacing(l[0]);break;case P.OPS.setWordSpacing:this.setWordSpacing(l[0]);break;case P.OPS.setHScale:this.setHScale(l[0]);break;case P.OPS.setTextMatrix:this.setTextMatrix(l[0],l[1],l[2],l[3],l[4],l[5]);break;case P.OPS.setTextRise:this.setTextRise(l[0]);break;case P.OPS.setTextRenderingMode:this.setTextRenderingMode(l[0]);break;case P.OPS.setLineWidth:this.setLineWidth(l[0]);break;case P.OPS.setLineJoin:this.setLineJoin(l[0]);break;case P.OPS.setLineCap:this.setLineCap(l[0]);break;case P.OPS.setMiterLimit:this.setMiterLimit(l[0]);break;case P.OPS.setFillRGBColor:this.setFillRGBColor(l[0],l[1],l[2]);break;case P.OPS.setStrokeRGBColor:this.setStrokeRGBColor(l[0],l[1],l[2]);break;case P.OPS.setStrokeColorN:this.setStrokeColorN(l);break;case P.OPS.setFillColorN:this.setFillColorN(l);break;case P.OPS.shadingFill:this.shadingFill(l[0]);break;case P.OPS.setDash:this.setDash(l[0],l[1]);break;case P.OPS.setRenderingIntent:this.setRenderingIntent(l[0]);break;case P.OPS.setFlatness:this.setFlatness(l[0]);break;case P.OPS.setGState:this.setGState(l[0]);break;case P.OPS.fill:this.fill();break;case P.OPS.eoFill:this.eoFill();break;case P.OPS.stroke:this.stroke();break;case P.OPS.fillStroke:this.fillStroke();break;case P.OPS.eoFillStroke:this.eoFillStroke();break;case P.OPS.clip:this.clip("nonzero");break;case P.OPS.eoClip:this.clip("evenodd");break;case P.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case P.OPS.paintImageXObject:this.paintImageXObject(l[0]);break;case P.OPS.paintInlineImageXObject:this.paintInlineImageXObject(l[0]);break;case P.OPS.paintImageMaskXObject:this.paintImageMaskXObject(l[0]);break;case P.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(l[0],l[1]);break;case P.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case P.OPS.closePath:this.closePath();break;case P.OPS.closeStroke:this.closeStroke();break;case P.OPS.closeFillStroke:this.closeFillStroke();break;case P.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case P.OPS.nextLine:this.nextLine();break;case P.OPS.transform:this.transform(l[0],l[1],l[2],l[3],l[4],l[5]);break;case P.OPS.constructPath:this.constructPath(l[0],l[1]);break;case P.OPS.endPath:this.endPath();break;case 92:this.group(u.items);break;default:(0,P.warn)(`Unimplemented operator ${L}`);break}}}setWordSpacing(o){this.current.wordSpacing=o}setCharSpacing(o){this.current.charSpacing=o}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(o,u,L,_,l,F){const A=this.current;A.textMatrix=A.lineMatrix=[o,u,L,_,l,F],A.textMatrixScale=Math.hypot(o,u),A.x=A.lineX=0,A.y=A.lineY=0,A.xcoords=[],A.ycoords=[],A.tspan=this.svgFactory.createElement("svg:tspan"),A.tspan.setAttributeNS(null,"font-family",A.fontFamily),A.tspan.setAttributeNS(null,"font-size",`${y(A.fontSize)}px`),A.tspan.setAttributeNS(null,"y",y(-A.y)),A.txtElement=this.svgFactory.createElement("svg:text"),A.txtElement.append(A.tspan)}beginText(){const o=this.current;o.x=o.lineX=0,o.y=o.lineY=0,o.textMatrix=P.IDENTITY_MATRIX,o.lineMatrix=P.IDENTITY_MATRIX,o.textMatrixScale=1,o.tspan=this.svgFactory.createElement("svg:tspan"),o.txtElement=this.svgFactory.createElement("svg:text"),o.txtgrp=this.svgFactory.createElement("svg:g"),o.xcoords=[],o.ycoords=[]}moveText(o,u){const L=this.current;L.x=L.lineX+=o,L.y=L.lineY+=u,L.xcoords=[],L.ycoords=[],L.tspan=this.svgFactory.createElement("svg:tspan"),L.tspan.setAttributeNS(null,"font-family",L.fontFamily),L.tspan.setAttributeNS(null,"font-size",`${y(L.fontSize)}px`),L.tspan.setAttributeNS(null,"y",y(-L.y))}showText(o){const u=this.current,L=u.font,_=u.fontSize;if(_===0)return;const l=u.fontSizeScale,F=u.charSpacing,A=u.wordSpacing,q=u.fontDirection,Z=u.textHScale*q,R=L.vertical,nt=R?1:-1,pt=L.defaultVMetrics,bt=_*u.fontMatrix[0];let Y=0;for(const Bt of o){if(Bt===null){Y+=q*A;continue}else if(typeof Bt=="number"){Y+=nt*Bt*_/1e3;continue}const Gt=(Bt.isSpace?A:0)+F,T=Bt.fontChar;let i,a,s=Bt.width;if(R){let h;const S=Bt.vmetric||pt;h=Bt.vmetric?S[1]:s*.5,h=-h*bt;const I=S[2]*bt;s=S?-S[0]:s,i=h/l,a=(Y+I)/l}else i=Y/l,a=0;(Bt.isInFont||L.missingFile)&&(u.xcoords.push(u.x+i),R&&u.ycoords.push(-u.y+a),u.tspan.textContent+=T);const c=R?s*bt-Gt*q:s*bt+Gt*q;Y+=c}u.tspan.setAttributeNS(null,"x",u.xcoords.map(y).join(" ")),R?u.tspan.setAttributeNS(null,"y",u.ycoords.map(y).join(" ")):u.tspan.setAttributeNS(null,"y",y(-u.y)),R?u.y-=Y:u.x+=Y*Z,u.tspan.setAttributeNS(null,"font-family",u.fontFamily),u.tspan.setAttributeNS(null,"font-size",`${y(u.fontSize)}px`),u.fontStyle!==e.fontStyle&&u.tspan.setAttributeNS(null,"font-style",u.fontStyle),u.fontWeight!==e.fontWeight&&u.tspan.setAttributeNS(null,"font-weight",u.fontWeight);const yt=u.textRenderingMode&P.TextRenderingMode.FILL_STROKE_MASK;if(yt===P.TextRenderingMode.FILL||yt===P.TextRenderingMode.FILL_STROKE?(u.fillColor!==e.fillColor&&u.tspan.setAttributeNS(null,"fill",u.fillColor),u.fillAlpha<1&&u.tspan.setAttributeNS(null,"fill-opacity",u.fillAlpha)):u.textRenderingMode===P.TextRenderingMode.ADD_TO_PATH?u.tspan.setAttributeNS(null,"fill","transparent"):u.tspan.setAttributeNS(null,"fill","none"),yt===P.TextRenderingMode.STROKE||yt===P.TextRenderingMode.FILL_STROKE){const Bt=1/(u.textMatrixScale||1);this._setStrokeAttributes(u.tspan,Bt)}let kt=u.textMatrix;u.textRise!==0&&(kt=kt.slice(),kt[5]+=u.textRise),u.txtElement.setAttributeNS(null,"transform",`${w(kt)} scale(${y(Z)}, -1)`),u.txtElement.setAttributeNS(tt,"xml:space","preserve"),u.txtElement.append(u.tspan),u.txtgrp.append(u.txtElement),this._ensureTransformGroup().append(u.txtElement)}setLeadingMoveText(o,u){this.setLeading(-u),this.moveText(o,u)}addFontStyle(o){if(!o.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.append(this.cssStyle));const u=g(o.data,o.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${o.loadedName}"; src: url(${u}); }
`}setFont(o){const u=this.current,L=this.commonObjs.get(o[0]);let _=o[1];u.font=L,this.embedFonts&&!L.missingFile&&!this.embeddedFonts[L.loadedName]&&(this.addFontStyle(L),this.embeddedFonts[L.loadedName]=L),u.fontMatrix=L.fontMatrix||P.FONT_IDENTITY_MATRIX;let l="normal";L.black?l="900":L.bold&&(l="bold");const F=L.italic?"italic":"normal";_<0?(_=-_,u.fontDirection=-1):u.fontDirection=1,u.fontSize=_,u.fontFamily=L.loadedName,u.fontWeight=l,u.fontStyle=F,u.tspan=this.svgFactory.createElement("svg:tspan"),u.tspan.setAttributeNS(null,"y",y(-u.y)),u.xcoords=[],u.ycoords=[]}endText(){var u;const o=this.current;o.textRenderingMode&P.TextRenderingMode.ADD_TO_PATH_FLAG&&((u=o.txtElement)!=null&&u.hasChildNodes())&&(o.element=o.txtElement,this.clip("nonzero"),this.endPath())}setLineWidth(o){o>0&&(this.current.lineWidth=o)}setLineCap(o){this.current.lineCap=G[o]}setLineJoin(o){this.current.lineJoin=D[o]}setMiterLimit(o){this.current.miterLimit=o}setStrokeAlpha(o){this.current.strokeAlpha=o}setStrokeRGBColor(o,u,L){this.current.strokeColor=P.Util.makeHexColor(o,u,L)}setFillAlpha(o){this.current.fillAlpha=o}setFillRGBColor(o,u,L){this.current.fillColor=P.Util.makeHexColor(o,u,L),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}setStrokeColorN(o){this.current.strokeColor=this._makeColorN_Pattern(o)}setFillColorN(o){this.current.fillColor=this._makeColorN_Pattern(o)}shadingFill(o){const{width:u,height:L}=this.viewport,_=P.Util.inverseTransform(this.transformMatrix),[l,F,A,q]=P.Util.getAxialAlignedBoundingBox([0,0,u,L],_),Z=this.svgFactory.createElement("svg:rect");Z.setAttributeNS(null,"x",l),Z.setAttributeNS(null,"y",F),Z.setAttributeNS(null,"width",A-l),Z.setAttributeNS(null,"height",q-F),Z.setAttributeNS(null,"fill",this._makeShadingPattern(o)),this.current.fillAlpha<1&&Z.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().append(Z)}_makeColorN_Pattern(o){return o[0]==="TilingPattern"?this._makeTilingPattern(o):this._makeShadingPattern(o)}_makeTilingPattern(o){const u=o[1],L=o[2],_=o[3]||P.IDENTITY_MATRIX,[l,F,A,q]=o[4],Z=o[5],R=o[6],nt=o[7],pt=`shading${x++}`,[bt,Y,yt,kt]=P.Util.normalizeRect([...P.Util.applyTransform([l,F],_),...P.Util.applyTransform([A,q],_)]),[Bt,Gt]=P.Util.singularValueDecompose2dScale(_),T=Z*Bt,i=R*Gt,a=this.svgFactory.createElement("svg:pattern");a.setAttributeNS(null,"id",pt),a.setAttributeNS(null,"patternUnits","userSpaceOnUse"),a.setAttributeNS(null,"width",T),a.setAttributeNS(null,"height",i),a.setAttributeNS(null,"x",`${bt}`),a.setAttributeNS(null,"y",`${Y}`);const s=this.svg,c=this.transformMatrix,h=this.current.fillColor,S=this.current.strokeColor,I=this.svgFactory.create(yt-bt,kt-Y);if(this.svg=I,this.transformMatrix=_,nt===2){const z=P.Util.makeHexColor(...u);this.current.fillColor=z,this.current.strokeColor=z}return this.executeOpTree(this.convertOpList(L)),this.svg=s,this.transformMatrix=c,this.current.fillColor=h,this.current.strokeColor=S,a.append(I.childNodes[0]),this.defs.append(a),`url(#${pt})`}_makeShadingPattern(o){switch(typeof o=="string"&&(o=this.objs.get(o)),o[0]){case"RadialAxial":const u=`shading${x++}`,L=o[3];let _;switch(o[1]){case"axial":const l=o[4],F=o[5];_=this.svgFactory.createElement("svg:linearGradient"),_.setAttributeNS(null,"id",u),_.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),_.setAttributeNS(null,"x1",l[0]),_.setAttributeNS(null,"y1",l[1]),_.setAttributeNS(null,"x2",F[0]),_.setAttributeNS(null,"y2",F[1]);break;case"radial":const A=o[4],q=o[5],Z=o[6],R=o[7];_=this.svgFactory.createElement("svg:radialGradient"),_.setAttributeNS(null,"id",u),_.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),_.setAttributeNS(null,"cx",q[0]),_.setAttributeNS(null,"cy",q[1]),_.setAttributeNS(null,"r",R),_.setAttributeNS(null,"fx",A[0]),_.setAttributeNS(null,"fy",A[1]),_.setAttributeNS(null,"fr",Z);break;default:throw new Error(`Unknown RadialAxial type: ${o[1]}`)}for(const l of L){const F=this.svgFactory.createElement("svg:stop");F.setAttributeNS(null,"offset",l[0]),F.setAttributeNS(null,"stop-color",l[1]),_.append(F)}return this.defs.append(_),`url(#${u})`;case"Mesh":return(0,P.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${o[0]}`)}}setDash(o,u){this.current.dashArray=o,this.current.dashPhase=u}constructPath(o,u){const L=this.current;let _=L.x,l=L.y,F=[],A=0;for(const q of o)switch(q|0){case P.OPS.rectangle:_=u[A++],l=u[A++];const Z=u[A++],R=u[A++],nt=_+Z,pt=l+R;F.push("M",y(_),y(l),"L",y(nt),y(l),"L",y(nt),y(pt),"L",y(_),y(pt),"Z");break;case P.OPS.moveTo:_=u[A++],l=u[A++],F.push("M",y(_),y(l));break;case P.OPS.lineTo:_=u[A++],l=u[A++],F.push("L",y(_),y(l));break;case P.OPS.curveTo:_=u[A+4],l=u[A+5],F.push("C",y(u[A]),y(u[A+1]),y(u[A+2]),y(u[A+3]),y(_),y(l)),A+=6;break;case P.OPS.curveTo2:F.push("C",y(_),y(l),y(u[A]),y(u[A+1]),y(u[A+2]),y(u[A+3])),_=u[A+2],l=u[A+3],A+=4;break;case P.OPS.curveTo3:_=u[A+2],l=u[A+3],F.push("C",y(u[A]),y(u[A+1]),y(_),y(l),y(_),y(l)),A+=4;break;case P.OPS.closePath:F.push("Z");break}F=F.join(" "),L.path&&o.length>0&&o[0]!==P.OPS.rectangle&&o[0]!==P.OPS.moveTo?F=L.path.getAttributeNS(null,"d")+F:(L.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().append(L.path)),L.path.setAttributeNS(null,"d",F),L.path.setAttributeNS(null,"fill","none"),L.element=L.path,L.setCurrentPoint(_,l)}endPath(){const o=this.current;if(o.path=null,!this.pendingClip)return;if(!o.element){this.pendingClip=null;return}const u=`clippath${m++}`,L=this.svgFactory.createElement("svg:clipPath");L.setAttributeNS(null,"id",u),L.setAttributeNS(null,"transform",w(this.transformMatrix));const _=o.element.cloneNode(!0);if(this.pendingClip==="evenodd"?_.setAttributeNS(null,"clip-rule","evenodd"):_.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,L.append(_),this.defs.append(L),o.activeClipUrl){o.clipGroup=null;for(const l of this.extraStack)l.clipGroup=null;L.setAttributeNS(null,"clip-path",o.activeClipUrl)}o.activeClipUrl=`url(#${u})`,this.tgrp=null}clip(o){this.pendingClip=o}closePath(){const o=this.current;if(o.path){const u=`${o.path.getAttributeNS(null,"d")}Z`;o.path.setAttributeNS(null,"d",u)}}setLeading(o){this.current.leading=-o}setTextRise(o){this.current.textRise=o}setTextRenderingMode(o){this.current.textRenderingMode=o}setHScale(o){this.current.textHScale=o/100}setRenderingIntent(o){}setFlatness(o){}setGState(o){for(const[u,L]of o)switch(u){case"LW":this.setLineWidth(L);break;case"LC":this.setLineCap(L);break;case"LJ":this.setLineJoin(L);break;case"ML":this.setMiterLimit(L);break;case"D":this.setDash(L[0],L[1]);break;case"RI":this.setRenderingIntent(L);break;case"FL":this.setFlatness(L);break;case"Font":this.setFont(L);break;case"CA":this.setStrokeAlpha(L);break;case"ca":this.setFillAlpha(L);break;default:(0,P.warn)(`Unimplemented graphic state operator ${u}`);break}}fill(){const o=this.current;o.element&&(o.element.setAttributeNS(null,"fill",o.fillColor),o.element.setAttributeNS(null,"fill-opacity",o.fillAlpha),this.endPath())}stroke(){const o=this.current;o.element&&(this._setStrokeAttributes(o.element),o.element.setAttributeNS(null,"fill","none"),this.endPath())}_setStrokeAttributes(o,u=1){const L=this.current;let _=L.dashArray;u!==1&&_.length>0&&(_=_.map(function(l){return u*l})),o.setAttributeNS(null,"stroke",L.strokeColor),o.setAttributeNS(null,"stroke-opacity",L.strokeAlpha),o.setAttributeNS(null,"stroke-miterlimit",y(L.miterLimit)),o.setAttributeNS(null,"stroke-linecap",L.lineCap),o.setAttributeNS(null,"stroke-linejoin",L.lineJoin),o.setAttributeNS(null,"stroke-width",y(u*L.lineWidth)+"px"),o.setAttributeNS(null,"stroke-dasharray",_.map(y).join(" ")),o.setAttributeNS(null,"stroke-dashoffset",y(u*L.dashPhase)+"px")}eoFill(){var o;(o=this.current.element)==null||o.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}fillStroke(){this.stroke(),this.fill()}eoFillStroke(){var o;(o=this.current.element)==null||o.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}closeStroke(){this.closePath(),this.stroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.closePath(),this.eoFillStroke()}paintSolidColorImageMask(){const o=this.svgFactory.createElement("svg:rect");o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y","0"),o.setAttributeNS(null,"width","1px"),o.setAttributeNS(null,"height","1px"),o.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().append(o)}paintImageXObject(o){const u=this.getObject(o);if(!u){(0,P.warn)(`Dependent image with object ID ${o} is not ready yet`);return}this.paintInlineImageXObject(u)}paintInlineImageXObject(o,u){const L=o.width,_=o.height,l=j(o,this.forceDataSchema,!!u),F=this.svgFactory.createElement("svg:rect");F.setAttributeNS(null,"x","0"),F.setAttributeNS(null,"y","0"),F.setAttributeNS(null,"width",y(L)),F.setAttributeNS(null,"height",y(_)),this.current.element=F,this.clip("nonzero");const A=this.svgFactory.createElement("svg:image");A.setAttributeNS(Tt,"xlink:href",l),A.setAttributeNS(null,"x","0"),A.setAttributeNS(null,"y",y(-_)),A.setAttributeNS(null,"width",y(L)+"px"),A.setAttributeNS(null,"height",y(_)+"px"),A.setAttributeNS(null,"transform",`scale(${y(1/L)} ${y(-1/_)})`),u?u.append(A):this._ensureTransformGroup().append(A)}paintImageMaskXObject(o){const u=this.getObject(o.data,o);if(u.bitmap){(0,P.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");return}const L=this.current,_=u.width,l=u.height,F=L.fillColor;L.maskId=`mask${E++}`;const A=this.svgFactory.createElement("svg:mask");A.setAttributeNS(null,"id",L.maskId);const q=this.svgFactory.createElement("svg:rect");q.setAttributeNS(null,"x","0"),q.setAttributeNS(null,"y","0"),q.setAttributeNS(null,"width",y(_)),q.setAttributeNS(null,"height",y(l)),q.setAttributeNS(null,"fill",F),q.setAttributeNS(null,"mask",`url(#${L.maskId})`),this.defs.append(A),this._ensureTransformGroup().append(q),this.paintInlineImageXObject(u,A)}paintFormXObjectBegin(o,u){if(Array.isArray(o)&&o.length===6&&this.transform(o[0],o[1],o[2],o[3],o[4],o[5]),u){const L=u[2]-u[0],_=u[3]-u[1],l=this.svgFactory.createElement("svg:rect");l.setAttributeNS(null,"x",u[0]),l.setAttributeNS(null,"y",u[1]),l.setAttributeNS(null,"width",y(L)),l.setAttributeNS(null,"height",y(_)),this.current.element=l,this.clip("nonzero"),this.endPath()}}paintFormXObjectEnd(){}_initialize(o){const u=this.svgFactory.create(o.width,o.height),L=this.svgFactory.createElement("svg:defs");u.append(L),this.defs=L;const _=this.svgFactory.createElement("svg:g");return _.setAttributeNS(null,"transform",w(o.transform)),u.append(_),this.svg=_,u}_ensureClipGroup(){if(!this.current.clipGroup){const o=this.svgFactory.createElement("svg:g");o.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.append(o),this.current.clipGroup=o}return this.current.clipGroup}_ensureTransformGroup(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",w(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)),this.tgrp}}v.SVGGraphics=M},(f,v)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.XfaText=void 0;class vt{static textContent(P){const e=[],tt={items:e,styles:Object.create(null)};function Tt(G){var j;if(!G)return;let D=null;const g=G.name;if(g==="#text")D=G.value;else if(vt.shouldBuildText(g))(j=G==null?void 0:G.attributes)!=null&&j.textContent?D=G.attributes.textContent:G.value&&(D=G.value);else return;if(D!==null&&e.push({str:D}),!!G.children)for(const H of G.children)Tt(H)}return Tt(P),tt}static shouldBuildText(P){return!(P==="textarea"||P==="input"||P==="option"||P==="select")}}v.XfaText=vt},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.TextLayerRenderTask=void 0,v.renderTextLayer=w,v.updateTextLayer=m;var d=vt(1),P=vt(6);const e=1e5,tt=30,Tt=.8,G=new Map;function D(E,x){let M;if(x&&d.FeatureTest.isOffscreenCanvasSupported)M=new OffscreenCanvas(E,E).getContext("2d",{alpha:!1});else{const C=document.createElement("canvas");C.width=C.height=E,M=C.getContext("2d",{alpha:!1})}return M}function g(E,x){const M=G.get(E);if(M)return M;const C=D(tt,x);C.font=`${tt}px ${E}`;const o=C.measureText("");let u=o.fontBoundingBoxAscent,L=Math.abs(o.fontBoundingBoxDescent);if(u){const l=u/(u+L);return G.set(E,l),C.canvas.width=C.canvas.height=0,l}C.strokeStyle="red",C.clearRect(0,0,tt,tt),C.strokeText("g",0,0);let _=C.getImageData(0,0,tt,tt).data;L=0;for(let l=_.length-1-3;l>=0;l-=4)if(_[l]>0){L=Math.ceil(l/4/tt);break}C.clearRect(0,0,tt,tt),C.strokeText("A",0,tt),_=C.getImageData(0,0,tt,tt).data,u=0;for(let l=0,F=_.length;l<F;l+=4)if(_[l]>0){u=tt-Math.floor(l/4/tt);break}if(C.canvas.width=C.canvas.height=0,u){const l=u/(u+L);return G.set(E,l),l}return G.set(E,Tt),Tt}function j(E,x,M){const C=document.createElement("span"),o={angle:0,canvasWidth:0,hasText:x.str!=="",hasEOL:x.hasEOL,fontSize:0};E._textDivs.push(C);const u=d.Util.transform(E._transform,x.transform);let L=Math.atan2(u[1],u[0]);const _=M[x.fontName];_.vertical&&(L+=Math.PI/2);const l=Math.hypot(u[2],u[3]),F=l*g(_.fontFamily,E._isOffscreenCanvasSupported);let A,q;L===0?(A=u[4],q=u[5]-F):(A=u[4]+F*Math.sin(L),q=u[5]-F*Math.cos(L));const Z="calc(var(--scale-factor)*",R=C.style;E._container===E._rootContainer?(R.left=`${(100*A/E._pageWidth).toFixed(2)}%`,R.top=`${(100*q/E._pageHeight).toFixed(2)}%`):(R.left=`${Z}${A.toFixed(2)}px)`,R.top=`${Z}${q.toFixed(2)}px)`),R.fontSize=`${Z}${l.toFixed(2)}px)`,R.fontFamily=_.fontFamily,o.fontSize=l,C.setAttribute("role","presentation"),C.textContent=x.str,C.dir=x.dir,E._fontInspectorEnabled&&(C.dataset.fontName=x.fontName),L!==0&&(o.angle=L*(180/Math.PI));let nt=!1;if(x.str.length>1)nt=!0;else if(x.str!==" "&&x.transform[0]!==x.transform[3]){const pt=Math.abs(x.transform[0]),bt=Math.abs(x.transform[3]);pt!==bt&&Math.max(pt,bt)/Math.min(pt,bt)>1.5&&(nt=!0)}nt&&(o.canvasWidth=_.vertical?x.height:x.width),E._textDivProperties.set(C,o),E._isReadableStream&&E._layoutText(C)}function H(E){const{div:x,scale:M,properties:C,ctx:o,prevFontSize:u,prevFontFamily:L}=E,{style:_}=x;let l="";if(C.canvasWidth!==0&&C.hasText){const{fontFamily:F}=_,{canvasWidth:A,fontSize:q}=C;(u!==q||L!==F)&&(o.font=`${q*M}px ${F}`,E.prevFontSize=q,E.prevFontFamily=F);const{width:Z}=o.measureText(x.textContent);Z>0&&(l=`scaleX(${A*M/Z})`)}C.angle!==0&&(l=`rotate(${C.angle}deg) ${l}`),l.length>0&&(_.transform=l)}function k(E){if(E._canceled)return;const x=E._textDivs,M=E._capability;if(x.length>e){M.resolve();return}if(!E._isReadableStream)for(const o of x)E._layoutText(o);M.resolve()}class y{constructor({textContentSource:x,container:M,viewport:C,textDivs:o,textDivProperties:u,textContentItemsStr:L,isOffscreenCanvasSupported:_}){var Z;this._textContentSource=x,this._isReadableStream=x instanceof ReadableStream,this._container=this._rootContainer=M,this._textDivs=o||[],this._textContentItemsStr=L||[],this._isOffscreenCanvasSupported=_,this._fontInspectorEnabled=!!((Z=globalThis.FontInspector)!=null&&Z.enabled),this._reader=null,this._textDivProperties=u||new WeakMap,this._canceled=!1,this._capability=new d.PromiseCapability,this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:C.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:D(0,_)};const{pageWidth:l,pageHeight:F,pageX:A,pageY:q}=C.rawDims;this._transform=[1,0,0,-1,-A,q+F],this._pageWidth=l,this._pageHeight=F,(0,P.setLayerDimensions)(M,C),this._capability.promise.finally(()=>{this._layoutTextParams=null}).catch(()=>{})}get promise(){return this._capability.promise}cancel(){this._canceled=!0,this._reader&&(this._reader.cancel(new d.AbortException("TextLayer task cancelled.")).catch(()=>{}),this._reader=null),this._capability.reject(new d.AbortException("TextLayer task cancelled."))}_processItems(x,M){for(const C of x){if(C.str===void 0){if(C.type==="beginMarkedContentProps"||C.type==="beginMarkedContent"){const o=this._container;this._container=document.createElement("span"),this._container.classList.add("markedContent"),C.id!==null&&this._container.setAttribute("id",`${C.id}`),o.append(this._container)}else C.type==="endMarkedContent"&&(this._container=this._container.parentNode);continue}this._textContentItemsStr.push(C.str),j(this,C,M)}}_layoutText(x){const M=this._layoutTextParams.properties=this._textDivProperties.get(x);if(this._layoutTextParams.div=x,H(this._layoutTextParams),M.hasText&&this._container.append(x),M.hasEOL){const C=document.createElement("br");C.setAttribute("role","presentation"),this._container.append(C)}}_render(){const x=new d.PromiseCapability;let M=Object.create(null);if(this._isReadableStream){const C=()=>{this._reader.read().then(({value:o,done:u})=>{if(u){x.resolve();return}Object.assign(M,o.styles),this._processItems(o.items,M),C()},x.reject)};this._reader=this._textContentSource.getReader(),C()}else if(this._textContentSource){const{items:C,styles:o}=this._textContentSource;this._processItems(C,o),x.resolve()}else throw new Error('No "textContentSource" parameter specified.');x.promise.then(()=>{M=null,k(this)},this._capability.reject)}}v.TextLayerRenderTask=y;function w(E){!E.textContentSource&&(E.textContent||E.textContentStream)&&((0,P.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead."),E.textContentSource=E.textContent||E.textContentStream);const{container:x,viewport:M}=E,C=getComputedStyle(x),o=C.getPropertyValue("visibility"),u=parseFloat(C.getPropertyValue("--scale-factor"));o==="visible"&&(!u||Math.abs(u-M.scale)>1e-5)&&console.error("The `--scale-factor` CSS-variable must be set, to the same value as `viewport.scale`, either on the `container`-element itself or higher up in the DOM.");const L=new y(E);return L._render(),L}function m({container:E,viewport:x,textDivs:M,textDivProperties:C,isOffscreenCanvasSupported:o,mustRotate:u=!0,mustRescale:L=!0}){if(u&&(0,P.setLayerDimensions)(E,{rotation:x.rotation}),L){const _=D(0,o),F={prevFontSize:null,prevFontFamily:null,div:null,scale:x.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:_};for(const A of M)F.properties=C.get(A),F.div=A,H(F)}}},(f,v,vt)=>{var g,j,H,k,y,w,m,E,x,M,C,Ji,u,fi,_,Zi,F,Qi;Object.defineProperty(v,"__esModule",{value:!0}),v.AnnotationEditorLayer=void 0;var d=vt(1),P=vt(4),e=vt(28),tt=vt(33),Tt=vt(6),G=vt(34);const q=class q{constructor({uiManager:R,pageIndex:nt,div:pt,accessibilityManager:bt,annotationLayer:Y,viewport:yt,l10n:kt}){U(this,C);U(this,u);U(this,_);U(this,F);U(this,g,void 0);U(this,j,!1);U(this,H,null);U(this,k,this.pointerup.bind(this));U(this,y,this.pointerdown.bind(this));U(this,w,new Map);U(this,m,!1);U(this,E,!1);U(this,x,!1);U(this,M,void 0);const Bt=[e.FreeTextEditor,tt.InkEditor,G.StampEditor];if(!q._initialized){q._initialized=!0;for(const Gt of Bt)Gt.initialize(kt)}R.registerEditorTypes(Bt),ft(this,M,R),this.pageIndex=nt,this.div=pt,ft(this,g,bt),ft(this,H,Y),this.viewport=yt,t(this,M).addLayer(this)}get isEmpty(){return t(this,w).size===0}updateToolbar(R){t(this,M).updateToolbar(R)}updateMode(R=t(this,M).getMode()){Q(this,F,Qi).call(this),R===d.AnnotationEditorType.INK?(this.addInkEditorIfNeeded(!1),this.disableClick()):this.enableClick(),R!==d.AnnotationEditorType.NONE&&(this.div.classList.toggle("freeTextEditing",R===d.AnnotationEditorType.FREETEXT),this.div.classList.toggle("inkEditing",R===d.AnnotationEditorType.INK),this.div.classList.toggle("stampEditing",R===d.AnnotationEditorType.STAMP),this.div.hidden=!1)}addInkEditorIfNeeded(R){if(!R&&t(this,M).getMode()!==d.AnnotationEditorType.INK)return;if(!R){for(const pt of t(this,w).values())if(pt.isEmpty()){pt.setInBackground();return}}Q(this,u,fi).call(this,{offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(R){t(this,M).setEditingState(R)}addCommands(R){t(this,M).addCommands(R)}enable(){this.div.style.pointerEvents="auto";const R=new Set;for(const pt of t(this,w).values())pt.enableEditing(),pt.annotationElementId&&R.add(pt.annotationElementId);if(!t(this,H))return;const nt=t(this,H).getEditableAnnotations();for(const pt of nt){if(pt.hide(),t(this,M).isDeletedAnnotationElement(pt.data.id)||R.has(pt.data.id))continue;const bt=this.deserialize(pt);bt&&(this.addOrRebuild(bt),bt.enableEditing())}}disable(){var nt;ft(this,x,!0),this.div.style.pointerEvents="none";const R=new Set;for(const pt of t(this,w).values()){if(pt.disableEditing(),!pt.annotationElementId||pt.serialize()!==null){R.add(pt.annotationElementId);continue}(nt=this.getEditableAnnotation(pt.annotationElementId))==null||nt.show(),pt.remove()}if(t(this,H)){const pt=t(this,H).getEditableAnnotations();for(const bt of pt){const{id:Y}=bt.data;R.has(Y)||t(this,M).isDeletedAnnotationElement(Y)||bt.show()}}Q(this,F,Qi).call(this),this.isEmpty&&(this.div.hidden=!0),ft(this,x,!1)}getEditableAnnotation(R){var nt;return((nt=t(this,H))==null?void 0:nt.getEditableAnnotation(R))||null}setActiveEditor(R){t(this,M).getActive()!==R&&t(this,M).setActiveEditor(R)}enableClick(){this.div.addEventListener("pointerdown",t(this,y)),this.div.addEventListener("pointerup",t(this,k))}disableClick(){this.div.removeEventListener("pointerdown",t(this,y)),this.div.removeEventListener("pointerup",t(this,k))}attach(R){t(this,w).set(R.id,R);const{annotationElementId:nt}=R;nt&&t(this,M).isDeletedAnnotationElement(nt)&&t(this,M).removeDeletedAnnotationElement(R)}detach(R){var nt;t(this,w).delete(R.id),(nt=t(this,g))==null||nt.removePointerInTextLayer(R.contentDiv),!t(this,x)&&R.annotationElementId&&t(this,M).addDeletedAnnotationElement(R)}remove(R){this.detach(R),t(this,M).removeEditor(R),R.div.contains(document.activeElement)&&setTimeout(()=>{t(this,M).focusMainContainer()},0),R.div.remove(),R.isAttachedToDOM=!1,t(this,E)||this.addInkEditorIfNeeded(!1)}changeParent(R){var nt;R.parent!==this&&(R.annotationElementId&&(t(this,M).addDeletedAnnotationElement(R.annotationElementId),P.AnnotationEditor.deleteAnnotationElement(R),R.annotationElementId=null),this.attach(R),(nt=R.parent)==null||nt.detach(R),R.setParent(this),R.div&&R.isAttachedToDOM&&(R.div.remove(),this.div.append(R.div)))}add(R){if(this.changeParent(R),t(this,M).addEditor(R),this.attach(R),!R.isAttachedToDOM){const nt=R.render();this.div.append(nt),R.isAttachedToDOM=!0}R.fixAndSetPosition(),R.onceAdded(),t(this,M).addToAnnotationStorage(R)}moveEditorInDOM(R){var pt;if(!R.isAttachedToDOM)return;const{activeElement:nt}=document;R.div.contains(nt)&&(R._focusEventsAllowed=!1,setTimeout(()=>{R.div.contains(document.activeElement)?R._focusEventsAllowed=!0:(R.div.addEventListener("focusin",()=>{R._focusEventsAllowed=!0},{once:!0}),nt.focus())},0)),R._structTreeParentId=(pt=t(this,g))==null?void 0:pt.moveElementInDOM(this.div,R.div,R.contentDiv,!0)}addOrRebuild(R){R.needsToBeRebuilt()?R.rebuild():this.add(R)}addUndoableEditor(R){const nt=()=>R._uiManager.rebuild(R),pt=()=>{R.remove()};this.addCommands({cmd:nt,undo:pt,mustExec:!1})}getNextId(){return t(this,M).getId()}pasteEditor(R,nt){t(this,M).updateToolbar(R),t(this,M).updateMode(R);const{offsetX:pt,offsetY:bt}=Q(this,_,Zi).call(this),Y=this.getNextId(),yt=Q(this,C,Ji).call(this,{parent:this,id:Y,x:pt,y:bt,uiManager:t(this,M),isCentered:!0,...nt});yt&&this.add(yt)}deserialize(R){switch(R.annotationType??R.annotationEditorType){case d.AnnotationEditorType.FREETEXT:return e.FreeTextEditor.deserialize(R,this,t(this,M));case d.AnnotationEditorType.INK:return tt.InkEditor.deserialize(R,this,t(this,M));case d.AnnotationEditorType.STAMP:return G.StampEditor.deserialize(R,this,t(this,M))}return null}addNewEditor(){Q(this,u,fi).call(this,Q(this,_,Zi).call(this),!0)}setSelected(R){t(this,M).setSelected(R)}toggleSelected(R){t(this,M).toggleSelected(R)}isSelected(R){return t(this,M).isSelected(R)}unselect(R){t(this,M).unselect(R)}pointerup(R){const{isMac:nt}=d.FeatureTest.platform;if(!(R.button!==0||R.ctrlKey&&nt)&&R.target===this.div&&t(this,m)){if(ft(this,m,!1),!t(this,j)){ft(this,j,!0);return}if(t(this,M).getMode()===d.AnnotationEditorType.STAMP){t(this,M).unselectAll();return}Q(this,u,fi).call(this,R,!1)}}pointerdown(R){if(t(this,m)){ft(this,m,!1);return}const{isMac:nt}=d.FeatureTest.platform;if(R.button!==0||R.ctrlKey&&nt||R.target!==this.div)return;ft(this,m,!0);const pt=t(this,M).getActive();ft(this,j,!pt||pt.isEmpty())}findNewParent(R,nt,pt){const bt=t(this,M).findParent(nt,pt);return bt===null||bt===this?!1:(bt.changeParent(R),!0)}destroy(){var R,nt;((R=t(this,M).getActive())==null?void 0:R.parent)===this&&(t(this,M).commitOrRemove(),t(this,M).setActiveEditor(null));for(const pt of t(this,w).values())(nt=t(this,g))==null||nt.removePointerInTextLayer(pt.contentDiv),pt.setParent(null),pt.isAttachedToDOM=!1,pt.div.remove();this.div=null,t(this,w).clear(),t(this,M).removeLayer(this)}render({viewport:R}){this.viewport=R,(0,Tt.setLayerDimensions)(this.div,R);for(const nt of t(this,M).getEditors(this.pageIndex))this.add(nt);this.updateMode()}update({viewport:R}){t(this,M).commitOrRemove(),this.viewport=R,(0,Tt.setLayerDimensions)(this.div,{rotation:R.rotation}),this.updateMode()}get pageDimensions(){const{pageWidth:R,pageHeight:nt}=this.viewport.rawDims;return[R,nt]}};g=new WeakMap,j=new WeakMap,H=new WeakMap,k=new WeakMap,y=new WeakMap,w=new WeakMap,m=new WeakMap,E=new WeakMap,x=new WeakMap,M=new WeakMap,C=new WeakSet,Ji=function(R){switch(t(this,M).getMode()){case d.AnnotationEditorType.FREETEXT:return new e.FreeTextEditor(R);case d.AnnotationEditorType.INK:return new tt.InkEditor(R);case d.AnnotationEditorType.STAMP:return new G.StampEditor(R)}return null},u=new WeakSet,fi=function(R,nt){const pt=this.getNextId(),bt=Q(this,C,Ji).call(this,{parent:this,id:pt,x:R.offsetX,y:R.offsetY,uiManager:t(this,M),isCentered:nt});return bt&&this.add(bt),bt},_=new WeakSet,Zi=function(){const{x:R,y:nt,width:pt,height:bt}=this.div.getBoundingClientRect(),Y=Math.max(0,R),yt=Math.max(0,nt),kt=Math.min(window.innerWidth,R+pt),Bt=Math.min(window.innerHeight,nt+bt),Gt=(Y+kt)/2-R,T=(yt+Bt)/2-nt,[i,a]=this.viewport.rotation%180===0?[Gt,T]:[T,Gt];return{offsetX:i,offsetY:a}},F=new WeakSet,Qi=function(){ft(this,E,!0);for(const R of t(this,w).values())R.isEmpty()&&R.remove();ft(this,E,!1)},Je(q,"_initialized",!1);let D=q;v.AnnotationEditorLayer=D},(f,v,vt)=>{var G,D,g,j,H,k,y,w,m,E,zr,M,Vr,o,Gr,L,Jn,l,tr,A,qr,Z,er;Object.defineProperty(v,"__esModule",{value:!0}),v.FreeTextEditor=void 0;var d=vt(1),P=vt(5),e=vt(4),tt=vt(29);const nt=class nt extends e.AnnotationEditor{constructor(Y){super({...Y,name:"freeTextEditor"});U(this,E);U(this,M);U(this,o);U(this,L);U(this,l);U(this,A);U(this,Z);U(this,G,this.editorDivBlur.bind(this));U(this,D,this.editorDivFocus.bind(this));U(this,g,this.editorDivInput.bind(this));U(this,j,this.editorDivKeydown.bind(this));U(this,H,void 0);U(this,k,"");U(this,y,`${this.id}-editor`);U(this,w,void 0);U(this,m,null);ft(this,H,Y.color||nt._defaultColor||e.AnnotationEditor._defaultLineColor),ft(this,w,Y.fontSize||nt._defaultFontSize)}static get _keyboardManager(){const Y=nt.prototype,yt=Gt=>Gt.isEmpty(),kt=P.AnnotationEditorUIManager.TRANSLATE_SMALL,Bt=P.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,d.shadow)(this,"_keyboardManager",new P.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],Y.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],Y.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],Y._translateEmpty,{args:[-kt,0],checker:yt}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],Y._translateEmpty,{args:[-Bt,0],checker:yt}],[["ArrowRight","mac+ArrowRight"],Y._translateEmpty,{args:[kt,0],checker:yt}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],Y._translateEmpty,{args:[Bt,0],checker:yt}],[["ArrowUp","mac+ArrowUp"],Y._translateEmpty,{args:[0,-kt],checker:yt}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],Y._translateEmpty,{args:[0,-Bt],checker:yt}],[["ArrowDown","mac+ArrowDown"],Y._translateEmpty,{args:[0,kt],checker:yt}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],Y._translateEmpty,{args:[0,Bt],checker:yt}]]))}static initialize(Y){e.AnnotationEditor.initialize(Y,{strings:["free_text2_default_content","editor_free_text2_aria_label"]});const yt=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(yt.getPropertyValue("--freetext-padding"))}static updateDefaultParams(Y,yt){switch(Y){case d.AnnotationEditorParamsType.FREETEXT_SIZE:nt._defaultFontSize=yt;break;case d.AnnotationEditorParamsType.FREETEXT_COLOR:nt._defaultColor=yt;break}}updateParams(Y,yt){switch(Y){case d.AnnotationEditorParamsType.FREETEXT_SIZE:Q(this,E,zr).call(this,yt);break;case d.AnnotationEditorParamsType.FREETEXT_COLOR:Q(this,M,Vr).call(this,yt);break}}static get defaultPropertiesToUpdate(){return[[d.AnnotationEditorParamsType.FREETEXT_SIZE,nt._defaultFontSize],[d.AnnotationEditorParamsType.FREETEXT_COLOR,nt._defaultColor||e.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[d.AnnotationEditorParamsType.FREETEXT_SIZE,t(this,w)],[d.AnnotationEditorParamsType.FREETEXT_COLOR,t(this,H)]]}_translateEmpty(Y,yt){this._uiManager.translateSelectedEditors(Y,yt,!0)}getInitialTranslation(){const Y=this.parentScale;return[-nt._internalPadding*Y,-(nt._internalPadding+t(this,w))*Y]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(d.AnnotationEditorType.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",t(this,j)),this.editorDiv.addEventListener("focus",t(this,D)),this.editorDiv.addEventListener("blur",t(this,G)),this.editorDiv.addEventListener("input",t(this,g)))}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",t(this,y)),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",t(this,j)),this.editorDiv.removeEventListener("focus",t(this,D)),this.editorDiv.removeEventListener("blur",t(this,G)),this.editorDiv.removeEventListener("input",t(this,g)),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freeTextEditing"))}focusin(Y){this._focusEventsAllowed&&(super.focusin(Y),Y.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(){var Y;if(this.width){Q(this,Z,er).call(this);return}this.enableEditMode(),this.editorDiv.focus(),(Y=this._initialOptions)!=null&&Y.isCentered&&this.center(),this._initialOptions=null}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freeTextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const Y=t(this,k),yt=ft(this,k,Q(this,o,Gr).call(this).trimEnd());if(Y===yt)return;const kt=Bt=>{if(ft(this,k,Bt),!Bt){this.remove();return}Q(this,l,tr).call(this),this._uiManager.rebuild(this),Q(this,L,Jn).call(this)};this.addCommands({cmd:()=>{kt(yt)},undo:()=>{kt(Y)},mustExec:!1}),Q(this,L,Jn).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(Y){this.enterInEditMode()}keydown(Y){Y.target===this.div&&Y.key==="Enter"&&(this.enterInEditMode(),Y.preventDefault())}editorDivKeydown(Y){nt._keyboardManager.exec(this,Y)}editorDivFocus(Y){this.isEditing=!0}editorDivBlur(Y){this.isEditing=!1}editorDivInput(Y){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let Y,yt;this.width&&(Y=this.x,yt=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",t(this,y)),this.enableEditing(),e.AnnotationEditor._l10nPromise.get("editor_free_text2_aria_label").then(Bt=>{var Gt;return(Gt=this.editorDiv)==null?void 0:Gt.setAttribute("aria-label",Bt)}),e.AnnotationEditor._l10nPromise.get("free_text2_default_content").then(Bt=>{var Gt;return(Gt=this.editorDiv)==null?void 0:Gt.setAttribute("default-content",Bt)}),this.editorDiv.contentEditable=!0;const{style:kt}=this.editorDiv;if(kt.fontSize=`calc(${t(this,w)}px * var(--scale-factor))`,kt.color=t(this,H),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),(0,P.bindEvents)(this,this.div,["dblclick","keydown"]),this.width){const[Bt,Gt]=this.parentDimensions;if(this.annotationElementId){const{position:T}=t(this,m);let[i,a]=this.getInitialTranslation();[i,a]=this.pageTranslationToScreen(i,a);const[s,c]=this.pageDimensions,[h,S]=this.pageTranslation;let I,z;switch(this.rotation){case 0:I=Y+(T[0]-h)/s,z=yt+this.height-(T[1]-S)/c;break;case 90:I=Y+(T[0]-h)/s,z=yt-(T[1]-S)/c,[i,a]=[a,-i];break;case 180:I=Y-this.width+(T[0]-h)/s,z=yt-(T[1]-S)/c,[i,a]=[-i,-a];break;case 270:I=Y+(T[0]-h-this.height*c)/s,z=yt+(T[1]-S-this.width*s)/c,[i,a]=[-a,i];break}this.setAt(I*Bt,z*Gt,i,a)}else this.setAt(Y*Bt,yt*Gt,this.width*Bt,this.height*Gt);Q(this,l,tr).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}get contentDiv(){return this.editorDiv}static deserialize(Y,yt,kt){let Bt=null;if(Y instanceof tt.FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:T,fontColor:i},rect:a,rotation:s,id:c},textContent:h,textPosition:S,parent:{page:{pageNumber:I}}}=Y;if(!h||h.length===0)return null;Bt=Y={annotationType:d.AnnotationEditorType.FREETEXT,color:Array.from(i),fontSize:T,value:h.join(`
`),position:S,pageIndex:I-1,rect:a,rotation:s,id:c,deleted:!1}}const Gt=super.deserialize(Y,yt,kt);return ft(Gt,w,Y.fontSize),ft(Gt,H,d.Util.makeHexColor(...Y.color)),ft(Gt,k,Y.value),Gt.annotationElementId=Y.id||null,ft(Gt,m,Bt),Gt}serialize(Y=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const yt=nt._internalPadding*this.parentScale,kt=this.getRect(yt,yt),Bt=e.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:t(this,H)),Gt={annotationType:d.AnnotationEditorType.FREETEXT,color:Bt,fontSize:t(this,w),value:t(this,k),pageIndex:this.pageIndex,rect:kt,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return Y?Gt:this.annotationElementId&&!Q(this,A,qr).call(this,Gt)?null:(Gt.id=this.annotationElementId,Gt)}};G=new WeakMap,D=new WeakMap,g=new WeakMap,j=new WeakMap,H=new WeakMap,k=new WeakMap,y=new WeakMap,w=new WeakMap,m=new WeakMap,E=new WeakSet,zr=function(Y){const yt=Bt=>{this.editorDiv.style.fontSize=`calc(${Bt}px * var(--scale-factor))`,this.translate(0,-(Bt-t(this,w))*this.parentScale),ft(this,w,Bt),Q(this,L,Jn).call(this)},kt=t(this,w);this.addCommands({cmd:()=>{yt(Y)},undo:()=>{yt(kt)},mustExec:!0,type:d.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},M=new WeakSet,Vr=function(Y){const yt=t(this,H);this.addCommands({cmd:()=>{ft(this,H,this.editorDiv.style.color=Y)},undo:()=>{ft(this,H,this.editorDiv.style.color=yt)},mustExec:!0,type:d.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},o=new WeakSet,Gr=function(){const Y=this.editorDiv.getElementsByTagName("div");if(Y.length===0)return this.editorDiv.innerText;const yt=[];for(const kt of Y)yt.push(kt.innerText.replace(/\r\n?|\n/,""));return yt.join(`
`)},L=new WeakSet,Jn=function(){const[Y,yt]=this.parentDimensions;let kt;if(this.isAttachedToDOM)kt=this.div.getBoundingClientRect();else{const{currentLayer:Bt,div:Gt}=this,T=Gt.style.display;Gt.style.display="hidden",Bt.div.append(this.div),kt=Gt.getBoundingClientRect(),Gt.remove(),Gt.style.display=T}this.rotation%180===this.parentRotation%180?(this.width=kt.width/Y,this.height=kt.height/yt):(this.width=kt.height/Y,this.height=kt.width/yt),this.fixAndSetPosition()},l=new WeakSet,tr=function(){if(this.editorDiv.replaceChildren(),!!t(this,k))for(const Y of t(this,k).split(`
`)){const yt=document.createElement("div");yt.append(Y?document.createTextNode(Y):document.createElement("br")),this.editorDiv.append(yt)}},A=new WeakSet,qr=function(Y){const{value:yt,fontSize:kt,color:Bt,rect:Gt,pageIndex:T}=t(this,m);return Y.value!==yt||Y.fontSize!==kt||Y.rect.some((i,a)=>Math.abs(i-Gt[a])>=1)||Y.color.some((i,a)=>i!==Bt[a])||Y.pageIndex!==T},Z=new WeakSet,er=function(Y=!1){if(!this.annotationElementId)return;if(Q(this,L,Jn).call(this),!Y&&(this.width===0||this.height===0)){setTimeout(()=>Q(this,Z,er).call(this,!0),0);return}const yt=nt._internalPadding*this.parentScale;t(this,m).rect=this.getRect(yt,yt)},Je(nt,"_freeTextDefaultContent",""),Je(nt,"_internalPadding",0),Je(nt,"_defaultColor",null),Je(nt,"_defaultFontSize",10),Je(nt,"_type","freetext");let Tt=nt;v.FreeTextEditor=Tt},(f,v,vt)=>{var a,c,In,S,Xr,z,dt,ct,mt,Et,Dt,wt,$t,ie,It,Jt,Ct,ht,at,St,jt,Ht,le,$r,he,pi,_e,nr,we,ir,et,$,Ft,At,xe,je,st,rr,Qt,Wt,te,ne,Yr,Lt,sr;Object.defineProperty(v,"__esModule",{value:!0}),v.StampAnnotationElement=v.InkAnnotationElement=v.FreeTextAnnotationElement=v.AnnotationLayer=void 0;var d=vt(1),P=vt(6),e=vt(3),tt=vt(30),Tt=vt(31),G=vt(32);const D=1e3,g=9,j=new WeakSet;function H(Zt){return{width:Zt[2]-Zt[0],height:Zt[3]-Zt[1]}}class k{static create(N){switch(N.data.annotationType){case d.AnnotationType.LINK:return new w(N);case d.AnnotationType.TEXT:return new m(N);case d.AnnotationType.WIDGET:switch(N.data.fieldType){case"Tx":return new x(N);case"Btn":return N.data.radioButton?new o(N):N.data.checkBox?new C(N):new u(N);case"Ch":return new L(N);case"Sig":return new M(N)}return new E(N);case d.AnnotationType.POPUP:return new _(N);case d.AnnotationType.FREETEXT:return new F(N);case d.AnnotationType.LINE:return new A(N);case d.AnnotationType.SQUARE:return new q(N);case d.AnnotationType.CIRCLE:return new Z(N);case d.AnnotationType.POLYLINE:return new R(N);case d.AnnotationType.CARET:return new pt(N);case d.AnnotationType.INK:return new bt(N);case d.AnnotationType.POLYGON:return new nt(N);case d.AnnotationType.HIGHLIGHT:return new Y(N);case d.AnnotationType.UNDERLINE:return new yt(N);case d.AnnotationType.SQUIGGLY:return new kt(N);case d.AnnotationType.STRIKEOUT:return new Bt(N);case d.AnnotationType.STAMP:return new Gt(N);case d.AnnotationType.FILEATTACHMENT:return new T(N);default:return new y(N)}}}const s=class s{constructor(N,{isRenderable:n=!1,ignoreBorder:b=!1,createQuadrilaterals:B=!1}={}){U(this,a,!1);this.isRenderable=n,this.data=N.data,this.layer=N.layer,this.linkService=N.linkService,this.downloadManager=N.downloadManager,this.imageResourcesPath=N.imageResourcesPath,this.renderForms=N.renderForms,this.svgFactory=N.svgFactory,this.annotationStorage=N.annotationStorage,this.enableScripting=N.enableScripting,this.hasJSActions=N.hasJSActions,this._fieldObjects=N.fieldObjects,this.parent=N.parent,n&&(this.container=this._createContainer(b)),B&&this._createQuadrilaterals()}static _hasPopupData({titleObj:N,contentsObj:n,richText:b}){return!!(N!=null&&N.str||n!=null&&n.str||b!=null&&b.str)}get hasPopupData(){return s._hasPopupData(this.data)}_createContainer(N){const{data:n,parent:{page:b,viewport:B}}=this,K=document.createElement("section");K.setAttribute("data-annotation-id",n.id),this instanceof E||(K.tabIndex=D),K.style.zIndex=this.parent.zIndex++,this.data.popupRef&&K.setAttribute("aria-haspopup","dialog"),n.noRotate&&K.classList.add("norotate");const{pageWidth:it,pageHeight:gt,pageX:Mt,pageY:qt}=B.rawDims;if(!n.rect||this instanceof _){const{rotation:ce}=n;return!n.hasOwnCanvas&&ce!==0&&this.setRotation(ce,K),K}const{width:zt,height:ve}=H(n.rect),Yt=d.Util.normalizeRect([n.rect[0],b.view[3]-n.rect[1]+b.view[1],n.rect[2],b.view[3]-n.rect[3]+b.view[1]]);if(!N&&n.borderStyle.width>0){K.style.borderWidth=`${n.borderStyle.width}px`;const ce=n.borderStyle.horizontalCornerRadius,Ee=n.borderStyle.verticalCornerRadius;if(ce>0||Ee>0){const He=`calc(${ce}px * var(--scale-factor)) / calc(${Ee}px * var(--scale-factor))`;K.style.borderRadius=He}else if(this instanceof o){const He=`calc(${zt}px * var(--scale-factor)) / calc(${ve}px * var(--scale-factor))`;K.style.borderRadius=He}switch(n.borderStyle.style){case d.AnnotationBorderStyleType.SOLID:K.style.borderStyle="solid";break;case d.AnnotationBorderStyleType.DASHED:K.style.borderStyle="dashed";break;case d.AnnotationBorderStyleType.BEVELED:(0,d.warn)("Unimplemented border style: beveled");break;case d.AnnotationBorderStyleType.INSET:(0,d.warn)("Unimplemented border style: inset");break;case d.AnnotationBorderStyleType.UNDERLINE:K.style.borderBottomStyle="solid";break}const De=n.borderColor||null;De?(ft(this,a,!0),K.style.borderColor=d.Util.makeHexColor(De[0]|0,De[1]|0,De[2]|0)):K.style.borderWidth=0}K.style.left=`${100*(Yt[0]-Mt)/it}%`,K.style.top=`${100*(Yt[1]-qt)/gt}%`;const{rotation:Kt}=n;return n.hasOwnCanvas||Kt===0?(K.style.width=`${100*zt/it}%`,K.style.height=`${100*ve/gt}%`):this.setRotation(Kt,K),K}setRotation(N,n=this.container){if(!this.data.rect)return;const{pageWidth:b,pageHeight:B}=this.parent.viewport.rawDims,{width:K,height:it}=H(this.data.rect);let gt,Mt;N%180===0?(gt=100*K/b,Mt=100*it/B):(gt=100*it/b,Mt=100*K/B),n.style.width=`${gt}%`,n.style.height=`${Mt}%`,n.setAttribute("data-main-rotation",(360-N)%360)}get _commonActions(){const N=(n,b,B)=>{const K=B.detail[n],it=K[0],gt=K.slice(1);B.target.style[b]=tt.ColorConverters[`${it}_HTML`](gt),this.annotationStorage.setValue(this.data.id,{[b]:tt.ColorConverters[`${it}_rgb`](gt)})};return(0,d.shadow)(this,"_commonActions",{display:n=>{const{display:b}=n.detail,B=b%2===1;this.container.style.visibility=B?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:B,noPrint:b===1||b===2})},print:n=>{this.annotationStorage.setValue(this.data.id,{noPrint:!n.detail.print})},hidden:n=>{const{hidden:b}=n.detail;this.container.style.visibility=b?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:b,noView:b})},focus:n=>{setTimeout(()=>n.target.focus({preventScroll:!1}),0)},userName:n=>{n.target.title=n.detail.userName},readonly:n=>{n.target.disabled=n.detail.readonly},required:n=>{this._setRequired(n.target,n.detail.required)},bgColor:n=>{N("bgColor","backgroundColor",n)},fillColor:n=>{N("fillColor","backgroundColor",n)},fgColor:n=>{N("fgColor","color",n)},textColor:n=>{N("textColor","color",n)},borderColor:n=>{N("borderColor","borderColor",n)},strokeColor:n=>{N("strokeColor","borderColor",n)},rotation:n=>{const b=n.detail.rotation;this.setRotation(b),this.annotationStorage.setValue(this.data.id,{rotation:b})}})}_dispatchEventFromSandbox(N,n){const b=this._commonActions;for(const B of Object.keys(n.detail)){const K=N[B]||b[B];K==null||K(n)}}_setDefaultPropertiesFromJS(N){if(!this.enableScripting)return;const n=this.annotationStorage.getRawValue(this.data.id);if(!n)return;const b=this._commonActions;for(const[B,K]of Object.entries(n)){const it=b[B];if(it){const gt={detail:{[B]:K},target:N};it(gt),delete n[B]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:N}=this.data;if(!N)return;const[n,b,B,K]=this.data.rect;if(N.length===1){const[,{x:Ee,y:De},{x:He,y:ln}]=N[0];if(B===Ee&&K===De&&n===He&&b===ln)return}const{style:it}=this.container;let gt;if(t(this,a)){const{borderColor:Ee,borderWidth:De}=it;it.borderWidth=0,gt=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${Ee}" stroke-width="${De}">`],this.container.classList.add("hasBorder")}const Mt=B-n,qt=K-b,{svgFactory:zt}=this,ve=zt.createElement("svg");ve.classList.add("quadrilateralsContainer"),ve.setAttribute("width",0),ve.setAttribute("height",0);const Yt=zt.createElement("defs");ve.append(Yt);const Kt=zt.createElement("clipPath"),ce=`clippath_${this.data.id}`;Kt.setAttribute("id",ce),Kt.setAttribute("clipPathUnits","objectBoundingBox"),Yt.append(Kt);for(const[,{x:Ee,y:De},{x:He,y:ln}]of N){const Ze=zt.createElement("rect"),vn=(He-n)/Mt,_n=(K-De)/qt,wn=(Ee-He)/Mt,ti=(De-ln)/qt;Ze.setAttribute("x",vn),Ze.setAttribute("y",_n),Ze.setAttribute("width",wn),Ze.setAttribute("height",ti),Kt.append(Ze),gt==null||gt.push(`<rect vector-effect="non-scaling-stroke" x="${vn}" y="${_n}" width="${wn}" height="${ti}"/>`)}t(this,a)&&(gt.push("</g></svg>')"),it.backgroundImage=gt.join("")),this.container.append(ve),this.container.style.clipPath=`url(#${ce})`}_createPopup(){const{container:N,data:n}=this;N.setAttribute("aria-haspopup","dialog");const b=new _({data:{color:n.color,titleObj:n.titleObj,modificationDate:n.modificationDate,contentsObj:n.contentsObj,richText:n.richText,parentRect:n.rect,borderStyle:0,id:`popup_${n.id}`,rotation:n.rotation},parent:this.parent,elements:[this]});this.parent.div.append(b.render())}render(){(0,d.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(N,n=null){const b=[];if(this._fieldObjects){const B=this._fieldObjects[N];if(B)for(const{page:K,id:it,exportValues:gt}of B){if(K===-1||it===n)continue;const Mt=typeof gt=="string"?gt:null,qt=document.querySelector(`[data-element-id="${it}"]`);if(qt&&!j.has(qt)){(0,d.warn)(`_getElementsByName - element not allowed: ${it}`);continue}b.push({id:it,exportValue:Mt,domElement:qt})}return b}for(const B of document.getElementsByName(N)){const{exportValue:K}=B,it=B.getAttribute("data-element-id");it!==n&&j.has(B)&&b.push({id:it,exportValue:K,domElement:B})}return b}show(){var N;this.container&&(this.container.hidden=!1),(N=this.popup)==null||N.maybeShow()}hide(){var N;this.container&&(this.container.hidden=!0),(N=this.popup)==null||N.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const N=this.getElementsToTriggerPopup();if(Array.isArray(N))for(const n of N)n.classList.add("highlightArea");else N.classList.add("highlightArea")}_editOnDoubleClick(){const{annotationEditorType:N,data:{id:n}}=this;this.container.addEventListener("dblclick",()=>{var b;(b=this.linkService.eventBus)==null||b.dispatch("switchannotationeditormode",{source:this,mode:N,editId:n})})}};a=new WeakMap;let y=s;class w extends y{constructor(n,b=null){super(n,{isRenderable:!0,ignoreBorder:!!(b!=null&&b.ignoreBorder),createQuadrilaterals:!0});U(this,c);U(this,S);this.isTooltipOnly=n.data.isTooltipOnly}render(){const{data:n,linkService:b}=this,B=document.createElement("a");B.setAttribute("data-element-id",n.id);let K=!1;return n.url?(b.addLinkAttributes(B,n.url,n.newWindow),K=!0):n.action?(this._bindNamedAction(B,n.action),K=!0):n.attachment?(this._bindAttachment(B,n.attachment),K=!0):n.setOCGState?(Q(this,S,Xr).call(this,B,n.setOCGState),K=!0):n.dest?(this._bindLink(B,n.dest),K=!0):(n.actions&&(n.actions.Action||n.actions["Mouse Up"]||n.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(B,n),K=!0),n.resetForm?(this._bindResetFormAction(B,n.resetForm),K=!0):this.isTooltipOnly&&!K&&(this._bindLink(B,""),K=!0)),this.container.classList.add("linkAnnotation"),K&&this.container.append(B),this.container}_bindLink(n,b){n.href=this.linkService.getDestinationHash(b),n.onclick=()=>(b&&this.linkService.goToDestination(b),!1),(b||b==="")&&Q(this,c,In).call(this)}_bindNamedAction(n,b){n.href=this.linkService.getAnchorUrl(""),n.onclick=()=>(this.linkService.executeNamedAction(b),!1),Q(this,c,In).call(this)}_bindAttachment(n,b){n.href=this.linkService.getAnchorUrl(""),n.onclick=()=>{var B;return(B=this.downloadManager)==null||B.openOrDownloadData(this.container,b.content,b.filename),!1},Q(this,c,In).call(this)}_bindJSAction(n,b){n.href=this.linkService.getAnchorUrl("");const B=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const K of Object.keys(b.actions)){const it=B.get(K);it&&(n[it]=()=>{var gt;return(gt=this.linkService.eventBus)==null||gt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:b.id,name:K}}),!1})}n.onclick||(n.onclick=()=>!1),Q(this,c,In).call(this)}_bindResetFormAction(n,b){const B=n.onclick;if(B||(n.href=this.linkService.getAnchorUrl("")),Q(this,c,In).call(this),!this._fieldObjects){(0,d.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),B||(n.onclick=()=>!1);return}n.onclick=()=>{var ve;B==null||B();const{fields:K,refs:it,include:gt}=b,Mt=[];if(K.length!==0||it.length!==0){const Yt=new Set(it);for(const Kt of K){const ce=this._fieldObjects[Kt]||[];for(const{id:Ee}of ce)Yt.add(Ee)}for(const Kt of Object.values(this._fieldObjects))for(const ce of Kt)Yt.has(ce.id)===gt&&Mt.push(ce)}else for(const Yt of Object.values(this._fieldObjects))Mt.push(...Yt);const qt=this.annotationStorage,zt=[];for(const Yt of Mt){const{id:Kt}=Yt;switch(zt.push(Kt),Yt.type){case"text":{const Ee=Yt.defaultValue||"";qt.setValue(Kt,{value:Ee});break}case"checkbox":case"radiobutton":{const Ee=Yt.defaultValue===Yt.exportValues;qt.setValue(Kt,{value:Ee});break}case"combobox":case"listbox":{const Ee=Yt.defaultValue||"";qt.setValue(Kt,{value:Ee});break}default:continue}const ce=document.querySelector(`[data-element-id="${Kt}"]`);if(ce){if(!j.has(ce)){(0,d.warn)(`_bindResetFormAction - element not allowed: ${Kt}`);continue}}else continue;ce.dispatchEvent(new Event("resetform"))}return this.enableScripting&&((ve=this.linkService.eventBus)==null||ve.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:zt,name:"ResetForm"}})),!1}}}c=new WeakSet,In=function(){this.container.setAttribute("data-internal-link","")},S=new WeakSet,Xr=function(n,b){n.href=this.linkService.getAnchorUrl(""),n.onclick=()=>(this.linkService.executeSetOCGState(b),!1),Q(this,c,In).call(this)};class m extends y{constructor(N){super(N,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const N=document.createElement("img");return N.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",N.alt="[{{type}} Annotation]",N.dataset.l10nId="text_annotation_type",N.dataset.l10nArgs=JSON.stringify({type:this.data.name}),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(N),this.container}}class E extends y{render(){return this.data.alternativeText&&(this.container.title=this.data.alternativeText),this.container}showElementAndHideCanvas(N){var n;this.data.hasOwnCanvas&&(((n=N.previousSibling)==null?void 0:n.nodeName)==="CANVAS"&&(N.previousSibling.hidden=!0),N.hidden=!1)}_getKeyModifier(N){const{isWin:n,isMac:b}=d.FeatureTest.platform;return n&&N.ctrlKey||b&&N.metaKey}_setEventListener(N,n,b,B,K){b.includes("mouse")?N.addEventListener(b,it=>{var gt;(gt=this.linkService.eventBus)==null||gt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:B,value:K(it),shift:it.shiftKey,modifier:this._getKeyModifier(it)}})}):N.addEventListener(b,it=>{var gt;if(b==="blur"){if(!n.focused||!it.relatedTarget)return;n.focused=!1}else if(b==="focus"){if(n.focused)return;n.focused=!0}K&&((gt=this.linkService.eventBus)==null||gt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:B,value:K(it)}}))})}_setEventListeners(N,n,b,B){var K,it,gt;for(const[Mt,qt]of b)(qt==="Action"||(K=this.data.actions)!=null&&K[qt])&&((qt==="Focus"||qt==="Blur")&&(n||(n={focused:!1})),this._setEventListener(N,n,Mt,qt,B),qt==="Focus"&&!((it=this.data.actions)!=null&&it.Blur)?this._setEventListener(N,n,"blur","Blur",null):qt==="Blur"&&!((gt=this.data.actions)!=null&&gt.Focus)&&this._setEventListener(N,n,"focus","Focus",null))}_setBackgroundColor(N){const n=this.data.backgroundColor||null;N.style.backgroundColor=n===null?"transparent":d.Util.makeHexColor(n[0],n[1],n[2])}_setTextStyle(N){const n=["left","center","right"],{fontColor:b}=this.data.defaultAppearanceData,B=this.data.defaultAppearanceData.fontSize||g,K=N.style;let it;const gt=2,Mt=qt=>Math.round(10*qt)/10;if(this.data.multiLine){const qt=Math.abs(this.data.rect[3]-this.data.rect[1]-gt),zt=Math.round(qt/(d.LINE_FACTOR*B))||1,ve=qt/zt;it=Math.min(B,Mt(ve/d.LINE_FACTOR))}else{const qt=Math.abs(this.data.rect[3]-this.data.rect[1]-gt);it=Math.min(B,Mt(qt/d.LINE_FACTOR))}K.fontSize=`calc(${it}px * var(--scale-factor))`,K.color=d.Util.makeHexColor(b[0],b[1],b[2]),this.data.textAlignment!==null&&(K.textAlign=n[this.data.textAlignment])}_setRequired(N,n){n?N.setAttribute("required",!0):N.removeAttribute("required"),N.setAttribute("aria-required",n)}}class x extends E{constructor(N){const n=N.renderForms||!N.data.hasAppearance&&!!N.data.fieldValue;super(N,{isRenderable:n})}setPropertyOnSiblings(N,n,b,B){const K=this.annotationStorage;for(const it of this._getElementsByName(N.name,N.id))it.domElement&&(it.domElement[n]=b),K.setValue(it.id,{[B]:b})}render(){var B,K;const N=this.annotationStorage,n=this.data.id;this.container.classList.add("textWidgetAnnotation");let b=null;if(this.renderForms){const it=N.getValue(n,{value:this.data.fieldValue});let gt=it.value||"";const Mt=N.getValue(n,{charLimit:this.data.maxLen}).charLimit;Mt&&gt.length>Mt&&(gt=gt.slice(0,Mt));let qt=it.formattedValue||((B=this.data.textContent)==null?void 0:B.join(`
`))||null;qt&&this.data.comb&&(qt=qt.replaceAll(/\s+/g,""));const zt={userValue:gt,formattedValue:qt,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(b=document.createElement("textarea"),b.textContent=qt??gt,this.data.doNotScroll&&(b.style.overflowY="hidden")):(b=document.createElement("input"),b.type="text",b.setAttribute("value",qt??gt),this.data.doNotScroll&&(b.style.overflowX="hidden")),this.data.hasOwnCanvas&&(b.hidden=!0),j.add(b),b.setAttribute("data-element-id",n),b.disabled=this.data.readOnly,b.name=this.data.fieldName,b.tabIndex=D,this._setRequired(b,this.data.required),Mt&&(b.maxLength=Mt),b.addEventListener("input",Yt=>{N.setValue(n,{value:Yt.target.value}),this.setPropertyOnSiblings(b,"value",Yt.target.value,"value"),zt.formattedValue=null}),b.addEventListener("resetform",Yt=>{const Kt=this.data.defaultFieldValue??"";b.value=zt.userValue=Kt,zt.formattedValue=null});let ve=Yt=>{const{formattedValue:Kt}=zt;Kt!=null&&(Yt.target.value=Kt),Yt.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){b.addEventListener("focus",Kt=>{if(zt.focused)return;const{target:ce}=Kt;zt.userValue&&(ce.value=zt.userValue),zt.lastCommittedValue=ce.value,zt.commitKey=1,zt.focused=!0}),b.addEventListener("updatefromsandbox",Kt=>{this.showElementAndHideCanvas(Kt.target);const ce={value(Ee){zt.userValue=Ee.detail.value??"",N.setValue(n,{value:zt.userValue.toString()}),Ee.target.value=zt.userValue},formattedValue(Ee){const{formattedValue:De}=Ee.detail;zt.formattedValue=De,De!=null&&Ee.target!==document.activeElement&&(Ee.target.value=De),N.setValue(n,{formattedValue:De})},selRange(Ee){Ee.target.setSelectionRange(...Ee.detail.selRange)},charLimit:Ee=>{var Ze;const{charLimit:De}=Ee.detail,{target:He}=Ee;if(De===0){He.removeAttribute("maxLength");return}He.setAttribute("maxLength",De);let ln=zt.userValue;!ln||ln.length<=De||(ln=ln.slice(0,De),He.value=zt.userValue=ln,N.setValue(n,{value:ln}),(Ze=this.linkService.eventBus)==null||Ze.dispatch("dispatcheventinsandbox",{source:this,detail:{id:n,name:"Keystroke",value:ln,willCommit:!0,commitKey:1,selStart:He.selectionStart,selEnd:He.selectionEnd}}))}};this._dispatchEventFromSandbox(ce,Kt)}),b.addEventListener("keydown",Kt=>{var De;zt.commitKey=1;let ce=-1;if(Kt.key==="Escape"?ce=0:Kt.key==="Enter"&&!this.data.multiLine?ce=2:Kt.key==="Tab"&&(zt.commitKey=3),ce===-1)return;const{value:Ee}=Kt.target;zt.lastCommittedValue!==Ee&&(zt.lastCommittedValue=Ee,zt.userValue=Ee,(De=this.linkService.eventBus)==null||De.dispatch("dispatcheventinsandbox",{source:this,detail:{id:n,name:"Keystroke",value:Ee,willCommit:!0,commitKey:ce,selStart:Kt.target.selectionStart,selEnd:Kt.target.selectionEnd}}))});const Yt=ve;ve=null,b.addEventListener("blur",Kt=>{var Ee;if(!zt.focused||!Kt.relatedTarget)return;zt.focused=!1;const{value:ce}=Kt.target;zt.userValue=ce,zt.lastCommittedValue!==ce&&((Ee=this.linkService.eventBus)==null||Ee.dispatch("dispatcheventinsandbox",{source:this,detail:{id:n,name:"Keystroke",value:ce,willCommit:!0,commitKey:zt.commitKey,selStart:Kt.target.selectionStart,selEnd:Kt.target.selectionEnd}})),Yt(Kt)}),(K=this.data.actions)!=null&&K.Keystroke&&b.addEventListener("beforeinput",Kt=>{var _n;zt.lastCommittedValue=null;const{data:ce,target:Ee}=Kt,{value:De,selectionStart:He,selectionEnd:ln}=Ee;let Ze=He,vn=ln;switch(Kt.inputType){case"deleteWordBackward":{const wn=De.substring(0,He).match(/\w*[^\w]*$/);wn&&(Ze-=wn[0].length);break}case"deleteWordForward":{const wn=De.substring(He).match(/^[^\w]*\w*/);wn&&(vn+=wn[0].length);break}case"deleteContentBackward":He===ln&&(Ze-=1);break;case"deleteContentForward":He===ln&&(vn+=1);break}Kt.preventDefault(),(_n=this.linkService.eventBus)==null||_n.dispatch("dispatcheventinsandbox",{source:this,detail:{id:n,name:"Keystroke",value:De,change:ce||"",willCommit:!1,selStart:Ze,selEnd:vn}})}),this._setEventListeners(b,zt,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],Kt=>Kt.target.value)}if(ve&&b.addEventListener("blur",ve),this.data.comb){const Kt=(this.data.rect[2]-this.data.rect[0])/Mt;b.classList.add("comb"),b.style.letterSpacing=`calc(${Kt}px * var(--scale-factor) - 1ch)`}}else b=document.createElement("div"),b.textContent=this.data.fieldValue,b.style.verticalAlign="middle",b.style.display="table-cell";return this._setTextStyle(b),this._setBackgroundColor(b),this._setDefaultPropertiesFromJS(b),this.container.append(b),this.container}}class M extends E{constructor(N){super(N,{isRenderable:!!N.data.hasOwnCanvas})}}class C extends E{constructor(N){super(N,{isRenderable:N.renderForms})}render(){const N=this.annotationStorage,n=this.data,b=n.id;let B=N.getValue(b,{value:n.exportValue===n.fieldValue}).value;typeof B=="string"&&(B=B!=="Off",N.setValue(b,{value:B})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const K=document.createElement("input");return j.add(K),K.setAttribute("data-element-id",b),K.disabled=n.readOnly,this._setRequired(K,this.data.required),K.type="checkbox",K.name=n.fieldName,B&&K.setAttribute("checked",!0),K.setAttribute("exportValue",n.exportValue),K.tabIndex=D,K.addEventListener("change",it=>{const{name:gt,checked:Mt}=it.target;for(const qt of this._getElementsByName(gt,b)){const zt=Mt&&qt.exportValue===n.exportValue;qt.domElement&&(qt.domElement.checked=zt),N.setValue(qt.id,{value:zt})}N.setValue(b,{value:Mt})}),K.addEventListener("resetform",it=>{const gt=n.defaultFieldValue||"Off";it.target.checked=gt===n.exportValue}),this.enableScripting&&this.hasJSActions&&(K.addEventListener("updatefromsandbox",it=>{const gt={value(Mt){Mt.target.checked=Mt.detail.value!=="Off",N.setValue(b,{value:Mt.target.checked})}};this._dispatchEventFromSandbox(gt,it)}),this._setEventListeners(K,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],it=>it.target.checked)),this._setBackgroundColor(K),this._setDefaultPropertiesFromJS(K),this.container.append(K),this.container}}class o extends E{constructor(N){super(N,{isRenderable:N.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const N=this.annotationStorage,n=this.data,b=n.id;let B=N.getValue(b,{value:n.fieldValue===n.buttonValue}).value;typeof B=="string"&&(B=B!==n.buttonValue,N.setValue(b,{value:B}));const K=document.createElement("input");if(j.add(K),K.setAttribute("data-element-id",b),K.disabled=n.readOnly,this._setRequired(K,this.data.required),K.type="radio",K.name=n.fieldName,B&&K.setAttribute("checked",!0),K.tabIndex=D,K.addEventListener("change",it=>{const{name:gt,checked:Mt}=it.target;for(const qt of this._getElementsByName(gt,b))N.setValue(qt.id,{value:!1});N.setValue(b,{value:Mt})}),K.addEventListener("resetform",it=>{const gt=n.defaultFieldValue;it.target.checked=gt!=null&&gt===n.buttonValue}),this.enableScripting&&this.hasJSActions){const it=n.buttonValue;K.addEventListener("updatefromsandbox",gt=>{const Mt={value:qt=>{const zt=it===qt.detail.value;for(const ve of this._getElementsByName(qt.target.name)){const Yt=zt&&ve.id===b;ve.domElement&&(ve.domElement.checked=Yt),N.setValue(ve.id,{value:Yt})}}};this._dispatchEventFromSandbox(Mt,gt)}),this._setEventListeners(K,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],gt=>gt.target.checked)}return this._setBackgroundColor(K),this._setDefaultPropertiesFromJS(K),this.container.append(K),this.container}}class u extends w{constructor(N){super(N,{ignoreBorder:N.data.hasAppearance})}render(){const N=super.render();N.classList.add("buttonWidgetAnnotation","pushButton"),this.data.alternativeText&&(N.title=this.data.alternativeText);const n=N.lastChild;return this.enableScripting&&this.hasJSActions&&n&&(this._setDefaultPropertiesFromJS(n),n.addEventListener("updatefromsandbox",b=>{this._dispatchEventFromSandbox({},b)})),N}}class L extends E{constructor(N){super(N,{isRenderable:N.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const N=this.annotationStorage,n=this.data.id,b=N.getValue(n,{value:this.data.fieldValue}),B=document.createElement("select");j.add(B),B.setAttribute("data-element-id",n),B.disabled=this.data.readOnly,this._setRequired(B,this.data.required),B.name=this.data.fieldName,B.tabIndex=D;let K=this.data.combo&&this.data.options.length>0;this.data.combo||(B.size=this.data.options.length,this.data.multiSelect&&(B.multiple=!0)),B.addEventListener("resetform",zt=>{const ve=this.data.defaultFieldValue;for(const Yt of B.options)Yt.selected=Yt.value===ve});for(const zt of this.data.options){const ve=document.createElement("option");ve.textContent=zt.displayValue,ve.value=zt.exportValue,b.value.includes(zt.exportValue)&&(ve.setAttribute("selected",!0),K=!1),B.append(ve)}let it=null;if(K){const zt=document.createElement("option");zt.value=" ",zt.setAttribute("hidden",!0),zt.setAttribute("selected",!0),B.prepend(zt),it=()=>{zt.remove(),B.removeEventListener("input",it),it=null},B.addEventListener("input",it)}const gt=zt=>{const ve=zt?"value":"textContent",{options:Yt,multiple:Kt}=B;return Kt?Array.prototype.filter.call(Yt,ce=>ce.selected).map(ce=>ce[ve]):Yt.selectedIndex===-1?null:Yt[Yt.selectedIndex][ve]};let Mt=gt(!1);const qt=zt=>{const ve=zt.target.options;return Array.prototype.map.call(ve,Yt=>({displayValue:Yt.textContent,exportValue:Yt.value}))};return this.enableScripting&&this.hasJSActions?(B.addEventListener("updatefromsandbox",zt=>{const ve={value(Yt){it==null||it();const Kt=Yt.detail.value,ce=new Set(Array.isArray(Kt)?Kt:[Kt]);for(const Ee of B.options)Ee.selected=ce.has(Ee.value);N.setValue(n,{value:gt(!0)}),Mt=gt(!1)},multipleSelection(Yt){B.multiple=!0},remove(Yt){const Kt=B.options,ce=Yt.detail.remove;Kt[ce].selected=!1,B.remove(ce),Kt.length>0&&Array.prototype.findIndex.call(Kt,De=>De.selected)===-1&&(Kt[0].selected=!0),N.setValue(n,{value:gt(!0),items:qt(Yt)}),Mt=gt(!1)},clear(Yt){for(;B.length!==0;)B.remove(0);N.setValue(n,{value:null,items:[]}),Mt=gt(!1)},insert(Yt){const{index:Kt,displayValue:ce,exportValue:Ee}=Yt.detail.insert,De=B.children[Kt],He=document.createElement("option");He.textContent=ce,He.value=Ee,De?De.before(He):B.append(He),N.setValue(n,{value:gt(!0),items:qt(Yt)}),Mt=gt(!1)},items(Yt){const{items:Kt}=Yt.detail;for(;B.length!==0;)B.remove(0);for(const ce of Kt){const{displayValue:Ee,exportValue:De}=ce,He=document.createElement("option");He.textContent=Ee,He.value=De,B.append(He)}B.options.length>0&&(B.options[0].selected=!0),N.setValue(n,{value:gt(!0),items:qt(Yt)}),Mt=gt(!1)},indices(Yt){const Kt=new Set(Yt.detail.indices);for(const ce of Yt.target.options)ce.selected=Kt.has(ce.index);N.setValue(n,{value:gt(!0)}),Mt=gt(!1)},editable(Yt){Yt.target.disabled=!Yt.detail.editable}};this._dispatchEventFromSandbox(ve,zt)}),B.addEventListener("input",zt=>{var Yt;const ve=gt(!0);N.setValue(n,{value:ve}),zt.preventDefault(),(Yt=this.linkService.eventBus)==null||Yt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:n,name:"Keystroke",value:Mt,changeEx:ve,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(B,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],zt=>zt.target.value)):B.addEventListener("input",function(zt){N.setValue(n,{value:gt(!0)})}),this.data.combo&&this._setTextStyle(B),this._setBackgroundColor(B),this._setDefaultPropertiesFromJS(B),this.container.append(B),this.container}}class _ extends y{constructor(N){const{data:n,elements:b}=N;super(N,{isRenderable:y._hasPopupData(n)}),this.elements=b}render(){this.container.classList.add("popupAnnotation");const N=new l({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),n=[];for(const b of this.elements)b.popup=N,n.push(b.data.id),b.addHighlightArea();return this.container.setAttribute("aria-controls",n.map(b=>`${d.AnnotationPrefix}${b}`).join(",")),this.container}}class l{constructor({container:N,color:n,elements:b,titleObj:B,modificationDate:K,contentsObj:it,richText:gt,parent:Mt,rect:qt,parentRect:zt,open:ve}){U(this,le);U(this,he);U(this,_e);U(this,we);U(this,z,null);U(this,dt,Q(this,le,$r).bind(this));U(this,ct,Q(this,we,ir).bind(this));U(this,mt,Q(this,_e,nr).bind(this));U(this,Et,Q(this,he,pi).bind(this));U(this,Dt,null);U(this,wt,null);U(this,$t,null);U(this,ie,null);U(this,It,null);U(this,Jt,null);U(this,Ct,!1);U(this,ht,null);U(this,at,null);U(this,St,null);U(this,jt,null);U(this,Ht,!1);var Kt;ft(this,wt,N),ft(this,jt,B),ft(this,$t,it),ft(this,St,gt),ft(this,It,Mt),ft(this,Dt,n),ft(this,at,qt),ft(this,Jt,zt),ft(this,ie,b);const Yt=P.PDFDateString.toDateObject(K);Yt&&ft(this,z,Mt.l10n.get("annotation_date_string",{date:Yt.toLocaleDateString(),time:Yt.toLocaleTimeString()})),this.trigger=b.flatMap(ce=>ce.getElementsToTriggerPopup());for(const ce of this.trigger)ce.addEventListener("click",t(this,Et)),ce.addEventListener("mouseenter",t(this,mt)),ce.addEventListener("mouseleave",t(this,ct)),ce.classList.add("popupTriggerArea");for(const ce of b)(Kt=ce.container)==null||Kt.addEventListener("keydown",t(this,dt));t(this,wt).hidden=!0,ve&&Q(this,he,pi).call(this)}render(){if(t(this,ht))return;const{page:{view:N},viewport:{rawDims:{pageWidth:n,pageHeight:b,pageX:B,pageY:K}}}=t(this,It),it=ft(this,ht,document.createElement("div"));if(it.className="popup",t(this,Dt)){const Ze=it.style.outlineColor=d.Util.makeHexColor(...t(this,Dt));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?it.style.backgroundColor=`color-mix(in srgb, ${Ze} 30%, white)`:it.style.backgroundColor=d.Util.makeHexColor(...t(this,Dt).map(_n=>Math.floor(.7*(255-_n)+_n)))}const gt=document.createElement("span");gt.className="header";const Mt=document.createElement("h1");if(gt.append(Mt),{dir:Mt.dir,str:Mt.textContent}=t(this,jt),it.append(gt),t(this,z)){const Ze=document.createElement("span");Ze.classList.add("popupDate"),t(this,z).then(vn=>{Ze.textContent=vn}),gt.append(Ze)}const qt=t(this,$t),zt=t(this,St);if(zt!=null&&zt.str&&(!(qt!=null&&qt.str)||qt.str===zt.str))G.XfaLayer.render({xfaHtml:zt.html,intent:"richText",div:it}),it.lastChild.classList.add("richText","popupContent");else{const Ze=this._formatContents(qt);it.append(Ze)}let ve=!!t(this,Jt),Yt=ve?t(this,Jt):t(this,at);for(const Ze of t(this,ie))if(!Yt||d.Util.intersect(Ze.data.rect,Yt)!==null){Yt=Ze.data.rect,ve=!0;break}const Kt=d.Util.normalizeRect([Yt[0],N[3]-Yt[1]+N[1],Yt[2],N[3]-Yt[3]+N[1]]),ce=5,Ee=ve?Yt[2]-Yt[0]+ce:0,De=Kt[0]+Ee,He=Kt[1],{style:ln}=t(this,wt);ln.left=`${100*(De-B)/n}%`,ln.top=`${100*(He-K)/b}%`,t(this,wt).append(it)}_formatContents({str:N,dir:n}){const b=document.createElement("p");b.classList.add("popupContent"),b.dir=n;const B=N.split(/(?:\r\n?|\n)/);for(let K=0,it=B.length;K<it;++K){const gt=B[K];b.append(document.createTextNode(gt)),K<it-1&&b.append(document.createElement("br"))}return b}forceHide(){ft(this,Ht,this.isVisible),t(this,Ht)&&(t(this,wt).hidden=!0)}maybeShow(){t(this,Ht)&&(ft(this,Ht,!1),t(this,wt).hidden=!1)}get isVisible(){return t(this,wt).hidden===!1}}z=new WeakMap,dt=new WeakMap,ct=new WeakMap,mt=new WeakMap,Et=new WeakMap,Dt=new WeakMap,wt=new WeakMap,$t=new WeakMap,ie=new WeakMap,It=new WeakMap,Jt=new WeakMap,Ct=new WeakMap,ht=new WeakMap,at=new WeakMap,St=new WeakMap,jt=new WeakMap,Ht=new WeakMap,le=new WeakSet,$r=function(N){N.altKey||N.shiftKey||N.ctrlKey||N.metaKey||(N.key==="Enter"||N.key==="Escape"&&t(this,Ct))&&Q(this,he,pi).call(this)},he=new WeakSet,pi=function(){ft(this,Ct,!t(this,Ct)),t(this,Ct)?(Q(this,_e,nr).call(this),t(this,wt).addEventListener("click",t(this,Et)),t(this,wt).addEventListener("keydown",t(this,dt))):(Q(this,we,ir).call(this),t(this,wt).removeEventListener("click",t(this,Et)),t(this,wt).removeEventListener("keydown",t(this,dt)))},_e=new WeakSet,nr=function(){t(this,ht)||this.render(),this.isVisible?t(this,Ct)&&t(this,wt).classList.add("focused"):(t(this,wt).hidden=!1,t(this,wt).style.zIndex=parseInt(t(this,wt).style.zIndex)+1e3)},we=new WeakSet,ir=function(){t(this,wt).classList.remove("focused"),!(t(this,Ct)||!this.isVisible)&&(t(this,wt).hidden=!0,t(this,wt).style.zIndex=parseInt(t(this,wt).style.zIndex)-1e3)};class F extends y{constructor(N){super(N,{isRenderable:!0,ignoreBorder:!0}),this.textContent=N.data.textContent,this.textPosition=N.data.textPosition,this.annotationEditorType=d.AnnotationEditorType.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const N=document.createElement("div");N.classList.add("annotationTextContent"),N.setAttribute("role","comment");for(const n of this.textContent){const b=document.createElement("span");b.textContent=n,N.append(b)}this.container.append(N)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}v.FreeTextAnnotationElement=F;class A extends y{constructor(n){super(n,{isRenderable:!0,ignoreBorder:!0});U(this,et,null)}render(){this.container.classList.add("lineAnnotation");const n=this.data,{width:b,height:B}=H(n.rect),K=this.svgFactory.create(b,B,!0),it=ft(this,et,this.svgFactory.createElement("svg:line"));return it.setAttribute("x1",n.rect[2]-n.lineCoordinates[0]),it.setAttribute("y1",n.rect[3]-n.lineCoordinates[1]),it.setAttribute("x2",n.rect[2]-n.lineCoordinates[2]),it.setAttribute("y2",n.rect[3]-n.lineCoordinates[3]),it.setAttribute("stroke-width",n.borderStyle.width||1),it.setAttribute("stroke","transparent"),it.setAttribute("fill","transparent"),K.append(it),this.container.append(K),!n.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,et)}addHighlightArea(){this.container.classList.add("highlightArea")}}et=new WeakMap;class q extends y{constructor(n){super(n,{isRenderable:!0,ignoreBorder:!0});U(this,$,null)}render(){this.container.classList.add("squareAnnotation");const n=this.data,{width:b,height:B}=H(n.rect),K=this.svgFactory.create(b,B,!0),it=n.borderStyle.width,gt=ft(this,$,this.svgFactory.createElement("svg:rect"));return gt.setAttribute("x",it/2),gt.setAttribute("y",it/2),gt.setAttribute("width",b-it),gt.setAttribute("height",B-it),gt.setAttribute("stroke-width",it||1),gt.setAttribute("stroke","transparent"),gt.setAttribute("fill","transparent"),K.append(gt),this.container.append(K),!n.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,$)}addHighlightArea(){this.container.classList.add("highlightArea")}}$=new WeakMap;class Z extends y{constructor(n){super(n,{isRenderable:!0,ignoreBorder:!0});U(this,Ft,null)}render(){this.container.classList.add("circleAnnotation");const n=this.data,{width:b,height:B}=H(n.rect),K=this.svgFactory.create(b,B,!0),it=n.borderStyle.width,gt=ft(this,Ft,this.svgFactory.createElement("svg:ellipse"));return gt.setAttribute("cx",b/2),gt.setAttribute("cy",B/2),gt.setAttribute("rx",b/2-it/2),gt.setAttribute("ry",B/2-it/2),gt.setAttribute("stroke-width",it||1),gt.setAttribute("stroke","transparent"),gt.setAttribute("fill","transparent"),K.append(gt),this.container.append(K),!n.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,Ft)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ft=new WeakMap;class R extends y{constructor(n){super(n,{isRenderable:!0,ignoreBorder:!0});U(this,At,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const n=this.data,{width:b,height:B}=H(n.rect),K=this.svgFactory.create(b,B,!0);let it=[];for(const Mt of n.vertices){const qt=Mt.x-n.rect[0],zt=n.rect[3]-Mt.y;it.push(qt+","+zt)}it=it.join(" ");const gt=ft(this,At,this.svgFactory.createElement(this.svgElementName));return gt.setAttribute("points",it),gt.setAttribute("stroke-width",n.borderStyle.width||1),gt.setAttribute("stroke","transparent"),gt.setAttribute("fill","transparent"),K.append(gt),this.container.append(K),!n.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,At)}addHighlightArea(){this.container.classList.add("highlightArea")}}At=new WeakMap;class nt extends R{constructor(N){super(N),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class pt extends y{constructor(N){super(N,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class bt extends y{constructor(n){super(n,{isRenderable:!0,ignoreBorder:!0});U(this,xe,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=d.AnnotationEditorType.INK}render(){this.container.classList.add(this.containerClassName);const n=this.data,{width:b,height:B}=H(n.rect),K=this.svgFactory.create(b,B,!0);for(const it of n.inkLists){let gt=[];for(const qt of it){const zt=qt.x-n.rect[0],ve=n.rect[3]-qt.y;gt.push(`${zt},${ve}`)}gt=gt.join(" ");const Mt=this.svgFactory.createElement(this.svgElementName);t(this,xe).push(Mt),Mt.setAttribute("points",gt),Mt.setAttribute("stroke-width",n.borderStyle.width||1),Mt.setAttribute("stroke","transparent"),Mt.setAttribute("fill","transparent"),!n.popupRef&&this.hasPopupData&&this._createPopup(),K.append(Mt)}return this.container.append(K),this.container}getElementsToTriggerPopup(){return t(this,xe)}addHighlightArea(){this.container.classList.add("highlightArea")}}xe=new WeakMap,v.InkAnnotationElement=bt;class Y extends y{constructor(N){super(N,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class yt extends y{constructor(N){super(N,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class kt extends y{constructor(N){super(N,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class Bt extends y{constructor(N){super(N,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class Gt extends y{constructor(N){super(N,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}v.StampAnnotationElement=Gt;class T extends y{constructor(n){var K;super(n,{isRenderable:!0});U(this,st);U(this,je,null);const{filename:b,content:B}=this.data.file;this.filename=(0,P.getFilenameFromUrl)(b,!0),this.content=B,(K=this.linkService.eventBus)==null||K.dispatch("fileattachmentannotation",{source:this,filename:b,content:B})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:n,data:b}=this;let B;b.hasAppearance||b.fillAlpha===0?B=document.createElement("div"):(B=document.createElement("img"),B.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(b.name)?"paperclip":"pushpin"}.svg`,b.fillAlpha&&b.fillAlpha<1&&(B.style=`filter: opacity(${Math.round(b.fillAlpha*100)}%);`)),B.addEventListener("dblclick",Q(this,st,rr).bind(this)),ft(this,je,B);const{isMac:K}=d.FeatureTest.platform;return n.addEventListener("keydown",it=>{it.key==="Enter"&&(K?it.metaKey:it.ctrlKey)&&Q(this,st,rr).call(this)}),!b.popupRef&&this.hasPopupData?this._createPopup():B.classList.add("popupTriggerArea"),n.append(B),n}getElementsToTriggerPopup(){return t(this,je)}addHighlightArea(){this.container.classList.add("highlightArea")}}je=new WeakMap,st=new WeakSet,rr=function(){var n;(n=this.downloadManager)==null||n.openOrDownloadData(this.container,this.content,this.filename)};class i{constructor({div:N,accessibilityManager:n,annotationCanvasMap:b,l10n:B,page:K,viewport:it}){U(this,ne);U(this,Lt);U(this,Qt,null);U(this,Wt,null);U(this,te,new Map);this.div=N,ft(this,Qt,n),ft(this,Wt,b),this.l10n=B,this.page=K,this.viewport=it,this.zIndex=0,this.l10n||(this.l10n=Tt.NullL10n)}async render(N){const{annotations:n}=N,b=this.div;(0,P.setLayerDimensions)(b,this.viewport);const B=new Map,K={data:null,layer:b,linkService:N.linkService,downloadManager:N.downloadManager,imageResourcesPath:N.imageResourcesPath||"",renderForms:N.renderForms!==!1,svgFactory:new P.DOMSVGFactory,annotationStorage:N.annotationStorage||new e.AnnotationStorage,enableScripting:N.enableScripting===!0,hasJSActions:N.hasJSActions,fieldObjects:N.fieldObjects,parent:this,elements:null};for(const it of n){if(it.noHTML)continue;const gt=it.annotationType===d.AnnotationType.POPUP;if(gt){const zt=B.get(it.id);if(!zt)continue;K.elements=zt}else{const{width:zt,height:ve}=H(it.rect);if(zt<=0||ve<=0)continue}K.data=it;const Mt=k.create(K);if(!Mt.isRenderable)continue;if(!gt&&it.popupRef){const zt=B.get(it.popupRef);zt?zt.push(Mt):B.set(it.popupRef,[Mt])}Mt.annotationEditorType>0&&t(this,te).set(Mt.data.id,Mt);const qt=Mt.render();it.hidden&&(qt.style.visibility="hidden"),Q(this,ne,Yr).call(this,qt,it.id)}Q(this,Lt,sr).call(this),await this.l10n.translate(b)}update({viewport:N}){const n=this.div;this.viewport=N,(0,P.setLayerDimensions)(n,{rotation:N.rotation}),Q(this,Lt,sr).call(this),n.hidden=!1}getEditableAnnotations(){return Array.from(t(this,te).values())}getEditableAnnotation(N){return t(this,te).get(N)}}Qt=new WeakMap,Wt=new WeakMap,te=new WeakMap,ne=new WeakSet,Yr=function(N,n){var B;const b=N.firstChild||N;b.id=`${d.AnnotationPrefix}${n}`,this.div.append(N),(B=t(this,Qt))==null||B.moveElementInDOM(this.div,N,b,!1)},Lt=new WeakSet,sr=function(){if(!t(this,Wt))return;const N=this.div;for(const[n,b]of t(this,Wt)){const B=N.querySelector(`[data-annotation-id="${n}"]`);if(!B)continue;const{firstChild:K}=B;K?K.nodeName==="CANVAS"?K.replaceWith(b):K.before(b):B.append(b)}t(this,Wt).clear()},v.AnnotationLayer=i},(f,v)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.ColorConverters=void 0;function vt(e){return Math.floor(Math.max(0,Math.min(1,e))*255).toString(16).padStart(2,"0")}function d(e){return Math.max(0,Math.min(255,255*e))}class P{static CMYK_G([tt,Tt,G,D]){return["G",1-Math.min(1,.3*tt+.59*G+.11*Tt+D)]}static G_CMYK([tt]){return["CMYK",0,0,0,1-tt]}static G_RGB([tt]){return["RGB",tt,tt,tt]}static G_rgb([tt]){return tt=d(tt),[tt,tt,tt]}static G_HTML([tt]){const Tt=vt(tt);return`#${Tt}${Tt}${Tt}`}static RGB_G([tt,Tt,G]){return["G",.3*tt+.59*Tt+.11*G]}static RGB_rgb(tt){return tt.map(d)}static RGB_HTML(tt){return`#${tt.map(vt).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([tt,Tt,G,D]){return["RGB",1-Math.min(1,tt+D),1-Math.min(1,G+D),1-Math.min(1,Tt+D)]}static CMYK_rgb([tt,Tt,G,D]){return[d(1-Math.min(1,tt+D)),d(1-Math.min(1,G+D)),d(1-Math.min(1,Tt+D))]}static CMYK_HTML(tt){const Tt=this.CMYK_RGB(tt).slice(1);return this.RGB_HTML(Tt)}static RGB_CMYK([tt,Tt,G]){const D=1-tt,g=1-Tt,j=1-G,H=Math.min(D,g,j);return["CMYK",D,g,j,H]}}v.ColorConverters=P},(f,v)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.NullL10n=void 0,v.getL10nFallback=d;const vt={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative"};vt.print_progress_percent="{{progress}}%";function d(tt,Tt){switch(tt){case"find_match_count":tt=`find_match_count[${Tt.total===1?"one":"other"}]`;break;case"find_match_count_limit":tt=`find_match_count_limit[${Tt.limit===1?"one":"other"}]`;break}return vt[tt]||""}function P(tt,Tt){return Tt?tt.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,(G,D)=>D in Tt?Tt[D]:"{{"+D+"}}"):tt}const e={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(tt,Tt=null,G=d(tt,Tt)){return P(G,Tt)},async translate(tt){}};v.NullL10n=e},(f,v,vt)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.XfaLayer=void 0;var d=vt(25);class P{static setupStorage(tt,Tt,G,D,g){const j=D.getValue(Tt,{value:null});switch(G.name){case"textarea":if(j.value!==null&&(tt.textContent=j.value),g==="print")break;tt.addEventListener("input",H=>{D.setValue(Tt,{value:H.target.value})});break;case"input":if(G.attributes.type==="radio"||G.attributes.type==="checkbox"){if(j.value===G.attributes.xfaOn?tt.setAttribute("checked",!0):j.value===G.attributes.xfaOff&&tt.removeAttribute("checked"),g==="print")break;tt.addEventListener("change",H=>{D.setValue(Tt,{value:H.target.checked?H.target.getAttribute("xfaOn"):H.target.getAttribute("xfaOff")})})}else{if(j.value!==null&&tt.setAttribute("value",j.value),g==="print")break;tt.addEventListener("input",H=>{D.setValue(Tt,{value:H.target.value})})}break;case"select":if(j.value!==null){tt.setAttribute("value",j.value);for(const H of G.children)H.attributes.value===j.value?H.attributes.selected=!0:H.attributes.hasOwnProperty("selected")&&delete H.attributes.selected}tt.addEventListener("input",H=>{const k=H.target.options,y=k.selectedIndex===-1?"":k[k.selectedIndex].value;D.setValue(Tt,{value:y})});break}}static setAttributes({html:tt,element:Tt,storage:G=null,intent:D,linkService:g}){const{attributes:j}=Tt,H=tt instanceof HTMLAnchorElement;j.type==="radio"&&(j.name=`${j.name}-${D}`);for(const[k,y]of Object.entries(j))if(y!=null)switch(k){case"class":y.length&&tt.setAttribute(k,y.join(" "));break;case"dataId":break;case"id":tt.setAttribute("data-element-id",y);break;case"style":Object.assign(tt.style,y);break;case"textContent":tt.textContent=y;break;default:(!H||k!=="href"&&k!=="newWindow")&&tt.setAttribute(k,y)}H&&g.addLinkAttributes(tt,j.href,j.newWindow),G&&j.dataId&&this.setupStorage(tt,j.dataId,Tt,G)}static render(tt){var w;const Tt=tt.annotationStorage,G=tt.linkService,D=tt.xfaHtml,g=tt.intent||"display",j=document.createElement(D.name);D.attributes&&this.setAttributes({html:j,element:D,intent:g,linkService:G});const H=[[D,-1,j]],k=tt.div;if(k.append(j),tt.viewport){const m=`matrix(${tt.viewport.transform.join(",")})`;k.style.transform=m}g!=="richText"&&k.setAttribute("class","xfaLayer xfaFont");const y=[];for(;H.length>0;){const[m,E,x]=H.at(-1);if(E+1===m.children.length){H.pop();continue}const M=m.children[++H.at(-1)[1]];if(M===null)continue;const{name:C}=M;if(C==="#text"){const u=document.createTextNode(M.value);y.push(u),x.append(u);continue}const o=(w=M==null?void 0:M.attributes)!=null&&w.xmlns?document.createElementNS(M.attributes.xmlns,C):document.createElement(C);if(x.append(o),M.attributes&&this.setAttributes({html:o,element:M,storage:Tt,intent:g,linkService:G}),M.children&&M.children.length>0)H.push([M,-1,o]);else if(M.value){const u=document.createTextNode(M.value);d.XfaText.shouldBuildText(C)&&y.push(u),o.append(u)}}for(const m of k.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))m.setAttribute("readOnly",!0);return{textDivs:y}}static update(tt){const Tt=`matrix(${tt.viewport.transform.join(",")})`;tt.div.style.transform=Tt,tt.div.hidden=!1}}v.XfaLayer=P},(f,v,vt)=>{var D,g,j,H,k,y,w,m,E,x,M,C,o,u,L,Kr,l,Jr,A,Zr,Z,Qr,nt,ar,bt,ts,yt,or,Bt,es,T,ns,a,is,c,rs,S,ss,z,Tn,ct,lr,Et,gi,wt,mi,ie,Wn,Jt,cr,ht,vi,St,as,Ht,hr,fe,os,ge,ls,re,ur,_t,bi,$,zn;Object.defineProperty(v,"__esModule",{value:!0}),v.InkEditor=void 0;var d=vt(1),P=vt(4),e=vt(29),tt=vt(6),Tt=vt(5);const At=class At extends P.AnnotationEditor{constructor(st){super({...st,name:"inkEditor"});U(this,L);U(this,l);U(this,A);U(this,Z);U(this,nt);U(this,bt);U(this,yt);U(this,Bt);U(this,T);U(this,a);U(this,c);U(this,S);U(this,z);U(this,ct);U(this,Et);U(this,wt);U(this,ie);U(this,Jt);U(this,ht);U(this,ge);U(this,re);U(this,_t);U(this,$);U(this,D,0);U(this,g,0);U(this,j,this.canvasPointermove.bind(this));U(this,H,this.canvasPointerleave.bind(this));U(this,k,this.canvasPointerup.bind(this));U(this,y,this.canvasPointerdown.bind(this));U(this,w,new Path2D);U(this,m,!1);U(this,E,!1);U(this,x,!1);U(this,M,null);U(this,C,0);U(this,o,0);U(this,u,null);this.color=st.color||null,this.thickness=st.thickness||null,this.opacity=st.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(st){P.AnnotationEditor.initialize(st,{strings:["editor_ink_canvas_aria_label","editor_ink2_aria_label"]})}static updateDefaultParams(st,Ot){switch(st){case d.AnnotationEditorParamsType.INK_THICKNESS:At._defaultThickness=Ot;break;case d.AnnotationEditorParamsType.INK_COLOR:At._defaultColor=Ot;break;case d.AnnotationEditorParamsType.INK_OPACITY:At._defaultOpacity=Ot/100;break}}updateParams(st,Ot){switch(st){case d.AnnotationEditorParamsType.INK_THICKNESS:Q(this,L,Kr).call(this,Ot);break;case d.AnnotationEditorParamsType.INK_COLOR:Q(this,l,Jr).call(this,Ot);break;case d.AnnotationEditorParamsType.INK_OPACITY:Q(this,A,Zr).call(this,Ot);break}}static get defaultPropertiesToUpdate(){return[[d.AnnotationEditorParamsType.INK_THICKNESS,At._defaultThickness],[d.AnnotationEditorParamsType.INK_COLOR,At._defaultColor||P.AnnotationEditor._defaultLineColor],[d.AnnotationEditorParamsType.INK_OPACITY,Math.round(At._defaultOpacity*100)]]}get propertiesToUpdate(){return[[d.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||At._defaultThickness],[d.AnnotationEditorParamsType.INK_COLOR,this.color||At._defaultColor||P.AnnotationEditor._defaultLineColor],[d.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??At._defaultOpacity))]]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.canvas||(Q(this,Et,gi).call(this),Q(this,wt,mi).call(this)),this.isAttachedToDOM||(this.parent.add(this),Q(this,ie,Wn).call(this)),Q(this,$,zn).call(this)))}remove(){this.canvas!==null&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,t(this,M).disconnect(),ft(this,M,null),super.remove())}setParent(st){!this.parent&&st?this._uiManager.removeShouldRescale(this):this.parent&&st===null&&this._uiManager.addShouldRescale(this),super.setParent(st)}onScaleChanging(){const[st,Ot]=this.parentDimensions,Qt=this.width*st,Wt=this.height*Ot;this.setDimensions(Qt,Wt)}enableEditMode(){t(this,m)||this.canvas===null||(super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",t(this,y)))}disableEditMode(){!this.isInEditMode()||this.canvas===null||(super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",t(this,y)))}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return this.paths.length===0||this.paths.length===1&&this.paths[0].length===0}commit(){t(this,m)||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),ft(this,m,!0),this.div.classList.add("disabled"),Q(this,$,zn).call(this,!0),this.makeResizable(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}focusin(st){this._focusEventsAllowed&&(super.focusin(st),this.enableEditMode())}canvasPointerdown(st){st.button!==0||!this.isInEditMode()||t(this,m)||(this.setInForeground(),st.preventDefault(),st.type!=="mouse"&&this.div.focus(),Q(this,bt,ts).call(this,st.offsetX,st.offsetY))}canvasPointermove(st){st.preventDefault(),Q(this,yt,or).call(this,st.offsetX,st.offsetY)}canvasPointerup(st){st.preventDefault(),Q(this,ct,lr).call(this,st)}canvasPointerleave(st){Q(this,ct,lr).call(this,st)}get isResizable(){return!this.isEmpty()&&t(this,m)}render(){if(this.div)return this.div;let st,Ot;this.width&&(st=this.x,Ot=this.y),super.render(),P.AnnotationEditor._l10nPromise.get("editor_ink2_aria_label").then(Ie=>{var Lt;return(Lt=this.div)==null?void 0:Lt.setAttribute("aria-label",Ie)});const[Qt,Wt,te,ne]=Q(this,Z,Qr).call(this);if(this.setAt(Qt,Wt,0,0),this.setDims(te,ne),Q(this,Et,gi).call(this),this.width){const[Ie,Lt]=this.parentDimensions;this.setAspectRatio(this.width*Ie,this.height*Lt),this.setAt(st*Ie,Ot*Lt,this.width*Ie,this.height*Lt),ft(this,x,!0),Q(this,ie,Wn).call(this),this.setDims(this.width*Ie,this.height*Lt),Q(this,z,Tn).call(this),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return Q(this,wt,mi).call(this),this.div}setDimensions(st,Ot){const Qt=Math.round(st),Wt=Math.round(Ot);if(t(this,C)===Qt&&t(this,o)===Wt)return;ft(this,C,Qt),ft(this,o,Wt),this.canvas.style.visibility="hidden";const[te,ne]=this.parentDimensions;this.width=st/te,this.height=Ot/ne,this.fixAndSetPosition(),t(this,m)&&Q(this,Jt,cr).call(this,st,Ot),Q(this,ie,Wn).call(this),Q(this,z,Tn).call(this),this.canvas.style.visibility="visible",this.fixDims()}static deserialize(st,Ot,Qt){var K,it,gt;if(st instanceof e.InkAnnotationElement)return null;const Wt=super.deserialize(st,Ot,Qt);Wt.thickness=st.thickness,Wt.color=d.Util.makeHexColor(...st.color),Wt.opacity=st.opacity;const[te,ne]=Wt.pageDimensions,Ie=Wt.width*te,Lt=Wt.height*ne,ke=Wt.parentScale,Zt=st.thickness/2;ft(Wt,m,!0),ft(Wt,C,Math.round(Ie)),ft(Wt,o,Math.round(Lt));const{paths:N,rect:n,rotation:b}=st;for(let{bezier:Mt}of N){Mt=Q(K=At,fe,os).call(K,Mt,n,b);const qt=[];Wt.paths.push(qt);let zt=ke*(Mt[0]-Zt),ve=ke*(Mt[1]-Zt);for(let Kt=2,ce=Mt.length;Kt<ce;Kt+=6){const Ee=ke*(Mt[Kt]-Zt),De=ke*(Mt[Kt+1]-Zt),He=ke*(Mt[Kt+2]-Zt),ln=ke*(Mt[Kt+3]-Zt),Ze=ke*(Mt[Kt+4]-Zt),vn=ke*(Mt[Kt+5]-Zt);qt.push([[zt,ve],[Ee,De],[He,ln],[Ze,vn]]),zt=Ze,ve=vn}const Yt=Q(this,St,as).call(this,qt);Wt.bezierPath2D.push(Yt)}const B=Q(it=Wt,re,ur).call(it);return ft(Wt,g,Math.max(P.AnnotationEditor.MIN_SIZE,B[2]-B[0])),ft(Wt,D,Math.max(P.AnnotationEditor.MIN_SIZE,B[3]-B[1])),Q(gt=Wt,Jt,cr).call(gt,Ie,Lt),Wt}serialize(){if(this.isEmpty())return null;const st=this.getRect(0,0),Ot=P.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:d.AnnotationEditorType.INK,color:Ot,thickness:this.thickness,opacity:this.opacity,paths:Q(this,ge,ls).call(this,this.scaleFactor/this.parentScale,this.translationX,this.translationY,st),pageIndex:this.pageIndex,rect:st,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}};D=new WeakMap,g=new WeakMap,j=new WeakMap,H=new WeakMap,k=new WeakMap,y=new WeakMap,w=new WeakMap,m=new WeakMap,E=new WeakMap,x=new WeakMap,M=new WeakMap,C=new WeakMap,o=new WeakMap,u=new WeakMap,L=new WeakSet,Kr=function(st){const Ot=this.thickness;this.addCommands({cmd:()=>{this.thickness=st,Q(this,$,zn).call(this)},undo:()=>{this.thickness=Ot,Q(this,$,zn).call(this)},mustExec:!0,type:d.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})},l=new WeakSet,Jr=function(st){const Ot=this.color;this.addCommands({cmd:()=>{this.color=st,Q(this,z,Tn).call(this)},undo:()=>{this.color=Ot,Q(this,z,Tn).call(this)},mustExec:!0,type:d.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})},A=new WeakSet,Zr=function(st){st/=100;const Ot=this.opacity;this.addCommands({cmd:()=>{this.opacity=st,Q(this,z,Tn).call(this)},undo:()=>{this.opacity=Ot,Q(this,z,Tn).call(this)},mustExec:!0,type:d.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})},Z=new WeakSet,Qr=function(){const{parentRotation:st,parentDimensions:[Ot,Qt]}=this;switch(st){case 90:return[0,Qt,Qt,Ot];case 180:return[Ot,Qt,Ot,Qt];case 270:return[Ot,0,Qt,Ot];default:return[0,0,Ot,Qt]}},nt=new WeakSet,ar=function(){const{ctx:st,color:Ot,opacity:Qt,thickness:Wt,parentScale:te,scaleFactor:ne}=this;st.lineWidth=Wt*te/ne,st.lineCap="round",st.lineJoin="round",st.miterLimit=10,st.strokeStyle=`${Ot}${(0,Tt.opacityToHex)(Qt)}`},bt=new WeakSet,ts=function(st,Ot){this.canvas.addEventListener("contextmenu",tt.noContextMenu),this.canvas.addEventListener("pointerleave",t(this,H)),this.canvas.addEventListener("pointermove",t(this,j)),this.canvas.addEventListener("pointerup",t(this,k)),this.canvas.removeEventListener("pointerdown",t(this,y)),this.isEditing=!0,t(this,x)||(ft(this,x,!0),Q(this,ie,Wn).call(this),this.thickness||(this.thickness=At._defaultThickness),this.color||(this.color=At._defaultColor||P.AnnotationEditor._defaultLineColor),this.opacity??(this.opacity=At._defaultOpacity)),this.currentPath.push([st,Ot]),ft(this,E,!1),Q(this,nt,ar).call(this),ft(this,u,()=>{Q(this,a,is).call(this),t(this,u)&&window.requestAnimationFrame(t(this,u))}),window.requestAnimationFrame(t(this,u))},yt=new WeakSet,or=function(st,Ot){const[Qt,Wt]=this.currentPath.at(-1);if(this.currentPath.length>1&&st===Qt&&Ot===Wt)return;const te=this.currentPath;let ne=t(this,w);if(te.push([st,Ot]),ft(this,E,!0),te.length<=2){ne.moveTo(...te[0]),ne.lineTo(st,Ot);return}te.length===3&&(ft(this,w,ne=new Path2D),ne.moveTo(...te[0])),Q(this,c,rs).call(this,ne,...te.at(-3),...te.at(-2),st,Ot)},Bt=new WeakSet,es=function(){if(this.currentPath.length===0)return;const st=this.currentPath.at(-1);t(this,w).lineTo(...st)},T=new WeakSet,ns=function(st,Ot){ft(this,u,null),st=Math.min(Math.max(st,0),this.canvas.width),Ot=Math.min(Math.max(Ot,0),this.canvas.height),Q(this,yt,or).call(this,st,Ot),Q(this,Bt,es).call(this);let Qt;if(this.currentPath.length!==1)Qt=Q(this,S,ss).call(this);else{const Lt=[st,Ot];Qt=[[Lt,Lt.slice(),Lt.slice(),Lt]]}const Wt=t(this,w),te=this.currentPath;this.currentPath=[],ft(this,w,new Path2D);const ne=()=>{this.allRawPaths.push(te),this.paths.push(Qt),this.bezierPath2D.push(Wt),this.rebuild()},Ie=()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),this.paths.length===0?this.remove():(this.canvas||(Q(this,Et,gi).call(this),Q(this,wt,mi).call(this)),Q(this,$,zn).call(this))};this.addCommands({cmd:ne,undo:Ie,mustExec:!0})},a=new WeakSet,is=function(){if(!t(this,E))return;ft(this,E,!1);const st=Math.ceil(this.thickness*this.parentScale),Ot=this.currentPath.slice(-3),Qt=Ot.map(ne=>ne[0]),Wt=Ot.map(ne=>ne[1]);Math.min(...Qt)-st,Math.max(...Qt)+st,Math.min(...Wt)-st,Math.max(...Wt)+st;const{ctx:te}=this;te.save(),te.clearRect(0,0,this.canvas.width,this.canvas.height);for(const ne of this.bezierPath2D)te.stroke(ne);te.stroke(t(this,w)),te.restore()},c=new WeakSet,rs=function(st,Ot,Qt,Wt,te,ne,Ie){const Lt=(Ot+Wt)/2,ke=(Qt+te)/2,Zt=(Wt+ne)/2,N=(te+Ie)/2;st.bezierCurveTo(Lt+2*(Wt-Lt)/3,ke+2*(te-ke)/3,Zt+2*(Wt-Zt)/3,N+2*(te-N)/3,Zt,N)},S=new WeakSet,ss=function(){const st=this.currentPath;if(st.length<=2)return[[st[0],st[0],st.at(-1),st.at(-1)]];const Ot=[];let Qt,[Wt,te]=st[0];for(Qt=1;Qt<st.length-2;Qt++){const[n,b]=st[Qt],[B,K]=st[Qt+1],it=(n+B)/2,gt=(b+K)/2,Mt=[Wt+2*(n-Wt)/3,te+2*(b-te)/3],qt=[it+2*(n-it)/3,gt+2*(b-gt)/3];Ot.push([[Wt,te],Mt,qt,[it,gt]]),[Wt,te]=[it,gt]}const[ne,Ie]=st[Qt],[Lt,ke]=st[Qt+1],Zt=[Wt+2*(ne-Wt)/3,te+2*(Ie-te)/3],N=[Lt+2*(ne-Lt)/3,ke+2*(Ie-ke)/3];return Ot.push([[Wt,te],Zt,N,[Lt,ke]]),Ot},z=new WeakSet,Tn=function(){if(this.isEmpty()){Q(this,ht,vi).call(this);return}Q(this,nt,ar).call(this);const{canvas:st,ctx:Ot}=this;Ot.setTransform(1,0,0,1,0,0),Ot.clearRect(0,0,st.width,st.height),Q(this,ht,vi).call(this);for(const Qt of this.bezierPath2D)Ot.stroke(Qt)},ct=new WeakSet,lr=function(st){this.canvas.removeEventListener("pointerleave",t(this,H)),this.canvas.removeEventListener("pointermove",t(this,j)),this.canvas.removeEventListener("pointerup",t(this,k)),this.canvas.addEventListener("pointerdown",t(this,y)),setTimeout(()=>{this.canvas.removeEventListener("contextmenu",tt.noContextMenu)},10),Q(this,T,ns).call(this,st.offsetX,st.offsetY),this.addToAnnotationStorage(),this.setInBackground()},Et=new WeakSet,gi=function(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",P.AnnotationEditor._l10nPromise.get("editor_ink_canvas_aria_label").then(st=>{var Ot;return(Ot=this.canvas)==null?void 0:Ot.setAttribute("aria-label",st)}),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")},wt=new WeakSet,mi=function(){ft(this,M,new ResizeObserver(st=>{const Ot=st[0].contentRect;Ot.width&&Ot.height&&this.setDimensions(Ot.width,Ot.height)})),t(this,M).observe(this.div)},ie=new WeakSet,Wn=function(){if(!t(this,x))return;const[st,Ot]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*st),this.canvas.height=Math.ceil(this.height*Ot),Q(this,ht,vi).call(this)},Jt=new WeakSet,cr=function(st,Ot){const Qt=Q(this,_t,bi).call(this),Wt=(st-Qt)/t(this,g),te=(Ot-Qt)/t(this,D);this.scaleFactor=Math.min(Wt,te)},ht=new WeakSet,vi=function(){const st=Q(this,_t,bi).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+st,this.translationY*this.scaleFactor+st)},St=new WeakSet,as=function(st){const Ot=new Path2D;for(let Qt=0,Wt=st.length;Qt<Wt;Qt++){const[te,ne,Ie,Lt]=st[Qt];Qt===0&&Ot.moveTo(...te),Ot.bezierCurveTo(ne[0],ne[1],Ie[0],Ie[1],Lt[0],Lt[1])}return Ot},Ht=new WeakSet,hr=function(st,Ot,Qt){const[Wt,te,ne,Ie]=Ot;switch(Qt){case 0:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2)st[Lt]+=Wt,st[Lt+1]=Ie-st[Lt+1];break;case 90:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2){const Zt=st[Lt];st[Lt]=st[Lt+1]+Wt,st[Lt+1]=Zt+te}break;case 180:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2)st[Lt]=ne-st[Lt],st[Lt+1]+=te;break;case 270:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2){const Zt=st[Lt];st[Lt]=ne-st[Lt+1],st[Lt+1]=Ie-Zt}break;default:throw new Error("Invalid rotation")}return st},fe=new WeakSet,os=function(st,Ot,Qt){const[Wt,te,ne,Ie]=Ot;switch(Qt){case 0:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2)st[Lt]-=Wt,st[Lt+1]=Ie-st[Lt+1];break;case 90:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2){const Zt=st[Lt];st[Lt]=st[Lt+1]-te,st[Lt+1]=Zt-Wt}break;case 180:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2)st[Lt]=ne-st[Lt],st[Lt+1]-=te;break;case 270:for(let Lt=0,ke=st.length;Lt<ke;Lt+=2){const Zt=st[Lt];st[Lt]=Ie-st[Lt+1],st[Lt+1]=ne-Zt}break;default:throw new Error("Invalid rotation")}return st},ge=new WeakSet,ls=function(st,Ot,Qt,Wt){var ke,Zt;const te=[],ne=this.thickness/2,Ie=st*Ot+ne,Lt=st*Qt+ne;for(const N of this.paths){const n=[],b=[];for(let B=0,K=N.length;B<K;B++){const[it,gt,Mt,qt]=N[B],zt=st*it[0]+Ie,ve=st*it[1]+Lt,Yt=st*gt[0]+Ie,Kt=st*gt[1]+Lt,ce=st*Mt[0]+Ie,Ee=st*Mt[1]+Lt,De=st*qt[0]+Ie,He=st*qt[1]+Lt;B===0&&(n.push(zt,ve),b.push(zt,ve)),n.push(Yt,Kt,ce,Ee,De,He),b.push(Yt,Kt),B===K-1&&b.push(De,He)}te.push({bezier:Q(ke=At,Ht,hr).call(ke,n,Wt,this.rotation),points:Q(Zt=At,Ht,hr).call(Zt,b,Wt,this.rotation)})}return te},re=new WeakSet,ur=function(){let st=1/0,Ot=-1/0,Qt=1/0,Wt=-1/0;for(const te of this.paths)for(const[ne,Ie,Lt,ke]of te){const Zt=d.Util.bezierBoundingBox(...ne,...Ie,...Lt,...ke);st=Math.min(st,Zt[0]),Qt=Math.min(Qt,Zt[1]),Ot=Math.max(Ot,Zt[2]),Wt=Math.max(Wt,Zt[3])}return[st,Qt,Ot,Wt]},_t=new WeakSet,bi=function(){return t(this,m)?Math.ceil(this.thickness*this.parentScale):0},$=new WeakSet,zn=function(st=!1){if(this.isEmpty())return;if(!t(this,m)){Q(this,z,Tn).call(this);return}const Ot=Q(this,re,ur).call(this),Qt=Q(this,_t,bi).call(this);ft(this,g,Math.max(P.AnnotationEditor.MIN_SIZE,Ot[2]-Ot[0])),ft(this,D,Math.max(P.AnnotationEditor.MIN_SIZE,Ot[3]-Ot[1]));const Wt=Math.ceil(Qt+t(this,g)*this.scaleFactor),te=Math.ceil(Qt+t(this,D)*this.scaleFactor),[ne,Ie]=this.parentDimensions;this.width=Wt/ne,this.height=te/Ie,this.setAspectRatio(Wt,te);const Lt=this.translationX,ke=this.translationY;this.translationX=-Ot[0],this.translationY=-Ot[1],Q(this,ie,Wn).call(this),Q(this,z,Tn).call(this),ft(this,C,Wt),ft(this,o,te),this.setDims(Wt,te);const Zt=st?Qt/this.scaleFactor/2:0;this.translate(Lt-this.translationX-Zt,ke-this.translationY-Zt)},U(At,St),U(At,Ht),U(At,fe),Je(At,"_defaultColor",null),Je(At,"_defaultOpacity",1),Je(At,"_defaultThickness",1),Je(At,"_type","ink");let G=At;v.InkEditor=G},(f,v,vt)=>{var G,D,g,j,H,k,y,w,m,E,x,Zn,C,Qn,u,_i,_,dr,F,cs,q,hs,R,fr,pt,yi,Y,us;Object.defineProperty(v,"__esModule",{value:!0}),v.StampEditor=void 0;var d=vt(1),P=vt(4),e=vt(6),tt=vt(29);const kt=class kt extends P.AnnotationEditor{constructor(T){super({...T,name:"stampEditor"});U(this,x);U(this,C);U(this,u);U(this,_);U(this,F);U(this,q);U(this,R);U(this,pt);U(this,Y);U(this,G,null);U(this,D,null);U(this,g,null);U(this,j,null);U(this,H,null);U(this,k,null);U(this,y,null);U(this,w,null);U(this,m,!1);U(this,E,!1);ft(this,j,T.bitmapUrl),ft(this,H,T.bitmapFile)}static initialize(T){P.AnnotationEditor.initialize(T)}static get supportedTypes(){const T=["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"];return(0,d.shadow)(this,"supportedTypes",T.map(i=>`image/${i}`))}static get supportedTypesStr(){return(0,d.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(T){return this.supportedTypes.includes(T)}static paste(T,i){i.pasteEditor(d.AnnotationEditorType.STAMP,{bitmapFile:T.getAsFile()})}remove(){var T,i;t(this,D)&&(ft(this,G,null),this._uiManager.imageManager.deleteId(t(this,D)),(T=t(this,k))==null||T.remove(),ft(this,k,null),(i=t(this,y))==null||i.disconnect(),ft(this,y,null)),super.remove()}rebuild(){if(!this.parent){t(this,D)&&Q(this,u,_i).call(this);return}super.rebuild(),this.div!==null&&(t(this,D)&&Q(this,u,_i).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(t(this,g)||t(this,G)||t(this,j)||t(this,H))}get isResizable(){return!0}render(){if(this.div)return this.div;let T,i;if(this.width&&(T=this.x,i=this.y),super.render(),this.div.hidden=!0,t(this,G)?Q(this,_,dr).call(this):Q(this,u,_i).call(this),this.width){const[a,s]=this.parentDimensions;this.setAt(T*a,i*s,this.width*a,this.height*s)}return this.div}static deserialize(T,i,a){if(T instanceof tt.StampAnnotationElement)return null;const s=super.deserialize(T,i,a),{rect:c,bitmapUrl:h,bitmapId:S,isSvg:I,accessibilityData:z}=T;S&&a.imageManager.isValidId(S)?ft(s,D,S):ft(s,j,h),ft(s,m,I);const[dt,ct]=s.pageDimensions;return s.width=(c[2]-c[0])/dt,s.height=(c[3]-c[1])/ct,z&&(s.altTextData=z),s}serialize(T=!1,i=null){if(this.isEmpty())return null;const a={annotationType:d.AnnotationEditorType.STAMP,bitmapId:t(this,D),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:t(this,m),structTreeParentId:this._structTreeParentId};if(T)return a.bitmapUrl=Q(this,pt,yi).call(this,!0),a.accessibilityData=this.altTextData,a;const{decorative:s,altText:c}=this.altTextData;if(!s&&c&&(a.accessibilityData={type:"Figure",alt:c}),i===null)return a;i.stamps||(i.stamps=new Map);const h=t(this,m)?(a.rect[2]-a.rect[0])*(a.rect[3]-a.rect[1]):null;if(!i.stamps.has(t(this,D)))i.stamps.set(t(this,D),{area:h,serialized:a}),a.bitmap=Q(this,pt,yi).call(this,!1);else if(t(this,m)){const S=i.stamps.get(t(this,D));h>S.area&&(S.area=h,S.serialized.bitmap.close(),S.serialized.bitmap=Q(this,pt,yi).call(this,!1))}return a}};G=new WeakMap,D=new WeakMap,g=new WeakMap,j=new WeakMap,H=new WeakMap,k=new WeakMap,y=new WeakMap,w=new WeakMap,m=new WeakMap,E=new WeakMap,x=new WeakSet,Zn=function(T,i=!1){if(!T){this.remove();return}ft(this,G,T.bitmap),i||(ft(this,D,T.id),ft(this,m,T.isSvg)),Q(this,_,dr).call(this)},C=new WeakSet,Qn=function(){ft(this,g,null),this._uiManager.enableWaiting(!1),t(this,k)&&this.div.focus()},u=new WeakSet,_i=function(){if(t(this,D)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(t(this,D)).then(i=>Q(this,x,Zn).call(this,i,!0)).finally(()=>Q(this,C,Qn).call(this));return}if(t(this,j)){const i=t(this,j);ft(this,j,null),this._uiManager.enableWaiting(!0),ft(this,g,this._uiManager.imageManager.getFromUrl(i).then(a=>Q(this,x,Zn).call(this,a)).finally(()=>Q(this,C,Qn).call(this)));return}if(t(this,H)){const i=t(this,H);ft(this,H,null),this._uiManager.enableWaiting(!0),ft(this,g,this._uiManager.imageManager.getFromFile(i).then(a=>Q(this,x,Zn).call(this,a)).finally(()=>Q(this,C,Qn).call(this)));return}const T=document.createElement("input");T.type="file",T.accept=kt.supportedTypesStr,ft(this,g,new Promise(i=>{T.addEventListener("change",async()=>{if(!T.files||T.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const a=await this._uiManager.imageManager.getFromFile(T.files[0]);Q(this,x,Zn).call(this,a)}i()}),T.addEventListener("cancel",()=>{this.remove(),i()})}).finally(()=>Q(this,C,Qn).call(this))),T.click()},_=new WeakSet,dr=function(){const{div:T}=this;let{width:i,height:a}=t(this,G);const[s,c]=this.pageDimensions,h=.75;if(this.width)i=this.width*s,a=this.height*c;else if(i>h*s||a>h*c){const dt=Math.min(h*s/i,h*c/a);i*=dt,a*=dt}const[S,I]=this.parentDimensions;this.setDims(i*S/s,a*I/c),this._uiManager.enableWaiting(!1);const z=ft(this,k,document.createElement("canvas"));T.append(z),T.hidden=!1,Q(this,R,fr).call(this,i,a),Q(this,Y,us).call(this),t(this,E)||(this.parent.addUndoableEditor(this),ft(this,E,!0)),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"inserted_image"}}}),this.addAltTextButton()},F=new WeakSet,cs=function(T,i){var h;const[a,s]=this.parentDimensions;this.width=T/a,this.height=i/s,this.setDims(T,i),(h=this._initialOptions)!=null&&h.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,t(this,w)!==null&&clearTimeout(t(this,w)),ft(this,w,setTimeout(()=>{ft(this,w,null),Q(this,R,fr).call(this,T,i)},200))},q=new WeakSet,hs=function(T,i){const{width:a,height:s}=t(this,G);let c=a,h=s,S=t(this,G);for(;c>2*T||h>2*i;){const I=c,z=h;c>2*T&&(c=c>=16384?Math.floor(c/2)-1:Math.ceil(c/2)),h>2*i&&(h=h>=16384?Math.floor(h/2)-1:Math.ceil(h/2));const dt=new OffscreenCanvas(c,h);dt.getContext("2d").drawImage(S,0,0,I,z,0,0,c,h),S=dt.transferToImageBitmap()}return S},R=new WeakSet,fr=function(T,i){T=Math.ceil(T),i=Math.ceil(i);const a=t(this,k);if(!a||a.width===T&&a.height===i)return;a.width=T,a.height=i;const s=t(this,m)?t(this,G):Q(this,q,hs).call(this,T,i),c=a.getContext("2d");c.filter=this._uiManager.hcmFilter,c.drawImage(s,0,0,s.width,s.height,0,0,T,i)},pt=new WeakSet,yi=function(T){if(T){if(t(this,m)){const s=this._uiManager.imageManager.getSvgUrl(t(this,D));if(s)return s}const i=document.createElement("canvas");return{width:i.width,height:i.height}=t(this,G),i.getContext("2d").drawImage(t(this,G),0,0),i.toDataURL()}if(t(this,m)){const[i,a]=this.pageDimensions,s=Math.round(this.width*i*e.PixelsPerInch.PDF_TO_CSS_UNITS),c=Math.round(this.height*a*e.PixelsPerInch.PDF_TO_CSS_UNITS),h=new OffscreenCanvas(s,c);return h.getContext("2d").drawImage(t(this,G),0,0,t(this,G).width,t(this,G).height,0,0,s,c),h.transferToImageBitmap()}return structuredClone(t(this,G))},Y=new WeakSet,us=function(){ft(this,y,new ResizeObserver(T=>{const i=T[0].contentRect;i.width&&i.height&&Q(this,F,cs).call(this,i.width,i.height)})),t(this,y).observe(this.div)},Je(kt,"_type","stamp");let Tt=kt;v.StampEditor=Tt}],__webpack_module_cache__={};function __w_pdfjs_require__(f){var v=__webpack_module_cache__[f];if(v!==void 0)return v.exports;var vt=__webpack_module_cache__[f]={exports:{}};return __webpack_modules__[f](vt,vt.exports,__w_pdfjs_require__),vt.exports}var __webpack_exports__={};return(()=>{var f=__webpack_exports__;Object.defineProperty(f,"__esModule",{value:!0}),Object.defineProperty(f,"AbortException",{enumerable:!0,get:function(){return v.AbortException}}),Object.defineProperty(f,"AnnotationEditorLayer",{enumerable:!0,get:function(){return e.AnnotationEditorLayer}}),Object.defineProperty(f,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return v.AnnotationEditorParamsType}}),Object.defineProperty(f,"AnnotationEditorType",{enumerable:!0,get:function(){return v.AnnotationEditorType}}),Object.defineProperty(f,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return tt.AnnotationEditorUIManager}}),Object.defineProperty(f,"AnnotationLayer",{enumerable:!0,get:function(){return Tt.AnnotationLayer}}),Object.defineProperty(f,"AnnotationMode",{enumerable:!0,get:function(){return v.AnnotationMode}}),Object.defineProperty(f,"CMapCompressionType",{enumerable:!0,get:function(){return v.CMapCompressionType}}),Object.defineProperty(f,"DOMSVGFactory",{enumerable:!0,get:function(){return d.DOMSVGFactory}}),Object.defineProperty(f,"FeatureTest",{enumerable:!0,get:function(){return v.FeatureTest}}),Object.defineProperty(f,"GlobalWorkerOptions",{enumerable:!0,get:function(){return G.GlobalWorkerOptions}}),Object.defineProperty(f,"ImageKind",{enumerable:!0,get:function(){return v.ImageKind}}),Object.defineProperty(f,"InvalidPDFException",{enumerable:!0,get:function(){return v.InvalidPDFException}}),Object.defineProperty(f,"MissingPDFException",{enumerable:!0,get:function(){return v.MissingPDFException}}),Object.defineProperty(f,"OPS",{enumerable:!0,get:function(){return v.OPS}}),Object.defineProperty(f,"PDFDataRangeTransport",{enumerable:!0,get:function(){return vt.PDFDataRangeTransport}}),Object.defineProperty(f,"PDFDateString",{enumerable:!0,get:function(){return d.PDFDateString}}),Object.defineProperty(f,"PDFWorker",{enumerable:!0,get:function(){return vt.PDFWorker}}),Object.defineProperty(f,"PasswordResponses",{enumerable:!0,get:function(){return v.PasswordResponses}}),Object.defineProperty(f,"PermissionFlag",{enumerable:!0,get:function(){return v.PermissionFlag}}),Object.defineProperty(f,"PixelsPerInch",{enumerable:!0,get:function(){return d.PixelsPerInch}}),Object.defineProperty(f,"PromiseCapability",{enumerable:!0,get:function(){return v.PromiseCapability}}),Object.defineProperty(f,"RenderingCancelledException",{enumerable:!0,get:function(){return d.RenderingCancelledException}}),Object.defineProperty(f,"SVGGraphics",{enumerable:!0,get:function(){return vt.SVGGraphics}}),Object.defineProperty(f,"UnexpectedResponseException",{enumerable:!0,get:function(){return v.UnexpectedResponseException}}),Object.defineProperty(f,"Util",{enumerable:!0,get:function(){return v.Util}}),Object.defineProperty(f,"VerbosityLevel",{enumerable:!0,get:function(){return v.VerbosityLevel}}),Object.defineProperty(f,"XfaLayer",{enumerable:!0,get:function(){return D.XfaLayer}}),Object.defineProperty(f,"build",{enumerable:!0,get:function(){return vt.build}}),Object.defineProperty(f,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return v.createValidAbsoluteUrl}}),Object.defineProperty(f,"getDocument",{enumerable:!0,get:function(){return vt.getDocument}}),Object.defineProperty(f,"getFilenameFromUrl",{enumerable:!0,get:function(){return d.getFilenameFromUrl}}),Object.defineProperty(f,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return d.getPdfFilenameFromUrl}}),Object.defineProperty(f,"getXfaPageViewport",{enumerable:!0,get:function(){return d.getXfaPageViewport}}),Object.defineProperty(f,"isDataScheme",{enumerable:!0,get:function(){return d.isDataScheme}}),Object.defineProperty(f,"isPdfFile",{enumerable:!0,get:function(){return d.isPdfFile}}),Object.defineProperty(f,"loadScript",{enumerable:!0,get:function(){return d.loadScript}}),Object.defineProperty(f,"noContextMenu",{enumerable:!0,get:function(){return d.noContextMenu}}),Object.defineProperty(f,"normalizeUnicode",{enumerable:!0,get:function(){return v.normalizeUnicode}}),Object.defineProperty(f,"renderTextLayer",{enumerable:!0,get:function(){return P.renderTextLayer}}),Object.defineProperty(f,"setLayerDimensions",{enumerable:!0,get:function(){return d.setLayerDimensions}}),Object.defineProperty(f,"shadow",{enumerable:!0,get:function(){return v.shadow}}),Object.defineProperty(f,"updateTextLayer",{enumerable:!0,get:function(){return P.updateTextLayer}}),Object.defineProperty(f,"version",{enumerable:!0,get:function(){return vt.version}});var v=__w_pdfjs_require__(1),vt=__w_pdfjs_require__(2),d=__w_pdfjs_require__(6),P=__w_pdfjs_require__(26),e=__w_pdfjs_require__(27),tt=__w_pdfjs_require__(5),Tt=__w_pdfjs_require__(29),G=__w_pdfjs_require__(14),D=__w_pdfjs_require__(32)})(),__webpack_exports__})())})(pdf$1);var pdfExports=pdf$1.exports;const pdf=getDefaultExportFromCjs(pdfExports),pdfjsModule=_mergeNamespaces({__proto__:null,default:pdf},[pdfExports]);var lib={exports:{}},core_min={};(function(f){var v=reactExports,vt=pdfExports;function d(r){var p=Object.create(null);return r&&Object.keys(r).forEach(function(O){if(O!=="default"){var W=Object.getOwnPropertyDescriptor(r,O);Object.defineProperty(p,O,W.get?W:{enumerable:!0,get:function(){return r[O]}})}}),p.default=r,Object.freeze(p)}var P,e=d(v),tt=d(vt);f.AnnotationType=void 0,(P=f.AnnotationType||(f.AnnotationType={}))[P.Text=1]="Text",P[P.Link=2]="Link",P[P.FreeText=3]="FreeText",P[P.Line=4]="Line",P[P.Square=5]="Square",P[P.Circle=6]="Circle",P[P.Polygon=7]="Polygon",P[P.Polyline=8]="Polyline",P[P.Highlight=9]="Highlight",P[P.Underline=10]="Underline",P[P.Squiggly=11]="Squiggly",P[P.StrikeOut=12]="StrikeOut",P[P.Stamp=13]="Stamp",P[P.Caret=14]="Caret",P[P.Ink=15]="Ink",P[P.Popup=16]="Popup",P[P.FileAttachment=17]="FileAttachment";var Tt=function(r,p){return Tt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(O,W){O.__proto__=W}||function(O,W){for(var X in W)Object.prototype.hasOwnProperty.call(W,X)&&(O[X]=W[X])},Tt(r,p)};function G(r,p){if(typeof p!="function"&&p!==null)throw new TypeError("Class extends value "+String(p)+" is not a constructor or null");function O(){this.constructor=r}Tt(r,p),r.prototype=p===null?Object.create(p):(O.prototype=p.prototype,new O)}var D,g=function(){return g=Object.assign||function(r){for(var p,O=1,W=arguments.length;O<W;O++)for(var X in p=arguments[O])Object.prototype.hasOwnProperty.call(p,X)&&(r[X]=p[X]);return r},g.apply(this,arguments)};f.TextDirection=void 0,(D=f.TextDirection||(f.TextDirection={})).RightToLeft="RTL",D.LeftToRight="LTR";var j,H=e.createContext({currentTheme:"light",direction:f.TextDirection.LeftToRight,setCurrentTheme:function(){}}),k=function(r){var p=[];return Object.keys(r).forEach(function(O){O&&r[O]&&p.push(O)}),p.join(" ")},y=typeof window<"u"?e.useLayoutEffect:e.useEffect,w=function(r){var p=e.useRef(null),O=r.once,W=r.threshold,X=r.onVisibilityChanged;return y(function(){var J=p.current;if(J){var ut=new IntersectionObserver(function(rt){rt.forEach(function(V){var lt=V.isIntersecting,ot=V.intersectionRatio;X({isVisible:lt,ratio:ot}),lt&&O&&(ut.unobserve(J),ut.disconnect())})},{threshold:W||0});return ut.observe(J),function(){ut.unobserve(J),ut.disconnect()}}},[]),p},m=function(r){var p=r.children,O=r.ignoreDirection,W=O!==void 0&&O,X=r.size,J=X===void 0?24:X,ut=e.useContext(H).direction,rt=!W&&ut===f.TextDirection.RightToLeft,V="".concat(J||24,"px");return e.createElement("svg",{"aria-hidden":"true",className:k({"rpv-core__icon":!0,"rpv-core__icon--rtl":rt}),focusable:"false",height:V,viewBox:"0 0 24 24",width:V},p)},E=function(){return e.createElement(m,{size:16},e.createElement("path",{d:"M23.5,0.499l-16.5,23l-6.5-6.5"}))},x=function(r){var p=r.children,O=r.testId,W=r.onClick,X=e.useContext(H).direction===f.TextDirection.RightToLeft,J=O?{"data-testid":O}:{};return e.createElement("button",g({className:k({"rpv-core__primary-button":!0,"rpv-core__primary-button--rtl":X}),type:"button",onClick:W},J),p)},M=function(r){var p=r.size,O=p===void 0?"4rem":p,W=r.testId,X=e.useState(!1),J=X[0],ut=X[1],rt=W?{"data-testid":W}:{},V=w({onVisibilityChanged:function(lt){ut(lt.isVisible)}});return e.createElement("div",g({},rt,{className:k({"rpv-core__spinner":!0,"rpv-core__spinner--animating":J}),ref:V,style:{height:O,width:O}}))},C=function(r){var p=r.ariaLabel,O=p===void 0?"":p,W=r.autoFocus,X=W!==void 0&&W,J=r.placeholder,ut=J===void 0?"":J,rt=r.testId,V=r.type,lt=V===void 0?"text":V,ot=r.value,xt=ot===void 0?"":ot,Rt=r.onChange,Pt=r.onKeyDown,Xt=Pt===void 0?function(){}:Pt,Vt=e.useContext(H).direction,Ut=e.useRef(),Le=Vt===f.TextDirection.RightToLeft,pe={ref:Ut,"data-testid":"","aria-label":O,className:k({"rpv-core__textbox":!0,"rpv-core__textbox--rtl":Le}),placeholder:ut,value:xt,onChange:function(me){return Rt(me.target.value)},onKeyDown:Xt};return rt&&(pe["data-testid"]=rt),y(function(){if(X){var me=Ut.current;if(me){var Se=window.scrollX,se=window.scrollY;me.focus(),window.scrollTo(Se,se)}}},[]),lt==="text"?e.createElement("input",g({type:"text"},pe)):e.createElement("input",g({type:"password"},pe))};(function(r){r[r.ExitFullScreen=0]="ExitFullScreen",r[r.FullScreenChange=1]="FullScreenChange",r[r.FullScreenElement=2]="FullScreenElement",r[r.FullScreenEnabled=3]="FullScreenEnabled",r[r.RequestFullScreen=4]="RequestFullScreen"})(j||(j={}));var o,u={ExitFullScreen:"exitFullscreen",FullScreenChange:"fullscreenchange",FullScreenElement:"fullscreenElement",FullScreenEnabled:"fullscreenEnabled",RequestFullScreen:"requestFullscreen"},L={ExitFullScreen:"webkitExitFullscreen",FullScreenChange:"webkitfullscreenchange",FullScreenElement:"webkitFullscreenElement",FullScreenEnabled:"webkitFullscreenEnabled",RequestFullScreen:"webkitRequestFullscreen"},_={ExitFullScreen:"msExitFullscreen",FullScreenChange:"msFullscreenChange",FullScreenElement:"msFullscreenElement",FullScreenEnabled:"msFullscreenEnabled",RequestFullScreen:"msRequestFullscreen"},l=typeof window<"u",F=l&&(j.FullScreenEnabled in document&&u||L.FullScreenEnabled in document&&L||_.FullScreenEnabled in document&&_)||u,A=function(){return l&&F.FullScreenEnabled in document&&document[F.FullScreenEnabled]===!0},q=function(r){return l?r[F.ExitFullScreen]():Promise.resolve({})},Z=function(){return l?document[F.FullScreenElement]:null},R=function(r,p){var O=e.useRef(),W=function(){O.current&&clearTimeout(O.current)};return e.useEffect(function(){return function(){return W()}},[]),e.useCallback(function(){for(var X=[],J=0;J<arguments.length;J++)X[J]=arguments[J];W(),O.current=setTimeout(function(){r.apply(void 0,X)},p)},[r,p])},nt=function(){var r=e.useRef(!1);return e.useEffect(function(){return r.current=!0,function(){r.current=!1}},[]),r},pt=function(r){var p=e.useRef(r);return e.useEffect(function(){p.current=r},[r]),p.current};(function(r){r.NotRenderedYet="NotRenderedYet",r.Rendering="Rendering",r.Rendered="Rendered"})(o||(o={}));var bt,Y=-9999,yt=function(r){var p=r.doc,O=p.numPages,W=p.loadingTask.docId,X=e.useMemo(function(){return Array(O).fill(null).map(function(rt,V){return{pageIndex:V,renderStatus:o.NotRenderedYet,visibility:Y}})},[W]),J=e.useRef({currentRenderingPage:-1,startRange:0,endRange:O-1,visibilities:X}),ut=function(rt,V){J.current.visibilities[rt].visibility=V};return{getHighestPriorityPage:function(){var rt=J.current.visibilities.slice(J.current.startRange,J.current.endRange+1).filter(function(Rt){return Rt.visibility>Y});if(!rt.length)return-1;for(var V=rt[0].pageIndex,lt=rt[rt.length-1].pageIndex,ot=rt.length,xt=0;xt<ot;xt++){if(rt[xt].renderStatus===o.Rendering)return-1;if(rt[xt].renderStatus===o.NotRenderedYet)return rt[xt].pageIndex}return lt+1<O&&J.current.visibilities[lt+1].renderStatus!==o.Rendered?lt+1:V-1>=0&&J.current.visibilities[V-1].renderStatus!==o.Rendered?V-1:-1},isInRange:function(rt){return rt>=J.current.startRange&&rt<=J.current.endRange},markNotRendered:function(){for(var rt=0;rt<O;rt++)J.current.visibilities[rt].renderStatus=o.NotRenderedYet},markRendered:function(rt){J.current.visibilities[rt].renderStatus=o.Rendered},markRendering:function(rt){J.current.currentRenderingPage!==-1&&J.current.currentRenderingPage!==rt&&J.current.visibilities[J.current.currentRenderingPage].renderStatus===o.Rendering&&(J.current.visibilities[J.current.currentRenderingPage].renderStatus=o.NotRenderedYet),J.current.visibilities[rt].renderStatus=o.Rendering,J.current.currentRenderingPage=rt},setOutOfRange:function(rt){ut(rt,Y)},setRange:function(rt,V){J.current.startRange=rt,J.current.endRange=V;for(var lt=0;lt<O;lt++)(lt<rt||lt>V)&&(J.current.visibilities[lt].visibility=Y,J.current.visibilities[lt].renderStatus=o.NotRenderedYet)},setVisibility:ut}},kt={core:{askingPassword:{requirePasswordToOpen:"This document requires a password to open",submit:"Submit"},wrongPassword:{tryAgain:"The password is wrong. Please try again"},pageLabel:"Page {{pageIndex}}"}},Bt=e.createContext({l10n:kt,setL10n:function(){}}),Gt=0,T=function(){return Gt++},i=function(r,p,O){var W=function(X){var J=p.current;if(J){var ut=X.target;if(ut instanceof Element&&ut.shadowRoot){var rt=X.composedPath();rt.length>0&&!J.contains(rt[0])&&O()}else J.contains(ut)||O()}};e.useEffect(function(){if(r){var X={capture:!0};return document.addEventListener("click",W,X),function(){document.removeEventListener("click",W,X)}}},[])},a=function(r){var p=function(O){O.key==="Escape"&&r()};e.useEffect(function(){return document.addEventListener("keyup",p),function(){document.removeEventListener("keyup",p)}},[])},s=function(r){var p=r.ariaControlsSuffix,O=r.children,W=r.closeOnClickOutside,X=r.closeOnEscape,J=r.onToggle,ut=e.useRef(),rt=e.useContext(H).direction===f.TextDirection.RightToLeft;return e.useEffect(function(){var V=window.getComputedStyle(document.body).overflow;return document.body.style.overflow="hidden",function(){document.body.style.overflow=V}},[]),a(function(){ut.current&&X&&J()}),i(W,ut,J),y(function(){var V=ut.current;if(V){var lt=.75*document.body.clientHeight;V.getBoundingClientRect().height>=lt&&(V.style.overflow="auto",V.style.maxHeight="".concat(lt,"px"))}},[]),e.createElement("div",{"aria-modal":"true",className:k({"rpv-core__modal-body":!0,"rpv-core__modal-body--rtl":rt}),id:"rpv-core__modal-body-".concat(p),ref:ut,role:"dialog",tabIndex:-1},O)},c=function(r){var p=r.children;return e.createElement("div",{className:"rpv-core__modal-overlay"},p)};f.ToggleStatus=void 0,(bt=f.ToggleStatus||(f.ToggleStatus={})).Close="Close",bt.Open="Open",bt.Toggle="Toggle";var h,S=function(r){var p=e.useState(r),O=p[0],W=p[1];return{opened:O,toggle:function(X){switch(X){case f.ToggleStatus.Close:W(!1);break;case f.ToggleStatus.Open:W(!0);break;case f.ToggleStatus.Toggle:default:W(function(J){return!J})}}}},I=function(r){var p=r.content,O=r.isOpened,W=O!==void 0&&O,X=r.target,J=S(W),ut=J.opened,rt=J.toggle;return e.createElement(e.Fragment,null,X&&X(rt,ut),ut&&p(rt))};f.Position=void 0,(h=f.Position||(f.Position={})).TopLeft="TOP_LEFT",h.TopCenter="TOP_CENTER",h.TopRight="TOP_RIGHT",h.RightTop="RIGHT_TOP",h.RightCenter="RIGHT_CENTER",h.RightBottom="RIGHT_BOTTOM",h.BottomLeft="BOTTOM_LEFT",h.BottomCenter="BOTTOM_CENTER",h.BottomRight="BOTTOM_RIGHT",h.LeftTop="LEFT_TOP",h.LeftCenter="LEFT_CENTER",h.LeftBottom="LEFT_BOTTOM";var z,dt,ct,mt,Et,Dt,wt,$t,ie=function(r,p,O,W,X){y(function(){var J=p.current,ut=r.current,rt=O.current;if(ut&&J&&rt){var V=rt.getBoundingClientRect(),lt=function(Rt,Pt,Xt,Vt){var Ut=Pt.getBoundingClientRect(),Le=Rt.getBoundingClientRect(),pe=Le.height,me=Le.width,Se=0,se=0;switch(Xt){case f.Position.TopLeft:Se=Ut.top-pe,se=Ut.left;break;case f.Position.TopCenter:Se=Ut.top-pe,se=Ut.left+Ut.width/2-me/2;break;case f.Position.TopRight:Se=Ut.top-pe,se=Ut.left+Ut.width-me;break;case f.Position.RightTop:Se=Ut.top,se=Ut.left+Ut.width;break;case f.Position.RightCenter:Se=Ut.top+Ut.height/2-pe/2,se=Ut.left+Ut.width;break;case f.Position.RightBottom:Se=Ut.top+Ut.height-pe,se=Ut.left+Ut.width;break;case f.Position.BottomLeft:Se=Ut.top+Ut.height,se=Ut.left;break;case f.Position.BottomCenter:Se=Ut.top+Ut.height,se=Ut.left+Ut.width/2-me/2;break;case f.Position.BottomRight:Se=Ut.top+Ut.height,se=Ut.left+Ut.width-me;break;case f.Position.LeftTop:Se=Ut.top,se=Ut.left-me;break;case f.Position.LeftCenter:Se=Ut.top+Ut.height/2-pe/2,se=Ut.left-me;break;case f.Position.LeftBottom:Se=Ut.top+Ut.height-pe,se=Ut.left-me}return{left:se+(Vt.left||0),top:Se+(Vt.top||0)}}(ut,J,W,X),ot=lt.top,xt=lt.left;ut.style.top="".concat(ot-V.top,"px"),ut.style.left="".concat(xt-V.left,"px")}},[])},It=function(r){var p,O=r.customClassName,W=r.position;return e.createElement("div",{className:k((p={"rpv-core__arrow":!0,"rpv-core__arrow--tl":W===f.Position.TopLeft,"rpv-core__arrow--tc":W===f.Position.TopCenter,"rpv-core__arrow--tr":W===f.Position.TopRight,"rpv-core__arrow--rt":W===f.Position.RightTop,"rpv-core__arrow--rc":W===f.Position.RightCenter,"rpv-core__arrow--rb":W===f.Position.RightBottom,"rpv-core__arrow--bl":W===f.Position.BottomLeft,"rpv-core__arrow--bc":W===f.Position.BottomCenter,"rpv-core__arrow--br":W===f.Position.BottomRight,"rpv-core__arrow--lt":W===f.Position.LeftTop,"rpv-core__arrow--lc":W===f.Position.LeftCenter,"rpv-core__arrow--lb":W===f.Position.LeftBottom},p["".concat(O)]=O!=="",p))})},Jt=function(r){var p=r.ariaControlsSuffix,O=r.children,W=r.closeOnClickOutside,X=r.offset,J=r.position,ut=r.targetRef,rt=r.onClose,V=e.useRef(),lt=e.useRef(),ot=e.useRef(),xt=e.useContext(H).direction===f.TextDirection.RightToLeft;i(W,V,rt),ie(V,ut,ot,J,X),y(function(){var Pt=lt.current;if(Pt){var Xt=.75*document.body.clientHeight;Pt.getBoundingClientRect().height>=Xt&&(Pt.style.overflow="auto",Pt.style.maxHeight="".concat(Xt,"px"))}},[]);var Rt="rpv-core__popover-body-inner-".concat(p);return e.createElement(e.Fragment,null,e.createElement("div",{ref:ot,style:{left:0,position:"absolute",top:0}}),e.createElement("div",{"aria-describedby":Rt,className:k({"rpv-core__popover-body":!0,"rpv-core__popover-body--rtl":xt}),id:"rpv-core__popover-body-".concat(p),ref:V,role:"dialog",tabIndex:-1},e.createElement(It,{customClassName:"rpv-core__popover-body-arrow",position:J}),e.createElement("div",{id:Rt,ref:lt},O)))},Ct=function(r){var p=r.closeOnEscape,O=r.onClose,W=e.useRef();return a(function(){W.current&&p&&O()}),e.createElement("div",{className:"rpv-core__popover-overlay",ref:W})},ht=function(r){var p=r.ariaControlsSuffix,O=r.children,W=r.contentRef,X=r.offset,J=r.position,ut=r.targetRef,rt=e.useRef(),V=e.useContext(H).direction===f.TextDirection.RightToLeft;return ie(W,ut,rt,J,X),e.createElement(e.Fragment,null,e.createElement("div",{ref:rt,style:{left:0,position:"absolute",top:0}}),e.createElement("div",{className:k({"rpv-core__tooltip-body":!0,"rpv-core__tooltip-body--rtl":V}),id:"rpv-core__tooltip-body-".concat(p),ref:W,role:"tooltip"},e.createElement(It,{customClassName:"rpv-core__tooltip-body-arrow",position:J}),e.createElement("div",{className:"rpv-core__tooltip-body-content"},O)))};f.FullScreenMode=void 0,(z=f.FullScreenMode||(f.FullScreenMode={})).Normal="Normal",z.Entering="Entering",z.Entered="Entered",z.EnteredCompletely="EnteredCompletely",z.Exitting="Exitting",z.Exited="Exited",f.LayerRenderStatus=void 0,(dt=f.LayerRenderStatus||(f.LayerRenderStatus={}))[dt.PreRender=0]="PreRender",dt[dt.DidRender=1]="DidRender",f.PageMode=void 0,(ct=f.PageMode||(f.PageMode={})).Attachments="UseAttachments",ct.Bookmarks="UseOutlines",ct.ContentGroup="UseOC",ct.Default="UserNone",ct.FullScreen="FullScreen",ct.Thumbnails="UseThumbs",f.PasswordStatus=void 0,(mt=f.PasswordStatus||(f.PasswordStatus={})).RequiredPassword="RequiredPassword",mt.WrongPassword="WrongPassword",f.RotateDirection=void 0,(Et=f.RotateDirection||(f.RotateDirection={})).Backward="Backward",Et.Forward="Forward",f.ScrollMode=void 0,(Dt=f.ScrollMode||(f.ScrollMode={})).Page="Page",Dt.Horizontal="Horizontal",Dt.Vertical="Vertical",Dt.Wrapped="Wrapped",f.SpecialZoomLevel=void 0,(wt=f.SpecialZoomLevel||(f.SpecialZoomLevel={})).ActualSize="ActualSize",wt.PageFit="PageFit",wt.PageWidth="PageWidth",f.ViewMode=void 0,($t=f.ViewMode||(f.ViewMode={})).DualPage="DualPage",$t.DualPageWithCover="DualPageWithCover",$t.SinglePage="SinglePage";var at,St=function(r,p){return r.reduce(function(O,W,X){return X%p?O[O.length-1].push(W):O.push([W]),O},[])},jt=function(r,p){switch(p[1].name){case"XYZ":return{bottomOffset:function(O,W){return p[3]===null?W:p[3]},leftOffset:function(O,W){return p[2]===null?0:p[2]},pageIndex:r,scaleTo:p[4]};case"Fit":case"FitB":return{bottomOffset:0,leftOffset:0,pageIndex:r,scaleTo:f.SpecialZoomLevel.PageFit};case"FitH":case"FitBH":return{bottomOffset:p[2],leftOffset:0,pageIndex:r,scaleTo:f.SpecialZoomLevel.PageWidth};default:return{bottomOffset:0,leftOffset:0,pageIndex:r,scaleTo:1}}},Ht=new Map,le=new Map,fe=function(r,p){return"".concat(r.loadingTask.docId,"___").concat(p.num,"R").concat(p.gen===0?"":p.gen)},he=function(r,p,O){Ht.set(fe(r,p),O)},ge=function(r,p){if(!r)return Promise.reject("The document is not loaded yet");var O="".concat(r.loadingTask.docId,"___").concat(p),W=le.get(O);return W?Promise.resolve(W):new Promise(function(X,J){r.getPage(p+1).then(function(ut){le.set(O,ut),ut.ref&&he(r,ut.ref,p),X(ut)})})},_e=function(r,p){return new Promise(function(O){new Promise(function(W){typeof p=="string"?r.getDestination(p).then(function(X){W(X)}):W(p)}).then(function(W){if(typeof W[0]=="object"&&W[0]!==null){var X=W[0],J=function(rt,V){var lt=fe(rt,V);return Ht.has(lt)?Ht.get(lt):null}(r,X);J===null?r.getPageIndex(X).then(function(rt){he(r,X,rt),_e(r,p).then(function(V){return O(V)})}):O(jt(J,W))}else{var ut=jt(W[0],W);O(ut)}})})};(function(r){r[r.Solid=1]="Solid",r[r.Dashed=2]="Dashed",r[r.Beveled=3]="Beveled",r[r.Inset=4]="Inset",r[r.Underline=5]="Underline"})(at||(at={}));var re,we=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"),_t=function(r,p,O,W){var X=parseInt(r,10);return X>=p&&X<=O?X:W},et=function(r){return r.contentsObj?r.contentsObj.str:r.contents||""},$=function(r){return r.titleObj?r.titleObj.str:r.title||""},Ft=function(r){var p=r.annotation,O=e.useContext(H).direction,W=$(p),X=et(p),J=O===f.TextDirection.RightToLeft,ut=e.useRef(),rt="";if(p.modificationDate){var V=function(lt){var ot=we.exec(lt);if(!ot)return null;var xt=parseInt(ot[1],10),Rt=_t(ot[2],1,12,1)-1,Pt=_t(ot[3],1,31,1),Xt=_t(ot[4],0,23,0),Vt=_t(ot[5],0,59,0),Ut=_t(ot[6],0,59,0),Le=ot[7]||"Z",pe=_t(ot[8],0,23,0),me=_t(ot[9],0,59,0);switch(Le){case"-":Xt+=pe,Vt+=me;break;case"+":Xt-=pe,Vt-=me}return new Date(Date.UTC(xt,Rt,Pt,Xt,Vt,Ut))}(p.modificationDate);rt=V?"".concat(V.toLocaleDateString(),", ").concat(V.toLocaleTimeString()):""}return e.useLayoutEffect(function(){if(ut.current){var lt=document.querySelector('[data-annotation-id="'.concat(p.id,'"]'));if(lt){var ot=lt;return ot.style.zIndex+=1,function(){ot.style.zIndex="".concat(parseInt(ot.style.zIndex,10)-1)}}}},[]),e.createElement("div",{ref:ut,className:k({"rpv-core__annotation-popup-wrapper":!0,"rpv-core__annotation-popup-wrapper--rtl":J}),style:{top:p.annotationType===f.AnnotationType.Popup?"":"100%"}},W&&e.createElement(e.Fragment,null,e.createElement("div",{className:k({"rpv-core__annotation-popup-title":!0,"rpv-core__annotation-popup-title--ltr":!J,"rpv-core__annotation-popup-title--rtl":J})},W),e.createElement("div",{className:"rpv-core__annotation-popup-date"},rt)),X&&e.createElement("div",{className:"rpv-core__annotation-popup-content"},X.split(`
`).map(function(lt,ot){return e.createElement(e.Fragment,{key:ot},lt,e.createElement("br",null))})))};(function(r){r.Click="Click",r.Hover="Hover"})(re||(re={}));var At,xe=function(r){var p,O=r.annotation,W=r.children,X=r.ignoreBorder,J=r.hasPopup,ut=r.isRenderable,rt=r.page,V=r.viewport,lt=O.rect,ot=function(){var ye=S(!1),We=ye.opened,Ue=ye.toggle,ze=e.useState(re.Hover),Ve=ze[0],qe=ze[1];return{opened:We,closeOnHover:function(){Ve===re.Hover&&Ue(f.ToggleStatus.Close)},openOnHover:function(){Ve===re.Hover&&Ue(f.ToggleStatus.Open)},toggleOnClick:function(){switch(Ve){case re.Click:We&&qe(re.Hover),Ue(f.ToggleStatus.Toggle);break;case re.Hover:qe(re.Click),Ue(f.ToggleStatus.Open)}}}}(),xt=ot.closeOnHover,Rt=ot.opened,Pt=ot.openOnHover,Xt=ot.toggleOnClick,Vt=(p=[lt[0],rt.view[3]+rt.view[1]-lt[1],lt[2],rt.view[3]+rt.view[1]-lt[3]],[Math.min(p[0],p[2]),Math.min(p[1],p[3]),Math.max(p[0],p[2]),Math.max(p[1],p[3])]),Ut=lt[2]-lt[0],Le=lt[3]-lt[1],pe={borderColor:"",borderRadius:"",borderStyle:"",borderWidth:""};if(!X&&O.borderStyle.width>0){switch(O.borderStyle.style){case at.Dashed:pe.borderStyle="dashed";break;case at.Solid:pe.borderStyle="solid";break;case at.Underline:pe=Object.assign({borderBottomStyle:"solid"},pe);case at.Beveled:case at.Inset:}var me=O.borderStyle.width;pe.borderWidth="".concat(me,"px"),O.borderStyle.style!==at.Underline&&(Ut-=2*me,Le-=2*me);var Se=O.borderStyle,se=Se.horizontalCornerRadius,Ce=Se.verticalCornerRadius;(se>0||Ce>0)&&(pe.borderRadius="".concat(se,"px / ").concat(Ce,"px")),O.color?pe.borderColor="rgb(".concat(0|O.color[0],", ").concat(0|O.color[1],", ").concat(0|O.color[2],")"):pe.borderWidth="0"}return e.createElement(e.Fragment,null,ut&&W({popup:{opened:Rt,closeOnHover:xt,openOnHover:Pt,toggleOnClick:Xt},slot:{attrs:{style:Object.assign({height:"".concat(Le,"px"),left:"".concat(Vt[0],"px"),top:"".concat(Vt[1],"px"),transform:"matrix(".concat(V.transform.join(","),")"),transformOrigin:"-".concat(Vt[0],"px -").concat(Vt[1],"px"),width:"".concat(Ut,"px")},pe)},children:e.createElement(e.Fragment,null,J&&Rt&&e.createElement(Ft,{annotation:O}))}}))},je=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut);return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--caret","data-annotation-id":p.id,onClick:V.popup.toggleOnClick,onMouseEnter:V.popup.openOnHover,onMouseLeave:V.popup.closeOnHover}),V.slot.children)})},st=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut),V=p.rect,lt=V[2]-V[0],ot=V[3]-V[1],xt=p.borderStyle.width;return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(Rt){return e.createElement("div",g({},Rt.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--circle","data-annotation-id":p.id,onClick:Rt.popup.toggleOnClick,onMouseEnter:Rt.popup.openOnHover,onMouseLeave:Rt.popup.closeOnHover}),e.createElement("svg",{height:"".concat(ot,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(lt," ").concat(ot),width:"".concat(lt,"px")},e.createElement("circle",{cy:ot/2,fill:"none",rx:lt/2-xt/2,ry:ot/2-xt/2,stroke:"transparent",strokeWidth:xt||1})),Rt.slot.children)})},Ot=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=$(p),J=et(p),ut=!(p.hasPopup!==!1||!X&&!J),rt=function(){var V,lt,ot,xt,Rt=p.file;Rt&&(V=Rt.filename,lt=Rt.content,ot=typeof lt=="string"?"":URL.createObjectURL(new Blob([lt],{type:""})),(xt=document.createElement("a")).style.display="none",xt.href=ot||V,xt.setAttribute("download",function(Pt){var Xt=Pt.split("/").pop();return Xt?Xt.split("#")[0].split("?")[0]:Pt}(V)),document.body.appendChild(xt),xt.click(),document.body.removeChild(xt),ot&&URL.revokeObjectURL(ot))};return e.createElement(xe,{annotation:p,hasPopup:ut,ignoreBorder:!0,isRenderable:!0,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--file-attachment","data-annotation-id":p.id,onClick:V.popup.toggleOnClick,onDoubleClick:rt,onMouseEnter:V.popup.openOnHover,onMouseLeave:V.popup.closeOnHover}),V.slot.children)})},Qt=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut);return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--free-text","data-annotation-id":p.id,onClick:V.popup.toggleOnClick,onMouseEnter:V.popup.openOnHover,onMouseLeave:V.popup.closeOnHover}),V.slot.children)})},Wt=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=$(p),J=et(p),ut=!(!X&&!J),rt=!p.parentType||["Circle","Ink","Line","Polygon","PolyLine","Square"].indexOf(p.parentType)!==-1;return y(function(){if(p.parentId){var V=document.querySelector('[data-annotation-id="'.concat(p.parentId,'"]')),lt=document.querySelector('[data-annotation-id="'.concat(p.id,'"]'));if(V&&lt){var ot=parseFloat(V.style.left),xt=parseFloat(V.style.top)+parseFloat(V.style.height);lt.style.left="".concat(ot,"px"),lt.style.top="".concat(xt,"px"),lt.style.transformOrigin="-".concat(ot,"px -").concat(xt,"px")}}},[]),e.createElement(xe,{annotation:p,hasPopup:rt,ignoreBorder:!1,isRenderable:ut,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--popup","data-annotation-id":p.id}),e.createElement(Ft,{annotation:p}))})},te=function(r){var p=r.annotation,O=r.childAnnotation,W=r.page,X=r.viewport,J=p.hasPopup===!1,ut=$(p),rt=et(p),V=!!(p.hasPopup||ut||rt);if(p.quadPoints&&p.quadPoints.length>0){var lt=p.quadPoints.map(function(ot){return Object.assign({},p,{rect:[ot[2].x,ot[2].y,ot[1].x,ot[1].y],quadPoints:[]})});return e.createElement(e.Fragment,null,lt.map(function(ot,xt){return e.createElement(te,{key:xt,annotation:ot,childAnnotation:O,page:W,viewport:X})}))}return e.createElement(xe,{annotation:p,hasPopup:J,ignoreBorder:!0,isRenderable:V,page:W,viewport:X},function(ot){return e.createElement(e.Fragment,null,e.createElement("div",g({},ot.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--highlight","data-annotation-id":p.id,onClick:ot.popup.toggleOnClick,onMouseEnter:ot.popup.openOnHover,onMouseLeave:ot.popup.closeOnHover}),ot.slot.children),O&&O.annotationType===f.AnnotationType.Popup&&ot.popup.opened&&e.createElement(Wt,{annotation:O,page:W,viewport:X}))})},ne=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut),V=p.rect,lt=V[2]-V[0],ot=V[3]-V[1],xt=p.borderStyle.width;return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(Rt){return e.createElement("div",g({},Rt.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--ink","data-annotation-id":p.id,onClick:Rt.popup.toggleOnClick,onMouseEnter:Rt.popup.openOnHover,onMouseLeave:Rt.popup.closeOnHover}),p.inkLists&&p.inkLists.length&&e.createElement("svg",{height:"".concat(ot,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(lt," ").concat(ot),width:"".concat(lt,"px")},p.inkLists.map(function(Pt,Xt){return e.createElement("polyline",{key:Xt,fill:"none",stroke:"transparent",strokeWidth:xt||1,points:Pt.map(function(Vt){return"".concat(Vt.x-V[0],",").concat(V[3]-Vt.y)}).join(" ")})})),Rt.slot.children)})},Ie=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut),V=p.rect,lt=V[2]-V[0],ot=V[3]-V[1],xt=p.borderStyle.width;return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(Rt){return e.createElement("div",g({},Rt.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--line","data-annotation-id":p.id,onClick:Rt.popup.toggleOnClick,onMouseEnter:Rt.popup.openOnHover,onMouseLeave:Rt.popup.closeOnHover}),e.createElement("svg",{height:"".concat(ot,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(lt," ").concat(ot),width:"".concat(lt,"px")},e.createElement("line",{stroke:"transparent",strokeWidth:xt||1,x1:V[2]-p.lineCoordinates[0],x2:V[2]-p.lineCoordinates[2],y1:V[3]-p.lineCoordinates[1],y2:V[3]-p.lineCoordinates[3]})),Rt.slot.children)})},Lt=/^([^\w]*)(javascript|data|vbscript)/im,ke=/&#(\w+)(^\w|;)?/g,Zt=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,N=/^([^:]+):/gm,n=function(r,p){p===void 0&&(p="about:blank");var O,W=(O=r||"",O.replace(ke,function(rt,V){return String.fromCharCode(V)})).replace(Zt,"").trim();if(!W)return p;var X=W[0];if(X==="."||X==="/")return W;var J=W.match(N);if(!J)return W;var ut=J[0];return Lt.test(ut)?p:W},b=function(r){var p,O=r.annotation,W=r.annotationContainerRef,X=r.doc,J=r.outlines,ut=r.page,rt=r.pageIndex,V=r.scale,lt=r.viewport,ot=r.onExecuteNamedAction,xt=r.onJumpFromLinkAnnotation,Rt=r.onJumpToDest,Pt=e.useRef(),Xt=J&&J.length&&O.dest&&typeof O.dest=="string"?(p=J.find(function(pe){return pe.dest===O.dest}))===null||p===void 0?void 0:p.title:"",Vt=!!(O.url||O.dest||O.action||O.unsafeUrl),Ut={};if(O.url||O.unsafeUrl){var Le=n(O.url||O.unsafeUrl,"");Le?Ut={"data-target":"external",href:Le,rel:"noopener noreferrer nofollow",target:O.newWindow?"_blank":"",title:Le}:Vt=!1}else Ut={href:"","data-annotation-link":O.id,onClick:function(pe){pe.preventDefault(),O.action?ot(O.action):_e(X,O.dest).then(function(me){var Se=Pt.current,se=W.current;if(Se&&se){var Ce=Se.getBoundingClientRect();se.style.setProperty("height","100%"),se.style.setProperty("width","100%");var ye=se.getBoundingClientRect();se.style.removeProperty("height"),se.style.removeProperty("width");var We=(Ce.left-ye.left)/V,Ue=(ye.bottom-Ce.bottom+Ce.height)/V;xt({bottomOffset:Ue,label:Xt,leftOffset:We,pageIndex:rt})}Rt(me)})}};return Xt&&(Ut=Object.assign({},Ut,{title:Xt,"aria-label":Xt})),e.createElement(xe,{annotation:O,hasPopup:!1,ignoreBorder:!1,isRenderable:Vt,page:ut,viewport:lt},function(pe){return e.createElement("div",g({},pe.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--link","data-annotation-id":O.id,"data-testid":"core__annotation--link-".concat(O.id)}),e.createElement("a",g({ref:Pt},Ut)))})},B=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut),V=p.rect,lt=V[2]-V[0],ot=V[3]-V[1],xt=p.borderStyle.width;return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(Rt){return e.createElement("div",g({},Rt.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--polygon","data-annotation-id":p.id,onClick:Rt.popup.toggleOnClick,onMouseEnter:Rt.popup.openOnHover,onMouseLeave:Rt.popup.closeOnHover}),p.vertices&&p.vertices.length&&e.createElement("svg",{height:"".concat(ot,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(lt," ").concat(ot),width:"".concat(lt,"px")},e.createElement("polygon",{fill:"none",stroke:"transparent",strokeWidth:xt||1,points:p.vertices.map(function(Pt){return"".concat(Pt.x-V[0],",").concat(V[3]-Pt.y)}).join(" ")})),Rt.slot.children)})},K=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut),V=p.rect,lt=V[2]-V[0],ot=V[3]-V[1],xt=p.borderStyle.width;return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(Rt){return e.createElement("div",g({},Rt.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--polyline","data-annotation-id":p.id,onClick:Rt.popup.toggleOnClick,onMouseEnter:Rt.popup.openOnHover,onMouseLeave:Rt.popup.closeOnHover}),p.vertices&&p.vertices.length&&e.createElement("svg",{height:"".concat(ot,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(lt," ").concat(ot),width:"".concat(lt,"px")},e.createElement("polyline",{fill:"none",stroke:"transparent",strokeWidth:xt||1,points:p.vertices.map(function(Pt){return"".concat(Pt.x-V[0],",").concat(V[3]-Pt.y)}).join(" ")})),Rt.slot.children)})},it=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut),V=p.rect,lt=V[2]-V[0],ot=V[3]-V[1],xt=p.borderStyle.width;return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(Rt){return e.createElement("div",g({},Rt.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--square","data-annotation-id":p.id,onClick:Rt.popup.toggleOnClick,onMouseEnter:Rt.popup.openOnHover,onMouseLeave:Rt.popup.closeOnHover}),e.createElement("svg",{height:"".concat(ot,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(lt," ").concat(ot),width:"".concat(lt,"px")},e.createElement("rect",{height:ot-xt,fill:"none",stroke:"transparent",strokeWidth:xt||1,x:xt/2,y:xt/2,width:lt-xt})),Rt.slot.children)})},gt=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut);return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--squiggly","data-annotation-id":p.id,onClick:V.popup.toggleOnClick,onMouseEnter:V.popup.openOnHover,onMouseLeave:V.popup.closeOnHover}),V.slot.children)})},Mt=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut);return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--stamp","data-annotation-id":p.id,onClick:V.popup.toggleOnClick,onMouseEnter:V.popup.openOnHover,onMouseLeave:V.popup.closeOnHover}),V.slot.children)})},qt=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut);return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--strike-out","data-annotation-id":p.id,onClick:V.popup.toggleOnClick,onMouseEnter:V.popup.openOnHover,onMouseLeave:V.popup.closeOnHover}),V.slot.children)})},zt=function(){return e.createElement(m,{size:16},e.createElement("path",{d:"M.5,16.5a1,1,0,0,0,1,1h2v4l4-4h15a1,1,0,0,0,1-1V3.5a1,1,0,0,0-1-1H1.5a1,1,0,0,0-1,1Z"}),e.createElement("path",{d:"M7.25,9.75A.25.25,0,1,1,7,10a.25.25,0,0,1,.25-.25"}),e.createElement("path",{d:"M12,9.75a.25.25,0,1,1-.25.25A.25.25,0,0,1,12,9.75"}),e.createElement("path",{d:"M16.75,9.75a.25.25,0,1,1-.25.25.25.25,0,0,1,.25-.25"}))},ve=function(){return e.createElement(m,{size:16},e.createElement("path",{d:"M0.500 12.001 A11.500 11.500 0 1 0 23.500 12.001 A11.500 11.500 0 1 0 0.500 12.001 Z"}),e.createElement("path",{d:"M6.000 12.001 A6.000 6.000 0 1 0 18.000 12.001 A6.000 6.000 0 1 0 6.000 12.001 Z"}),e.createElement("path",{d:"M21.423 5.406L17.415 9.414"}),e.createElement("path",{d:"M14.587 6.585L18.607 2.565"}),e.createElement("path",{d:"M5.405 21.424L9.413 17.416"}),e.createElement("path",{d:"M6.585 14.588L2.577 18.596"}),e.createElement("path",{d:"M18.602 21.419L14.595 17.412"}),e.createElement("path",{d:"M17.419 14.58L21.428 18.589"}),e.createElement("path",{d:"M2.582 5.399L6.588 9.406"}),e.createElement("path",{d:"M9.421 6.581L5.412 2.572"}))},Yt=function(){return e.createElement(m,{size:16},e.createElement("path",{d:"M4.000 18.500 A1.500 1.500 0 1 0 7.000 18.500 A1.500 1.500 0 1 0 4.000 18.500 Z"}),e.createElement("path",{d:"M20.5.5l-9.782,9.783a7,7,0,1,0,3,3L17,10h1.5V8.5L19,8h1.5V6.5L21,6h1.5V4.5l1-1V.5Z"}))},Kt=function(){return e.createElement(m,{size:16},e.createElement("path",{d:"M2.000 2.500 L22.000 2.500 L22.000 23.500 L2.000 23.500 Z"}),e.createElement("path",{d:"M6 4.5L6 0.5"}),e.createElement("path",{d:"M18 4.5L18 0.5"}),e.createElement("path",{d:"M10 4.5L10 0.5"}),e.createElement("path",{d:"M14 4.5L14 0.5"}))},ce=function(){return e.createElement(m,{size:16},e.createElement("path",{d:"M17.5 0.498L17.5 23.498"}),e.createElement("path",{d:"M10.5 0.498L10.5 23.498"}),e.createElement("path",{d:"M23.5.5H6.5a6,6,0,0,0,0,12h4"}))},Ee=function(){return e.createElement(m,{size:16},e.createElement("path",{d:"M2.5 22.995L12 6.005 21.5 22.995 2.5 22.995z"}))},De=function(r){var p=r.annotation,O=r.childAnnotation,W=r.page,X=r.viewport,J=p.hasPopup===!1,ut=$(p),rt=et(p),V=!!(p.hasPopup||ut||rt),lt=p.name?p.name.toLowerCase():"";return e.createElement(xe,{annotation:p,hasPopup:J,ignoreBorder:!1,isRenderable:V,page:W,viewport:X},function(ot){return e.createElement(e.Fragment,null,e.createElement("div",g({},ot.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--text","data-annotation-id":p.id,onClick:ot.popup.toggleOnClick,onMouseEnter:ot.popup.openOnHover,onMouseLeave:ot.popup.closeOnHover}),lt&&e.createElement("div",{className:"rpv-core__annotation-text-icon"},lt==="check"&&e.createElement(E,null),lt==="comment"&&e.createElement(zt,null),lt==="help"&&e.createElement(ve,null),lt==="insert"&&e.createElement(Ee,null),lt==="key"&&e.createElement(Yt,null),lt==="note"&&e.createElement(Kt,null),(lt==="newparagraph"||lt==="paragraph")&&e.createElement(ce,null)),ot.slot.children),O&&O.annotationType===f.AnnotationType.Popup&&ot.popup.opened&&e.createElement(Wt,{annotation:O,page:W,viewport:X}))})},He=function(r){var p=r.annotation,O=r.page,W=r.viewport,X=p.hasPopup===!1,J=$(p),ut=et(p),rt=!!(p.hasPopup||J||ut);return e.createElement(xe,{annotation:p,hasPopup:X,ignoreBorder:!0,isRenderable:rt,page:O,viewport:W},function(V){return e.createElement("div",g({},V.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--underline","data-annotation-id":p.id,onClick:V.popup.toggleOnClick,onMouseEnter:V.popup.openOnHover,onMouseLeave:V.popup.closeOnHover}),V.slot.children)})},ln=function(r){var p=r.annotations,O=r.doc,W=r.outlines,X=r.page,J=r.pageIndex,ut=r.plugins,rt=r.rotation,V=r.scale,lt=r.onExecuteNamedAction,ot=r.onJumpFromLinkAnnotation,xt=r.onJumpToDest,Rt=e.useRef(),Pt=X.getViewport({rotation:rt,scale:V}).clone({dontFlip:!0}),Xt=p.filter(function(Vt){return!Vt.parentId});return y(function(){var Vt=Rt.current;Vt&&ut.forEach(function(Ut){Ut.onAnnotationLayerRender&&Ut.onAnnotationLayerRender({annotations:Xt,container:Vt,pageIndex:J,rotation:rt,scale:V})})},[]),e.createElement("div",{ref:Rt,className:"rpv-core__annotation-layer","data-testid":"core__annotation-layer-".concat(J)},Xt.map(function(Vt){var Ut=p.find(function(Le){return Le.parentId===Vt.id});switch(Vt.annotationType){case f.AnnotationType.Caret:return e.createElement(je,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Circle:return e.createElement(st,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.FileAttachment:return e.createElement(Ot,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.FreeText:return e.createElement(Qt,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Highlight:return e.createElement(te,{key:Vt.id,annotation:Vt,childAnnotation:Ut,page:X,viewport:Pt});case f.AnnotationType.Ink:return e.createElement(ne,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Line:return e.createElement(Ie,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Link:return e.createElement(b,{key:Vt.id,annotation:Vt,annotationContainerRef:Rt,doc:O,outlines:W,page:X,pageIndex:J,scale:V,viewport:Pt,onExecuteNamedAction:lt,onJumpFromLinkAnnotation:ot,onJumpToDest:xt});case f.AnnotationType.Polygon:return e.createElement(B,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Polyline:return e.createElement(K,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Popup:return e.createElement(Wt,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Square:return e.createElement(it,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Squiggly:return e.createElement(gt,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Stamp:return e.createElement(Mt,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.StrikeOut:return e.createElement(qt,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});case f.AnnotationType.Text:return e.createElement(De,{key:Vt.id,annotation:Vt,childAnnotation:Ut,page:X,viewport:Pt});case f.AnnotationType.Underline:return e.createElement(He,{key:Vt.id,annotation:Vt,page:X,viewport:Pt});default:return e.createElement(e.Fragment,{key:Vt.id})}}))},Ze=function(r){var p=r.page,O=r.renderAnnotations,W=nt(),X=e.useState({loading:!0,annotations:[]}),J=X[0],ut=X[1];return e.useEffect(function(){p.getAnnotations({intent:"display"}).then(function(rt){W.current&&ut({loading:!1,annotations:rt})})},[]),J.loading?e.createElement(e.Fragment,null):O(J.annotations)},vn=function(r){var p=r.doc,O=r.outlines,W=r.page,X=r.pageIndex,J=r.plugins,ut=r.rotation,rt=r.scale,V=r.onExecuteNamedAction,lt=r.onJumpFromLinkAnnotation,ot=r.onJumpToDest;return e.createElement(Ze,{page:W,renderAnnotations:function(xt){return e.createElement(ln,{annotations:xt,doc:p,outlines:O,page:W,pageIndex:X,plugins:J,rotation:ut,scale:rt,onExecuteNamedAction:V,onJumpFromLinkAnnotation:lt,onJumpToDest:ot})}})},_n=function(r,p){var O=r%p;return O===0?r:Math.floor(r-O)},wn=function(r){var p=r.canvasLayerRef,O=r.height,W=r.page,X=r.pageIndex,J=r.plugins,ut=r.rotation,rt=r.scale,V=r.width,lt=r.onRenderCanvasCompleted,ot=e.useRef();return y(function(){var xt=ot.current;xt&&xt.cancel();var Rt=p.current;Rt.removeAttribute("data-testid"),J.forEach(function(ye){ye.onCanvasLayerRender&&ye.onCanvasLayerRender({ele:Rt,pageIndex:X,rotation:ut,scale:rt,status:f.LayerRenderStatus.PreRender})});var Pt=W.getViewport({rotation:ut,scale:rt}),Xt=window.devicePixelRatio||1,Vt=Math.sqrt(16777216/(Pt.width*Pt.height)),Ut=Xt>Vt;Ut?Rt.style.transform="scale(1, 1)":Rt.style.removeProperty("transform");var Le=Math.min(Vt,Xt),pe=function(ye,We){var Ue,ze;if(Math.floor(ye)===ye)return[ye,1];var Ve=1/ye;if(Ve>We)return[1,We];if(Math.floor(Ve)===Ve)return[1,Ve];for(var qe=ye>1?Ve:ye,cn=0,en=1,hn=1,rn=1;;){var an=cn+hn,on=en+rn;if(on>We)break;qe<=an/on?(hn=(Ue=[an,on])[0],rn=Ue[1]):(cn=(ze=[an,on])[0],en=ze[1])}return qe<(cn/en+hn/rn)/2?qe===ye?[cn,en]:[en,cn]:qe===ye?[hn,rn]:[rn,hn]}(Le,8),me=pe[0],Se=pe[1];Rt.width=_n(Pt.width*Le,me),Rt.height=_n(Pt.height*Le,me),Rt.style.width="".concat(_n(Pt.width,Se),"px"),Rt.style.height="".concat(_n(Pt.height,Se),"px"),Rt.hidden=!0;var se=Rt.getContext("2d",{alpha:!1}),Ce=Ut||Xt!==1?[Le,0,0,Le,0,0]:null;return ot.current=W.render({canvasContext:se,transform:Ce,viewport:Pt}),ot.current.promise.then(function(){Rt.hidden=!1,Rt.setAttribute("data-testid","core__canvas-layer-".concat(X)),J.forEach(function(ye){ye.onCanvasLayerRender&&ye.onCanvasLayerRender({ele:Rt,pageIndex:X,rotation:ut,scale:rt,status:f.LayerRenderStatus.DidRender})}),lt()},function(){lt()}),function(){Rt&&(Rt.width=0,Rt.height=0)}},[]),e.createElement("div",{className:"rpv-core__canvas-layer",style:{height:"".concat(O,"px"),width:"".concat(V,"px")}},e.createElement("canvas",{ref:p}))},ti=function(r){var p=r.height,O=r.page,W=r.rotation,X=r.scale,J=r.width,ut=e.useRef();return y(function(){var rt=ut.current,V=O.getViewport({rotation:W,scale:X});O.getOperatorList().then(function(lt){(function(){var ot=ut.current;ot&&(ot.innerHTML="")})(),new tt.SVGGraphics(O.commonObjs,O.objs).getSVG(lt,V).then(function(ot){ot.style.height="".concat(p,"px"),ot.style.width="".concat(J,"px"),rt.appendChild(ot)})})},[]),e.createElement("div",{className:"rpv-core__svg-layer",ref:ut})},ds=function(r){var p=r.containerRef,O=r.page,W=r.pageIndex,X=r.plugins,J=r.rotation,ut=r.scale,rt=r.onRenderTextCompleted,V=e.useRef(),lt=function(){var ot=p.current;ot&&([].slice.call(ot.querySelectorAll(".rpv-core__text-layer-text")).forEach(function(xt){return ot.removeChild(xt)}),[].slice.call(ot.querySelectorAll('br[role="presentation"]')).forEach(function(xt){return ot.removeChild(xt)}))};return y(function(){var ot=V.current;ot&&ot.cancel();var xt=p.current;if(xt){xt.removeAttribute("data-testid");var Rt=O.getViewport({rotation:J,scale:ut});return X.forEach(function(Pt){Pt.onTextLayerRender&&Pt.onTextLayerRender({ele:xt,pageIndex:W,scale:ut,status:f.LayerRenderStatus.PreRender})}),O.getTextContent().then(function(Pt){lt(),V.current=tt.renderTextLayer({container:xt,textContent:Pt,textContentSource:Pt,viewport:Rt}),V.current.promise.then(function(){xt.setAttribute("data-testid","core__text-layer-".concat(W)),[].slice.call(xt.children).forEach(function(Xt){Xt.classList.contains("rpv-core__text-layer-text--not")||Xt.classList.add("rpv-core__text-layer-text")}),X.forEach(function(Xt){Xt.onTextLayerRender&&Xt.onTextLayerRender({ele:xt,pageIndex:W,scale:ut,status:f.LayerRenderStatus.DidRender})}),rt()},function(){xt.removeAttribute("data-testid"),rt()})}),function(){var Pt;lt(),(Pt=V.current)===null||Pt===void 0||Pt.cancel()}}},[]),e.createElement("div",{className:"rpv-core__text-layer",ref:p})},fs=function(r){var p=r.doc,O=r.measureRef,W=r.outlines,X=r.pageIndex,J=r.pageRotation,ut=r.pageSize,rt=r.plugins,V=r.renderPage,lt=r.renderQueueKey,ot=r.rotation,xt=r.scale,Rt=r.shouldRender,Pt=r.viewMode,Xt=r.onExecuteNamedAction,Vt=r.onJumpFromLinkAnnotation,Ut=r.onJumpToDest,Le=r.onRenderCompleted,pe=r.onRotatePage,me=nt(),Se=e.useState(null),se=Se[0],Ce=Se[1],ye=e.useState(!1),We=ye[0],Ue=ye[1],ze=e.useState(!1),Ve=ze[0],qe=ze[1],cn=e.useRef(),en=e.useRef(),hn=Math.abs(ot+J)%180==0,rn=ut.pageWidth*xt,an=ut.pageHeight*xt,on=hn?rn:an,sn=hn?an:rn,mn=(ut.rotation+ot+J)%360,Cn=e.useRef(0),En=V||function(fn){return e.createElement(e.Fragment,null,fn.canvasLayer.children,fn.textLayer.children,fn.annotationLayer.children)};return e.useEffect(function(){Ce(null),Ue(!1),qe(!1)},[J,ot,xt]),e.useEffect(function(){Rt&&me.current&&!se&&ge(p,X).then(function(fn){me.current&&(Cn.current=lt,Ce(fn))})},[Rt,se]),e.useEffect(function(){We&&Ve&&(lt!==Cn.current?(Ce(null),Ue(!1),qe(!1)):Le(X))},[We,Ve]),e.createElement("div",{className:k({"rpv-core__page-layer":!0,"rpv-core__page-layer--dual":Pt===f.ViewMode.DualPage,"rpv-core__page-layer--dual-cover":Pt===f.ViewMode.DualPageWithCover,"rpv-core__page-layer--single":Pt===f.ViewMode.SinglePage}),"data-testid":"core__page-layer-".concat(X),ref:O,style:{height:"".concat(sn,"px"),width:"".concat(on,"px")}},se?e.createElement(e.Fragment,null,En({annotationLayer:{attrs:{},children:e.createElement(vn,{doc:p,outlines:W,page:se,pageIndex:X,plugins:rt,rotation:mn,scale:xt,onExecuteNamedAction:Xt,onJumpFromLinkAnnotation:Vt,onJumpToDest:Ut})},canvasLayer:{attrs:{},children:e.createElement(wn,{canvasLayerRef:cn,height:sn,page:se,pageIndex:X,plugins:rt,rotation:mn,scale:xt,width:on,onRenderCanvasCompleted:function(){me.current&&Ue(!0)}})},canvasLayerRendered:We,doc:p,height:sn,pageIndex:X,rotation:mn,scale:xt,svgLayer:{attrs:{},children:e.createElement(ti,{height:sn,page:se,rotation:mn,scale:xt,width:on})},textLayer:{attrs:{},children:e.createElement(ds,{containerRef:en,page:se,pageIndex:X,plugins:rt,rotation:mn,scale:xt,onRenderTextCompleted:function(){me.current&&qe(!0)}})},textLayerRendered:Ve,width:on,markRendered:Le,onRotatePage:function(fn){return pe(X,fn)}}),rt.map(function(fn,bn){return fn.renderPageLayer?e.createElement(e.Fragment,{key:bn},fn.renderPageLayer({canvasLayerRef:cn,canvasLayerRendered:We,doc:p,height:sn,pageIndex:X,rotation:mn,scale:xt,textLayerRef:en,textLayerRendered:Ve,width:on})):e.createElement(e.Fragment,{key:bn})})):e.createElement(M,{testId:"core__page-layer-loading-".concat(X)}))},ps=function(r,p){var O=p.rect;return r.height!==O.height||r.width!==O.width?O:r};(function(r){r.Horizontal="Horizontal",r.Vertical="Vertical",r.Both="Both"})(At||(At={}));var gs=function(r){return 1-Math.pow(1-r,4)},ei=1e-4,pr={left:0,top:0},gr={capture:!1,passive:!0},ms=function(r){var p=r.elementRef,O=r.enableSmoothScroll,W=r.isRtl,X=r.scrollDirection,J=r.onSmoothScroll,ut=e.useState(pr),rt=ut[0],V=ut[1],lt=e.useState(p.current),ot=lt[0],xt=lt[1],Rt=W?-1:1,Pt=e.useRef(X);Pt.current=X;var Xt=e.useRef(pr),Vt=e.useRef(!0),Ut=e.useCallback(function(){Vt.current=!0,O&&V(Xt.current),J(!1)},[]),Le=e.useCallback(function(){if(ot){switch(Pt.current){case At.Horizontal:Xt.current={left:Rt*ot.scrollLeft,top:0};break;case At.Both:Xt.current={left:Rt*ot.scrollLeft,top:ot.scrollTop};break;case At.Vertical:default:Xt.current={left:0,top:ot.scrollTop}}O&&!Vt.current||V(Xt.current)}},[ot]);y(function(){xt(p.current)}),y(function(){if(ot)return ot.addEventListener("scroll",Le,gr),function(){ot.removeEventListener("scroll",Le,gr)}},[ot]);var pe=e.useCallback(function(me,Se){var se=p.current;if(!se)return Promise.resolve();var Ce={left:0,top:0};switch(Pt.current){case At.Horizontal:Ce.left=Rt*me.left;break;case At.Both:Ce.left=Rt*me.left,Ce.top=me.top;break;case At.Vertical:default:Ce.top=me.top}return Se?(Vt.current=!1,J(!0),new Promise(function(ye,We){(function(Ue,ze,Ve,qe,cn,en){cn===void 0&&(cn=function(bn){return bn}),en===void 0&&(en=function(){});var hn=0,rn=0,an=!1;switch(ze){case At.Horizontal:rn=Ue.scrollLeft,hn=0;case At.Both:rn=Ue.scrollLeft,hn=Ue.scrollTop;break;case At.Vertical:default:rn=0,hn=Ue.scrollTop}var on=function(){an||(an=!0,Ue.scrollLeft=Ve.left,Ue.scrollTop=Ve.top,en())};if(Math.abs(hn-Ve.top)<=ei&&ze===At.Vertical)on();else if(Math.abs(rn-Ve.left)<=ei&&ze===At.Horizontal)on();else{var sn,mn=-1,Cn=rn-Ve.left,En=hn-Ve.top,fn=function(bn){mn===-1&&(mn=bn);var Pn=bn-mn,Ae=Math.min(Pn/qe,1),be=cn(Ae),ae={left:rn-Cn*be,top:hn-En*be};switch(ze){case At.Horizontal:Ue.scrollLeft=ae.left;break;case At.Both:Ue.scrollLeft=ae.left,Ue.scrollTop=ae.top;break;case At.Vertical:default:Ue.scrollTop=ae.top}Math.abs(ae.top-Ve.top)<=ei&&Math.abs(ae.left-Ve.left)<=ei&&!an&&(window.cancelAnimationFrame(sn),on()),Pn<qe?sn=window.requestAnimationFrame(fn):window.cancelAnimationFrame(sn)};sn=window.requestAnimationFrame(fn)}})(se,Pt.current,Ce,400,gs,function(){Ut(),ye()})})):new Promise(function(ye,We){switch(Pt.current){case At.Horizontal:se.scrollLeft=Ce.left;break;case At.Both:se.scrollLeft=Ce.left,se.scrollTop=Ce.top;break;case At.Vertical:default:se.scrollTop=Ce.top}ye()})},[p]);return{scrollOffset:rt,scrollTo:pe}},Vn=function(r,p,O){return Math.max(r,Math.min(O,p))},vs=function(r,p){if(p!==f.ViewMode.DualPageWithCover)return 0;if(!function(X){var J=X.length;if(J===1)return!1;for(var ut=1;ut<J;ut++)if(X[ut].height!==X[0].height||X[ut].width!==X[0].width)return!0;return!1}(r))return 2*r[0].width;var O=St(r.slice(1),2).map(function(X){return X.length===2?X[0].width+X[1].width:X[0].width}),W=[r[0].width].concat(O);return Math.max.apply(Math,W)},bs={left:0,top:0},_s={left:0,top:0},Ai={left:0,top:0},ys={left:0,top:0},As={height:0,width:0},mr={left:0,top:0},vr="data-virtual-index",Ss=[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],Es=function(r){var p=r.enableSmoothScroll,O=r.isRtl,W=r.numberOfItems,X=r.parentRef,J=r.setRenderRange,ut=r.sizes,rt=r.scrollMode,V=r.viewMode,lt=e.useState(!1),ot=lt[0],xt=lt[1],Rt=e.useCallback(function(Ae){return xt(Ae)},[]),Pt=e.useRef(rt);Pt.current=rt;var Xt=e.useRef(V);Xt.current=V;var Vt=rt===f.ScrollMode.Wrapped||V===f.ViewMode.DualPageWithCover||V===f.ViewMode.DualPage?At.Both:rt===f.ScrollMode.Horizontal?At.Horizontal:At.Vertical,Ut=ms({elementRef:X,enableSmoothScroll:p,isRtl:O,scrollDirection:Vt,onSmoothScroll:Rt}),Le=Ut.scrollOffset,pe=Ut.scrollTo,me=function(Ae){var be=Ae.elementRef,ae=e.useState(be.current),ue=ae[0],oe=ae[1],Pe=e.useRef(!1),Te=e.useReducer(ps,{height:0,width:0}),de=Te[0],ee=Te[1];return y(function(){be.current!==ue&&oe(be.current)}),y(function(){if(ue&&!Pe.current){Pe.current=!0;var Re=ue.getBoundingClientRect(),Oe=Re.height,Qe=Re.width;ee({rect:{height:Oe,width:Qe}})}},[ue]),e.useEffect(function(){if(ue){var Re=new ResizeObserver(function(Oe,Qe){Oe.forEach(function(Ye){if(Ye.target===ue){var pn=Ye.contentRect,yn=pn.height,Fe=pn.width;ee({rect:{height:yn,width:Fe}})}})});return Re.observe(ue),function(){Re.unobserve(ue)}}},[ue]),de}({elementRef:X}),Se=e.useRef({scrollOffset:mr,measurements:[]});Se.current.scrollOffset=Le;var se=e.useMemo(function(){return Array(W).fill(-1)},[]),Ce=e.useState(se),ye=Ce[0],We=Ce[1],Ue=e.useMemo(function(){var Ae=new IntersectionObserver(function(be){be.forEach(function(ae){var ue=ae.isIntersecting?ae.intersectionRatio:-1,oe=ae.target.getAttribute(vr);if(oe){var Pe=parseInt(oe,10);0<=Pe&&Pe<W&&We(function(Te){return Te[Pe]=ue,function(de,ee,Re){if(Re||arguments.length===2)for(var Oe,Qe=0,Ye=ee.length;Qe<Ye;Qe++)!Oe&&Qe in ee||(Oe||(Oe=Array.prototype.slice.call(ee,0,Qe)),Oe[Qe]=ee[Qe]);return de.concat(Oe||Array.prototype.slice.call(ee))}([],Te,!0)})}})},{threshold:Ss});return Ae},[]),ze=e.useMemo(function(){return rt===f.ScrollMode.Page&&V===f.ViewMode.SinglePage?function(Ae,be,ae){for(var ue=[],oe=0;oe<Ae;oe++){var Pe={height:Math.max(be.height,ae[oe].height),width:Math.max(be.width,ae[oe].width)},Te=oe===0?ys:ue[oe-1].end,de={left:Te.left+Pe.width,top:Te.top+Pe.height};ue[oe]={index:oe,start:Te,size:Pe,end:de,visibility:-1}}return ue}(W,me,ut):V===f.ViewMode.DualPageWithCover?function(Ae,be,ae,ue){for(var oe=[],Pe=0,Te=0,de=Ai,ee=0;ee<Ae;ee++){var Re=ee===0?{height:ue===f.ScrollMode.Page?Math.max(be.height,ae[ee].height):ae[ee].height,width:ue===f.ScrollMode.Page?Math.max(be.width,ae[ee].width):ae[ee].width}:{height:ue===f.ScrollMode.Page?Math.max(be.height,ae[ee].height):ae[ee].height,width:Math.max(be.width/2,ae[ee].width)};ue===f.ScrollMode.Page?de=ee===0?Ai:{left:ee%2==0?Re.width:0,top:Math.floor((ee-1)/2)*Re.height+oe[0].end.top}:ee===0?(de=Ai,Pe=ae[0].height,Te=0):ee%2==1?(de={left:0,top:Pe+=Te},Te=ee===Ae-1?ae[ee].height:Math.max(ae[ee].height,ae[ee+1].height)):de={left:oe[ee-1].end.left,top:Pe};var Oe={left:de.left+Re.width,top:de.top+Re.height};oe[ee]={index:ee,start:de,size:Re,end:Oe,visibility:-1}}return oe}(W,me,ut,rt):V===f.ViewMode.DualPage?function(Ae,be,ae,ue){for(var oe=[],Pe=0,Te=0,de=_s,ee=0;ee<Ae;ee++){var Re={height:ue===f.ScrollMode.Page?Math.max(be.height,ae[ee].height):ae[ee].height,width:Math.max(be.width/2,ae[ee].width)};ue===f.ScrollMode.Page?de={left:ee%2==0?0:Re.width,top:Math.floor(ee/2)*Re.height}:ee%2==0?(de={left:0,top:Pe+=Te},Te=ee===Ae-1?ae[ee].height:Math.max(ae[ee].height,ae[ee+1].height)):de={left:oe[ee-1].end.left,top:Pe};var Oe={left:de.left+Re.width,top:de.top+Re.height};oe[ee]={index:ee,start:de,size:Re,end:Oe,visibility:-1}}return oe}(W,me,ut,rt):function(Ae,be,ae,ue){for(var oe=[],Pe=0,Te={left:0,top:0},de=0,ee=bs,Re=0;Re<Ae;Re++){var Oe=ae[Re];if(Re===0)Pe=Oe.width,Te={left:0,top:0},de=Oe.height;else switch(ue){case f.ScrollMode.Wrapped:(Pe+=Oe.width)<be.width?(ee={left:oe[Re-1].end.left,top:Te.top},de=Math.max(de,Oe.height)):(Pe=Oe.width,Te={left:(ee={left:Te.left,top:Te.top+de}).left,top:ee.top},de=Oe.height);break;case f.ScrollMode.Horizontal:case f.ScrollMode.Vertical:default:ee=oe[Re-1].end}var Qe={left:ee.left+Oe.width,top:ee.top+Oe.height};oe[Re]={index:Re,start:ee,size:Oe,end:Qe,visibility:-1}}return oe}(W,me,ut,rt)},[rt,ut,V,me]),Ve=ze[W-1]?{height:ze[W-1].end.top,width:ze[W-1].end.left}:As;Se.current.measurements=ze;var qe=e.useMemo(function(){var Ae=function(Re,Oe,Qe,Ye){var pn=0;switch(Re){case At.Horizontal:pn=Ye.left;break;case At.Vertical:default:pn=Ye.top}var yn=Oe.length-1,Fe=function($e,kn,Nn,ii){for(;$e<=kn;){var Ln=($e+kn)/2|0,Gn=ii(Ln);if(Gn<Nn)$e=Ln+1;else{if(!(Gn>Nn))return Ln;kn=Ln-1}}return $e>0?$e-1:0}(0,yn,pn,function($e){switch(Re){case At.Horizontal:return Oe[$e].start.left;case At.Both:case At.Vertical:default:return Oe[$e].start.top}});if(Re===At.Both)for(var gn=Oe[Fe].start.top;Fe-1>=0&&Oe[Fe-1].start.top===gn&&Oe[Fe-1].start.left>=Ye.left;)Fe--;for(var tn=Fe;tn<=yn;){var nn={top:Oe[tn].start.top-Ye.top,left:Oe[tn].start.left-Ye.left},un={height:Qe.height-nn.top,width:Qe.width-nn.left};if(Re===At.Horizontal&&un.width<0||Re===At.Vertical&&un.height<0||Re===At.Both&&(un.width<0||un.height<0))break;tn++}return{start:Fe,end:tn}}(Vt,ze,me,Le),be=Ae.start,ae=Ae.end,ue=ye.slice(Vn(0,W,be),Vn(0,W,ae)),oe=be+ue.reduce(function(Re,Oe,Qe,Ye){return Oe>Ye[Re]?Qe:Re},0),Pe=oe=Vn(0,W-1,oe),Te=J({endPage:ae,numPages:W,startPage:be}),de=Te.startPage,ee=Te.endPage;switch(de=Math.max(de,0),ee=Math.min(ee,W-1),V){case f.ViewMode.DualPageWithCover:oe>0&&(Pe=oe%2==1?oe:oe-1),de=de===0?0:de%2==1?de:de-1,W-(ee=ee%2==1?ee-1:ee)<=2&&(ee=W-1);break;case f.ViewMode.DualPage:Pe=oe%2==0?oe:oe-1,de=de%2==0?de:de-1,ee=ee%2==1?ee:ee-1;break;case f.ViewMode.SinglePage:default:Pe=oe}return{startPage:de,endPage:ee,maxVisbilityIndex:Pe}},[ze,me,Le,V,ye]),cn=qe.startPage,en=qe.endPage,hn=qe.maxVisbilityIndex,rn=e.useMemo(function(){for(var Ae=[],be=function(ue){var oe=ze[ue],Pe=g(g({},oe),{visibility:ye[ue]!==void 0?ye[ue]:-1,measureRef:function(Te){Te&&(Te.setAttribute(vr,"".concat(ue)),Ue.observe(Te))}});Ae.push(Pe)},ae=cn;ae<=en;ae++)be(ae);return Ae},[cn,en,ye,ze]),an=e.useCallback(function(Ae,be){var ae=Se.current.measurements[Vn(0,W-1,Ae)],ue=Pt.current===f.ScrollMode.Page?mr:be;return ae?pe({left:ue.left+ae.start.left,top:ue.top+ae.start.top},p):Promise.resolve()},[pe,p]),on=e.useCallback(function(Ae,be){var ae=Se.current.measurements,ue=ae[Ae].start,oe=ae.find(function(Te){return Te.start.top-ue.top>1e-12});if(!oe)return Promise.resolve();var Pe=oe.index;switch(Xt.current){case f.ViewMode.DualPage:Pe=Pe%2==0?Pe:Pe+1;break;case f.ViewMode.DualPageWithCover:Pe=Pe%2==1?Pe:Pe+1}return an(Pe,be)},[]),sn=e.useCallback(function(Ae,be){for(var ae=Se.current.measurements,ue=ae[Ae].start,oe=Ae,Pe=!1,Te=W-1;Te>=0;Te--)if(ue.top-ae[Te].start.top>1e-12){Pe=!0,oe=ae[Te].index;break}if(!Pe)return Promise.resolve();switch(Xt.current){case f.ViewMode.DualPage:oe=oe%2==0?oe:oe-1;break;case f.ViewMode.DualPageWithCover:oe=oe%2==0?oe-1:oe}return oe===Ae&&(oe=Ae-1),an(oe,be)},[]),mn=e.useCallback(function(Ae,be){if(Xt.current===f.ViewMode.DualPageWithCover||Xt.current===f.ViewMode.DualPage)return on(Ae,be);switch(Pt.current){case f.ScrollMode.Wrapped:return on(Ae,be);case f.ScrollMode.Horizontal:case f.ScrollMode.Vertical:default:return an(Ae+1,be)}},[]),Cn=e.useCallback(function(Ae,be){if(Xt.current===f.ViewMode.DualPageWithCover||Xt.current===f.ViewMode.DualPage)return sn(Ae,be);switch(Pt.current){case f.ScrollMode.Wrapped:return sn(Ae,be);case f.ScrollMode.Horizontal:case f.ScrollMode.Vertical:default:return an(Ae-1,be)}},[]),En=e.useCallback(function(){return function(Ae,be){switch(be){case f.ScrollMode.Horizontal:return{position:"relative",height:"100%",width:"".concat(Ae.width,"px")};case f.ScrollMode.Vertical:default:return{position:"relative",height:"".concat(Ae.height,"px"),width:"100%"}}}(Ve,Pt.current)},[Ve]),fn=e.useCallback(function(Ae){return function(be,ae,ue){return ue!==f.ScrollMode.Page?{}:{height:"".concat(ae.height,"px"),width:"100%",position:"absolute",top:0,transform:"translateY(".concat(be.start.top,"px)")}}(Ae,me,Pt.current)},[me]),bn=e.useCallback(function(Ae){return function(be,ae,ue,oe,Pe){var Te,de,ee,Re,Oe,Qe,Ye,pn=ae?"right":"left",yn=ae?-1:1,Fe=ue.length,gn=be.start.left*yn,tn=be.size,nn=tn.height,un=tn.width;if(oe===f.ViewMode.DualPageWithCover){var $e=Pe===f.ScrollMode.Page?0:be.start.top;return be.index===0||Fe%2==0&&be.index===Fe-1?((Te={height:"".concat(nn,"px"),minWidth:"".concat(vs(ue,oe),"px"),width:"100%"})[pn]=0,Te.position="absolute",Te.top=0,Te.transform="translate(".concat(gn,"px, ").concat($e,"px)"),Te):((de={height:"".concat(nn,"px"),width:"".concat(un,"px")})[pn]=0,de.position="absolute",de.top=0,de.transform="translate(".concat(gn,"px, ").concat($e,"px)"),de)}if(oe===f.ViewMode.DualPage)return(ee={height:"".concat(nn,"px"),width:"".concat(un,"px")})[pn]=0,ee.position="absolute",ee.top=0,ee.transform="translate(".concat(gn,"px, ").concat(Pe===f.ScrollMode.Page?0:be.start.top,"px)"),ee;switch(Pe){case f.ScrollMode.Horizontal:return(Re={height:"100%",width:"".concat(un,"px")})[pn]=0,Re.position="absolute",Re.top=0,Re.transform="translateX(".concat(gn,"px)"),Re;case f.ScrollMode.Page:return(Oe={height:"".concat(nn,"px"),width:"".concat(un,"px")})[pn]=0,Oe.position="absolute",Oe.top=0,Oe;case f.ScrollMode.Wrapped:return(Qe={height:"".concat(nn,"px"),width:"".concat(un,"px")})[pn]=0,Qe.position="absolute",Qe.top=0,Qe.transform="translate(".concat(gn,"px, ").concat(be.start.top,"px)"),Qe;case f.ScrollMode.Vertical:default:return(Ye={height:"".concat(nn,"px"),width:"100%"})[pn]=0,Ye.position="absolute",Ye.top=0,Ye.transform="translateY(".concat(be.start.top,"px)"),Ye}}(Ae,O,ut,Xt.current,Pt.current)},[O,ut]),Pn=e.useCallback(function(Ae,be){var ae=Se.current,ue=ae.measurements,oe=ae.scrollOffset,Pe=ue[Vn(0,W-1,be)];if(Pe){var Te=Pt.current===f.ScrollMode.Page?{left:Pe.start.left,top:Pe.start.top}:{left:oe.left*Ae,top:oe.top*Ae};return pe(Te,!1)}return Promise.resolve()},[]);return e.useEffect(function(){return function(){Ue.disconnect()}},[]),{boundingClientRect:me,isSmoothScrolling:ot,startPage:cn,endPage:en,maxVisbilityIndex:hn,virtualItems:rn,getContainerStyles:En,getItemContainerStyles:fn,getItemStyles:bn,scrollToItem:an,scrollToNextItem:mn,scrollToPreviousItem:Cn,zoom:Pn}},Si=function(r,p,O,W,X,J){var ut=O;switch(!0){case(X===f.ViewMode.DualPageWithCover&&J>=3):case(X===f.ViewMode.DualPage&&J>=3):ut=2*O;break;default:ut=O}switch(W){case f.SpecialZoomLevel.ActualSize:return 1;case f.SpecialZoomLevel.PageFit:return Math.min((r.clientWidth-17)/ut,(r.clientHeight-16)/p);case f.SpecialZoomLevel.PageWidth:return(r.clientWidth-17)/ut}},ws=function(r){var p,O,W=r.getCurrentPage,X=(p=50,O=e.useRef([]),e.useEffect(function(){return function(){O.current=[]}},[]),{push:function(lt){var ot=O.current;ot.length+1>p&&ot.shift(),ot.push(lt),O.current=ot},map:function(lt){return O.current.map(function(ot){return lt(ot)})},pop:function(){var lt=O.current;if(lt.length===0)return null;var ot=lt.pop();return O.current=lt,ot}}),J=function(lt){var ot=e.useRef([]);return e.useEffect(function(){return function(){ot.current=[]}},[]),{dequeue:function(){var xt=ot.current;if(xt.length===0)return null;var Rt=xt.shift();return ot.current=xt,Rt||null},enqueue:function(xt){var Rt=ot.current;Rt.length+1>lt&&Rt.pop(),ot.current=[xt].concat(Rt)},map:function(xt){return ot.current.map(function(Rt){return xt(Rt)})}}}(50),ut=function(){var lt=J.dequeue();return lt&&X.push(lt),lt&&lt.pageIndex===W()?ut():lt},rt=function(){var lt=X.pop();return lt&&J.enqueue(lt),lt&&lt.pageIndex===W()?rt():lt},V=e.useCallback(function(lt){X.push(lt)},[]);return{getNextDestination:ut,getPreviousDestination:rt,markVisitedDestination:V}},br=function(r){var p=[];return r.map(function(O){p=p.concat(O).concat(function(W){var X=[];return W.items&&W.items.length>0&&(X=X.concat(br(W.items))),X}(O))}),p},_r={capture:!1,passive:!0},Cs={height:0,width:0},Ei={height:0,width:0},Ps=function(r){var p=r.getCurrentPage,O=r.getCurrentScrollMode,W=r.jumpToPage,X=r.targetRef,J=e.useState(f.FullScreenMode.Normal),ut=J[0],rt=J[1],V=function(){var ye=e.useState(Cs),We=ye[0],Ue=ye[1],ze=R(function(){Ue({height:window.innerHeight,width:window.innerWidth})},100);return y(function(){return window.addEventListener("resize",ze,_r),function(){window.removeEventListener("resize",ze,_r)}},[]),We}(),lt=e.useState(Ei),ot=lt[0],xt=lt[1],Rt=e.useRef(Ei),Pt=e.useRef(p()),Xt=e.useRef(Ei),Vt=e.useState(X.current),Ut=Vt[0],Le=Vt[1],pe=e.useRef();y(function(){X.current!==Ut&&Le(X.current)},[]),y(function(){if(Ut){var ye=new ResizeObserver(function(We){We.forEach(function(Ue){var ze=Ue.target.getBoundingClientRect(),Ve=ze.height,qe=ze.width;xt({height:Ve,width:qe})})});return ye.observe(Ut),function(){ye.unobserve(Ut),ye.disconnect()}}},[Ut]);var me=e.useCallback(function(ye){var We=Z();return We&&We!==ye?(rt(f.FullScreenMode.Normal),q(We)):Promise.resolve()},[]),Se=e.useCallback(function(ye){ye&&A()&&(Le(ye),me(ye).then(function(){pe.current=ye,rt(f.FullScreenMode.Entering),function(We){l&&We[F.RequestFullScreen]()}(ye)}))},[]),se=e.useCallback(function(){Z()&&(rt(f.FullScreenMode.Exitting),q(document))},[]),Ce=e.useCallback(function(){Ut&&Z()!==Ut&&rt(f.FullScreenMode.Exitting)},[Ut]);return e.useEffect(function(){switch(ut){case f.FullScreenMode.Entering:pe.current&&(pe.current.style.backgroundColor="var(--rpv-core__full-screen-target-background-color)"),Pt.current=p(),Rt.current={height:window.innerHeight,width:window.innerWidth};break;case f.FullScreenMode.Entered:O()===f.ScrollMode.Page?W(Pt.current).then(function(){rt(f.FullScreenMode.EnteredCompletely)}):rt(f.FullScreenMode.EnteredCompletely);break;case f.FullScreenMode.Exitting:pe.current&&(pe.current.style.backgroundColor="",pe.current=null),Pt.current=p();break;case f.FullScreenMode.Exited:rt(f.FullScreenMode.Normal),O()===f.ScrollMode.Page&&W(Pt.current)}},[ut]),e.useEffect(function(){if(ut!==f.FullScreenMode.Normal)return ut===f.FullScreenMode.Entering&&V.height===ot.height&&V.width===ot.width&&V.height>0&&V.width>0&&(Xt.current.height===0||V.height==Xt.current.height)?(Xt.current={height:window.innerHeight,width:window.innerWidth},void rt(f.FullScreenMode.Entered)):void(ut===f.FullScreenMode.Exitting&&Rt.current.height===V.height&&Rt.current.width===V.width&&V.height>0&&V.width>0&&rt(f.FullScreenMode.Exited))},[ut,V,ot]),e.useEffect(function(){var ye;return ye=Ce,l&&document.addEventListener(F.FullScreenChange,ye),function(){(function(We){l&&document.removeEventListener(F.FullScreenChange,We)})(Ce)}},[Ut]),{enterFullScreenMode:Se,exitFullScreenMode:se,fullScreenMode:ut}},Ts={buildPageStyles:function(){return{}},transformSize:function(r){return r.size}},On={left:0,top:0},ks=function(r){var p=r.currentFile,O=r.defaultScale,W=r.doc,X=r.enableSmoothScroll,J=r.initialPage,ut=r.initialRotation,rt=r.initialScale,V=r.pageLayout,lt=r.pageSizes,ot=r.plugins,xt=r.renderPage,Rt=r.scrollMode,Pt=r.setRenderRange,Xt=r.viewMode,Vt=r.viewerState,Ut=r.onDocumentLoad,Le=r.onOpenFile,pe=r.onPageChange,me=r.onRotate,Se=r.onRotatePage,se=r.onZoom,Ce=W.numPages,ye=W.loadingTask.docId,We=e.useContext(Bt).l10n,Ue=e.useContext(H),ze=Ue.direction===f.TextDirection.RightToLeft,Ve=e.useRef(),qe=e.useRef(),cn=e.useState(J),en=cn[0],hn=cn[1],rn=e.useRef(null),an=ws({getCurrentPage:function(){return Fe.current.pageIndex}}),on=e.useState(ut),sn=on[0],mn=on[1],Cn=pt(sn),En=e.useState(!1),fn=En[0],bn=En[1],Pn=e.useState(new Map),Ae=Pn[0],be=Pn[1],ae=e.useState(Rt),ue=ae[0],oe=ae[1],Pe=pt(ue),Te=e.useState(Xt),de=Te[0],ee=Te[1],Re=pt(de),Oe=function(Nt){var Me=nt(),Ne=e.useState([]),Xe=Ne[0],Ge=Ne[1];return e.useEffect(function(){Nt.getOutline().then(function(Be){if(Me.current&&Be!==null){var dn=br(Be);Ge(dn)}})},[]),Xe}(W),Qe=e.useState(rt),Ye=Qe[0],pn=Qe[1],yn=pt(Ye),Fe=e.useRef(Vt),gn=e.useRef(typeof O=="string"?O:null),tn=e.useRef(-1),nn=e.useRef(-1),un=e.useRef(J),$e=Ps({getCurrentPage:function(){return Fe.current.pageIndex},getCurrentScrollMode:function(){return Fe.current.scrollMode},jumpToPage:function(Nt){return Mn(Nt)},targetRef:qe}),kn=e.useState(-1),Nn=kn[0],ii=kn[1],Ln=e.useState(0),Gn=Ln[0],Ds=Ln[1],An=yt({doc:W});e.useEffect(function(){return function(){Ht.clear(),le.clear()}},[ye]);var Er=e.useMemo(function(){return Object.assign({},Ts,V)},[]),Is=e.useMemo(function(){return Array(Ce).fill(0).map(function(Nt,Me){var Ne=[lt[Me].pageHeight,lt[Me].pageWidth],Xe=Math.abs(sn)%180==0?{height:Ne[0],width:Ne[1]}:{height:Ne[1],width:Ne[0]},Ge={height:Xe.height*Ye,width:Xe.width*Ye};return Er.transformSize({numPages:Ce,pageIndex:Me,size:Ge})})},[sn,Ye]),Ke=Es({enableSmoothScroll:X,isRtl:ze,numberOfItems:Ce,parentRef:qe,scrollMode:ue,setRenderRange:Pt,sizes:Is,viewMode:de}),Os=R(function(){!gn.current||Fe.current.fullScreenMode!==f.FullScreenMode.Normal||J>0&&un.current===J||Dn(gn.current)},200);(function(Nt){var Me=Nt.targetRef,Ne=Nt.onResize;y(function(){var Xe=new ResizeObserver(function(Be){Be.forEach(function(dn){Ne(dn.target)})}),Ge=Me.current;if(Ge)return Xe.observe(Ge),function(){Xe.unobserve(Ge)}},[])})({targetRef:qe,onResize:Os});var Rn=function(Nt){var Me=Nt;ot.forEach(function(Ne){Ne.onViewerStateChange&&(Me=Ne.onViewerStateChange(Me))}),Fe.current=Me},Ns=function(){return qe.current},Bs=function(){return Fe.current},Hs=e.useCallback(function(Nt){an.markVisitedDestination(Nt)},[]),Pi=e.useCallback(function(Nt){var Me=Nt.pageIndex,Ne=Nt.bottomOffset,Xe=Nt.leftOffset,Ge=Nt.scaleTo,Be=qe.current,dn=Fe.current;return Be&&dn?new Promise(function(xn,Fi){ge(W,Me).then(function(ri){var Fn=ri.getViewport({scale:1}),si=0,xr=(typeof Ne=="function"?Ne(Fn.width,Fn.height):Ne)||0,ai=(typeof Xe=="function"?Xe(Fn.width,Fn.height):Xe)||0,Bn=dn.scale;switch(Ge){case f.SpecialZoomLevel.PageFit:si=0,ai=0,Dn(f.SpecialZoomLevel.PageFit);break;case f.SpecialZoomLevel.PageWidth:Bn=Si(Be,lt[Me].pageHeight,lt[Me].pageWidth,f.SpecialZoomLevel.PageWidth,Xt,Ce),si=(Fn.height-xr)*Bn,ai*=Bn,Dn(Bn);break;default:si=(Fn.height-xr)*Bn,ai*=Bn}switch(dn.scrollMode){case f.ScrollMode.Horizontal:Ke.scrollToItem(Me,{left:ai,top:0}).then(function(){xn()});break;case f.ScrollMode.Vertical:default:Ke.scrollToItem(Me,{left:0,top:si}).then(function(){xn()})}})}):Promise.resolve()},[]),wr=e.useCallback(function(Nt){return an.markVisitedDestination(Nt),Pi(Nt)},[]),Us=e.useCallback(function(){var Nt=an.getNextDestination();return Nt?Pi(Nt):Promise.resolve()},[]),js=e.useCallback(function(){var Nt=an.getPreviousDestination();return Nt?Pi(Nt):Promise.resolve()},[]),Ws=e.useCallback(function(){return Ke.scrollToNextItem(Fe.current.pageIndex,On)},[]),Mn=e.useCallback(function(Nt){return 0<=Nt&&Nt<Ce?Ke.scrollToItem(Nt,On):Promise.resolve()},[]),zs=e.useCallback(function(){return Ke.scrollToPreviousItem(Fe.current.pageIndex,On)},[]),Cr=e.useCallback(function(Nt){var Me,Ne;(Me=Nt.name,Ne=Me.split(/\./).pop(),Ne?Ne.toLowerCase():"").toLowerCase()==="pdf"&&new Promise(function(Xe){var Ge=new FileReader;Ge.readAsArrayBuffer(Nt),Ge.onload=function(){var Be=new Uint8Array(Ge.result);Xe(Be)}}).then(function(Xe){Le(Nt.name,Xe)})},[Le]),Pr=e.useCallback(function(Nt){var Me=Nt===f.RotateDirection.Backward?-90:90,Ne=Fe.current.rotation,Xe=Ne===360||Ne===-360?Me:Ne+Me;An.markNotRendered(),mn(Xe),Rn(g(g({},Fe.current),{rotation:Xe})),me({direction:Nt,doc:W,rotation:Xe})},[]),Ti=e.useCallback(function(Nt,Me){var Ne=Me===f.RotateDirection.Backward?-90:90,Xe=Fe.current.pagesRotation,Ge=(Xe.has(Nt)?Xe.get(Nt):ut)+Ne,Be=Xe.set(Nt,Ge);be(Be),bn(function(dn){return!dn}),Rn(g(g({},Fe.current),{pagesRotation:Be,rotatedPage:Nt})),Se({direction:Me,doc:W,pageIndex:Nt,rotation:Ge}),An.markRendering(Nt),ii(Nt)},[]),Tr=e.useCallback(function(Nt){Rn(g(g({},Fe.current),{scrollMode:Nt})),oe(Nt)},[]),kr=e.useCallback(function(Nt){Rn(g(g({},Fe.current),{viewMode:Nt})),ee(Nt)},[]),Dn=e.useCallback(function(Nt){var Me=qe.current,Ne=Fe.current.pageIndex;if(!(Ne<0||Ne>=Ce)){var Xe=lt[Ne].pageHeight,Ge=lt[Ne].pageWidth,Be=Me?typeof Nt=="string"?Si(Me,Xe,Ge,Nt,Fe.current.viewMode,Ce):Nt:1;gn.current=typeof Nt=="string"?Nt:null,Be!==Fe.current.scale&&(Ds(function(dn){return dn+1}),An.markNotRendered(),pn(Be),se({doc:W,scale:Be}),Rn(g(g({},Fe.current),{scale:Be})))}},[]),Vs=e.useCallback(function(Nt){$e.enterFullScreenMode(Nt)},[]),Gs=e.useCallback(function(){$e.exitFullScreenMode()},[]);e.useEffect(function(){Rn(g(g({},Fe.current),{fullScreenMode:$e.fullScreenMode}))},[$e.fullScreenMode]),e.useEffect(function(){var Nt={enterFullScreenMode:Vs,exitFullScreenMode:Gs,getPagesContainer:Ns,getViewerState:Bs,jumpToDestination:wr,jumpToNextDestination:Us,jumpToPreviousDestination:js,jumpToNextPage:Ws,jumpToPreviousPage:zs,jumpToPage:Mn,openFile:Cr,rotate:Pr,rotatePage:Ti,setViewerState:Rn,switchScrollMode:Tr,switchViewMode:kr,zoom:Dn};return ot.forEach(function(Me){Me.install&&Me.install(Nt)}),function(){ot.forEach(function(Me){Me.uninstall&&Me.uninstall(Nt)})}},[ye]),e.useEffect(function(){Ut({doc:W,file:p}),ot.forEach(function(Nt){Nt.onDocumentLoad&&Nt.onDocumentLoad({doc:W,file:p})})},[ye]);var ki,Ri,Mi,Rr=Ke.boundingClientRect;ki=function(){J&&Mn(J)},Ri=Rr.height>0&&Rr.width>0,Mi=e.useRef(!1),y(function(){Ri&&!Mi.current&&(Mi.current=!0,ki())},[ki,Ri]),y(function(){var Nt=Fe.current.pageIndex;Nt>-1&&Pe!==ue&&Ke.scrollToItem(Nt,On).then(function(){$e.fullScreenMode===f.FullScreenMode.EnteredCompletely&&(X||An.markNotRendered(),tn.current=-1)})},[ue]),y(function(){var Nt=Fe.current.pageIndex;Nt>-1&&Cn!==sn&&Ke.scrollToItem(Nt,On)},[sn]),y(function(){yn!=0&&yn!=Fe.current.scale&&Ke.zoom(Fe.current.scale/yn,Fe.current.pageIndex).then(function(){$e.fullScreenMode===f.FullScreenMode.EnteredCompletely&&(nn.current=-1)})},[Ye]),y(function(){if(Re!==Fe.current.viewMode){var Nt=Ke.startPage,Me=Ke.endPage,Ne=Ke.virtualItems;An.markNotRendered(),An.setRange(Nt,Me);for(var Xe=function(Be){var dn=Ne.find(function(xn){return xn.index===Be});dn&&An.setVisibility(Be,dn.visibility)},Ge=Nt;Ge<=Me;Ge++)Xe(Ge);xi()}},[de]),y(function(){var Nt=Fe.current.pageIndex;Nt>-1&&Re!==de&&Ke.scrollToItem(Nt,On)},[de]),y(function(){var Nt=Fe.current.pageIndex;Nt>0&&Nt===J&&un.current===J&&gn.current&&(un.current=-1,Dn(gn.current))},[en]),e.useEffect(function(){Ke.isSmoothScrolling||rn.current!==null&&rn.current===en||(rn.current=en,pe({currentPage:en,doc:W}))},[en,Ke.isSmoothScrolling]),e.useEffect(function(){$e.fullScreenMode===f.FullScreenMode.Entering&&Fe.current.scrollMode===f.ScrollMode.Page&&(tn.current=Fe.current.pageIndex),$e.fullScreenMode===f.FullScreenMode.EnteredCompletely&&Fe.current.scrollMode===f.ScrollMode.Page&&X&&(tn.current=-1),$e.fullScreenMode===f.FullScreenMode.EnteredCompletely&&gn.current&&(nn.current=Fe.current.pageIndex,Dn(gn.current))},[$e.fullScreenMode]),e.useEffect(function(){if($e.fullScreenMode!==f.FullScreenMode.Entering&&$e.fullScreenMode!==f.FullScreenMode.Exitting&&!Ke.isSmoothScrolling){var Nt=Ke.startPage,Me=Ke.endPage,Ne=Ke.maxVisbilityIndex,Xe=Ke.virtualItems,Ge=Ne,Be=$e.fullScreenMode===f.FullScreenMode.Entered||$e.fullScreenMode===f.FullScreenMode.EnteredCompletely;if(!(Be&&Ge!==tn.current&&tn.current>-1||Be&&Ge!==nn.current&&nn.current>-1)){hn(Ge),Rn(g(g({},Fe.current),{pageIndex:Ge})),An.setRange(Nt,Me);for(var dn=function(Fi){var ri=Xe.find(function(Fn){return Fn.index===Fi});ri&&An.setVisibility(Fi,ri.visibility)},xn=Nt;xn<=Me;xn++)dn(xn);xi()}}},[Ke.startPage,Ke.endPage,Ke.isSmoothScrolling,Ke.maxVisbilityIndex,$e.fullScreenMode,fn,sn,Ye]);var qs=e.useCallback(function(Nt){An.markRendered(Nt),xi()},[Gn]),xi=function(){var Nt=An.getHighestPriorityPage();Nt>-1&&An.isInRange(Nt)&&(An.markRendering(Nt),ii(Nt))},Xs=function(Nt){var Me=en-1,Ne=en+1;switch(Nt){case"FirstPage":Mn(0);break;case"LastPage":Mn(Ce-1);break;case"NextPage":Ne<Ce&&Mn(Ne);break;case"PrevPage":Me>=0&&Mn(Me)}},$s=e.useCallback(function(){var Nt=Ke.virtualItems,Me=[];switch(de){case f.ViewMode.DualPage:Me=St(Nt,2);break;case f.ViewMode.DualPageWithCover:Nt.length&&(Me=Nt[0].index===0?[[Nt[0]]].concat(St(Nt.slice(1),2)):St(Nt,2));break;case f.ViewMode.SinglePage:default:Me=St(Nt,1)}var Ne=We&&We.core?We.core.pageLabel:"Page {{pageIndex}}",Xe={attrs:{className:"rpv-core__inner-container","data-testid":"core__inner-container",ref:Ve,style:{height:"100%"}},children:e.createElement(e.Fragment,null),subSlot:{attrs:{"data-testid":"core__inner-pages",className:k({"rpv-core__inner-pages":!0,"rpv-core__inner-pages--horizontal":ue===f.ScrollMode.Horizontal,"rpv-core__inner-pages--rtl":ze,"rpv-core__inner-pages--single":ue===f.ScrollMode.Page,"rpv-core__inner-pages--vertical":ue===f.ScrollMode.Vertical,"rpv-core__inner-pages--wrapped":ue===f.ScrollMode.Wrapped}),ref:qe,style:{height:"100%",position:"relative"}},children:e.createElement("div",{"data-testid":"core__inner-current-page-".concat(en),style:Object.assign({"--scale-factor":Ye},Ke.getContainerStyles())},Me.map(function(Ge){return e.createElement("div",{className:k({"rpv-core__inner-page-container":!0,"rpv-core__inner-page-container--single":ue===f.ScrollMode.Page}),style:Ke.getItemContainerStyles(Ge[0]),key:"".concat(Ge[0].index,"-").concat(de)},Ge.map(function(Be){var dn=de===f.ViewMode.DualPageWithCover&&(Be.index===0||Ce%2==0&&Be.index===Ce-1);return e.createElement("div",{"aria-label":Ne.replace("{{pageIndex}}","".concat(Be.index+1)),className:k({"rpv-core__inner-page":!0,"rpv-core__inner-page--dual-even":de===f.ViewMode.DualPage&&Be.index%2==0,"rpv-core__inner-page--dual-odd":de===f.ViewMode.DualPage&&Be.index%2==1,"rpv-core__inner-page--dual-cover":dn,"rpv-core__inner-page--dual-cover-even":de===f.ViewMode.DualPageWithCover&&!dn&&Be.index%2==0,"rpv-core__inner-page--dual-cover-odd":de===f.ViewMode.DualPageWithCover&&!dn&&Be.index%2==1,"rpv-core__inner-page--single":de===f.ViewMode.SinglePage&&ue===f.ScrollMode.Page}),role:"region",key:"".concat(Be.index,"-").concat(de),style:Object.assign({},Ke.getItemStyles(Be),Er.buildPageStyles({numPages:Ce,pageIndex:Be.index,scrollMode:ue,viewMode:de}))},e.createElement(fs,{doc:W,measureRef:Be.measureRef,outlines:Oe,pageIndex:Be.index,pageRotation:Ae.has(Be.index)?Ae.get(Be.index):0,pageSize:lt[Be.index],plugins:ot,renderPage:xt,renderQueueKey:Gn,rotation:sn,scale:Ye,shouldRender:Nn===Be.index,viewMode:de,onExecuteNamedAction:Xs,onJumpFromLinkAnnotation:Hs,onJumpToDest:wr,onRenderCompleted:qs,onRotatePage:Ti}))}))}))}};return ot.forEach(function(Ge){Ge.renderViewer&&(Xe=Ge.renderViewer({containerRef:Ve,doc:W,pagesContainerRef:qe,pagesRotation:Ae,pageSizes:lt,rotation:sn,slot:Xe,themeContext:Ue,jumpToPage:Mn,openFile:Cr,rotate:Pr,rotatePage:Ti,switchScrollMode:Tr,switchViewMode:kr,zoom:Dn}))}),Xe},[ot,Ke]),Mr=e.useCallback(function(Nt){return e.createElement("div",g({},Nt.attrs,{style:Nt.attrs&&Nt.attrs.style?Nt.attrs.style:{}}),Nt.children,Nt.subSlot&&Mr(Nt.subSlot))},[]);return Mr($s())},yr=[.1,.2,.3,.4,.5,.6,.7,.8,.9,1,1.1,1.3,1.5,1.7,1.9,2.1,2.4,2.7,3,3.3,3.7,4.1,4.6,5.1,5.7,6.3,7,7.7,8.5,9.4,10],Rs=function(r){var p=r.defaultScale,O=r.doc,W=r.render,X=r.scrollMode,J=r.viewMode,ut=e.useRef(),rt=e.useState({pageSizes:[],scale:0}),V=rt[0],lt=rt[1];return e.useLayoutEffect(function(){var ot=Array(O.numPages).fill(0).map(function(xt,Rt){return new Promise(function(Pt,Xt){ge(O,Rt).then(function(Vt){var Ut=Vt.getViewport({scale:1});Pt({pageHeight:Ut.height,pageWidth:Ut.width,rotation:Ut.rotation})})})});Promise.all(ot).then(function(xt){var Rt=ut.current;if(Rt&&xt.length!==0){var Pt=xt[0].pageWidth,Xt=xt[0].pageHeight,Vt=Rt.parentElement,Ut=(Vt.clientWidth-45)/Pt,Le=(Vt.clientHeight-45)/Xt,pe=Ut;switch(X){case f.ScrollMode.Horizontal:pe=Math.min(Ut,Le);break;case f.ScrollMode.Vertical:default:pe=Ut}var me,Se,se=p?typeof p=="string"?Si(Vt,Xt,Pt,p,J,O.numPages):p:(me=pe,(Se=yr.findIndex(function(Ce){return Ce>=me}))===-1||Se===0?me:yr[Se-1]);lt({pageSizes:xt,scale:se})}})},[O.loadingTask.docId]),V.pageSizes.length===0||V.scale===0?e.createElement("div",{className:"rpv-core__page-size-calculator","data-testid":"core__page-size-calculating",ref:ut},e.createElement(M,null)):W(V.pageSizes,V.scale)},ni=function(){},wi=function(r){function p(O,W){var X=r.call(this)||this;return X.verifyPassword=O,X.passwordStatus=W,X}return G(p,r),p}(ni),Ms=function(r){var p=r.passwordStatus,O=r.renderProtectedView,W=r.verifyPassword,X=r.onDocumentAskPassword,J=e.useContext(Bt).l10n,ut=e.useState(""),rt=ut[0],V=ut[1],lt=e.useContext(H).direction===f.TextDirection.RightToLeft,ot=function(){return W(rt)};return e.useEffect(function(){X&&X({verifyPassword:W})},[]),O?O({passwordStatus:p,verifyPassword:W}):e.createElement("div",{className:"rpv-core__asking-password-wrapper"},e.createElement("div",{className:k({"rpv-core__asking-password":!0,"rpv-core__asking-password--rtl":lt})},e.createElement("div",{className:"rpv-core__asking-password-message"},p===f.PasswordStatus.RequiredPassword&&J.core.askingPassword.requirePasswordToOpen,p===f.PasswordStatus.WrongPassword&&J.core.wrongPassword.tryAgain),e.createElement("div",{className:"rpv-core__asking-password-body"},e.createElement("div",{className:k({"rpv-core__asking-password-input":!0,"rpv-core__asking-password-input--ltr":!lt,"rpv-core__asking-password-input--rtl":lt})},e.createElement(C,{testId:"core__asking-password-input",type:"password",value:rt,onChange:V,onKeyDown:function(xt){xt.key==="Enter"&&ot()}})),e.createElement(x,{onClick:ot},J.core.askingPassword.submit))))},Ar=function(r){function p(O){var W=r.call(this)||this;return W.doc=O,W}return G(p,r),p}(ni),Sr=function(r){function p(O){var W=r.call(this)||this;return W.error=O,W}return G(p,r),p}(ni),Ci=function(r){function p(O){var W=r.call(this)||this;return W.percentages=O,W}return G(p,r),p}(ni),xs=function(r){var p=r.characterMap,O=r.file,W=r.httpHeaders,X=r.render,J=r.renderError,ut=r.renderLoader,rt=r.renderProtectedView,V=r.transformGetDocumentParams,lt=r.withCredentials,ot=r.onDocumentAskPassword,xt=e.useContext(H).direction===f.TextDirection.RightToLeft,Rt=e.useState(new Ci(0)),Pt=Rt[0],Xt=Rt[1],Vt=e.useRef(""),Ut=nt();return e.useEffect(function(){Vt.current="",Xt(new Ci(0));var Le=new tt.PDFWorker({name:"PDFWorker_".concat(Date.now())}),pe=Object.assign({httpHeaders:W,withCredentials:lt,worker:Le},typeof O=="string"?{url:O}:{data:O},p?{cMapUrl:p.url,cMapPacked:p.isCompressed}:{}),me=V?V(pe):pe,Se=tt.getDocument(me);return Se.onPassword=function(se,Ce){switch(Ce){case tt.PasswordResponses.NEED_PASSWORD:Ut.current&&Xt(new wi(se,f.PasswordStatus.RequiredPassword));break;case tt.PasswordResponses.INCORRECT_PASSWORD:Ut.current&&Xt(new wi(se,f.PasswordStatus.WrongPassword))}},Se.onProgress=function(se){var Ce=se.total>0?Math.min(100,100*se.loaded/se.total):100;Ut.current&&Vt.current===""&&Xt(new Ci(Ce))},Se.promise.then(function(se){Vt.current=se.loadingTask.docId,Ut.current&&Xt(new Ar(se))},function(se){return Ut.current&&!Le.destroyed&&Xt(new Sr({message:se.message||"Cannot load document",name:se.name}))}),function(){Se.destroy(),Le.destroy()}},[O]),Pt instanceof wi?e.createElement(Ms,{passwordStatus:Pt.passwordStatus,renderProtectedView:rt,verifyPassword:Pt.verifyPassword,onDocumentAskPassword:ot}):Pt instanceof Ar?X(Pt.doc):Pt instanceof Sr?J?J(Pt.error):e.createElement("div",{className:k({"rpv-core__doc-error":!0,"rpv-core__doc-error--rtl":xt})},e.createElement("div",{className:"rpv-core__doc-error-text"},Pt.error.message)):e.createElement("div",{"data-testid":"core__doc-loading",className:k({"rpv-core__doc-loading":!0,"rpv-core__doc-loading--rtl":xt})},ut?ut(Pt.percentages):e.createElement(M,null))},Fs=function(r,p){var O=e.useMemo(function(){return r==="auto"?typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":r},[]),W=e.useState(O),X=W[0],J=W[1],ut=pt(X);return e.useEffect(function(){if(r==="auto"){var rt=window.matchMedia("(prefers-color-scheme: dark)"),V=function(lt){J(lt.matches?"dark":"light")};return rt.addEventListener("change",V),function(){return rt.removeEventListener("change",V)}}},[]),e.useEffect(function(){X!==ut&&p&&p(X)},[X]),e.useEffect(function(){r!==X&&J(r)},[r]),{currentTheme:X,setCurrentTheme:J}},Ls=function(r){return{startPage:r.startPage-3,endPage:r.endPage+3}};f.Button=function(r){var p=r.children,O=r.testId,W=r.onClick,X=e.useContext(H).direction===f.TextDirection.RightToLeft,J=O?{"data-testid":O}:{};return e.createElement("button",g({className:k({"rpv-core__button":!0,"rpv-core__button--rtl":X}),type:"button",onClick:W},J),p)},f.Icon=m,f.LazyRender=function(r){var p=r.attrs,O=r.children,W=r.testId,X=e.useState(!1),J=X[0],ut=X[1],rt=W?g(g({},p),{"data-testid":W}):p,V=w({once:!0,onVisibilityChanged:function(lt){lt.isVisible&&ut(!0)}});return e.createElement("div",g({ref:V},rt),J&&O)},f.LocalizationContext=Bt,f.Menu=function(r){var p=r.children,O=e.useRef(),W=e.useRef([]),X=e.useContext(H).direction===f.TextDirection.RightToLeft,J=function(rt){if(O.current)switch(rt.key){case"Tab":rt.preventDefault();break;case"ArrowDown":rt.preventDefault(),ut(function(V,lt){return lt+1});break;case"ArrowUp":rt.preventDefault(),ut(function(V,lt){return lt-1});break;case"End":rt.preventDefault(),ut(function(V,lt){return V.length-1});break;case"Home":rt.preventDefault(),ut(function(V,lt){return 0})}},ut=function(rt){if(O.current){var V=W.current,lt=V.findIndex(function(xt){return xt.getAttribute("tabindex")==="0"}),ot=Math.min(V.length-1,Math.max(0,rt(V,lt)));lt>=0&&lt<=V.length-1&&V[lt].setAttribute("tabindex","-1"),V[ot].setAttribute("tabindex","0"),V[ot].focus()}};return y(function(){var rt=O.current;if(rt){var V=function(lt){var ot=[];return lt.querySelectorAll('.rpv-core__menu-item[role="menuitem"]').forEach(function(xt){if(xt instanceof HTMLElement){var Rt=xt.parentElement;(Rt===lt||window.getComputedStyle(Rt).display!=="none")&&ot.push(xt)}}),ot}(rt);W.current=V}},[]),y(function(){return document.addEventListener("keydown",J),function(){document.removeEventListener("keydown",J)}},[]),e.createElement("div",{ref:O,"aria-orientation":"vertical",className:k({"rpv-core__menu":!0,"rpv-core__menu--rtl":X}),role:"menu",tabIndex:0},p)},f.MenuDivider=function(){return e.createElement("div",{"aria-orientation":"horizontal",className:"rpv-core__menu-divider",role:"separator"})},f.MenuItem=function(r){var p=r.checked,O=p!==void 0&&p,W=r.children,X=r.icon,J=X===void 0?null:X,ut=r.isDisabled,rt=ut!==void 0&&ut,V=r.testId,lt=r.onClick,ot=e.useContext(H).direction===f.TextDirection.RightToLeft,xt=V?{"data-testid":V}:{};return e.createElement("button",g({className:k({"rpv-core__menu-item":!0,"rpv-core__menu-item--disabled":rt,"rpv-core__menu-item--ltr":!ot,"rpv-core__menu-item--rtl":ot}),role:"menuitem",tabIndex:-1,type:"button",onClick:lt},xt),e.createElement("div",{className:k({"rpv-core__menu-item-icon":!0,"rpv-core__menu-item-icon--ltr":!ot,"rpv-core__menu-item-icon--rtl":ot})},J),e.createElement("div",{className:k({"rpv-core__menu-item-label":!0,"rpv-core__menu-item-label--ltr":!ot,"rpv-core__menu-item-label--rtl":ot})},W),e.createElement("div",{className:k({"rpv-core__menu-item-check":!0,"rpv-core__menu-item-check--ltr":!ot,"rpv-core__menu-item-check--rtl":ot})},O&&e.createElement(E,null)))},f.MinimalButton=function(r){var p=r.ariaLabel,O=p===void 0?"":p,W=r.ariaKeyShortcuts,X=W===void 0?"":W,J=r.children,ut=r.isDisabled,rt=ut!==void 0&&ut,V=r.isSelected,lt=V!==void 0&&V,ot=r.testId,xt=r.onClick,Rt=e.useContext(H).direction===f.TextDirection.RightToLeft,Pt=ot?{"data-testid":ot}:{};return e.createElement("button",g({"aria-label":O},X&&{"aria-keyshortcuts":X},rt&&{"aria-disabled":!0},{className:k({"rpv-core__minimal-button":!0,"rpv-core__minimal-button--disabled":rt,"rpv-core__minimal-button--rtl":Rt,"rpv-core__minimal-button--selected":lt}),type:"button",onClick:xt},Pt),J)},f.Modal=function(r){var p=r.ariaControlsSuffix,O=r.closeOnClickOutside,W=r.closeOnEscape,X=r.content,J=r.isOpened,ut=J!==void 0&&J,rt=r.target,V=p||"".concat(T());return e.createElement(I,{target:rt?function(lt,ot){return e.createElement("div",{"aria-expanded":ot?"true":"false","aria-haspopup":"dialog","aria-controls":"rpv-core__modal-body-".concat(V)},rt(lt,ot))}:null,content:function(lt){return e.createElement(c,null,e.createElement(s,{ariaControlsSuffix:V,closeOnClickOutside:O,closeOnEscape:W,onToggle:lt},X(lt)))},isOpened:ut})},f.Popover=function(r){var p=r.ariaHasPopup,O=p===void 0?"dialog":p,W=r.ariaControlsSuffix,X=r.closeOnClickOutside,J=r.closeOnEscape,ut=r.content,rt=r.lockScroll,V=rt===void 0||rt,lt=r.offset,ot=r.position,xt=r.target,Rt=S(!1),Pt=Rt.opened,Xt=Rt.toggle,Vt=e.useRef(),Ut=e.useMemo(function(){return W||"".concat(T())},[]);return e.createElement("div",{ref:Vt,"aria-expanded":Pt?"true":"false","aria-haspopup":O,"aria-controls":"rpv-core__popver-body-".concat(Ut)},xt(Xt,Pt),Pt&&e.createElement(e.Fragment,null,V&&e.createElement(Ct,{closeOnEscape:J,onClose:Xt}),e.createElement(Jt,{ariaControlsSuffix:Ut,closeOnClickOutside:X,offset:lt,position:ot,targetRef:Vt,onClose:Xt},ut(Xt))))},f.PrimaryButton=x,f.ProgressBar=function(r){var p=r.progress,O=e.useContext(H).direction===f.TextDirection.RightToLeft;return e.createElement("div",{className:k({"rpv-core__progress-bar":!0,"rpv-core__progress-bar--rtl":O})},e.createElement("div",{className:"rpv-core__progress-bar-progress",style:{width:"".concat(p,"%")}},p,"%"))},f.Separator=function(){return e.createElement("div",{className:"rpv-core__separator"})},f.Spinner=M,f.Splitter=function(r){var p=r.constrain,O=e.useContext(H).direction===f.TextDirection.RightToLeft,W=e.useRef(),X=e.useRef(),J=e.useRef(),ut=e.useRef(0),rt=e.useRef(0),V=e.useRef(0),lt=e.useRef(0),ot={capture:!0},xt=function(Pt){var Xt=W.current,Vt=X.current,Ut=J.current;if(Xt&&Vt&&Ut){var Le=lt.current,pe=Pt.clientX-ut.current,me=V.current+(O?-pe:pe),Se=Xt.parentElement.getBoundingClientRect().width,se=100*me/Se;if(Xt.classList.add("rpv-core__splitter--resizing"),p){var Ce=Se-me-Le;if(!p({firstHalfPercentage:se,firstHalfSize:me,secondHalfPercentage:100*Ce/Se,secondHalfSize:Ce}))return}Vt.style.width="".concat(se,"%"),document.body.classList.add("rpv-core__splitter-body--resizing"),Vt.classList.add("rpv-core__splitter-sibling--resizing"),Ut.classList.add("rpv-core__splitter-sibling--resizing")}},Rt=function(Pt){var Xt=W.current,Vt=X.current,Ut=J.current;Xt&&Vt&&Ut&&(document.body.classList.remove("rpv-core__splitter-body--resizing"),Xt.classList.remove("rpv-core__splitter--resizing"),Vt.classList.remove("rpv-core__splitter-sibling--resizing"),Ut.classList.remove("rpv-core__splitter-sibling--resizing"),document.removeEventListener("mousemove",xt,ot),document.removeEventListener("mouseup",Rt,ot))};return e.useEffect(function(){var Pt=W.current;Pt&&(lt.current=Pt.getBoundingClientRect().width,X.current=Pt.previousElementSibling,J.current=Pt.nextElementSibling)},[]),e.createElement("div",{ref:W,className:"rpv-core__splitter",onMouseDown:function(Pt){var Xt=X.current;Xt&&(ut.current=Pt.clientX,rt.current=Pt.clientY,V.current=Xt.getBoundingClientRect().width,document.addEventListener("mousemove",xt,ot),document.addEventListener("mouseup",Rt,ot))}})},f.TextBox=C,f.ThemeContext=H,f.Tooltip=function(r){var p=r.ariaControlsSuffix,O=r.content,W=r.offset,X=r.position,J=r.target,ut=S(!1),rt=ut.opened,V=ut.toggle,lt=e.useRef(),ot=e.useRef(),xt=e.useMemo(function(){return p||"".concat(T())},[]);a(function(){lt.current&&document.activeElement&&lt.current.contains(document.activeElement)&&Pt()});var Rt=function(){V(f.ToggleStatus.Open)},Pt=function(){V(f.ToggleStatus.Close)};return e.createElement(e.Fragment,null,e.createElement("div",{ref:lt,"aria-describedby":"rpv-core__tooltip-body-".concat(xt),onBlur:function(Xt){Xt.relatedTarget instanceof HTMLElement&&Xt.currentTarget.parentElement&&Xt.currentTarget.parentElement.contains(Xt.relatedTarget)?ot.current&&(ot.current.style.display="none"):Pt()},onFocus:Rt,onMouseEnter:Rt,onMouseLeave:Pt},J),rt&&e.createElement(ht,{ariaControlsSuffix:xt,contentRef:ot,offset:W,position:X,targetRef:lt},O()))},f.Viewer=function(r){var p=r.characterMap,O=r.defaultScale,W=r.enableSmoothScroll,X=W===void 0||W,J=r.fileUrl,ut=r.httpHeaders,rt=ut===void 0?{}:ut,V=r.initialPage,lt=V===void 0?0:V,ot=r.pageLayout,xt=r.initialRotation,Rt=xt===void 0?0:xt,Pt=r.localization,Xt=r.plugins,Vt=Xt===void 0?[]:Xt,Ut=r.renderError,Le=r.renderLoader,pe=r.renderPage,me=r.renderProtectedView,Se=r.scrollMode,se=Se===void 0?f.ScrollMode.Vertical:Se,Ce=r.setRenderRange,ye=Ce===void 0?Ls:Ce,We=r.transformGetDocumentParams,Ue=r.theme,ze=Ue===void 0?{direction:f.TextDirection.LeftToRight,theme:"light"}:Ue,Ve=r.viewMode,qe=Ve===void 0?f.ViewMode.SinglePage:Ve,cn=r.withCredentials,en=cn!==void 0&&cn,hn=r.onDocumentAskPassword,rn=r.onDocumentLoad,an=rn===void 0?function(){}:rn,on=r.onPageChange,sn=on===void 0?function(){}:on,mn=r.onRotate,Cn=mn===void 0?function(){}:mn,En=r.onRotatePage,fn=En===void 0?function(){}:En,bn=r.onSwitchTheme,Pn=bn===void 0?function(){}:bn,Ae=r.onZoom,be=Ae===void 0?function(){}:Ae,ae=e.useState({data:J,name:typeof J=="string"?J:"",shouldLoad:!1}),ue=ae[0],oe=ae[1],Pe=function(tn,nn){oe({data:nn,name:tn,shouldLoad:!0})},Te=e.useState(!1),de=Te[0],ee=Te[1],Re=pt(ue);e.useEffect(function(){var tn,nn,un,$e;tn=Re.data,$e=typeof(nn=J),(un=typeof tn)=="string"&&$e==="string"&&tn===nn||un==="object"&&$e==="object"&&tn.length===nn.length&&tn.every(function(kn,Nn){return kn===nn[Nn]})||oe({data:J,name:typeof J=="string"?J:"",shouldLoad:de})},[J,de]);var Oe=w({onVisibilityChanged:function(tn){ee(tn.isVisible),tn.isVisible&&oe(function(nn){return Object.assign({},nn,{shouldLoad:!0})})}}),Qe=typeof ze=="string"?{direction:f.TextDirection.LeftToRight,theme:ze}:ze,Ye=e.useState(Pt||kt),pn=Ye[0],yn=Ye[1],Fe={l10n:pn,setL10n:yn},gn=Object.assign({},{direction:Qe.direction},Fs(Qe.theme||"light",Pn));return e.useEffect(function(){Pt&&yn(Pt)},[Pt]),e.createElement(Bt.Provider,{value:Fe},e.createElement(H.Provider,{value:gn},e.createElement("div",{ref:Oe,className:"rpv-core__viewer rpv-core__viewer--".concat(gn.currentTheme),"data-testid":"core__viewer",style:{height:"100%",width:"100%"}},ue.shouldLoad&&e.createElement(xs,{characterMap:p,file:ue.data,httpHeaders:rt,render:function(tn){return e.createElement(Rs,{defaultScale:O,doc:tn,render:function(nn,un){return e.createElement(ks,{currentFile:{data:ue.data,name:ue.name},defaultScale:O,doc:tn,enableSmoothScroll:X,initialPage:lt,initialRotation:Rt,initialScale:un,pageLayout:ot,pageSizes:nn,plugins:Vt,renderPage:pe,scrollMode:se,setRenderRange:ye,viewMode:qe,viewerState:{file:ue,fullScreenMode:f.FullScreenMode.Normal,pageIndex:-1,pageHeight:nn[0].pageHeight,pageWidth:nn[0].pageWidth,pagesRotation:new Map,rotation:Rt,scale:un,scrollMode:se,viewMode:qe},onDocumentLoad:an,onOpenFile:Pe,onPageChange:sn,onRotate:Cn,onRotatePage:fn,onZoom:be})},scrollMode:se,viewMode:qe})},renderError:Ut,renderLoader:Le,renderProtectedView:me,transformGetDocumentParams:We,withCredentials:en,onDocumentAskPassword:hn}))))},f.Worker=function(r){var p=r.children,O=r.workerUrl;return tt.GlobalWorkerOptions.workerSrc=O,e.createElement(e.Fragment,null,p)},f.chunk=St,f.classNames=k,f.createStore=function(r){var p=r||{},O={},W=function(J,ut){var rt;p=g(g({},p),((rt={})[J]=ut,rt)),(O[J]||[]).forEach(function(V){return V(p[J])})},X=function(J){return p[J]};return{subscribe:function(J,ut){O[J]=(O[J]||[]).concat(ut)},unsubscribe:function(J,ut){O[J]=(O[J]||[]).filter(function(rt){return rt!==ut})},update:function(J,ut){W(J,ut)},updateCurrentValue:function(J,ut){var rt=X(J);rt!==void 0&&W(J,ut(rt))},get:function(J){return X(J)}}},f.getDestination=_e,f.getPage=ge,f.isFullScreenEnabled=A,f.isMac=function(){return typeof window<"u"&&/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)},f.useDebounceCallback=R,f.useIntersectionObserver=w,f.useIsMounted=nt,f.useIsomorphicLayoutEffect=y,f.usePrevious=pt,f.useRenderQueue=yt})(core_min);/**
 * A React component to view a PDF document
 *
 * @see https://react-pdf-viewer.dev
 * @license https://react-pdf-viewer.dev/license
 * @copyright 2019-2023 Nguyen Huu Phuoc <<EMAIL>>
 */lib.exports=core_min;var libExports=lib.exports;export{pdf as a,commonjsRequire as c,libExports as l,pdfjsModule as p,require$$0 as r};
