import{j as s}from"./@chakra-ui/react-e5fbf24f.js";import{a as t,u as d}from"./vendor-df163860.js";import"./yup-b43a9d72.js";import{e as x,G as m,t as p,c as u}from"./index-ca2a1632.js";import"./pdf-lib-d770586c.js";import"./react-toggle-6e1dcb63.js";import"./index-f1c3f18b.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";import"./react-confirm-alert-ec5b262e.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-159f2aea.js";import"./@tanstack/react-virtual-d31b3415.js";import"./react-hook-form-c0d39979.js";import"./@hookform/resolvers-6ec18a70.js";import"./react-icons-2f7b5d9d.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-5b9c29d1.js";import"./react-pdf-1a37acfb.js";import"./@craftjs/core-cf2f5d59.js";import"./react-calendar-51cdf067.js";import"./@react-pdf-viewer/core-9b83754d.js";import"./@tanstack/react-query-eaef12f9.js";import"./@react-pdf/renderer-d57b912e.js";import"./@fullcalendar/core-7f118d00.js";import"./@uppy/dashboard-12912511.js";import"./@uppy/core-59463b77.js";import"./@uppy/aws-s3-67e39052.js";import"./@uppy/compressor-abfad62a.js";let l=new x;const X=()=>{t.useContext(m);const{dispatch:c}=t.useContext(m),[e,o]=t.useState({}),[n,r]=t.useState(!0),a=d();return t.useEffect(function(){(async function(){try{r(!0),l.setTable("requests");const i=await l.callRestAPI({id:Number(a==null?void 0:a.id),join:""},"GET");i.error||(o(i.model),r(!1))}catch(i){r(!1),console.log("error",i),p(c,i.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:n?s.jsx(u,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Requests"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Customer"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.customer})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Sku"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.sku})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Purge Quantity"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.purge_quantity})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Available Quantity"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.available_quantity})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Actions"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.actions})]})})]})})};export{X as default};
