import{j as t}from"./@chakra-ui/react-e5fbf24f.js";import"./vendor-df163860.js";import"./@emotion/serialize-358fdd7f.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/react-4b83849f.js";import"./@emotion/cache-febbd770.js";const a=({className:s="",fill:o="#A1A1A9",stroke:i="#717179",onClick:r=()=>{}})=>t.jsxs("svg",{className:`${s}`,onClick:r,width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M12.6546 10.5335C13.296 9.50909 13.6668 8.29793 13.6668 7.00016C13.6668 3.31826 10.6821 0.333496 7.00018 0.333496C5.70241 0.333496 4.49125 0.704314 3.46688 1.34577L12.6546 10.5335Z",fill:o}),t.jsx("path",{d:"M1.94569 2.65279L0.979943 1.68705C0.784681 1.49179 0.784681 1.1752 0.979943 0.979943C1.1752 0.784681 1.49179 0.784681 1.68705 0.979943L2.98503 2.27793C2.98779 2.28056 2.99052 2.28323 2.99322 2.28593L11.7142 11.0069C11.7169 11.0096 11.7196 11.0124 11.7222 11.0151L13.0204 12.3133C13.2156 12.5085 13.2156 12.8251 13.0204 13.0204C12.8251 13.2156 12.5085 13.2156 12.3133 13.0204L11.3473 12.0545C10.1805 13.0589 8.6609 13.6666 7.00016 13.6666C3.31826 13.6666 0.333496 10.6819 0.333496 6.99998C0.333496 5.33924 0.941287 3.81968 1.94569 2.65279Z",fill:o})]});export{a as default};
