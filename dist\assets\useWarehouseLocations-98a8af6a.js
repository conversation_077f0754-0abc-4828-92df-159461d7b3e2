import{a as l}from"./vendor-df163860.js";import{e as E,G as k,A as v,r as y,t as L,F as S}from"./index-ca2a1632.js";const A=(e={refresh:!1,filter:["status,eq,1"],saveState:"warehouse_location"})=>{var f;new E;const{state:x,dispatch:w}=l.useContext(k),{state:{},dispatch:s}=l.useContext(v),c=x[e==null?void 0:e.saveState],[r,t]=l.useState([]),C=l.useCallback(()=>{(async()=>{var h,g,u,m,d;try{const a=await y(w,s,"warehouse_location",{...e!=null&&e.filter&&((h=e==null?void 0:e.filter)!=null&&h.length)?{filter:e==null?void 0:e.filter}:null},e==null?void 0:e.saveState);a!=null&&a.error||t(()=>a==null?void 0:a.data)}catch(a){console.log(a.message);const b=(u=(g=a==null?void 0:a.response)==null?void 0:g.data)!=null&&u.message?(d=(m=a==null?void 0:a.response)==null?void 0:m.data)==null?void 0:d.message:a==null?void 0:a.message;L(s,b)}})()},[r]);return l.useEffect(()=>{if(S(c==null?void 0:c.data)||e!=null&&e.refresh)return C();t(()=>c==null?void 0:c.data)},[e==null?void 0:e.refresh,(f=e==null?void 0:e.filter)==null?void 0:f.length]),[r,t]},_=A;export{_ as u};
